{
   "capacityProviderStrategy": [
    {
      "capacityProvider": "FARGATE",
      "base": 1,
      "weight": 1
    },
    {
      "capacityProvider": "FARGATE_SPOT",
      "base": 0,
      "weight": 0
    }
  ],
  "deploymentConfiguration": {
    "deploymentCircuitBreaker": {
      "enable": true,
      "rollback": true
    },
    "maximumPercent": 200,
    "minimumHealthyPercent": 50
  },
  "deploymentController": {
    "type": "ECS"
  },
  "desiredCount": 2,
  "enableECSManagedTags": false,
  "enableExecuteCommand": false,
  "healthCheckGracePeriodSeconds": 60,
  "launchType": "",
  "loadBalancers": [
    {
      "containerName": "EcsApp",
      "containerPort": {{ must_env `PORT_NUMBER` }},
      "targetGroupArn": "{{ must_env `TARGET_GROUP_ARN` }}"
    }
  ],
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "assignPublicIp": "DISABLED",
      "securityGroups": ["{{ must_env `SECURITY_GROUP` }}"],
      "subnets": ["{{ must_env `SUBNET_1` }}", "{{ must_env `SUBNET_2` }}", "{{ must_env `SUBNET_3` }}"]
    }
  },
  "pendingCount": 0,
  "platformFamily": "Linux",
  "platformVersion": "LATEST",
  "propagateTags": "NONE",
  "runningCount": 0,
  "schedulingStrategy": "REPLICA",
  "serviceConnectConfiguration": {
    "enabled": true,
    "namespace": "{{ must_env `NAMESPACE` }}",
    "services": []
  }
}
