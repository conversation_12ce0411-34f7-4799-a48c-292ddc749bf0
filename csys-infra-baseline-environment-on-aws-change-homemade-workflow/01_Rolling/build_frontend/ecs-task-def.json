{
  "containerDefinitions": [
    {
      "cpu": 0,
      "docker<PERSON><PERSON><PERSON>": {},
      "essential": true,
      "image": "{{ must_env `IMAGE1_NAME` }}",
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "{{ must_env `LOG_GROUP` }}",
          "awslogs-region": "ap-northeast-1",
          "awslogs-stream-prefix": "ECSApp-"
        }
      },
      "name": "EcsApp",
      "portMappings": [
        {
          "appProtocol": "",
          "containerPort": {{ must_env `PORT_NUMBER` }},
          "hostPort": {{ must_env `PORT_NUMBER` }},
          "protocol": "tcp"
        }
      ]
    }
  ],
  "cpu": "256",
  "executionRoleArn": "{{ must_env `EXECUTION_ROLE_ARN` }}",
  "taskRoleArn": "{{ must_env `TASK_ROLE_ARN` }}",
  "family": "{{ must_env `FAMILY` }}",
  "ipcMode": "",
  "memory": "512",
  "networkMode": "awsvpc",
  "pidMode": "",
  "requiresCompatibilities": ["FARGATE"],
  "volumes": [
    {
      "name": "Efs",
      "efsVolumeConfiguration": {
          "fileSystemId": "fs-01d95044710c14195",
          "rootDirectory": "/"
      }
    }
  ]
}
