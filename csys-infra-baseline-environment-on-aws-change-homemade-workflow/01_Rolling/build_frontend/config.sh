#!/bin/bash

aws application-autoscaling register-scalable-target \
--service-namespace ecs --scalable-dimension ecs:service:DesiredCount \
--resource-id service/${ECS_CLUSTER}/${ECS_SERVICE} \
--min-capacity 2 --max-capacity 5

aws application-autoscaling put-scaling-policy \
--service-namespace ecs --scalable-dimension ecs:service:DesiredCount \
--resource-id service/${ECS_CLUSTER}/${ECS_SERVICE} \
--policy-name Test-target-tracking-scaling-policy --policy-type TargetTrackingScaling \
--target-tracking-scaling-policy-configuration '{ "TargetValue": '60', "PredefinedMetricSpecification": {"PredefinedMetricType": "ECSServiceAverageCPUUtilization" }, "ScaleOutCooldown": 60,"ScaleInCooldown": 60}'

aws elbv2 modify-target-group \
--target-group-arn ${TARGET_GROUP_ARN} \
--health-check-path "/" \
--health-check-interval-seconds 30 \
--health-check-timeout-seconds 5 