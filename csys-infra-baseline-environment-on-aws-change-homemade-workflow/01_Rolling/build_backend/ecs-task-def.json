{
  "containerDefinitions": [
    {
      "cpu": 512,
      "docker<PERSON><PERSON><PERSON>": {},
      "essential": true,
      "image": "{{ must_env `IMAGE1_NAME` }}",
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "{{ must_env `LOG_GROUP` }}",
          "awslogs-region": "ap-northeast-1",
          "awslogs-stream-prefix": "EcsBackend-"
        }
      },
      "name": "EcsBackend",
      "portMappings": [
        {
          "name": "backend",
          "containerPort": {{ must_env `PORT_NUMBER` }},
          "hostPort": {{ must_env `PORT_NUMBER` }},
          "protocol": "tcp"
        }
      ],
      "healthCheck": {
        "command": [
          "CMD-SHELL",
          "echo 'Health check OK' && exit 0"
        ],
        "interval": 30,
        "timeout": 5,
        "retries": 2,
        "startPeriod": 60
      },
      "volumesFrom": [],
      "systemControls": [],
      "linuxParameters": {
        "initProcessEnabled": true
      }
    }
  ],
  "cpu": "512",
  "executionRoleArn": "{{ must_env `EXECUTION_ROLE_ARN` }}",
  "taskRoleArn": "{{ must_env `TASK_ROLE_ARN` }}",
  "family": "{{ must_env `FAMILY` }}",
  "memory": "1024",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"]
}