# HowToUseEcspresso

- ここでは、ECS のデプロイツールである Ecspresso の利用方法について記載する。

## 概要

- ECS サービスへのデプロイ作業は CodePipeline を利用して実施する。
- 全体的なデプロイフローは以下のとおり。
  1. アプリケーションチームのリポジトリから GitHubActions を実行する。
  1. リポジトリ配下の build.sh 内で docker build を行う。
  1. 作成されたイメージを ECR へプッシュする。
  1. タスク定義などの設定ファイルを zip に圧縮して S3 へ配置する。
  1. 配置をトリガーに CodePipeline が起動する。
  1. CodeBuild 内で ecspresso コマンドが実行され、ECS サービスへのデプロイが実行される。

**全体構成図**
![](./images/Ecspresso-deployflow.png)

## ECS デプロイ関連ファイルの概要

- ecspresso をデプロイする上で利用する関連ファイルは以下の２つ。
  1. ecspresso conf ファイル
  1. ワークフローファイル

### ecspresso conf

- ecspresso conf とは、ecspresso に関わる設定ファイルを指す。
- [こちら](https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade-workflow)のリポジトリで管理されており、フロントエンドコンテナ用とバックエンドコンテナ用でそれぞれ別のフォルダで管理されている。
- フロントエンドコンテナとバックエンドコンテナはそれぞれアーキテクチャが異なるため（後述）、設定ファイルの内容も別管理とする。
- アプリケーションのビルドを実施する build.sh や、ECS サービス・タスクの設定ファイルが当該フォルダで管理されている。
  ```
  .github
  ├── workflows
  │
  01_Rolling
  ├── build_frontend
  │   ├── build.sh
  │   ├── dev.conf
  │   ├── stg.conf
  │   └── prd.conf
  │   .
  │   .
  │
  └── build_backend
      ├── build.sh
      ├── dev.conf
      ├── stg.conf
      └── prd.conf
      .
      .
  ```

### ワークフローファイル

- ワークフローファイルとは、`.github/workflows`配下の GitHubActions を実行するためのファイルである。ecspresso conf と同じリポジトリで管理されている。
- ecspresso conf はこちらのワークファイルを通じて実行される。
  ```
  .github
  ├── workflows
  │   ├── run-build-backend.yml
  │   ├── run-build-frontend.yml
  │   .
  │   .
  01_Rolling
  ├── build_frontend
  └── build_backend
  ```

### ユースケースごとのフロー

- 大きく分けて２つのユースケースが存在するが、大まかな流れとしては双方とも、➀ecspresso conf を編集 ➁GitHubAction によりパイプラインを稼働、という流れになる。

#### フロー１：アプリを更新

- 想定ケース
  - アプリケーションコードの変更
  - Dockerfile の変更

#### 事前準備

- 管理用リポジトリに配置しているフォルダはサンプルコードのため、アプリケーションのリポジトリにクローンした後、各 Pj 毎にカスタマイズを実施する必要がある。 具体的には以下 3 点を事前にカスタマイズする。
  1. build.sh
  1. 環境ファイル（dev,stg,prd）
  1. ecs-task-def.json 内の環境変数周り

##### 1.build.sh

- 当該シェル内で docker build を実施するため、Dockerfile の場所を相対パスで指定する必要がある。  
   ※`${SCRIPT_DIR}`が build.sh の絶対パスを示している。
  ```bash
  1  # コンテナフォルダのハッシュ値をDockerタグに設定
  2  IMAGE_TAG=$(git rev-parse --short=7 HEAD)
  3  echo "echo Building the Docker image..."
  4  cd $SCRIPT_DIR/../
  5  docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .
  6  if [ $? -ne 0 ]; then
  7      echo "Failed Docker build."
  8     exit 1
  9  fi
  10 docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
  ```

##### 2.環境ファイル（dev,stg,prd）

- build.sh 内では CloudFormation スタック内から、パイプラインの関連リソース名を取得する処理が実装されている。
- デプロイされているスタック名を特定するために`PJ_PREFIX`を修正する。
- また、コンテナ名も併せて特定する必要があるため、`APP_NAME`も修正する。  
  **環境名.conf**
  ```bash
  # スタックで指定されているプレフィックスを指定
  PJ_PREFIX=BLEA
  # CDK上で指定しているAPP_NAMEを記載する
  APP_NAME=EcsApp
  ```

##### 3.ecs-task-def.json 内の環境変数周り

- 管理リポジトリに格納されている ecs-task-def.json には、環境変数として Systems Manager と Secrets Manager のサンプル値が記載されている。
- こちらはサンプル値のため、そのままデプロイすると存在しないパラメータを参照し、エラーになってしまう。
- したがって、デプロイ作業前にパラメータ登録を済ませておく。
- Systems Manager と Secrets Manager の登録方法とテンプレートへの記載方法を以下に記載する。

  ##### 登録方法（Systems Manager）

  1. Systems Manager コンソールにアクセスし、左ペインの「パラメータストア」を選択  
     ![](./images/ssm.png)
  2. 「パラメータの作成」からパラメータを登録  
     ![](./images/ssm-add-param.png)
  3. パラメータの情報を登録
  4. パラメータの名前を設定(「/」で区切ることで階層を分けることが可能)
  5. タイプを選択
     - 文字列：文字列型や数値型の値に使用
     - 文字列のリスト：文字列型配列の値に使用
     - 安全な文字列：KMS で暗号化する際に使用
  6. 登録したい値を入力
  7. 「パラメータを作成」をクリックすると、パラメータの登録が完了  
     ![](./images/ssm-set-param.png)

  ##### テンプレートへの記載方法（Systems Manager）

  - Systems Manager に登録した環境変数を使用する場合は、`` "変数名":{{ssm `パラメータ名`}} ``の形で宣言することで使用可能。
  - 使用例は以下の通りである。

  (例)ecs-task-def.json

  ```json
  "taskRoleArn": "{{ ssm `/ecs/frontend/task-role` }}"
  "containerDefinitions": [
      "environment": [
          {
              "name": "RAILS_ENV",
              "value": "{{ssm `/ecs/frontend/rails_env`}}"
          }
      ]
  ]
  ```

  - パラメータ名は下記の赤枠からコピーする。  
    ![](./images/ssm-show-param.png)

  ##### 登録方法（Secrets Manager）

  1. Secrets Manager コンソールにアクセスし、「新しいシークレットを保存する」からシークレットを登録  
     ![](./images/secrets-add-secret.png)
  2. シークレットの情報を登録
     1. シークレットのタイプを選択
     2. キーと値を登録
     3. 暗号化キーの中から「AppKey-for-app」と付くものを選択
     4. 「次」を選択  
        ![](./images/secrets-set-param.png)
  3. シークレットの設定を登録
     1. シークレットの名前を設定（「/」で区切ることで階層を分けることが可能）
     2. 「次」を選択  
        ![](./images/secrets-set-name.png)
  4. ローテーションの設定を登録
     1. デフォルトのまま、ローテーションは無効にしておく（※ON にすると値が書き換えられてしまうため）
     2. 「次」を選択  
        ![](./images/secrets-set-rotation.png)
  5. 登録内容に問題なければ、「保存」を選択

  ##### テンプレートへの記載方法（Secrets Manager）

  Secrets Manager に登録した環境変数を使用する場合は、`"secrets:[{"name":"変数名", "valueFrom":"シークレットのARN"}]"`の形で宣言することで使用可能。（[公式ドキュメント](https://docs.aws.amazon.com/ja_jp/AmazonECS/latest/developerguide/secrets-envvar-secrets-manager.html#secrets-envvar-secrets-manager-update-container-definition)）

  ```json
  # シークレット全体を参照する
  "valueFrom": "<シークレットのARN>"

  # シークレット内の特定のキーを参照する
  "valueFrom": "<シークレットのARN>:<キー>::"
  ```

  - 使用例は以下の通りである。

  (例)ecs-task-def.json  
  ※Aurora のシークレットは CDK デプロイ時に自動で作成される。  
  ![](./images/secrets-aurora.png)

  ```json
  "containerDefinitions": [
      "secrets": [
              {
                  "name": "APP_DATABASE_HOST",
                  "valueFrom": "arn:aws:secretsmanager:ap-northeast-1:111111111111:secret:StgBLEADBAuroraAuroraMysqlC-ffQGLbDialFI-UfBaJK:host::"
              },
              {
                  "name": "APP_DATABASE_USER",
                  "valueFrom": "arn:aws:secretsmanager:ap-northeast-1:111111111111:secret:StgBLEADBAuroraAuroraMysqlC-ffQGLbDialFI-UfBaJK:username::"
              },
              {
                  "name": "APP_DATABASE_PASSWORD",
                  "valueFrom": "arn:aws:secretsmanager:ap-northeast-1:111111111111:secret:StgBLEADBAuroraAuroraMysqlC-ffQGLbDialFI-UfBaJK:password::"
              },
              {
                  "name": "API_KEY",
                  "valueFrom": "arn:aws:secretsmanager:ap-northeast-1:111111111111:secret:stg/ecs/frontend/appauth-46j1mY:api_key::"
              }
      ]
  ]
  ```

  - シークレットの ARN は下記の赤枠（シークレットの ARN）からコピーする。  
    「シークレットの値を取得」を選択すると、登録した値を確認できる。  
    ![](./images/secrets-show-arn.png)
  - シークレットのデータを確認  
    ![](./images/secrets-show-secrets.png)

#### デプロイ作業

- デプロイ作業は GitHubAction から実行する。
- 詳細な手順は[HowToUse-ecs-deploy.md](./../.github/workflows/HowToUse-ecs-deploy.md)を参照。

#### フロー 2：アプリの更新なしでコンテナ数などを更新

- 想定ケース
  - ヘルスチェックの設定変更
  - オートスケールの設定変更
  - コンテナスペック等の変更

##### ヘルスチェックの設定変更

- ヘルスチェックはフロントエンドコンテナとバックエンドコンテナで実施方法が異なるため、設定の更新方法も異なる。
- フロントエンドは環境ファイルから、バックエンドは ecs-task-def.json から設定変更を行う。
  ![](./images/difference-between-front-and-back.dio.png)

  ##### フロントエンドの変更方法

  - 環境ファイルの以下項目を変更する。

  **環境名.conf**

  ```bash
  # ALB HealthCheck Setting
  # ヘルスチェックパス
  HEALTH_PATH="/"
  # ヘルスチェックを実施する間隔（秒）
  INTERVAL=30
  # ヘルスチェックを失敗と見なす、ターゲットからレスポンスがない時間（秒）
  TIMEOUT=5
  ```

  ##### バックエンドの変更方法

  - ecs-task-def.json の`healthCheck`を変更する。

  **ecs-task-def.json**

  ```json
  "healthCheck": {
    "command": [
        "CMD-SHELL",
        "curl -f http://localhost/ || exit 1"
    ],
    "interval": 30,
    "timeout": 5
  }
  ```

##### オートスケールの設定変更

- オートスケールの変更はフロントエンド、バックエンド双方とも環境ファイルから変更する。

**環境名.conf**

```bash
# AutoScale Setting
# タスク数の最小値
MIN_CAPACITY=2
# タスク数の最大値
MAX_CAPACITY=5
# タスクの負荷がTARGET_VALUEを超えるとタスク起動数を増やし、収まるとタスク数が減る。
TARGET_VALUE=60
```

##### コンテナスペック等の変更

- ヘルスチェック、オートスケール以外の設定変更は、`ecs-task-def.json`や`ecs-service-def.json`から変更する。  
  ※フロントエンド、バックエンドとも json ファイルから変更
- ECS サービスに関わる設定変更は、`ecs-service-def.json`から、ECS タスクに関わる設定変更は`ecs-task-def.json`から行う。

#### デプロイ作業

- デプロイ作業は GitHubAction から実行する。
- 詳細な手順は[HowToUse-ecs-deploy.md](./../.github/workflows/HowToUse-ecs-deploy.md)を参照。
