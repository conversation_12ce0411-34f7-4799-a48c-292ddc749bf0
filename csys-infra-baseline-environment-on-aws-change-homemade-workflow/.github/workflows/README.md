# GitHubActions workflow 一覧

GitHubActions から AWS に接続する際に、利用する workflow ファイルを管理する。

## 概要

- 環境毎のブランチで利用する workflow ファイルはプロジェクトに依存するため、各担当者で適宜、取捨選択を行う。

  ※順次更新

  | 名称                    | 用途                                           | 手順書                                               |
  | ----------------------- | ---------------------------------------------- | ---------------------------------------------------- |
  | ecs-deploy-frontend.yml | フロントエンドコンテナ用 CodePipeline を発火   | [HowToUse-ecs-deploy.md](./HowToUse-ecs-deploy.md)   |
  | ecs-deploy-backend.yml  | バックエンドコンテナ用 CodePipeline を発火     | [HowToUse-ecs-deploy.md](./HowToUse-ecs-deploy.md)   |
  | maintenance-on          | Waf に IP 制限とベーシック認証のルールを有効化 | [HowToUse-maintenance.md](./HowToUse-maintenance.md) |
  | maintenance-off         | Waf に IP 制限とベーシック認証のルールを無効化 | [HowToUse-maintenance.md](./HowToUse-maintenance.md) |

## ecs-deploy-{frontend|backend}.yml 詳細

- ecs-deploy-{frontend|backend}.yml はアプリケーションのビルド、ECR へのプッシュ、パイプライン稼働の一連動作を担う。
  - ビルドしたアプリケーションに脆弱性診断を行う必要があり、脆弱性管理に FutureVuls を利用している。
  - 脆弱性診断と、FutureVuls への診断結果のアップロードを build.py で実行している。
  - そのため、事前準備として FutureVuls の ID 情報を GitHub Secrets に登録する必要がある。
- ワークフローを実行する上で、以下 4 つの手順を実施する必要がある。
  1.  FutureVuls の利用申請
  1.  FutureVuls の ID を GitHub Secrets に登録

### 1. FutureVuls の利用申請

- ビルドするアプリケーション毎に、マイナビ社内で FutureVuls の利用申請を事前に行う。
- 環境毎 (dev,stg,prd) にフロント/バックエンドのアプリケーションビルドする場合、計6つ分の申請が必要。
- 診断結果を FutureVuls にアップロードするため、申請時に払い出された FutureVuls のグループ ID、トークン、UUID が環境ごとにそれぞれ必要となる。
  - 参考：https://help.vuls.biz/manual/scan/trivy_scan/#cicdパイプラインに組み込む方法
- ビルド時に払い出された ID が必要となるため、ワークフロー実行環境の GitHub Secrets に登録する必要がある。

### 2. FutureVuls の ID を GitHubSecrets に登録

- グループ ID とトークンはマイナビ社内の FutureVuls 利用申請時に払い出されたものを登録する。
  - FutureVuls の利用申請が完了していない状態で開発を進める場合、後述の「[FutureVuls 脆弱性スキャンの無効化手順](#futurevuls-脆弱性スキャンの無効化手順)」を参照して FutureVuls スキャンを無効化する。
  - **※ 利用申請が完了して ID 情報が払い出されたら、有効化し ID 情報の登録をすること。**
- Secrets 登録手順は下記の通り。

1.  Github にアクセスし、対象リポジトリの「Settings」をクリックする。  
    ![](./images/how-to-register-GitHubSecrets-1.png)
1.  左ペインにある「Secrets and Variables」→「Actions」をクリックしたら、「Secrets」→「New repository secret」をクリック。  
    ![](./images/how-to-register-GitHubSecrets-2.png)
1.  `Name` には下記の Secrets 名を入力する。  
    ![](./images/how-to-register-GitHubSecrets-3.png)

#### 登録する Secrets 

- 登録が必要な Secrets 名は以下の通り。
  - VULS_SAAS_GROUPID_{FRONT|BACK}_{DEV|STG|PRD}
    - スキャン結果をアップロードするグループの ID.
  - VULS_SAAS_TOKEN_{FRONT|BACK}_{DEV|STG|PRD}
    - スキャン権限をもった FutureVuls のトークン。
  - VULS_SAAS_UUID_{FRONT|BACK}_{DEV|STG|PRD}
    - 対象イメージを一意に識別する FutureVuls 内のサーバ UUID.
    - 新規登録の場合は uuidgen コマンド等で生成した値を指定する。

#### FutureVuls 脆弱性スキャンの無効化手順

- 以下のファイルを編集する。
  - [ecs-deploy-frontend.yml](/.github/workflows/ecs-deploy-frontend.yml)
  - [ecs-deploy-backend.yml](/.github/workflows/ecs-deploy-backend.yml)
- yaml ファイル内の `env` > `enable-futurevuls-scan` の値を `false` に変更する。
  - 有効化する際は、`true` にする。

**※ 利用申請が完了し、ID 情報が払い出されたら有効化と Secrets の登録すること。**

## workflows_templates 詳細

### トリガーパターン

- 以下の３パターンを用意しており、要件に応じてコメントイン・削除を実施する。

  1. 手動実行
  1. 自動実行(プルリクエスト作成時)
  1. 自動実行(プッシュ or マージ実行時)

  **workflows_template.yml（on section）**

  ```
  name: <ワークフロー名>

  on:
  # ➀手動実行の場合
  #   workflow_dispatch:
  #     #GitHubActionsの実行時にGUIからユーザに環境変数の入力を求める場合は以下を利用
  #     inputs:
  #       <変数名(ユーザ入力)>:
  #         description: '表示名'
  #         required: true

  # ➁自動実行(プルリクエスト作成時)の場合
  #   pull_request:
  #   branches: main
  #   paths: ['lib/**', 'bin/**', 'params/**'] #トリガー対象ファイル指定箇所

  # ➂自動実行(プッシュ or マージ実行時)の場合
  #   push:
  #     branches: [ "main" ]
  ```

### AWS リソースへの処理実装

- run 以下に実際の AWS リソースに対する操作を AWS CLI 形式 で記載していく。
- AWS CLI コマンド操作をする上で必要な IAM ポリシーをアタッチした IAM ロールを CDK 側で用意しておく必要がある。

  **workflows_template.yml（run section）**

  ```
  # - name: <動作名>
  #  run: |
  #~以下に動作内容を追記する~
  ```

## GitHubVariables への Role 登録

OIDC にて接続を行う際は必ず下記の手順を実行し、該当のロールを GithubVariables に登録すること。

1.  IAM ロールの ARN 取得
    AWS マネージメントコンソールにアクセスし、デプロイした OIDC スタックから 使用するロールの ARN を取得
    GitHubAction から実行するのに必要な権限が付与された IAM ロールを CDK で作成
2.  Github にアクセスし、対象リポジトリの「Settings」をクリックし、左ペインにある「Secrets and Variables」→「Actions」をクリックしたら、「New repository variable」をクリック。

    ![](../../doc/images/GithubVariables-Settings.png)

3.  画面の Name に、使用するワークフローファイルにて「`vars.<Variable 名>_<環境名>`」と記載されている箇所の 「`<Variable 名>_<環境名>`」を入力する。  
    Value に先ほど取得した ARN を貼り付けて Add Variable をクリック。  
     ※`<環境名>`は環境に合わせて yml ファイル側、GithubVariables 側、共に修正・変更必要。  
     ![](../../doc/images/GithubVariables-ARN.png)
