name: Deploy ECS Frontend

on:
  #  ※手動実行の場合
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: environment
        options:
          - dev
          - stg
          - prd
env:
  environment: ${{github.event.inputs.environment}}
  elable-futurevuls-scan: true  # Enable vulnerability scanning using FutureVuls. If not using FutureVuls, set to false.

# ※自動実行(プッシュ)の場合
#   push:
#     branches: [ "main" ]
# env:
#   environment: '<dev/stg/prod>から選択'

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.ecs_frontend }}
          role-session-name: GitHubActions
          aws-region: ap-northeast-1

      - name: Setting FutureVuls Environment
        run: |
          if [ ${{ env.environment }} = "dev" ]; then
            echo "VULS_SAAS_GROUPID=${{ secrets.VULS_SAAS_GROUPID_FRONT_DEV }}" >> $GITHUB_ENV
            echo "VULS_SAAS_TOKEN=${{ secrets.VULS_SAAS_TOKEN_FRONT_DEV }}" >> $GITHUB_ENV
            echo "VULS_SAAS_UUID=${{ secrets.VULS_SAAS_UUID_FRONT_DEV }}" >> $GITHUB_ENV
          elif [ ${{ env.environment }} = "stg" ]; then
            echo "VULS_SAAS_GROUPID=${{ secrets.VULS_SAAS_GROUPID_FRONT_STG }}" >> $GITHUB_ENV
            echo "VULS_SAAS_TOKEWN=${{ secrets.VULS_SAAS_TOKEN_FRONT_STG }}" >> $GITHUB_ENV
            echo "VULS_SAAS_UUID=${{ secrets.VULS_SAAS_UUID_FRONT_STG }}" >> $GITHUB_ENV
          elif [ ${{ env.environment}} = "prd" ]; then
            echo "VULS_SAAS_GROUPID=${{ secrets.VULS_SAAS_GROUPID_FRONT_PRD }}" >> $GITHUB_ENV
            echo "VULS_SAAS_TOKEN=${{ secrets.VULS_SAAS_TOKEN_FRONT_PRD }}" >> $GITHUB_ENV
            echo "VULS_SAAS_UUID=${{ secrets.VULS_SAAS_UUID_FRONT_PRD }}" >> $GITHUB_ENV
          fi

      - name: Run build.sh
        run: bash ./01_Rolling/build_frontend/build.sh ${{ env.environment }} ${{ env.enable-futurevuls-scan }}
