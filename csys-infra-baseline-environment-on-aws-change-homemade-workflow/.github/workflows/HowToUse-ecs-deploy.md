# HowToUse-ecs-deploy

ここでは、ecs-deploy-ecspresso.yml と ecs-deploy-bg.yml の利用方法について記載する

## 目的

- アプリ担当者が GitHub からの操作のみで、ECS アプリケーションのデプロイを行う。

## 概要

### 構成図

![](./../../doc/images/run-build.sh-deployflow.png)

### 「処理フロー」

1.　 build\.sh 起動用ワークフローを GitHubActions で手動または自動で実行する。  
2.　 build\.sh が実行され、build\.sh に実装された ECR と S3 への処理が開始する。  
3.　 ECR に Docker イメージをプッシュする。  
4.　 S3 バケットへ ECS サービスとタスク定義などのソースファイルを zip 化したものをアップロードする。  
5.　 S3 へのアップロードをトリガーとして、CodePipeline の処理が開始する。  
6.　 Rolling 方式または Blue/Green 方式で ECS アプリケーションがデプロイされる。

## ワークフローの内容

- ecs-deploy-frontend.yml
  - フロンドエンドコンテナ
  - Rolling 方式
  - Docker イメージを ECR へプッシュ
  - S3 バケットへソースファイルをアップロード
- ecs-deploy-backend.yml
  - バックエンドコンテナ
  - Rolling 方式
  - Docker イメージを ECR へプッシュ
  - S3 バケットへソースファイルをアップロード

## 事前に必要な設定

### AWS 側の設定

- ECS スタックとその他必要なスタックをデプロイ

### GitHub 側の設定

1. ワークフローファイルの修正

   1. build.sh の実行のタイミング（トリガーパターン）を選択  
      [README.md](README.md) を参照すること。

   2. 環境名を選択  
      選択した環境の環境ファイルの値を build.sh が参照する。

      - 手動実行の場合：ワークフロー実行時にプルダウンから環境名を選択する  
        ![](./../../doc/images/run-build.sh-GitHubActions.png)

      - 自動実行の場合：ワークフローファイルに書き込む
        ```bash
        env:
          environment: 'stg'
        ```

2. GitHubSecrets への Role 登録
   - [README.md](README.md) を参照

## 実行手順

- ワークフローの実行

  1. 「Actions」タブに遷移し、左ペインより「Deploy ECS Frontend」（または Deploy ECS Backend）というワークフローを選択する。
  2. 「Run workflow」のプルダウンを開き、「environment」で環境を選択する。
  3. 「Run workflow」ボタンを押下し、実行する。
