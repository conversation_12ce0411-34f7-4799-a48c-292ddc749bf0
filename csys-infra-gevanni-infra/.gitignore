*.d.ts
*.js
!jest.config.js
node_modules
.DS_Store
!.versionrc.js

# CDK asset staging directory
.cdk.staging
cdk.out

# Parcel default cache directory
.parcel-cache

# CDK context
cdk.context.json

# Lambda related
!/lambda/batch-sample-app
!/lambda/batch-sample-app/*.js

# Container Buld temp
/container/sample-frontend/build/image.zip
/container/sample-frontend/build/imagedefinitions.json
/container/sample-backend/build/image.zip
/container/sample-backend/build/imagedefinitions.json
