import boto3
import os
import sys
import time
import argparse
import configparser
from botocore.exceptions import ClientError

autoscaling = boto3.client("application-autoscaling")
ecs = boto3.client("ecs")

ecs_cluster = os.environ["ECS_CLUSTER"]
ecs_service = os.environ["ECS_SERVICE"]

script_dir = os.path.dirname(os.path.abspath(__file__))


# 設定ファイル読み込み
def read_param_file():
    param_file = os.path.join(script_dir, "param.ini")
    if not os.path.isfile(param_file):
        sys.exit("Configuration file 'param.ini' not found.")

    param = configparser.ConfigParser()
    param.read(param_file)
    return param


# スケーラブルターゲットを設定
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/application-autoscaling/client/register_scalable_target.html
def register_scalable_target(min_capacity, max_capacity):
    try:
        response = autoscaling.register_scalable_target(
            ServiceNamespace="ecs",
            ScalableDimension="ecs:service:DesiredCount",
            ResourceId=f"service/{ecs_cluster}/{ecs_service}",
            MinCapacity=int(min_capacity),
            MaxCapacity=int(max_capacity),
        )
        print("Scalable target registered:", response)
    except ClientError as e:
        print(f"Error registering scalable target: {e}")


# ターゲット追跡スケーリングポリシーの設定
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/application-autoscaling/client/put_scaling_policy.html
def put_scaling_policy(target_value):
    try:
        response = autoscaling.put_scaling_policy(
            ServiceNamespace="ecs",
            ScalableDimension="ecs:service:DesiredCount",
            ResourceId=f"service/{ecs_cluster}/{ecs_service}",
            PolicyName="Test-target-tracking-scaling-policy",
            PolicyType="TargetTrackingScaling",
            TargetTrackingScalingPolicyConfiguration={
                "TargetValue": int(target_value),
                "PredefinedMetricSpecification": {
                    "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
                },
            },
        )
        print("Scaling policy put:", response)
    except ClientError as e:
        print(f"Error putting scaling policy: {e}")

# Wait for ECS service to reach steady state for first deployment
def wait_for_deployment_complete(deployment_start_time, timeout_minutes=10):
    timeout_seconds = timeout_minutes * 60
    print(f"Waiting for service to reach steady state (timeout: {timeout_minutes} minutes)...")
    
    while time.time() - deployment_start_time < timeout_seconds:
        try:
            response = ecs.describe_services(
                cluster=ecs_cluster,
                services=[ecs_service]
            )

            service = response["services"][0]
            events = service.get("events", [])
            
             # Check events for steady state message AFTER deployment start
            for event in events:
                event_time = event.get("createdAt")
                event_message = event.get("message", "")
                
                # Only check events AFTER deployment start time
                if event_time and event_time.timestamp() > deployment_start_time:
                    if "has reached a steady state" in event_message:
                        print(event_message)
                        return True

            time.sleep(30)
            
        except ClientError as e:
            print(f"Error checking service status: {e}")
            time.sleep(30)
    
    print("Deployment timeout reached")
    return False

# Get current desired count from ECS service
def get_current_desired_count():
    try:
        response = ecs.describe_services(
            cluster=ecs_cluster,
            services=[ecs_service]
        )

        if not response["services"]:
            print(f"Service {ecs_service} not found in cluster {ecs_cluster}")
            return None

        current_desired_count = response["services"][0]["desiredCount"]
        print(f"Current desired count: {current_desired_count}")
        return current_desired_count

    except ClientError as e:
        print(f"Error getting current desired count: {e}")
        return None

# ECS Serviceの更新 (with scaling logic)
# https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/ecs/client/update_service.html
def update_service(
    desired_count,
    capacity_base,
    capacity_weight,
    capacity_spot_base,
    capacity_spot_weight,
):
    try:
        # Get current desired count
        current_desired_count = get_current_desired_count()
        if current_desired_count is None:
            print("Failed to get current desired count, proceeding with specified desired count")
            update_params = {
                "cluster": ecs_cluster,
                "service": ecs_service,
                "desiredCount": int(desired_count),
                "capacityProviderStrategy": [
                    {
                        "capacityProvider": "FARGATE",
                        "base": int(capacity_base),
                        "weight": int(capacity_weight),
                    },
                    {
                        "capacityProvider": "FARGATE_SPOT",
                        "base": int(capacity_spot_base),
                        "weight": int(capacity_spot_weight),
                    },
                ],
                "forceNewDeployment": True,
            }
        else:
            # Apply scaling logic
            specified_desired_count = int(desired_count)

            if current_desired_count >= specified_desired_count:
                # Current count is >= specified count, update without specifying desired count
                print(f"Current desired count ({current_desired_count}) >= specified count ({specified_desired_count})")
                print("Updating service without specifying desired count")
                update_params = {
                    "cluster": ecs_cluster,
                    "service": ecs_service,
                    "capacityProviderStrategy": [
                        {
                            "capacityProvider": "FARGATE",
                            "base": int(capacity_base),
                            "weight": int(capacity_weight),
                        },
                        {
                            "capacityProvider": "FARGATE_SPOT",
                            "base": int(capacity_spot_base),
                            "weight": int(capacity_spot_weight),
                        },
                    ],
                    "forceNewDeployment": True,
                }
            else:
                # Current count is < specified count, update with specified desired count
                print(f"Current desired count ({current_desired_count}) < specified count ({specified_desired_count})")
                print(f"Updating service with desired count: {specified_desired_count}")
                update_params = {
                    "cluster": ecs_cluster,
                    "service": ecs_service,
                    "desiredCount": specified_desired_count,
                    "capacityProviderStrategy": [
                        {
                            "capacityProvider": "FARGATE",
                            "base": int(capacity_base),
                            "weight": int(capacity_weight),
                        },
                        {
                            "capacityProvider": "FARGATE_SPOT",
                            "base": int(capacity_spot_base),
                            "weight": int(capacity_spot_weight),
                        },
                    ],
                    "forceNewDeployment": True,
                }

        response = ecs.update_service(**update_params)
        print("Service updated:", response)

    except ClientError as e:
        print(f"Error updating service: {e}")


def main():

    # 設定ファイル読み込み
    param = read_param_file()

    try:
        min_capacity = param["AutoScale"]["MIN_CAPACITY"]
        max_capacity = param["AutoScale"]["MAX_CAPACITY"]
        target_value = param["AutoScale"]["TARGET_VALUE"]
        
        # Optional parameters with defaults
        desired_count = param.get("Task", "DESIRED_COUNT", fallback="2")
        capacity_base = param.get("CapacityProvider", "FARGATE_BASE", fallback="2")
        capacity_weight = param.get("CapacityProvider", "FARGATE_WEIGHT", fallback="1")
        capacity_spot_base = param.get("CapacityProvider", "FARGATE_SPOT_BASE", fallback="0")
        capacity_spot_weight = param.get("CapacityProvider", "FARGATE_SPOT_WEIGHT", fallback="2")

    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    # AutoScalingの更新
    register_scalable_target(min_capacity, max_capacity)
    put_scaling_policy(target_value)

    # ECS Serviceの更新
    update_service(
        desired_count,
        capacity_base,
        capacity_weight,
        capacity_spot_base,
        capacity_spot_weight,
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--wait', action='store_true', help='Wait for deployment completion')
    parser.add_argument('--timeout', type=int, default=10, help='Timeout in minutes (default: 10)')
    args = parser.parse_args()
    
    if args.wait:
        print("=== Waiting for deployment to complete ===")
        deployment_start_time = time.time()
        if wait_for_deployment_complete(deployment_start_time, args.timeout):
            print("Deployment successful!")
        else:
            print("Deployment failed or timed out")
            sys.exit(1)
    else:
        main()