import time
import subprocess
import sys

login_user = "exist"
wait_time = 1800  # 待機時間を秒で指定 (30分)

# SSMエージェントのプロセス数を取得
try:
    result = subprocess.run(
        "ps -ef | grep ssm-session-worker | grep -v grep | wc -l",
        shell=True,
        check=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    # プロセス数を整数型に変換
    login_user_number = int(result.stdout.decode().strip())
except subprocess.SubprocessError as e:
    sys.exit(f"[ERROR] Failed to execute command: {e}")

# プロセスの監視
while True:
    print(f"[INFO] Waitting {wait_time} seconds...")
    time.sleep(wait_time)

    # プロセスの監視
    print(f"[INFO] SSM session worker process count: {login_user_number}")
    if login_user_number != 0:
        print("[INFO] SSM session worker exists.")
        login_user = "exist"
    else:
        # プロセス数が2回連続で0場合、タスクを停止
        if login_user == "no_exist":
            print(
                "[INFO] SSM session worker is not running for two consecutive checks. Exiting..."
            )
            break
        else:
            login_user = "no_exist"
            print(
                f"[INFO] SSM session worker is not running. Will check again after {wait_time} seconds."
            )
