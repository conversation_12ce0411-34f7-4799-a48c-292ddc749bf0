FROM public.ecr.aws/amazonlinux/amazonlinux:2023

COPY ./check_login.py .

RUN chmod 700 check_login.py

RUN yum update -y && \
    yum install -y procps python3 && \
    dnf -y localinstall  https://dev.mysql.com/get/mysql80-community-release-el9-1.noarch.rpm && \
    # 最新版の2023を明示的にインポート
    rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2023 && \
    dnf -y install mysql mysql-community-client

ENTRYPOINT ["python3","./check_login.py"]
