{
  "deploymentController": {
    "type": "CODE_DEPLOY"
  },
  "enableECSManagedTags": false,
  "enableExecuteCommand": true,
  "launchType": "",
  "loadBalancers": [
    {
      "containerName": "EcsBackendBg",
      "containerPort": {{ must_env `PORT_NUMBER` }},
      "targetGroupArn": "{{ must_env `TARGET_GROUP_ARN` }}"
    }
  ],
  "desiredCount": 1, 
  "capacityProviderStrategy": [],
  "healthCheckGracePeriodSeconds": 0, 
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "assignPublicIp": "DISABLED",
      "securityGroups": ["{{ must_env `SECURITY_GROUP` }}"],
      "subnets": ["{{ must_env `SUBNET_1` }}", "{{ must_env `SUBNET_2` }}", "{{ must_env `SUBNET_3` }}"]
    }
  },
  "tags":[
    {
    "key": "{{ must_env `CM_BILLING_GROUP_TAG_KEY` }}",
    "value": "{{ must_env `CM_BILLING_GROUP_TAG` }}"
    },
    {
    "key": "ServiceID",
    "value": "{{ must_env `ServiceID_TAG` }}"
    }
  ],
  "platformFamily": "Linux",
  "platformVersion": "LATEST",
  "propagateTags": "{{ must_env `PROPAGATE_TAG` }}",
  "schedulingStrategy": "REPLICA",
  "availabilityZoneRebalancing": "ENABLED"
}
