## 概要
- ホストゾーンは環境単位で作成するため、共有リソース・専有リソースとは別のディレクトリで管理する
- 構築方法についてはCDKを利用する
- デプロイ先は`mynavi_csys-infra_gevanni-route53-<環境>`

## 初期設定
- `cd usecases/hostedzone`で`hostedzone`に移動する
- `npm install`でnodeをインストール

## 構築手順
- 初期設定実施後、以下CDKコマンドで実行
- 環境名については(prod/stg/dev)

- List

```
cdk ls -c zone=<環境名>

# ex
cdk ls -c zone=dev
```

- スタック差分の確認

```
cdk diff -c zone=<環境名>

# ex
cdk diff -c zone=dev
```

- デプロイ

```
cdk deploy --all -c zone=<環境名>

# ex
cdk deploy --all -c zone=dev

```


