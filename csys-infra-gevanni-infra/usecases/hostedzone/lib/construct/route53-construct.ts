import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';

interface Route53Props {
  zoneName: string;
  accountID: string;
  /**
   * Log removal policy parameter
   */
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
}

export class Route53Construct extends Construct {
  constructor(scope: Construct, id: string, props: Route53Props) {
    super(scope, id);

    // Create S3 bucket for storing logs
    const logBucket = new s3.Bucket(this, 'LogBucket', {
      lifecycleRules: [
        {
          expiration: cdk.Duration.days(365 * 5), // 5 years
        },
      ],
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
    });

    // Create Kinesis Data Firehose to deliver logs to S3
    const firehoseRole = new iam.Role(this, 'FirehoseRole', {
      assumedBy: new iam.ServicePrincipal('firehose.amazonaws.com'),
    });
    firehoseRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['s3:PutObject', 's3:PutObjectAcl'],
        resources: [logBucket.bucketArn + '/*'],
      }),
    );

    logBucket.grantWrite(firehoseRole);

    const firehoseStream = new firehose.CfnDeliveryStream(this, 'FirehoseStream', {
      deliveryStreamType: 'DirectPut',
      s3DestinationConfiguration: {
        bucketArn: logBucket.bucketArn,
        roleArn: firehoseRole.roleArn,
      },
    });

    // Create CloudWatch log group with 1 day retention
    const logGroup = new logs.LogGroup(this, 'LogGroup', {
      retention: logs.RetentionDays.ONE_DAY,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    // Create IAM role for Route53 to write logs to CloudWatch Logs
    const route53LoggingRole = new iam.Role(this, 'Route53LoggingRole', {
      assumedBy: new iam.ServicePrincipal('route53.amazonaws.com'),
    });
    route53LoggingRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['logs:CreateLogStream', 'logs:PutLogEvents'],
        resources: [logGroup.logGroupArn],
      }),
    );

    // Create IAM role for CloudWatch Logs to trigger Kinesis Data Firehose
    const toKinesisFirehoseRole = new iam.Role(this, 'ToKinesisFirehoseRole', {
      assumedBy: new iam.ServicePrincipal('logs.amazonaws.com'),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonKinesisFirehoseFullAccess')],
    });

    // Create subscription filter for CloudWatch Logs
    new logs.CfnSubscriptionFilter(this, 'CfnSubscriptionFilter', {
      destinationArn: firehoseStream.attrArn,
      filterPattern: '',
      logGroupName: logGroup.logGroupName,
      roleArn: toKinesisFirehoseRole.roleArn,
    });

    //ホストゾーン作成
    const zone = new route53.PublicHostedZone(this, 'PublicHostedZone', {
      zoneName: props.zoneName,
      addTrailingDot: false,
      queryLogsLogGroupArn: logGroup.logGroupArn,
    });

    //lambda連携用ロール
    const crossAccountRole = new iam.Role(this, 'CrossAccountRole', {
      roleName: 'LambdaCrossAccountRole',
      assumedBy: new iam.AccountPrincipal(props.accountID),
    });
    crossAccountRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['route53:ChangeResourceRecordSets', 'route53:ListResourceRecordSets'],
        resources: [zone.hostedZoneArn],
      }),
    );

    // Grant permissions for Firehose to read from CloudWatch Logs
    logGroup.grant(firehoseRole, 'logs:PutLogEvents');
  }
}
