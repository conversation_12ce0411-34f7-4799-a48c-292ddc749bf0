import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Route53Construct } from '../construct/route53-construct';
import { IRemovalPolicyParam } from '../../params/interface';

export interface GevanniZoneProps extends cdk.StackProps {
  zoneName: string;
  accountID: string;
  logRemovalPolicyParam?: IRemovalPolicyParam;
}

export class GevanniZoneStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: GevanniZoneProps) {
    super(scope, id, props);
    new Route53Construct(this, 'Zone',{
      zoneName: props.zoneName,
      accountID: props.accountID,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
    });
  }
}