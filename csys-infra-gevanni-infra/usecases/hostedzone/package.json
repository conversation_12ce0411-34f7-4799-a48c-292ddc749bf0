{"name": "gevanni-hostedzone", "version": "0.1.0", "bin": {"gevanni-hostedzone": "bin/gevanni-hostedzone.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "20.11.16", "jest": "^29.7.0", "ts-jest": "^29.1.2", "aws-cdk": "^2.181.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "dependencies": {"aws-cdk-lib": "^2.181.1", "constructs": "^10.0.0", "source-map-support": "^0.5.21"}}