import * as cdk from 'aws-cdk-lib';
import * as inf from './interface';

export const Route53Param: inf.IRoute53Param = {
  zoneName: 'prod.gevanni.mynv.jp',
  accountID: '************'
};

export const Env: inf.IEnv = {
  envName: 'Prod',
  account: '************',
  region: 'ap-northeast-1',
};

export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;
