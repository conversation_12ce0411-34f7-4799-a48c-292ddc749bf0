import * as cdk from 'aws-cdk-lib';

export interface IRoute53Param {
  zoneName: string;
  accountID: string;
}

export interface IEnv {
  envName: string;
  account?: string;
  region?: string;
}

export interface IConfig {
  Route53Param: IRoute53Param;
  Env: IEnv;
  LogRemovalPolicyParam?: IRemovalPolicyParam; // For LogGroup and S3 for log
  OtherRemovalPolicyParam?: IRemovalPolicyParam; // For ECR, Pipeline, etc.
}

export interface IRemovalPolicyParam {
  removalPolicy: cdk.RemovalPolicy;
  autoDeleteObjects: boolean;
  emptyOnDelete: boolean;
}

export const DestroyRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
  emptyOnDelete: true,
};

export const RetainRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  autoDeleteObjects: false,
  emptyOnDelete: false,
};
