import * as cdk from 'aws-cdk-lib';
import * as inf from './interface';

export const Route53Param: inf.IRoute53Param = {
  zoneName: 'dev.gevanni.mynv.jp',
  accountID: '************',
};

export const Env: inf.IEnv = {
  envName: 'Dev',
  account: '************',
  region: 'ap-northeast-1',
};

export const LogRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
