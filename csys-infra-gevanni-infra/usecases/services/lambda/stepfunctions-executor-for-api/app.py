import json

import boto3
import botocore.utils
from aws_lambda_powertools.utilities.data_classes import (
    APIGatewayProxyEvent,
    event_source,
)
from aws_lambda_powertools.utilities.typing import LambdaContext
from pydantic import BaseModel, ValidationError


class RequestBody(BaseModel):
    batchName: str
    command: list["Command"] | None = None


class Command(BaseModel):
    containerName: str
    passedCommand: list[str]


@event_source(data_class=APIGatewayProxyEvent)
def lambda_handler(event: APIGatewayProxyEvent, context: LambdaContext):
    client = boto3.client("stepfunctions")
    account_id = botocore.utils.ArnParser().parse_arn(context.invoked_function_arn)["account"]

    if event.body is None:
        return {"statusCode": 400, "body": json.dumps({"message": "batchName must be specified"})}

    if not isinstance(event.body, str):
        return {"statusCode": 400, "body": json.dumps({"message": "request body must be string"})}

    try:
        body = RequestBody(**json.loads(event.body))
    except json.JSONDecodeError:
        return {"statusCode": 400, "body": json.dumps({"message": "request body must be json"})}
    except ValidationError:
        return {
            "statusCode": 400,
            "body": json.dumps(
                {
                    "message": "request body is invalid. The request body must conform to the following schema.",
                    "schema": RequestBody.model_json_schema(),
                }
            ),
        }

    batch_name = body.batchName
    state_machine_arn = f"arn:aws:states:ap-northeast-1:{account_id}:stateMachine:{batch_name}"
    args = {"stateMachineArn": state_machine_arn}

    if body.command is not None:
        commands = body.command
        state_machine_input = {
            "containerOverrides": [
                [{"Name": command.containerName, "Command": command.passedCommand}] for command in commands
            ]
        }
        args["input"] = json.dumps(state_machine_input)

    try:
        response = client.start_execution(**args)
        return {"statusCode": 200, "body": json.dumps(response, default=default)}
    except Exception as e:
        return {
            "statusCode": 500,
            "body": json.dumps({"message": str(e)}),
        }


def default(o):
    if hasattr(o, "isoformat"):
        return o.isoformat()
    else:
        return str(o)
