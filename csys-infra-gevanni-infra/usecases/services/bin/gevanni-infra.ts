import * as cdk from 'aws-cdk-lib';
import * as fs from 'fs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { WafAlbStack } from '../lib/stack/waf-alb-stack';
import { IConfig } from '../params/interface';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { EcsAppStack } from '../lib/stack/ecs-app-stack';
import { ImportResourcesStack } from '../lib/stack/import-resources-stack';
import { ElastiCacheStack } from '../lib/stack/elasticache-stack';
import { AcmStack } from '../lib/stack/acm-stack';
import { AlbAliasStack } from '../lib/stack/alb-aliasrecord-stack';
import { RoleForAppTeamStack } from '../lib/stack/role-for-app-team-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { EfsStack } from '../lib/stack/efs-stack';
import { SecretStack } from '../lib/stack/secret-stack';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import { BatchAppStack } from '../lib/stack/batch-app-stack';
import { NotifierStack } from '../lib/stack/notifier-stack';
import { DbAuroraStack } from '../lib/stack/db-aurora-stack';
import { ProtectResourceStack } from '../lib/stack/protect-resource-stack';

const app = new cdk.App();

// ----------------------- Load context variables ------------------------------
// This context need to be specified in args
const argContext = 'environment';
const envKey = app.node.tryGetContext(argContext);
if (envKey == undefined)
  throw new Error(`Please specify environment with context option. ex) cdk deploy -c ${argContext}=dev01`);

const argContextService = 'service';
const serviceKey = app.node.tryGetContext(argContextService);
if (serviceKey == undefined)
  throw new Error(
    `Please specify environment with context option. ex) cdk deploy -c ${argContext}=dev01 -c ${argContextService}=sample01-dev01-ab`,
  );
//Read Typescript Environment file
const TsEnvPath = './params/' + envKey + '/' + serviceKey + '.ts';
if (!fs.existsSync(TsEnvPath)) throw new Error(`Can't find a ts environment file [${TsEnvPath}]`);

//ESLintではrequireの利用が禁止されているため除外コメントを追加
//https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/issues/29#issuecomment-**********
const config: IConfig = require('../params/' + envKey + '/' + serviceKey);

// Add envName to Stack for avoiding duplication of Stack names.
const pjCommonPrefix = `${config.Env.envName}-${config.Env.prefix}-common`;
const pjPrefix = config.Env.envName + `-${config.Env.prefix}-` + serviceKey;

// ----------------------- Environment variables for stack ------------------------------
// Default environment
const procEnvDefault = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

// Define account id and region from context.
// If "env" isn't defined on the environment variable in context, use account and region specified by "--profile".
function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnvDefault;
  }
}

const isFrontEnd =
  config.DeployResource.DeployResource === 'ALL' || config.DeployResource.DeployResource === 'FRONTEND';
const isBackEnd = config.DeployResource.DeployResource === 'ALL' || config.DeployResource.DeployResource === 'BACKEND';

// Empty Secret Manager
const secrets = new SecretStack(app, `${pjPrefix}-Secrets`, {
  prefix: pjPrefix,
  env: getProcEnv(),
  terminationProtection: config.SecretParam.stackTerminationProtection ?? false,
});

// Import output from common CDK app
const shareResources = new ImportResourcesStack(app, `${pjPrefix}-ImportResources`, {
  pjCommonPrefix,
  pjPrefix,
  env: getProcEnv(),
  terminationProtection: config.ShareResourcesParam.stackTerminationProtection ?? false,
});

const notifier = new NotifierStack(app, `${pjPrefix}-Notifier`, {
  pjPrefix,
  env: getProcEnv(),
  isCreateChatbot: config.NotifierParam.isCreateChatbot,
  notifyEmail: config.NotifierParam.monitoringNotifyEmail,
  slackChannelConfiguration: config.NotifierParam.slackChannelConfiguration,
  teamsChannelConfiguration: config.NotifierParam.teamsChannelConfiguration,
  terminationProtection: config.NotifierParam.stackTerminationProtection ?? false,
});

// // InfraResources
// if (config.ACMParam.AcmDomainName && isFrontEnd) {
//   new AcmStack(app, `${pjPrefix}-acm`, {
//     ...config.ACMParam,
//     prefix: pjPrefix,
//     env: getProcEnv(),
//     providerServiceToken: shareResources.ACMCustomeResourceProviderServiceToken,
//     terminationProtection: config.ACMParam.stackTerminationProtection ?? false,
//   });
// }

const ecs = new EcsAppStack(app, `${pjPrefix}-ECS`, {
  serviceKey: serviceKey,
  vpc: shareResources.vpc,
  appKey: shareResources.appKey,
  alarmTopic: shareResources.alarmTopic,
  alarmTopicForAppTeam: notifier.alarmTopic,
  serviceId: serviceKey,
  pjCommonPrefix,
  prefix: pjPrefix,
  EcsFrontTask: config.EcsFrontTask,
  EcsBackTask: config.EcsBackTask,
  ecsFrontTaskRole: config.EcsFrontTaskRole,
  ecsBackTaskRole: config.EcsBackTaskRole,
  EcsBackBgTask: config.EcsBackBgTask,
  ecsBackBgTaskRole: config.EcsBackBgTaskRole,
  EcsFrontBgTask: config.EcsFrontBgTask,
  ecsFrontBgTaskRole: config.EcsFrontBgTaskRole,
  ecsBastionTasks: true,
  bastionTaskRole: config.BastionTaskRole,
  AlbAcm: config.ACMParam,
  env: getProcEnv(),
  crossRegionReferences: true,
  accessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
  appLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
  buildLogBucketLifecycleRules: config.s3BuildLogLifecycleRules,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
  pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
  fireLensImageBase: shareResources.fluentbitImageUri,
  newrelicSecretArn: secrets.nrSecret.attrId,
  newrelicLayer: shareResources.newrelicLayer,
  EnvName: config.Env.envName,
  namespace: shareResources.namespace,
  namespaceArn: shareResources.namespaceArn,
  deployResource: config.DeployResource.DeployResource,
  deployControl: config.DeployResource.DeployControl,
  ecspressoFrontDeployPipelineParam: config.EcspressoFrontDeployPipelineParam,
  ecspressoBackDeployPipelineParam: config.EcspressoBackDeployPipelineParam,
  blueGreenFrontDeployPipelineParam: config.BlueGreenFrontDeployPipelineParam,
  blueGreenBackDeployPipelineParam: config.BlueGreenBackDeployPipelineParam,
  terminationProtection: config.EcsAppParam.stackTerminationProtection ?? false,
});

// let batch: BatchAppStack | undefined = undefined;
// if (config.BatchParam) {
//   batch = new BatchAppStack(app, `${pjPrefix}-Batch`, {
//     prefix: pjPrefix,
//     pjCommonPrefix,
//     serviceId: serviceKey,
//     batchParam: config.BatchParam,
//     FluentBitImageBase: shareResources.fluentbitImageUri,
//     cluster: ecs.app.ecsCommon.ecsCluster,
//     newRelicIntegrationParam: {
//       secretArn: secrets.nrSecret.attrId,
//       firehoseBucketLifecycleRules: config.s3AuditLogLifecycleRules,
//       logRemovalPolicyParam: config.LogRemovalPolicyParam,
//     },
//     securityGroupForConnectingBackend: ecs.batchSecurityGroupForConnectingBackend,
//     vpc: shareResources.vpc,
//     appKey: shareResources.appKey,
//     ...(config.BatchParam?.some((value) => value.enableApiTrigger)
//       ? { backendAppTaskRole: ecs.app.backEcsTaskRole }
//       : {}),
//     env: getProcEnv(),
//     alarmTopicForAppTeam: notifier.alarmTopic,
//     terminationProtection: config.BatchParam.some((value) => value.stackTerminationProtection),
//   });
// }

// if (config.ACMParam.AcmDomainName && isFrontEnd) {
//   new AlbAliasStack(app, `${pjPrefix}-ALbAlias`, {
//     AliasDomainName: config.ACMParam.AcmDomainName,
//     Route53HostZoneId: config.ACMParam.HostZoneId,
//     AssumeRoleArn: config.ACMParam.AssumeRoleArn,
//     appAlb: ecs.app.frontAlb.appAlb,
//     env: getProcEnv(),
//     crossRegionReferences: true,
//     providerServiceToken: shareResources.AliasCustomeResourceProviderServiceToken,
//     terminationProtection: config.ACMParam.stackTerminationProtection ?? false,
//   });
// }
// if (isFrontEnd) {
//   const ecsAppAlbs: string[] = [
//     ecs.app.frontAlb?.appAlb.loadBalancerArn,
//     ecs.app.frontAlbBg?.appAlbBg.loadBalancerArn,
//     ecs.app.backAlbBg?.appAlbBg.loadBalancerArn,
//   ];
//   const associations = ecsAppAlbs.filter((v): v is string => v !== undefined);
//   const wafAlb = new WafAlbStack(app, `${pjPrefix}-WafAlb`, {
//     pjPrefix,
//     associations,
//     scope: 'REGIONAL',
//     env: getProcEnv(),
//     crossRegionReferences: true,
//     ...config.WafAlbParam,
//     terminationProtection: config.WafAlbParam.stackTerminationProtection ?? false,
//   });
// }

// new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
//   pjPrefix: `${pjPrefix}`,
//   dashboardName: `${pjPrefix}-ECSApp`,
//   albFullName: isFrontEnd ? ecs.app.frontAlb.appAlb.loadBalancerFullName : '',
//   appTargetGroupName: isFrontEnd ? ecs.app.frontAlb.AlbTg.lbForAppTargetGroup.targetGroupName : '',
//   isExistAlbTgUnHealthyHostCountAlarm: isFrontEnd && ecs.app.frontAlb.AlbTg.albTgUnHealthyHostCountAlarm ? true : false,
//   ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
//   ecsAlbServiceName: isFrontEnd ? ecs.app.frontEcsApps.ecsServiceName : '',
//   ecsInternalServiceName: isBackEnd ? ecs.app.backEcsApps.ecsServiceName : '',
//   // AutoScaleはCDK外で管理のため、固定値を修正要で設定
//   ecsScaleOnRequestCount: 50,
//   ecsTargetUtilizationPercent: 10000,
//   env: getProcEnv(),
//   deployResource: config.DeployResource.DeployResource,
//   terminationProtection: config.MonitorParam.stackTerminationProtection ?? false,
// });

// new OidcStack(app, `${pjPrefix}-OIDC`, {
//   OrganizationName: config.OidcParam.OrganizationName,
//   RepositoryNames: config.OidcParam.RepositoryNames,
//   oidcProvider: shareResources.oidcProvider,
//   confMasterBucket: shareResources.confMasterBucket,
//   EcsFrontTask: config.EcsFrontTask,
//   EcsBackTask: config.EcsBackTask,
//   EcsBackBgTask: config.EcsBackBgTask,
//   EcsFrontBgTask: config.EcsFrontBgTask,
//   // rolling
//   frontSourceBucketArn:
//     isFrontEnd && ecs.app.frontEcsAppPipeline?.sourceBucket?.bucketArn
//       ? ecs.app.frontEcsAppPipeline?.sourceBucket?.bucketArn
//       : '',
//   backSourceBucketArn:
//     isBackEnd && ecs.app.backEcsAppPipeline?.sourceBucket?.bucketArn
//       ? ecs.app.backEcsAppPipeline?.sourceBucket?.bucketArn
//       : '',
//   frontEcrRepository: isFrontEnd ? ecs.app.frontEcsApps?.ecrRepository?.repositoryArn : '',
//   backEcrRepository: isBackEnd ? ecs.app.backEcsApps?.ecrRepository?.repositoryArn : '',
//   frontAppName: isFrontEnd ? ecs.app.frontEcsApps?.appName : '',
//   backAppName: isBackEnd ? ecs.app.backEcsApps?.appName : '',
//   frontsecretArn: isFrontEnd ? ecs.app.frontEcsApps?.secret.secretArn : '',
//   backsecretArn: isBackEnd ? ecs.app.backEcsApps?.secret.secretArn : '',
//   // bluegreen
//   blueGreenFrontDeployPipelineParam: config.BlueGreenFrontDeployPipelineParam,
//   blueGreenBackDeployPipelineParam: config.BlueGreenBackDeployPipelineParam,
//   frontbgSourceBucketArn:
//     isFrontEnd && ecs.app.frontEcsAppBgPipeline?.sourceBucket?.bucketArn
//       ? ecs.app.frontEcsAppBgPipeline?.sourceBucket?.bucketArn
//       : '',
//   backbgSourceBucketArn:
//     isBackEnd && ecs.app.backEcsAppBgPipeline?.sourceBucket?.bucketArn
//       ? ecs.app.backEcsAppBgPipeline?.sourceBucket?.bucketArn
//       : '',
//   frontbgEcrRepository: isFrontEnd ? ecs.app.frontEcsAppsBg?.ecrRepository?.repositoryArn : '',
//   backbgEcrRepository: isBackEnd ? ecs.app.backEcsAppsBg?.ecrRepository?.repositoryArn : '',
//   frontbgAppName: isFrontEnd ? ecs.app.frontEcsAppsBg?.appName : '',
//   backbgAppName: isBackEnd ? ecs.app.backEcsAppsBg?.appName : '',
//   frontbgsecretArn: isFrontEnd ? ecs.app.frontEcsAppsBg?.secret?.secretArn : '',
//   backbgsecretArn: isBackEnd ? ecs.app.backEcsAppsBg?.secret?.secretArn : '',
//   ...(batch != undefined
//     ? {
//         batchContext: {
//           repositoryArns: batch.repositoryArns,
//           sourceBuckets: batch.sourceBuckets,
//           batchMasterBucket: shareResources.batchMasterBucket,
//         },
//       }
//     : {}),
//   env: getProcEnv(),
//   pjPrefix,
//   pjCommonPrefix,
//   deployResource: config.DeployResource.DeployResource,
//   deployControl: config.DeployResource.DeployControl,
//   terminationProtection: config.OidcParam.stackTerminationProtection ?? false,
// });

// new RoleForAppTeamStack(app, `${pjPrefix}-RoleForAppTeam`, {
//   pjPrefix,
//   pjCommonPrefix,
//   ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
//   ...config.RoleForAppTeamParam,
//   taskDefName: ecs.app.bastionApp.taskDef.family,
//   // rolling
//   frontsecretArn: isFrontEnd ? ecs.app.frontEcsApps?.secret.secretArn : '',
//   backsecretArn: isBackEnd ? ecs.app.backEcsApps?.secret.secretArn : '',
//   frontAppName: isFrontEnd ? ecs.app.frontEcsApps?.appName : '',
//   backAppName: isBackEnd ? ecs.app.backEcsApps?.appName : '',
//   frontSourceBucketArn: isFrontEnd ? ecs.app.frontEcsAppPipeline?.sourceBucket.bucketArn : '',
//   backSourceBucketArn: isBackEnd ? ecs.app.backEcsAppPipeline?.sourceBucket.bucketArn : '',
//   // bluegreen
//   frontbgsecretArn: isFrontEnd ? ecs.app.frontEcsAppsBg?.secret?.secretArn : '',
//   backbgsecretArn: isBackEnd ? ecs.app.backEcsAppsBg?.secret?.secretArn : '',
//   frontbgAppName: isFrontEnd ? ecs.app.frontEcsAppsBg?.appName : '',
//   backbgAppName: isBackEnd ? ecs.app.backEcsAppsBg?.appName : '',
//   frontbgSourceBucketArn: isFrontEnd ? ecs.app.frontEcsAppBgPipeline?.sourceBucket?.bucketArn : '',
//   backbgSourceBucketArn: isBackEnd ? ecs.app.backEcsAppBgPipeline?.sourceBucket?.bucketArn : '',
//   bastionServiceTaskRoleArn: ecs.app.bastionApp.ecsTaskServiceTaskRole.roleArn,
//   batchSecretArns: batch?.batchSecrets.map((secret) => secret.secretArn),
//   batchSourceBucketArns: batch?.sourceBuckets.map((bucket) => bucket.arnForObjects('*')),
//   // Batch name is <serviceKey>-<batchName>
//   batchNames: config.BatchParam?.map((param) => `${serviceKey}-${param.batchName}`),
//   batchApi: batch?.api,
//   env: getProcEnv(),
//   deployResource: config.DeployResource.DeployResource,
//   deployController: config.DeployResource.DeployControl,
//   terminationProtection: config.RoleForAppTeamParam.stackTerminationProtection ?? false,
// });

// // Declare variables with a wider scope
// let elastiCache: ElastiCacheStack | undefined;
// let efs: EfsStack | undefined;

// if (config.ElastiCacheParam) {
//   elastiCache = new ElastiCacheStack(app, `${pjPrefix}-ElastiCache`, {
//     pjPrefix,
//     elastiCacheType: config.ElastiCacheTypeParam,
//     myVpc: shareResources.vpc,
//     appKey: shareResources.appKey,
//     alarmTopic: shareResources.alarmTopic,
//     backendServerSecurityGroup: isBackEnd ? ecs.app.backEcsApps.securityGroupForFargate : undefined,
//     frontServerSecurityGroup: isFrontEnd ? ecs.app.frontEcsApps.securityGroupForFargate : undefined,
//     bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//     batchSecurityGroups: batch?.batchSecurityGroups,
//     ElastiCacheParam: config.ElastiCacheParam,
//     allowSgCrossService: config.ElastiCacheCrossServiceParam.allowSgCrossService,
//     env: getProcEnv(),
//     logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
//     newrelicSecretArn: secrets.nrSecret.attrId,
//     logRemovalPolicyParam: config.LogRemovalPolicyParam,
//     LogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
//     terminationProtection: config.ElastiCacheParam.stackTerminationProtection ?? false,
//   });
// }

// if (config.EfsParam) {
//   efs = new EfsStack(app, `${pjPrefix}-Efs`, {
//     pjPrefix,
//     vpc: shareResources.vpc,
//     appKey: shareResources.appKey,
//     backendServerSecurityGroup: isBackEnd ? ecs.app.backEcsApps.securityGroupForFargate : undefined,
//     frontServerSecurityGroup: isFrontEnd ? ecs.app.frontEcsApps.securityGroupForFargate : undefined,
//     bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//     batchSecurityGroups: batch?.batchSecurityGroups,
//     ...config.EfsParam,
//     env: getProcEnv(),
//     terminationProtection: config.EfsParam.stackTerminationProtection ?? false,
//   });
// }

// if (config.openSearchParam.isCreate) {
//   const opensearch = new OpenSearchStack(app, `${pjPrefix}-Opensearch`, {
//     pjPrefix,
//     myVpc: shareResources.vpc,
//     backendServerSecurityGroup: isBackEnd ? ecs.app.backEcsApps.securityGroupForFargate : undefined,
//     frontServerSecurityGroup: isFrontEnd ? ecs.app.frontEcsApps.securityGroupForFargate : undefined,
//     bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//     env: getProcEnv(),
//     terminationProtection: config.openSearchParam.stackTerminationProtection ?? false,
//   });
// }

// // Aurora
// let dbCluster = undefined;
// if (config.AuroraParam && config.AuroraParam.isCreate) {
//   dbCluster = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
//     pjPrefix,
//     vpc: shareResources.vpc,
//     vpcSubnets: shareResources.vpc.selectSubnets({
//       subnetGroupName: 'Protected',
//     }),
//     ingressSecurityGroups: [
//       ecs.app.backEcsApps ? ecs.app.backEcsApps.securityGroupForFargate : undefined,
//       ecs.app.frontEcsApps ? ecs.app.frontEcsApps.securityGroupForFargate : undefined,
//       ecs.app.backEcsAppsBg ? ecs.app.backEcsAppsBg.securityGroupForFargate : undefined,
//       ecs.app.frontEcsAppsBg ? ecs.app.frontEcsAppsBg.securityGroupForFargate : undefined,
//       ecs.app.bastionApp.securityGroup,
//       ...(batch?.batchSecurityGroups ?? []),
//     ].filter((sg) => sg !== undefined) as ec2.SecurityGroup[] | ec2.ISecurityGroup[],
//     appKey: shareResources.appKey,
//     alarmTopic: shareResources.alarmTopic,
//     newrelicSecretArn: secrets.nrSecret.attrId,
//     logRemovalPolicyParam: config.LogRemovalPolicyParam,
//     codeBuildBucketLogLifecycleRules: config.s3BuildLogLifecycleRules,
//     ...config.AuroraParam,
//     env: getProcEnv(),
//     terminationProtection: config.AuroraParam.stackTerminationProtection,
//   });
// }

// // --------------------------------- Protect Resource Stack  -------------------------------------
// const protectResourceStack = new ProtectResourceStack(app, `${pjPrefix}-ProtectResource`, {
//   pjPrefix: pjPrefix,
//   env: getProcEnv(),
//   stackResourceProtection: {
//     [ecs.stackName]: true,
//     ...(elastiCache ? { [elastiCache.stackName]: true } : {}),
//     ...(efs ? { [efs.stackName]: true } : {}),
//     ...(dbCluster ? { [dbCluster.stackName]: true } : {}),
//   },
// });
// protectResourceStack.addDependency(ecs);
// if (elastiCache) protectResourceStack.addDependency(elastiCache);
// if (efs) protectResourceStack.addDependency(efs);
// if (dbCluster) protectResourceStack.addDependency(dbCluster);

// --------------------------------- Tagging  -------------------------------------

// Tagging "Environment" tag to all resources in this app
const envTagName = 'Environment';
cdk.Tags.of(app).add(envTagName, config.Env.envName);
cdk.Tags.of(app).add('ServiceID', serviceKey);

//Add billing tag 'CmBillingGroup' to everything except Dev environment
if (!config.Env.envName.startsWith('Dev')) {
  const Billing = 'CmBillingGroup';
  cdk.Tags.of(app).add(Billing, pjPrefix);
}
