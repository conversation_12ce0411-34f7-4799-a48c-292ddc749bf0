import * as cdk from 'aws-cdk-lib';
import * as inf from '../interface';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';

export const DeployResource: inf.IDeployResource = {
  DeployResource: 'ALL',
  DeployControl: 'ROLLING',
};

export const ACMParam: inf.IACMParam = {
  AcmDomainName: 'locusmanage01-tg.stg.gevanni.mynv.jp',
  HostZoneId: 'Z0931887TOBWM1VPZUSY',
  AssumeRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
};

export const WafAlbParam: inf.IWafParam = {
  allowIPList: [
    '***************/25',
    '**************/32',  // NALS
    '************/32',  // NALS
    '*************/32',  // NALS
  ],
  preSharedKeys: ['rvits3b36y'],
  defaultAction: { block: {} },
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// ・pathが存在しないタスクがデフォルトルールになる。1タスク必ず設定する（0でも2以上でもNG）
// ・path：デフォルトルールでないタスクの場合に必ず設定する、リクエストを振り分けるパスの文字列を設定
// 本サンプルでは2種類のECSサービスを定義し、EcsAppをデフォルトルールを設定している
export const EcsFrontTask: inf.IEcsAlbParam = {
  appName: 'EcsApp',
  portNumber: 3001,
  enableAlarm: false,
  appLanguage: 'json',
};

export const EcsFrontTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

// VPC内でService Connectを経由して接続するECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// 本サンプルでは2種類のECSサービスを定義している
export const EcsBackTask: inf.IEcsParam = {
  appName: 'EcsBackend',
  portNumber: 3000,
  enableAlarm: false,
};

export const EcsBackTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: [],
};

export const BastionTaskRole: inf.IBastionTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
};

export const Env: inf.IEnv = {
  prefix: 'GEVANNI',
  envName: 'Stg01',
  account: '************',
  region: 'ap-northeast-1',
};

export const NotifierParam: inf.INotifierParam = {
  isCreateChatbot: false,
  workspaceId: 'T8XXXXXXX',
  channelIdMon: 'C01YYYYYYYY',
  monitoringNotifyEmail: '',
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    FrontEcspressoRepositoryName: 'locus_manage',
    BackEcspressoRepositoryName: 'locus',
    BatchAppRepositoryName: 'locus',
  },
};

export const RoleForAppTeamParam: inf.IRoleForAppTeamParam = {
  allowUsers: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  hasRoleForVendor: true,
  vendorBastionAccountId: '************',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];
export const LogRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;

export const pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const openSearchParam: inf.IOpenSearchParam = {
  isCreate: false,
};

export const BatchParam: inf.IBatchParam[] = [
  {
    shouldConnectAppContainer: true,
    batchName: 'god_batch',
    taskParam: [
      {
        taskName: 'task1',
        containerNames: ['batchApp'],
        crossAccountRoleArns: ['arn:aws:iam::************:role/Stg01Locus-manage-batch-crossaccount-iamrole'],
      },
    ],
  },
  {
    shouldConnectAppContainer: true,
    batchName: 'municipal_plans_status_batch',
    taskParam: [
      {
        taskName: 'task2',
        containerNames: ['batchApp'],
        crossAccountRoleArns: ['arn:aws:iam::************:role/Stg01Locus-manage-batch-crossaccount-iamrole'],
      },
    ],
  },
  {
    shouldConnectAppContainer: true,
    batchName: 'textbooks_status_batch',
    taskParam: [
      {
        taskName: 'task3',
        containerNames: ['batchApp'],
        crossAccountRoleArns: ['arn:aws:iam::************:role/Stg01Locus-manage-batch-crossaccount-iamrole'],
      },
    ],
  },
  {
    shouldConnectAppContainer: true,
    batchName: 'master_schools_batch',
    taskParam: [
      {
        taskName: 'task4',
        containerNames: ['batchApp'],
        crossAccountRoleArns: ['arn:aws:iam::************:role/Stg01Locus-manage-batch-crossaccount-iamrole'],
      },
    ],
  },
  {
    shouldConnectAppContainer: true,
    batchName: 'instruction_batch',
    taskParam: [
      {
        taskName: 'task5',
        containerNames: ['batchApp'],
        crossAccountRoleArns: ['arn:aws:iam::************:role/Stg01Locus-manage-batch-crossaccount-iamrole'],
      },
    ],
  },
  {
    shouldConnectAppContainer: true,
    batchName: 'retire_graduated_students_batch',
    taskParam: [
      {
        taskName: 'task6',
        containerNames: ['batchApp'],
      },
    ],
  },
];
