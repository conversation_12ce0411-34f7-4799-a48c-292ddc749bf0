import * as cdk from 'aws-cdk-lib';
import * as inf from '../interface';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as events from 'aws-cdk-lib/aws-events';

export const DeployResource: inf.IDeployResource = {
  DeployResource: 'FRONTEND',
  DeployControl: 'ALL',
};

export const BlueGreenFrontDeployPipelineParam: inf.IBlueGreenFrontDeployPipelineParam = {
  crossAccessAccountId: '************',
  crossAccessEnvName: 'Prod',
  crossAccessPjPrefix: 'petapi-prod01-qc',
  // CodeDeploy をデプロイするには、ECS Servce をデプロイ後に isEcsServiceDeployed を true に書き換えてデプロイする。
  isEcsServiceDeployed: true,
};

export const ACMParam: inf.IACMParam = {
  AcmDomainName: 'petapi-qc.prod.gevanni.mynv.jp',
  HostZoneId: 'Z02586805M90SV6SNCCE',
  AssumeRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
};

export const WafAlbParam: inf.IWafParam = {
  allowIPList: [
    '***************/25',
    '**************/32', // NALS
    '************/32', // NALS
    '*************/32', // NALS
  ],
  preSharedKeys: ['92q0n32o7c'],
  defaultAction: { block: {} },
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsFrontTask: inf.IEcsAlbParam = {
  appName: 'EcsApp',
  portNumber: 3000,
  enableAlarm: false,
  appLanguage: 'ruby',
};

export const EcsFrontTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: ['arn:aws:iam::************:role/Prodmynavi-pet-petapi-Cross-Account-role'],
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
export const EcsFrontBgTask: inf.IEcsAlbParam = {
  appName: 'EcsAppBg',
  portNumber: 3000,
  enableAlarm: false,
  appLanguage: 'ruby',
};

export const EcsFrontBgTaskRole: inf.IEcsTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
  crossAccountRoleArns: ['arn:aws:iam::************:role/Prodmynavi-pet-petapi-Cross-Account-role'],
};

export const BastionTaskRole: inf.IBastionTaskRoleParam = {
  policyStatements: [],
  managedPolicy: [],
};

export const Env: inf.IEnv = {
  prefix: 'GEVANNI',
  envName: 'Prod01',
  account: '************',
  region: 'ap-northeast-1',
};

export const NotifierParam: inf.INotifierParam = {
  isCreateChatbot: false,
  workspaceId: 'T8XXXXXXX',
  channelIdMon: 'C01YYYYYYYY',
  monitoringNotifyEmail: '<EMAIL>',
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    FrontEcspressoRepositoryName: 'move_pet',
    FrontBlueGreenRepositoryName: 'move_pet',
  },
};

export const RoleForAppTeamParam: inf.IRoleForAppTeamParam = {
  allowUsers: [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ],
  hasRoleForVendor: true,
  vendorBastionAccountId: '************',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];
export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;

export const pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const s3BuildLogLifecycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const openSearchParam: inf.IOpenSearchParam = {
  isCreate: false,
};
