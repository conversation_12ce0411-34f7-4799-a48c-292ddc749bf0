import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';

export interface AlbAliasProps extends cdk.StackProps {
  AliasDomainName: string;
  Route53HostZoneId: string;
  AssumeRoleArn: string;
  appAlb: elbv2.ApplicationLoadBalancer;
  providerServiceToken: string;
}

export class AlbAliasStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: AlbAliasProps) {
    super(scope, id, props);

    //ALBのAレコード、ホストゾーンを取得
    const AlbRecord = props.appAlb.loadBalancerDnsName;
    const AlbHostZoneID = props.appAlb.loadBalancerCanonicalHostedZoneId;

    //カスタムリソース作成
    new cdk.CustomResource(this, 'CustomResource', {
      serviceToken: props.providerServiceToken,
      properties: {
        DOMAIN_NAME: props.AliasDomainName,
        HOSTED_ZONE_ID: props.Route53HostZoneId,
        ROLE_ARN: props.AssumeRoleArn,
        ALB_HOSTED_ZONE_ID: AlbHostZoneID,
        ALB_DNS_ID: AlbRecord,
      },
    });
  }
}
