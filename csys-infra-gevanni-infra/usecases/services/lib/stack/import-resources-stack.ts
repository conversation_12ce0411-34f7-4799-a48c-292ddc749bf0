import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Vpc, IVpc } from 'aws-cdk-lib/aws-ec2';
import { Key, IKey } from 'aws-cdk-lib/aws-kms';
import { Topic, ITopic } from 'aws-cdk-lib/aws-sns';
import { Role, IRole } from 'aws-cdk-lib/aws-iam';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';

interface ImportResourcesStackProps extends cdk.StackProps {
  pjCommonPrefix: string;
  pjPrefix: string;
}

export class ImportResourcesStack extends cdk.Stack {
  public vpc: IVpc;
  public appKey: IKey;
  public alarmTopic: ITopic;
  public AcmFunctionlambdaRole: IRole;
  public oidcProvider: iam.IOpenIdConnectProvider;
  public confMasterBucket: s3.IBucket;
  public batchMasterBucket: s3.IBucket;
  public newrelicLayer: lambda.ILayerVersion;
  public ACMCustomeResourceProviderServiceToken: string;
  public AliasCustomeResourceProviderServiceToken: string;
  public namespace: servicediscovery.IPrivateDnsNamespace;
  public namespaceArn: string;
  public fluentbitImageUri: string;

  constructor(scope: Construct, id: string, props: ImportResourcesStackProps) {
    super(scope, id, props);

    const vpcId = ssm.StringParameter.valueFromLookup(this, `/${props.pjCommonPrefix}/vpcId`);
    const vpc = Vpc.fromLookup(this, 'importVpc', { vpcId });
    this.vpc = vpc;

    const appKeyArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importAppKeyArn',
      `/${props.pjCommonPrefix}/appKeyArn`,
    ).stringValue;
    const appKey = Key.fromKeyArn(this, 'importAppKey', appKeyArn);
    this.appKey = appKey;

    const alarmTopicArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importAlarmTopicArn',
      `/${props.pjCommonPrefix}/alarmTopicArn`,
    ).stringValue;
    const alarmTopic = Topic.fromTopicArn(this, 'importAlarmTopic', alarmTopicArn);
    this.alarmTopic = alarmTopic;

    const AcmFunctionlambdaRoleArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importAcmFunctionlambdaRoleArn',
      `/${props.pjCommonPrefix}/AcmFunctionLambdaRoleArn`,
    ).stringValue;
    const AcmFunctionlambdaRole = Role.fromRoleArn(this, 'importAcmFunctionlambdaRole', AcmFunctionlambdaRoleArn);
    this.AcmFunctionlambdaRole = AcmFunctionlambdaRole;

    const oidcProviderArn = ssm.StringParameter.fromStringParameterName(
      this,
      'importOidcProviderArn',
      `/${props.pjCommonPrefix}/oidcProviderArn`,
    ).stringValue;
    const oidcProvider = iam.OpenIdConnectProvider.fromOpenIdConnectProviderArn(
      this,
      'importOidcProvider',
      oidcProviderArn,
    );
    this.oidcProvider = oidcProvider;

    const confMasterBucketName = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importConfMasterBucket',
      `/${props.pjCommonPrefix}/ConfMasterBucketName`,
    ).stringValue;
    const confMasterBucket = s3.Bucket.fromBucketName(
      this,
      `${props.pjCommonPrefix}/ConfMasterBucketName`,
      confMasterBucketName,
    );
    this.confMasterBucket = confMasterBucket;

    const batchMasterBucketName = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importBatchMasterBucket',
      `/${props.pjCommonPrefix}/BatchMasterBucketName`,
    ).stringValue;
    const batchMasterBucket = s3.Bucket.fromBucketName(
      this,
      `${props.pjCommonPrefix}/BatchMasterBucketName`,
      batchMasterBucketName,
    );
    this.batchMasterBucket = batchMasterBucket;

    const newrelicLayerArn = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importNewRelicLayer',
      `/${props.pjCommonPrefix}/NewRelicLayerArn`,
    ).stringValue;

    const newrelicLayer = lambda.LayerVersion.fromLayerVersionArn(this, `NewRelicLayer`, newrelicLayerArn);
    this.newrelicLayer = newrelicLayer;

    const ACMCustomeResourceProviderServiceToken = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importAcmServiceToken',
      `/${props.pjCommonPrefix}/AcmServiceToken`,
    ).stringValue;
    this.ACMCustomeResourceProviderServiceToken = ACMCustomeResourceProviderServiceToken;

    const AliasCustomeResourceProviderServiceToken = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importAliasServiceToken',
      `/${props.pjCommonPrefix}/AliasServiceToken`,
    ).stringValue;
    this.AliasCustomeResourceProviderServiceToken = AliasCustomeResourceProviderServiceToken;

    const namespaceArn = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importNamespaceArn',
      `/${props.pjCommonPrefix}/servicediscovery/NamespaceArn`,
    ).stringValue;
    this.namespaceArn = namespaceArn;

    const namespaceId = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importNamespaceId',
      `/${props.pjCommonPrefix}/servicediscovery/NamespaceId`,
    ).stringValue;

    const namespaceName = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importNamespaceName',
      `/${props.pjCommonPrefix}/servicediscovery/NamespaceName`,
    ).stringValue;

    const namespace = servicediscovery.PrivateDnsNamespace.fromPrivateDnsNamespaceAttributes(
      this,
      'ImportedNamespace',
      {
        namespaceArn: namespaceArn,
        namespaceId: namespaceId,
        namespaceName: namespaceName,
      },
    );
    this.namespace = namespace;

    const fluentbitImageUri = cdk.aws_ssm.StringParameter.fromStringParameterName(
      this,
      'importFluentbitImageUri',
      `/${props.pjCommonPrefix}/fluentbit/ecr/repository-uri`,
    ).stringValue;
    this.fluentbitImageUri = fluentbitImageUri;

    const privateSubnets = cdk.Lazy.list({
      produce: () =>
        vpc.selectSubnets({
          subnetGroupName: 'Private',
        }).subnetIds,
    });

    new ssm.StringParameter(this, 'SSMSubnet1', {
      parameterName: `/${props.pjPrefix}/Subnet1`,
      stringValue: cdk.Fn.select(0, privateSubnets),
    });

    new ssm.StringParameter(this, 'SSMSubnet2', {
      parameterName: `/${props.pjPrefix}/Subnet2`,
      stringValue: cdk.Fn.select(1, privateSubnets),
    });

    new ssm.StringParameter(this, 'SSMSubnet3', {
      parameterName: `/${props.pjPrefix}/Subnet3`,
      stringValue: cdk.Fn.select(2, privateSubnets),
    });

    new ssm.StringParameter(this, 'SSMNamespace', {
      parameterName: `/${props.pjPrefix}/Namespace`,
      stringValue: namespaceArn,
    });
  }
}
