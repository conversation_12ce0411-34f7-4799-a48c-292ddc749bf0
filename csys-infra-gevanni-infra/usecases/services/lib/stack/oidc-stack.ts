import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { OidcIAMRoleConstruct } from '../../../common/lib/construct/oidc-iamrole-construct';
import { IBlueGreenPipelineParam, IEcsAlbParam, IEcsParam } from 'params/interface';

export interface OidcStackProps extends cdk.StackProps {
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
  oidcProvider: iam.IOpenIdConnectProvider;
  confMasterBucket: s3.IBucket;
  EcsFrontTask?: IEcsAlbParam;
  EcsBackTask?: IEcsParam;
  EcsFrontBgTask?: IEcsAlbParam;
  EcsBackBgTask?: IEcsParam;
  // rolling
  frontSourceBucketArn?: string;
  backSourceBucketArn?: string;
  frontEcrRepository?: string;
  backEcrRepository?: string;
  frontAppName?: string;
  backAppName?: string;
  frontsecretArn?: string;
  backsecretArn?: string;
  // bluegreen
  blueGreenFrontDeployPipelineParam?: IBlueGreenPipelineParam;
  blueGreenBackDeployPipelineParam?: IBlueGreenPipelineParam;
  frontbgSourceBucketArn?: string;
  backbgSourceBucketArn?: string;
  frontbgEcrRepository?: string;
  backbgEcrRepository?: string;
  frontbgAppName?: string;
  backbgAppName?: string;
  frontbgsecretArn?: string;
  backbgsecretArn?: string;
  batchContext?: {
    repositoryArns: string[];
    sourceBuckets: s3.IBucket[];
    batchMasterBucket: s3.IBucket;
  };
  pjPrefix: string;
  pjCommonPrefix: string;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
  /**
   * Deployment control.
   */
  deployControl: string;
}

export class OidcStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: OidcStackProps) {
    super(scope, id, props);

    // Front ECS Service
    // IAM Role for Front Rolling Pipeline
    if (
      props.EcsFrontTask &&
      (props.deployControl === 'ALL' || props.deployControl === 'ROLLING') &&
      (props.deployResource === 'ALL' || props.deployResource === 'FRONTEND')
    ) {
      new OidcIAMRoleConstruct(this, `${props.frontAppName}-EcspressoRole`, {
        OrganizationName: props.OrganizationName,
        RepositoryName:
          'FrontEcspressoRepositoryName' in props.RepositoryNames
            ? props.RepositoryNames.FrontEcspressoRepositoryName
            : '',
        openIdConnectProviderArn: props.oidcProvider.openIdConnectProviderArn,
        statement: [
          {
            effect: iam.Effect.ALLOW,
            actions: ['s3:GetObject', 's3:ListAllMyBuckets', 's3:ListBucket', 's3:PutObject', 's3:DeleteObject'],
            resources: [
              ...(props.frontSourceBucketArn ? [`${props.frontSourceBucketArn}/*`] : []),
              `${props.confMasterBucket.bucketArn}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: [
              'ecr:BatchCheckLayerAvailability',
              'ecr:CompleteLayerUpload',
              'ecr:InitiateLayerUpload',
              'ecr:PutImage',
              'ecr:UploadLayerPart',
            ],
            resources: props.frontEcrRepository
              ? [props.frontEcrRepository]
              : [`arn:aws:ecr:${props.env?.region}:${props.env?.account}:repository/*`],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ecr:GetAuthorizationToken'],
            resources: ['*'],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ssm:PutParameter', 'ssm:GetParameter'],
            resources: [
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjCommonPrefix}/*`,
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjPrefix}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['secretsmanager:*'],
            resources: [
              props.frontsecretArn ||
                `arn:aws:secretsmanager:${props.env?.region}:${props.env?.account}:secret:${props.pjPrefix}/*`,
            ],
          },
        ],
      });
    }

    // Backend ECS Service
    // IAM Role for Backend Rolling Pipeline
    if (
      props.EcsBackTask &&
      (props.deployControl === 'ALL' || props.deployControl === 'ROLLING') &&
      (props.deployResource === 'ALL' || props.deployResource === 'BACKEND')
    ) {
      new OidcIAMRoleConstruct(this, `${props.backAppName}-EcspressoRole`, {
        OrganizationName: props.OrganizationName,
        RepositoryName:
          'BackEcspressoRepositoryName' in props.RepositoryNames
            ? props.RepositoryNames.BackEcspressoRepositoryName
            : '',
        openIdConnectProviderArn: props.oidcProvider.openIdConnectProviderArn,
        statement: [
          {
            effect: iam.Effect.ALLOW,
            actions: ['s3:GetObject', 's3:ListAllMyBuckets', 's3:ListBucket', 's3:PutObject', 's3:DeleteObject'],
            resources: [
              ...(props.backSourceBucketArn ? [`${props.backSourceBucketArn}/*`] : []),
              `${props.confMasterBucket.bucketArn}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: [
              'ecr:BatchCheckLayerAvailability',
              'ecr:CompleteLayerUpload',
              'ecr:InitiateLayerUpload',
              'ecr:PutImage',
              'ecr:UploadLayerPart',
            ],
            resources: props.backEcrRepository
              ? [props.backEcrRepository]
              : [`arn:aws:ecr:${props.env?.region}:${props.env?.account}:repository/*`],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ecr:GetAuthorizationToken'],
            resources: ['*'],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ssm:PutParameter', 'ssm:GetParameter'],
            resources: [
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjCommonPrefix}/*`,
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjPrefix}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['secretsmanager:*'],
            resources: [
              props.backsecretArn ||
                `arn:aws:secretsmanager:${props.env?.region}:${props.env?.account}:secret:${props.pjPrefix}/*`,
            ],
          },
        ],
      });
    }

    // Front ECS Service
    // IAM Role for Front BlueGreen Pipeline
    if (
      props.EcsFrontBgTask &&
      props.blueGreenFrontDeployPipelineParam &&
      (props.deployControl === 'ALL' || props.deployControl === 'BLUE_GREEN') &&
      (props.deployResource === 'ALL' || props.deployResource === 'FRONTEND')
    ) {
      new OidcIAMRoleConstruct(this, `${props.frontbgAppName}-BlueGreenRole`, {
        OrganizationName: props.OrganizationName,
        RepositoryName:
          'FrontBlueGreenRepositoryName' in props.RepositoryNames
            ? props.RepositoryNames.FrontBlueGreenRepositoryName
            : '',
        openIdConnectProviderArn: props.oidcProvider.openIdConnectProviderArn,
        statement: [
          {
            effect: iam.Effect.ALLOW,
            actions: ['s3:GetObject', 's3:ListAllMyBuckets', 's3:ListBucket', 's3:PutObject', 's3:DeleteObject'],
            resources: [
              ...(props.frontbgSourceBucketArn ? [`${props.frontbgSourceBucketArn}/*`] : []),
              `${props.confMasterBucket.bucketArn}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: [
              'ecr:BatchCheckLayerAvailability',
              'ecr:CompleteLayerUpload',
              'ecr:InitiateLayerUpload',
              'ecr:PutImage',
              'ecr:UploadLayerPart',
            ],
            resources: props.frontbgEcrRepository
              ? [props.frontbgEcrRepository]
              : [`arn:aws:ecr:${props.env?.region}:${props.env?.account}:repository/*`],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ecr:GetAuthorizationToken'],
            resources: ['*'],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ssm:PutParameter', 'ssm:GetParameter'],
            resources: [
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjCommonPrefix}/*`,
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjPrefix}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['secretsmanager:*'],
            resources: [
              props.frontbgsecretArn ||
                `arn:aws:secretsmanager:${props.env?.region}:${props.env?.account}:secret:${props.pjPrefix}/*`,
            ],
          },
        ],
      });
    }

    // Backend ECS Service
    // IAM Role for Backend BlueGreen Pipeline
    if (
      props.EcsBackBgTask &&
      props.blueGreenBackDeployPipelineParam &&
      (props.deployControl === 'ALL' || props.deployControl === 'BLUE_GREEN') &&
      (props.deployResource === 'ALL' || props.deployResource === 'BACKEND')
    ) {
      new OidcIAMRoleConstruct(this, `${props.backbgAppName}-BlueGreenRole`, {
        OrganizationName: props.OrganizationName,
        RepositoryName:
          'BackBlueGreenRepositoryName' in props.RepositoryNames
            ? props.RepositoryNames.BackBlueGreenRepositoryName
            : '',
        openIdConnectProviderArn: props.oidcProvider.openIdConnectProviderArn,
        statement: [
          {
            effect: iam.Effect.ALLOW,
            actions: ['s3:GetObject', 's3:ListAllMyBuckets', 's3:ListBucket', 's3:PutObject', 's3:DeleteObject'],
            resources: [
              ...(props.backbgSourceBucketArn ? [`${props.backbgSourceBucketArn}/*`] : []),
              `${props.confMasterBucket.bucketArn}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: [
              'ecr:BatchCheckLayerAvailability',
              'ecr:CompleteLayerUpload',
              'ecr:InitiateLayerUpload',
              'ecr:PutImage',
              'ecr:UploadLayerPart',
            ],
            resources: props.backbgEcrRepository
              ? [props.backbgEcrRepository]
              : [`arn:aws:ecr:${props.env?.region}:${props.env?.account}:repository/*`],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ecr:GetAuthorizationToken'],
            resources: ['*'],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ssm:PutParameter', 'ssm:GetParameter'],
            resources: [
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjCommonPrefix}/*`,
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjPrefix}/*`,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['secretsmanager:*'],
            resources: [
              props.backbgsecretArn ||
                `arn:aws:secretsmanager:${props.env?.region}:${props.env?.account}:secret:${props.pjPrefix}/*`,
            ],
          },
        ],
      });
    }

    if (props.batchContext) {
      const batchSourceBucketArns = props.batchContext.sourceBuckets.map((bucket) => bucket.arnForObjects('*'));
      // Batch task
      // IAM Role for Batch App Repository
      new OidcIAMRoleConstruct(this, 'BatchGHARole', {
        OrganizationName: props.OrganizationName,
        RepositoryName:
          'BatchAppRepositoryName' in props.RepositoryNames ? props.RepositoryNames.BatchAppRepositoryName : '',
        openIdConnectProviderArn: props.oidcProvider.openIdConnectProviderArn,
        statement: [
          {
            effect: iam.Effect.ALLOW,
            actions: ['s3:GetObject', 's3:ListAllMyBuckets', 's3:ListBucket', 's3:PutObject', 's3:DeleteObject'],
            resources: [
              props.batchContext.batchMasterBucket.bucketArn,
              `${props.batchContext.batchMasterBucket.bucketArn}/*`,
              ...batchSourceBucketArns,
            ],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: [
              'ecr:BatchCheckLayerAvailability',
              'ecr:CompleteLayerUpload',
              'ecr:InitiateLayerUpload',
              'ecr:PutImage',
              'ecr:UploadLayerPart',
            ],
            resources: props.batchContext.repositoryArns,
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ecr:GetAuthorizationToken'],
            resources: ['*'],
          },
          {
            effect: iam.Effect.ALLOW,
            actions: ['ssm:PutParameter', 'ssm:GetParameter', 'ssm:GetParametersByPath'],
            resources: [
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjCommonPrefix}/*`,
              `arn:aws:ssm:${props.env?.region}:${props.env?.account}:parameter/${props.pjPrefix}/*`,
            ],
          },
        ],
      });
    }
  }
}
