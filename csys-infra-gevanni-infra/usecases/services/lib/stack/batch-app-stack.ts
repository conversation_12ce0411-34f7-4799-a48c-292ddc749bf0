import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as apigw from 'aws-cdk-lib/aws-apigateway';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as sns from 'aws-cdk-lib/aws-sns';
import { Construct } from 'constructs';
import { DataFirehose } from '../construct/data-firehose-construct';
import { IBatchParam, IRemovalPolicyParam } from 'params/interface';
import { BatchPipeline, ApiBatchTrigger } from '../construct/batch-construct';
import { Topic } from 'aws-cdk-lib/aws-sns';

interface BatchAppStackProps extends cdk.StackProps {
  /**
   * Project and environment prefix
   */
  prefix: string;

  /**
   * The common prefix of the project
   */
  pjCommonPrefix: string;

  /**
   * Gevanni Service ID
   */
  serviceId: string;

  /**
   * The security group for connecting to the backend
   */
  securityGroupForConnectingBackend: ec2.ISecurityGroup;

  /**
   * Batch parameters
   */
  batchParam: IBatchParam[];

  /**
   * Firelens parameters
   */
  FluentBitImageBase: string;

  /**
   * ECS cluster it made by ecs-app-stack
   */
  cluster: ecs.ICluster;

  /**
   * Parameter of new relic integration
   */
  newRelicIntegrationParam: {
    /**
     * New Relic Secret ARN
     */
    secretArn: string;

    /**
     * Batch log bucket lifecycle rules
     */
    firehoseBucketLifecycleRules: s3.LifecycleRule[];

    /**
     * Log removal policy parameters
     */
    logRemovalPolicyParam?: IRemovalPolicyParam;
  };

  /**
   * VPC
   */
  vpc: ec2.IVpc;

  /**
   * App key
   */
  appKey: kms.IKey;

  /**
   * Backend app task roles
   */
  backendAppTaskRole?: iam.IRole;

  /**
   * SNS topic ARN for sending pipeline status
   */
  alarmTopicForAppTeam: sns.ITopic;
}

export class BatchAppStack extends cdk.Stack {
  public readonly batchSecrets: secretsmanager.ISecret[];
  public readonly batchSecurityGroups: ec2.ISecurityGroup[];
  public readonly repositoryArns: string[];
  public readonly sourceBuckets: s3.IBucket[];
  public readonly api?: apigw.IRestApi;

  constructor(scope: Construct, id: string, props: BatchAppStackProps) {
    super(scope, id, props);

    const batchLogFirehose = new DataFirehose(this, 'BatchLogDatFirehose', {
      firehoseStreamName: `${props.prefix}-Batch-Stream`,
      firehoseLogGroupName: `/aws/firehose/${props.prefix}/batchlogs`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'AllData',
      ...props.newRelicIntegrationParam,
    });

    const executionRole = new iam.Role(this, 'EcsTaskExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy')],
      inlinePolicies: {
        createLogs: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['logs:CreateLogGroup'],
              resources: ['*'],
            }),
          ],
        }),
      },
    });

    let apiTrigger: ApiBatchTrigger | undefined;
    // If there are elements in batchParams with enableApiTrigger true,
    // then statemachineExecutionFunction must be created.
    if (props.batchParam.some((param) => param.enableApiTrigger)) {
      apiTrigger = new ApiBatchTrigger(this, 'ApiBatchTrigger', {
        availableBatchNames: props.batchParam.filter((param) => param.enableApiTrigger).map((param) => param.batchName),
        serviceId: props.serviceId,
      });
    }

    const taskSecurityGroup = new ec2.SecurityGroup(this, 'BatchSecurityGroupForDBConnection', {
      vpc: props.vpc,
    });
    this.batchSecurityGroups = [props.securityGroupForConnectingBackend, taskSecurityGroup];

    new ssm.StringParameter(this, 'SSMBatchTaskSGId', {
      parameterName: `/${props.serviceId}/Batch/BatchTaskSGId`,
      stringValue: taskSecurityGroup.securityGroupId,
    });

    new ssm.StringParameter(this, 'SSMBatchConnectBackendSGId', {
      parameterName: `/${props.serviceId}/Batch/BatchConnectBackendSGId`,
      stringValue: props.securityGroupForConnectingBackend.securityGroupId,
    });

    const batchSecrests = [];
    const sourceBuckets = [];
    const repositoryArns = [];
    for (const param of props.batchParam) {
      const { taskSecrets, pipelineSourceBucket, containerRepositories } = new BatchPipeline(
        this,
        `${param.batchName}-Pipeline`,
        {
          pjPrefix: props.prefix,
          pjCommonPrefix: props.pjCommonPrefix,
          batchName: param.batchName,
          taskSecurityGroup: param.shouldConnectAppContainer
            ? props.securityGroupForConnectingBackend
            : taskSecurityGroup,
          applicationLogTransferParam: {
            firehoseName: batchLogFirehose.stream.ref,
            firehoseArn: batchLogFirehose.stream.attrArn,
            FluentBitImage: `${props.FluentBitImageBase}:fluentbitimage${param?.appLanguage ?? ''}`,
            firelensLogGroupRemovalPolicy: props.newRelicIntegrationParam.logRemovalPolicyParam?.removalPolicy,
          },
          codeBuildLogTransferParam: {
            ...props.newRelicIntegrationParam,
          },
          serviceId: props.serviceId,
          cluster: props.cluster,
          taskExecutionRole: executionRole,
          batchParams: param.taskParam,
          apiTriggerEnvs:
            apiTrigger != null
              ? {
                  restApiId: apiTrigger.api.restApiId,
                  rootResourceId: apiTrigger.api.root.resourceId,
                  statemachineExecutionFunctionArn: apiTrigger.statemachineExecutionFunction.functionArn,
                  apiRoleArn: apiTrigger.apiRole.roleArn,
                  batchTriggerModelName: apiTrigger.batchApiTriggerBodyModel.modelId,
                  stageName: props.serviceId,
                }
              : undefined,
          appKey: props.appKey,
          vpc: props.vpc,
          alarmTopic: props.alarmTopicForAppTeam,
        },
      );
      batchSecrests.push(...taskSecrets);
      sourceBuckets.push(pipelineSourceBucket);
      repositoryArns.push(...containerRepositories.map((repository) => repository.repositoryArn));
      executionRole.addToPolicy(
        new iam.PolicyStatement({
          actions: ['secretsmanager:GetSecretValue'],
          resources: taskSecrets.map((secret) => secret.secretArn),
        }),
      );
    }
    this.batchSecrets = batchSecrests;
    this.sourceBuckets = sourceBuckets;
    this.repositoryArns = repositoryArns;
  }
}
