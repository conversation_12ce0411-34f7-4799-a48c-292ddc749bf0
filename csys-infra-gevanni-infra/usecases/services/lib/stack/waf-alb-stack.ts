import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { WafConstruct } from '../construct/waf-construct';

export interface WafAlbStackProps extends cdk.StackProps {
  pjPrefix: string;
  scope: string;
  allowIPList?: string[];
  preSharedKeys?: string[];
  associations?: string[];
  defaultAction?: wafv2.CfnWebACL.DefaultActionProperty;
}

export class WafAlbStack extends cdk.Stack {
  public readonly webAcl: wafv2.CfnWebACL;
  public readonly preSharedKeys: string[];

  constructor(scope: Construct, id: string, props: WafAlbStackProps) {
    super(scope, id, props);

    const webAcl = new WafConstruct(this, 'WafAlb', {
      pjPrefix: props.pjPrefix,
      scope: props.scope,
      allowIPList: props.allowIPList,
      preSharedKeys: props.preSharedKeys,
      associations: props.associations,
      defaultAction: props.defaultAction,
    });

    this.webAcl = webAcl.webAcl;
    this.preSharedKeys = webAcl.preSharedKeyValues;

    this.preSharedKeys.forEach((key, index) => {
      new cdk.CfnOutput(this, `PreSharedKey-${index}`, {
        value: key,
        description: 'Pre-shared key for WAF',
      });
    });
  }
}
