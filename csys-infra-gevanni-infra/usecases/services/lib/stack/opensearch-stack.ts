import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { aws_opensearchserverless as opensearchserverless } from 'aws-cdk-lib';
import { Construct } from 'constructs';

export interface OpenSearchStackProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.IVpc;
  backendServerSecurityGroup?: ec2.SecurityGroup;
  frontServerSecurityGroup?: ec2.SecurityGroup;
  bastionSecurityGroup?: ec2.SecurityGroup;
}

export class OpenSearchStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: OpenSearchStackProps) {
    super(scope, id, props);

    // Create security group for opensearch
    const securityGroupForOpensearch = new ec2.SecurityGroup(this, 'OpenSearchSecuritygGroup', {
      vpc: props.myVpc,
    });
    // Add security group for opensearch
    // For Backend Ecs App
    if (props.backendServerSecurityGroup) {
      securityGroupForOpensearch.connections.allowFrom(props.backendServerSecurityGroup, ec2.Port.tcp(443));
    }
    // For Frontend Ecs App
    if (props.frontServerSecurityGroup) {
      securityGroupForOpensearch.connections.allowFrom(props.frontServerSecurityGroup, ec2.Port.tcp(443));
    }
    // For Bastion Container
    if (props.bastionSecurityGroup) {
      securityGroupForOpensearch.connections.allowFrom(props.bastionSecurityGroup, ec2.Port.tcp(443));
    }

    // Create VPC Endpoint

    const privateSubnets = props.myVpc.privateSubnets.map((subnet) => subnet.subnetId);
    const securityGroupIds = [securityGroupForOpensearch.securityGroupId];
    const opensearchVpcEndpoint = new opensearchserverless.CfnVpcEndpoint(this, 'OpensearchVpcEndpoint', {
      name: 'opensearch-vpcendpoint',
      subnetIds: privateSubnets,
      vpcId: props.myVpc.vpcId,
      securityGroupIds: securityGroupIds,
    });
  }
}
