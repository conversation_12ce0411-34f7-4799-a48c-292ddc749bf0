import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_cloudwatch as cw } from 'aws-cdk-lib';

interface DashboardProps {
  /**
   * Cloudwatch dashboard name
   */
  dashboardName: string;
  /**
   * Application Load Balancer full name
   */
  albFullName: string;
  /**
   * Application Load Balancer target group name
   */
  appTargetGroupName: string;
  /**
   * Application Load Balancer target group unhealthy count alarms
   * Value get from alb-target-group-construct
   */
  albTgUnHealthyHostCountAlarm?: cw.IAlarm;
  /**
   * ECS cluster name
   */
  ecsClusterName: string;
  /**
   * ECS service name integrate with Public Application Load Balancer
   */
  ecsAlbServiceName: string;
  /**
   * ECS service name integrate with Internal Application Load Balancer
   */
  ecsInternalServiceName: string;
  /**
   * ECS target group CPU utilization percent threshold to scale
   */
  ecsTargetUtilizationPercent: number;
  /**
   * ECS target group request count threshold to scale
   */
  ecsScaleOnRequestCount: number;
  /**
   * Allow the creation of ECS service-related resources on only one side of the front/back end.
   */
  deployResource: string;
}

export class Dashboard extends Construct {
  constructor(scope: Construct, id: string, props: DashboardProps) {
    super(scope, id);

    const isFrontend = props.deployResource === 'ALL' || props.deployResource === 'FRONTEND';
    const isBackend = props.deployResource === 'ALL' || props.deployResource === 'BACKEND';

    let albRequests;
    let albNewConnectionCount;
    let albRejectedConnectionCount;
    let albTgRequests;
    let albTgResponseTime;
    let albTLSNegotiationErrors;
    let alb5xxErrors;
    let alb4xxErrors;
    let albTg5xxErrors;
    let albTg4xxErrors;
    let albTgConnectionErrors;
    let albTgTLSNegotiationErrors;
    let ecsAlbCPUUtilization;
    let ecsAlbMemoryUtilization;
    let ecsAlbDesiredTaskCount;
    let ecsAlbRunningTaskCount;
    let ecsAlbPendingTaskCount;
    let albTgRequestCountPerTarget;
    let ecsInternalCPUUtilization;
    let ecsInternalMemoryUtilization;
    let ecsInternalDesiredTaskCount;
    let ecsInternalRunningTaskCount;
    let ecsInternalPendingTaskCount;

    if (isFrontend) {
      /*
       *
       * Metrics definition
       * Note: These definitions do not create any resource. Just dashboard widget refer to these metrics.
       *
       */

      // Application Load Balancing
      // Available metrics: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-cloudwatch-metrics.html
      albRequests = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'RequestCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albNewConnectionCount = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'NewConnectionCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albRejectedConnectionCount = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'RejectedConnectionCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTLSNegotiationErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'ClientTLSNegotiationErrorCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      alb5xxErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_ELB_5XX_Count',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      alb4xxErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_ELB_4XX_Count',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });

      // Target Group
      // Available metrics: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-cloudwatch-metrics.html
      albTgRequests = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_Target_2XX_Count',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTg5xxErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_Target_5XX_Count',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTg4xxErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_Target_4XX_Count',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTgConnectionErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'TargetConnectionErrorCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTgTLSNegotiationErrors = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'TargetTLSNegotiationErrorCount',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      albTgResponseTime = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'TargetResponseTime',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.SECONDS,
      });
      albTgRequestCountPerTarget = new cw.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'RequestCountPerTarget',
        dimensionsMap: {
          LoadBalancer: props.albFullName,
          TargetGroup: props.appTargetGroupName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.SUM,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });

      // ECS
      // Available metrics:
      // - https://docs.aws.amazon.com/AmazonECS/latest/developerguide/cloudwatch-metrics.html
      // - https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/Container-Insights-metrics-ECS.html

      ecsAlbCPUUtilization = new cw.Metric({
        namespace: 'AWS/ECS',
        metricName: 'CPUUtilization',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsAlbServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.PERCENT,
      });
      ecsAlbMemoryUtilization = new cw.Metric({
        namespace: 'AWS/ECS',
        metricName: 'MemoryUtilization',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsAlbServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.PERCENT,
      });
      ecsAlbDesiredTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'DesiredTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsAlbServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      ecsAlbRunningTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'RunningTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsAlbServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      ecsAlbPendingTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'PendingTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsAlbServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
    }

    if (isBackend) {
      ecsInternalCPUUtilization = new cw.Metric({
        namespace: 'AWS/ECS',
        metricName: 'CPUUtilization',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsInternalServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.PERCENT,
      });
      ecsInternalMemoryUtilization = new cw.Metric({
        namespace: 'AWS/ECS',
        metricName: 'MemoryUtilization',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsInternalServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.PERCENT,
      });
      ecsInternalDesiredTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'DesiredTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsInternalServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      ecsInternalRunningTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'RunningTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsInternalServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
      ecsInternalPendingTaskCount = new cw.Metric({
        namespace: 'ECS/ContainerInsights',
        metricName: 'PendingTaskCount',
        dimensionsMap: {
          ClusterName: props.ecsClusterName,
          ServiceName: props.ecsInternalServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
        label: "${PROP('MetricName')} /${PROP('Period')}sec",
        unit: cw.Unit.COUNT,
      });
    }

    /*
     *
     * Dashboard definition
     *
     * Note:
     * - This sample summarize widgets in metrics group such as Requests, ResponseTime, Errors, Resources.
     *   We added header text widget on top of each metrics group.
     * - If you use the name CloudWatch-Default, the dashboard appears on the overview on the CloudWatch home page.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/create_dashboard.html
     *
     * - Widget Array Structure (height, width, x, y)
     *   width=24 means Full screen width. This sample is define widget height as 6.
     *   You can just add widgets in array, x and y axis are defined well by CDK.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/APIReference/CloudWatch-Dashboard-Body-Structure.html#CloudWatch-Dashboard-Properties-Widgets-Structure
     *
     * - "stacked: true," means stack(add) each metrics.
     *
     * - Label for each metrics is defined on metrics object and you can use "Dynamic Label".
     *   We usually use "${PROP('MetricName')} /${PROP('Period')}sec" so we can see period of the metrics.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/graph-dynamic-labels.html
     *
     */

    const dashboard = new cw.Dashboard(this, 'Dashboard', {
      dashboardName: props.dashboardName,
    });

    // ALB Requests Widgets
    if (albRequests && albNewConnectionCount && albRejectedConnectionCount && albTgRequests) {
      dashboard.addWidgets(
        // Requests
        new cw.TextWidget({
          markdown: '# Requests',
          height: 1,
          width: 24,
        }),
        new cw.GraphWidget({
          title: 'ALB Requests',
          width: 6,
          height: 6,
          stacked: false,
          left: [albRequests, albNewConnectionCount, albRejectedConnectionCount],
        }),
        new cw.GraphWidget({
          title: 'Target Group Requests',
          width: 6,
          height: 6,
          stacked: false,
          left: [albTgRequests],
        }),
      );
    }

    // ALB Response Time Widgets
    if (albTgResponseTime) {
      dashboard.addWidgets(
        // Response Time
        new cw.TextWidget({
          markdown: '# Response Time',
          height: 1,
          width: 24,
        }),
        new cw.GraphWidget({
          title: 'Target Group Response Time',
          width: 8,
          height: 6,
          stacked: false,
          left: [albTgResponseTime],
        }),
      );
    }

    // ALB Errors Widgets
    if (
      albTLSNegotiationErrors &&
      alb5xxErrors &&
      alb4xxErrors &&
      albTg5xxErrors &&
      albTg4xxErrors &&
      albTgConnectionErrors &&
      albTgTLSNegotiationErrors
    ) {
      dashboard.addWidgets(
        // Errors
        new cw.TextWidget({
          markdown: '# Errors',
          height: 1,
          width: 24,
        }),
        new cw.GraphWidget({
          title: 'ALB Errors',
          width: 6,
          height: 6,
          stacked: false,
          left: [albTLSNegotiationErrors, alb5xxErrors, alb4xxErrors],
        }),
        new cw.GraphWidget({
          title: 'Target Group Errors',
          width: 6,
          height: 6,
          // stacked: false,
          stacked: true,
          left: [albTg5xxErrors, albTg4xxErrors, albTgConnectionErrors, albTgTLSNegotiationErrors],
        }),
      );
    }

    // ALB Resources Widgets
    if (
      ecsAlbCPUUtilization &&
      ecsAlbMemoryUtilization &&
      ecsAlbDesiredTaskCount &&
      ecsAlbRunningTaskCount &&
      ecsAlbPendingTaskCount &&
      albTgRequestCountPerTarget
    ) {
      dashboard.addWidgets(
        // Resources
        new cw.TextWidget({
          markdown: '# Resources',
          height: 1,
          width: 24,
        }),
        new cw.GraphWidget({
          title: 'ECS CPU Utilization',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsAlbCPUUtilization],
        }),
        new cw.GraphWidget({
          title: 'ECS Memory Utilization',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsAlbMemoryUtilization],
        }),
        new cw.GraphWidget({
          title: 'ECS Desired Task Count',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsAlbDesiredTaskCount],
        }),
        new cw.GraphWidget({
          title: 'ECS Task Count',
          width: 6,
          height: 6,
          stacked: true,
          left: [ecsAlbRunningTaskCount, ecsAlbPendingTaskCount],
        }),
        new cw.GraphWidget({
          title: 'ECS Auto Scaling with Requests per tasks',
          width: 12,
          height: 6,
          stacked: false,
          left: [albTgRequestCountPerTarget],
          leftAnnotations: [
            {
              value: props.ecsScaleOnRequestCount, // Defined on ECSApp Stack
              label: 'Threshold: Requests per tasks',
              color: '#aec7e8',
              fill: cw.Shading.BELOW,
            },
          ],
          right: [ecsAlbRunningTaskCount, ecsAlbPendingTaskCount],
        }),
        new cw.GraphWidget({
          title: 'ECS Auto Scaling with CPU Utilization',
          width: 12,
          height: 6,
          stacked: false,
          left: [ecsAlbCPUUtilization],
          leftAnnotations: [
            {
              value: props.ecsTargetUtilizationPercent, // Defined on ECSApp Stack
              label: 'Threshold: CPU Utilization',
              color: '#aec7e8',
              fill: cw.Shading.BELOW,
            },
          ],
          right: [ecsAlbRunningTaskCount, ecsAlbPendingTaskCount],
        }),
      );
    }

    // ALB Alarm Widgets
    if (props.albTgUnHealthyHostCountAlarm) {
      dashboard.addWidgets(
        new cw.AlarmWidget({
          title: 'Alarm for UnHealthy Host in Target Group',
          width: 6,
          height: 6,
          alarm: props.albTgUnHealthyHostCountAlarm, // This alarm is defined on ECSApp Stack
        }),
      );
    }

    // ECS Resources Widgets
    if (
      ecsInternalCPUUtilization &&
      ecsInternalMemoryUtilization &&
      ecsInternalDesiredTaskCount &&
      ecsInternalRunningTaskCount &&
      ecsInternalPendingTaskCount
    ) {
      dashboard.addWidgets(
        // Resources
        new cw.TextWidget({
          markdown: '# Resources',
          height: 1,
          width: 24,
        }),
        new cw.GraphWidget({
          title: 'ECS CPU Utilization',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsInternalCPUUtilization],
        }),
        new cw.GraphWidget({
          title: 'ECS Memory Utilization',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsInternalMemoryUtilization],
        }),
        new cw.GraphWidget({
          title: 'ECS Desired Task Count',
          width: 6,
          height: 6,
          stacked: false,
          left: [ecsInternalDesiredTaskCount],
        }),
        new cw.GraphWidget({
          title: 'ECS Task Count',
          width: 6,
          height: 6,
          stacked: true,
          left: [ecsInternalRunningTaskCount, ecsInternalPendingTaskCount],
        }),
        new cw.GraphWidget({
          title: 'ECS Auto Scaling with CPU Utilization',
          width: 12,
          height: 6,
          stacked: false,
          left: [ecsInternalRunningTaskCount, ecsInternalPendingTaskCount],
          leftAnnotations: [
            {
              value: props.ecsTargetUtilizationPercent, // Defined on ECSApp Stack
              label: 'Threshold: CPU Utilization',
              color: '#aec7e8',
              fill: cw.Shading.BELOW,
            },
          ],
        }),
      );
    }

    // 'ECS Auto Scaling with CPU Utilization
    if (ecsAlbCPUUtilization && ecsInternalRunningTaskCount && ecsInternalPendingTaskCount) {
      dashboard.addWidgets(
        new cw.GraphWidget({
          title: 'ECS Auto Scaling with CPU Utilization',
          width: 12,
          height: 6,
          stacked: false,
          left: [ecsAlbCPUUtilization],
          leftAnnotations: [
            {
              value: props.ecsTargetUtilizationPercent, // Defined on ECSApp Stack
              label: 'Threshold: CPU Utilization',
              color: '#aec7e8',
              fill: cw.Shading.BELOW,
            },
          ],
          right: [ecsInternalRunningTaskCount, ecsInternalPendingTaskCount],
        }),
      );
    }
  }
}
