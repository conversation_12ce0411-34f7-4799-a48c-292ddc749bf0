import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as CryptoJS from 'crypto-js';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { CfnOutput } from 'aws-cdk-lib';

export interface WafConstructProps {
  /**
   * Project prefix for naming resources
   */
  pjPrefix: string;
  /**
   * WAF acl type, accept value CLOUDFRONT | REGIONAL
   */
  scope: string;
  /**
   * Web ACL default action
   *
   * @default - { allow: {} }
   */
  defaultAction?: wafv2.CfnWebACL.DefaultActionProperty;
  /**
   * List IP address will be allowed
   *
   * @example - https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-ipset.html#cfn-wafv2-ipset-description
   * @default - undefined will not create rule checking source IP request
   */
  allowIPList?: string[];
  /**
   * Whether to check header x-pre-shared-key or not
   *
   * @default - undefined will not create rule checking header x-pre-shared-key
   */
  preSharedKeys?: string[];
  /**
   * For WAF Regional type, attach Web ACL to list resources
   *
   * @example - https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-webaclassociation.html#cfn-wafv2-webaclassociation-resourcearn
   * @default - undefined will not attach Web ACL Regional type to any resources
   */
  associations?: string[];
  /**
   * Whether to add more rules to Web ACL, rule priority should be in range [4-99]
   *
   * @default - undefined will not attach additional rules to Web ACL
   */
  additionalRules?: wafv2.CfnWebACL.RuleProperty[];
  /**
   * Override action for restrict IP rule
   *
   * @default - { allow: {} }
   */
  ruleAction_IPsetRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
}

export class WafConstruct extends Construct {
  public readonly webAcl: wafv2.CfnWebACL;
  public readonly preSharedKeyValues: string[];

  constructor(scope: Construct, id: string, props: WafConstructProps) {
    super(scope, id);

    let preSharedKeyRule: wafv2.CfnWebACL.RuleProperty[] = [];
    let IPSetRule: wafv2.CfnWebACL.RuleProperty[] = [];

    // add additional rules
    const additionalRules: wafv2.CfnWebACL.RuleProperty[] = props.additionalRules ?? [];

    // Allow access from IP list
    if (props.allowIPList) {
      const IPSet = new wafv2.CfnIPSet(this, 'IPset', {
        ipAddressVersion: 'IPV4',
        scope: props.scope,
        addresses: props.allowIPList,
      });
      IPSetRule = [
        {
          priority: 11,
          action: props.ruleAction_IPsetRuleSet ?? { allow: {} },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: 'IPset',
          },
          name: 'IPset',
          statement: {
            ipSetReferenceStatement: {
              arn: IPSet.attrArn,
            },
          },
        },
      ];
    }

    // Check header x-pre-shared-key
    if (props.preSharedKeys) {
      const preSharedKeyValues: string[] = [];
      const byteMatchStatements: any[] = [];

      if (props.preSharedKeys.length > 1) {
        props.preSharedKeys.forEach((preSharedKey) => {
          const preSharedKeyValue = CryptoJS.SHA256(preSharedKey).toString().slice(0, 8);
          preSharedKeyValues.push(preSharedKeyValue);
          byteMatchStatements.push(createByteMatchStatement(preSharedKeyValue));
        });

        preSharedKeyRule = [createPreSharedKeyRule(byteMatchStatements)];
      } else {
        const preSharedKey = props.preSharedKeys[0];
        const preSharedKeyValue = CryptoJS.SHA256(preSharedKey).toString().slice(0, 8);
        preSharedKeyValues.push(preSharedKeyValue);

        preSharedKeyRule = [
          {
            ...createPreSharedKeyRule([createByteMatchStatement(preSharedKeyValue)]),
            statement: createByteMatchStatement(preSharedKeyValue),
          },
        ];
      }

      this.preSharedKeyValues = preSharedKeyValues;
      // Save hash value of each preSharedKey into a separate SSM Parameter Store entry
      preSharedKeyValues.forEach((hashedKey, index) => {
        const no = index + 1;
        const parameterName = `/${props.pjPrefix}/waf/PreSharedKeyValue-${no}`;
        new ssm.StringParameter(this, `PreSharedKeyValue-${no}`, {
          parameterName: parameterName,
          stringValue: hashedKey,
          description: `Hashed value for pre-shared key #${no}`,
        });
        new CfnOutput(this, `PreSharedKeyParameterName-${no}`, {
          value: parameterName,
          description: `SSM Parameter name for pre-shared key #${no}`,
          exportName: `${props.pjPrefix}-PreSharedKeyParameterName-${no}`,
        });
      });
    }

    // WebACLを作成
    const webAcl = new wafv2.CfnWebACL(this, `${cdk.Stack.of(this).stackName}WebAcl`, {
      defaultAction: props.defaultAction ?? { allow: {} },
      scope: props.scope,
      visibilityConfig: {
        cloudWatchMetricsEnabled: true,
        metricName: 'WafAcl',
        sampledRequestsEnabled: true,
      },
      rules: IPSetRule.concat(preSharedKeyRule).concat(additionalRules),
    });

    this.webAcl = webAcl;

    // Attach WebACL to list resources
    if (props.associations && props.scope == 'REGIONAL') {
      const webAclAssociation = props.associations.map((association, index) => {
        new wafv2.CfnWebACLAssociation(this, `WebAclAssociation-${index}`, {
          resourceArn: association,
          webAclArn: webAcl.attrArn,
        });
      });
    }
  }
}

const createByteMatchStatement = (preSharedKeyValue: string) => ({
  byteMatchStatement: {
    searchString: preSharedKeyValue,
    fieldToMatch: {
      singleHeader: {
        name: 'x-pre-shared-key',
      },
    },
    positionalConstraint: 'EXACTLY',
    textTransformations: [
      {
        priority: 0,
        type: 'NONE',
      },
    ],
  },
});

const createPreSharedKeyRule = (statements: any[]) => ({
  priority: 12,
  action: { allow: {} },
  visibilityConfig: {
    sampledRequestsEnabled: true,
    cloudWatchMetricsEnabled: true,
    metricName: 'IPset',
  },
  name: 'preSharedKey',
  statement: {
    orStatement: {
      statements,
    },
  },
});
