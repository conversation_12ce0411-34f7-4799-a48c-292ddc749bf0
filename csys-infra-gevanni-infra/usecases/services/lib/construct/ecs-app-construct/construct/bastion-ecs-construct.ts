import * as cdk from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { aws_iam as iam } from 'aws-cdk-lib';
import { aws_kms as kms } from 'aws-cdk-lib';
import { aws_ecs as ecs } from 'aws-cdk-lib';
import { aws_ecr as ecr } from 'aws-cdk-lib';
import { aws_logs as cwl } from 'aws-cdk-lib';

export interface BastionECSAppConstructProps extends cdk.StackProps {
  /**
   * Project and environment prefix
   */
  pjPrefix: string;
  /**
   * ECS application VPC
   */
  vpc: IVpc;
  /**
   * KMS key for encryption
   */
  appKey: kms.IKey;
  /**
   * ECR repository for bastion ECS task
   */
  ecrRepository: ecr.IRepository;
  /**
   * Container image tag for bastion ECS task
   */
  containerImageTag: string;
  /**
   * Container configuration for Fargate task definition
   *
   * @example - { cpu: 256, memoryLimitMiB: 512, }
   */
  containerConfig: {
    cpu: number;
    memoryLimitMiB: number;
  };
  /**
   * Task execution role for Fargate task definition
   */
  ecsTaskExecutionRole: iam.IRole;
  /**
   * Custom policy statements to add to the role
   * @default No custom policy statements
   */
  policyStatements?: iam.PolicyStatement[];
  /**
   * List of AWS managed policy name to add to the role
   * @default No AWS managed policy added to the role
   */
  managedPolicy?: string[];
  /**
   * Fargate log group removal policy
   * @default RETAIN
   */
  fargateLogGroupRemovalPolicy?: cdk.RemovalPolicy;
}

export class BastionECSAppConstruct extends Construct {
  public readonly taskDef: ecs.FargateTaskDefinition;
  public readonly securityGroup: ec2.SecurityGroup;
  public readonly ecsTaskServiceTaskRole: iam.Role;

  constructor(scope: Construct, id: string, props: BastionECSAppConstructProps) {
    super(scope, id);

    //ECS TaskRole
    const ecsTaskServiceTaskRole = new iam.Role(this, `TaskServiceTaskRole`, {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
    });
    this.ecsTaskServiceTaskRole = ecsTaskServiceTaskRole;

    ecsTaskServiceTaskRole.addToPrincipalPolicy(
      new iam.PolicyStatement({
        actions: [
          'ssmmessages:CreateControlChannel',
          'ssmmessages:CreateDataChannel',
          'ssmmessages:OpenControlChannel',
          'ssmmessages:OpenDataChannel',
        ],
        resources: ['*'],
      }),
    );

    // Provided policy statements
    if (props.policyStatements) {
      props.policyStatements.forEach((statement) => {
        ecsTaskServiceTaskRole.addToPolicy(statement);
      });
    }

    // Provided AWS Managed policy
    if (props.managedPolicy) {
      props.managedPolicy.forEach((policyName) => {
        ecsTaskServiceTaskRole.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName(policyName));
      });
    }

    //ECS LogGroup
    const fargateLogGroup = new cwl.LogGroup(this, 'LogGroup', {
      retention: cwl.RetentionDays.THREE_MONTHS,
      encryptionKey: props.appKey,
      logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
      removalPolicy: props.fargateLogGroupRemovalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    // ECS Task
    const task = new ecs.FargateTaskDefinition(this, `TaskDef`, {
      executionRole: props.ecsTaskExecutionRole,
      taskRole: ecsTaskServiceTaskRole,
      ...props.containerConfig,
    });

    task.addContainer(`${id}`, {
      image: ecs.ContainerImage.fromEcrRepository(props.ecrRepository, props.containerImageTag),
      logging: ecs.LogDriver.awsLogs({
        streamPrefix: `${id}-ECSApp-`,
        logGroup: fargateLogGroup,
      }),
    });

    this.taskDef = task;

    //SecurityGroup
    const securityGroup = new ec2.SecurityGroup(this, `SG`, {
      vpc: props.vpc,
      allowAllOutbound: true, // for AWS APIs
    });
    this.securityGroup = securityGroup;

    //Bastion Task Definition to SSM Parameter store（run_task.shが参照）
    new ssm.StringParameter(this, `${props.pjPrefix}-BastionEcsTaskDefNameParameter`, {
      parameterName: `/${props.pjPrefix}/bastion/ecs/task-def-name`,
      stringValue: task.family,
    });

    //Bastion SecurityGroup to SSM Parameter store（run_task.shが参照）
    new ssm.StringParameter(this, `${props.pjPrefix}-BastionSgIdParameter`, {
      parameterName: `/${props.pjPrefix}/bastion/sg-id`,
      stringValue: securityGroup.securityGroupId,
    });
  }
}
