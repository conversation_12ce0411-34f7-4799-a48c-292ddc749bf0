import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { aws_kms as kms } from 'aws-cdk-lib';
import { ITopic } from 'aws-cdk-lib/aws-sns';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { aws_cloudwatch as cw } from 'aws-cdk-lib';
import { aws_logs as cwl } from 'aws-cdk-lib';
import { aws_cloudwatch_actions as cw_actions } from 'aws-cdk-lib';
import { aws_ecs as ecs } from 'aws-cdk-lib';
import { aws_ecr as ecr } from 'aws-cdk-lib';
import { aws_events_targets as eventtarget } from 'aws-cdk-lib';

export interface EcsappConstructProps extends cdk.StackProps {
  serviceKey: string;
  /**
   * ECS application VPC
   */
  vpc: IVpc;
  /**
   * ECS cluster properties
   */
  ecsCluster: ecs.Cluster;
  /**
   * ECS application name
   */
  appName: string;
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * KMS key for encryption
   */
  appKey: kms.IKey;
  /**
   * SNS topic ARN for sending alarm
   */
  alarmTopic: ITopic;
  /**
   * Allow inbound access from other Security Groups
   *
   * @default - none
   */
  allowFromSG?: ec2.SecurityGroup[];
  /**
   * Port number of ECS application
   */
  portNumber: number;
  /**
   * Whether to creat service connect log group
   *
   * @default - false
   */
  useServiceConnect?: boolean;
  /**
   * ECR lifecycle rule
   * @default Keep last 10 images
   */
  ecrLifecycleRules?: ecr.LifecycleRule[];
  /**
   * ECR removal policy parameters
   */
  ecrRemovalPolicyParam?: {
    removalPolicy?: cdk.RemovalPolicy;
    emptyOnDelete?: boolean;
  };
  /**
   * Fargate log group removal policy
   * @default RETAIN
   */
  fireLensLogGroupRemovalPolicy?: cdk.RemovalPolicy;
  /**
   * Service connect log group removal policy
   * @default cdk.RemovalPolicy.RETAIN
   */
  serviceConnectLogGroupRemovalPolicy?: cdk.RemovalPolicy;
  /*
   * Enable alarm
   */
  enableAlarm: boolean;
  /**
   * Allow inbound access from other ECS App Security Groups
   *
   * @default - none
   */
  AllowSGOtherECSApp?: ec2.ISecurityGroup[];
}

export class EcsappConstruct extends Construct {
  public readonly securityGroupForFargate: ec2.SecurityGroup;
  public readonly serviceConnectLogGroup: cwl.LogGroup;
  public readonly fireLensLogGroup: cwl.LogGroup;
  public readonly ecsServiceName: string;
  public readonly appName: string;
  public readonly portNumber: number;
  public readonly ecrRepository: ecr.IRepository;
  public readonly secret: ISecret;

  constructor(scope: Construct, id: string, props: EcsappConstructProps) {
    super(scope, id);

    this.appName = props.appName;
    this.portNumber = props.portNumber;

    const secret = new secretsmanager.Secret(this, 'Secret', {
      secretName: `${props.prefix}/${props.appName}`,
      secretObjectValue: {},
    });
    this.secret = secret;

    const defaultLifecycleRules: ecr.LifecycleRule[] = [
      {
        description: 'Keep last 10 images',
        maxImageCount: 10,
      },
    ];
    const lifecycleRules = props.ecrLifecycleRules ?? defaultLifecycleRules;

    // Create a repository
    const repository = new ecr.Repository(this, `Repo`, {
      imageScanOnPush: true,
      lifecycleRules,
      removalPolicy: props.ecrRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      emptyOnDelete: props.ecrRemovalPolicyParam?.emptyOnDelete ?? false,
    });
    this.ecrRepository = repository;

    const target = new eventtarget.SnsTopic(props.alarmTopic);

    repository.onImageScanCompleted('ImageScanComplete').addTarget(target);

    const securityGroupForFargate = new ec2.SecurityGroup(this, `Sg`, {
      vpc: props.vpc,
      allowAllOutbound: true, // for AWS APIs
    });

    if (props.allowFromSG) {
      props.allowFromSG.forEach((securityGroup) => {
        securityGroupForFargate.connections.allowFrom(securityGroup, ec2.Port.tcp(props.portNumber));
      });
    }

    if (props.AllowSGOtherECSApp) {
      props.AllowSGOtherECSApp.forEach((securityGroup) => {
        securityGroupForFargate.connections.allowFrom(securityGroup, ec2.Port.tcp(props.portNumber));
      });
    }

    this.securityGroupForFargate = securityGroupForFargate;

    // CloudWatch Logs Group for FireLens
    const fireLensLogGroup = new cwl.LogGroup(this, `Log`, {
      // 保存期間3ヵ月とする
      retention: cwl.RetentionDays.THREE_MONTHS,
      encryptionKey: props.appKey,
      logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
      removalPolicy: props.fireLensLogGroupRemovalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });
    this.fireLensLogGroup = fireLensLogGroup;

    if (props.useServiceConnect) {
      // CloudWatch Logs Group for Service Connect
      const serviceConnectLogGroup = new cwl.LogGroup(this, `LogforSC`, {
        // 方式設計より保存期間5年とする
        retention: cwl.RetentionDays.FIVE_YEARS,
        encryptionKey: props.appKey,
        logGroupName: cdk.PhysicalName.GENERATE_IF_NEEDED,
        removalPolicy: props.serviceConnectLogGroupRemovalPolicy ?? cdk.RemovalPolicy.RETAIN,
      });
      this.serviceConnectLogGroup = serviceConnectLogGroup;

      new ssm.StringParameter(this, 'SSMLogGroupServiceConnect', {
        parameterName: `/${props.prefix}/${props.appName}/LogGroupServiceConnect`,
        stringValue: serviceConnectLogGroup.logGroupName,
      });
    }

    this.ecsServiceName = `${props.appName}-Service`; //PipelineStackでもこの名前を使用

    if (props.enableAlarm) {
      new cw.Metric({
        metricName: 'CPUUtilization',
        namespace: 'ECS',
        dimensionsMap: {
          ClusterName: props.ecsCluster.clusterName,
          ServiceName: this.ecsServiceName,
        },
        period: cdk.Duration.minutes(1),
        statistic: cw.Stats.AVERAGE,
      })
        .createAlarm(this, 'FargateCpuUtil', {
          evaluationPeriods: 3,
          datapointsToAlarm: 3,
          threshold: 80,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(props.alarmTopic));
    }

    new ssm.StringParameter(this, 'SSMSecretArn', {
      parameterName: `/${props.prefix}/${props.appName}/SecretArn`,
      stringValue: secret.secretArn,
    });

    new ssm.StringParameter(this, 'SSMEcrRepo', {
      parameterName: `/${props.prefix}/${props.appName}/EcrRepo`,
      stringValue: repository.repositoryName,
    });

    new ssm.StringParameter(this, 'SSMImageUri', {
      parameterName: `/${props.prefix}/${props.appName}/ImageUri`,
      stringValue: repository.repositoryUri,
    });

    new ssm.StringParameter(this, 'SSMEcsSGId', {
      parameterName: `/${props.serviceKey}/${props.appName}/EcsSGId`,
      stringValue: securityGroupForFargate.securityGroupId,
    });

    new ssm.StringParameter(this, 'SSMEcsServiceName', {
      parameterName: `/${props.prefix}/${props.appName}/EcsServiceName`,
      stringValue: this.ecsServiceName,
    });

    new ssm.StringParameter(this, 'SSMPortNumber', {
      parameterName: `/${props.prefix}/${props.appName}/PortNumber`,
      stringValue: String(props.portNumber ?? ''),
    });

    new ssm.StringParameter(this, 'SSMLogGroupFirelens', {
      parameterName: `/${props.prefix}/${props.appName}/LogGroupFirelens`,
      stringValue: fireLensLogGroup.logGroupName,
    });
  }
}
