import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import * as events from 'aws-cdk-lib/aws-events';
import * as eventsTarget from 'aws-cdk-lib/aws-events-targets';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';
import * as cwl from 'aws-cdk-lib/aws-logs';
import * as sd from 'aws-cdk-lib/aws-servicediscovery';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as secrets from 'aws-cdk-lib/aws-secretsmanager';
import * as codedeploy from 'aws-cdk-lib/aws-codedeploy';
import { IRemovalPolicyParam, IBlueGreenPipelineParam } from '../../../../params/interface';

export interface PipelineBlueGreenConstructProps extends IBlueGreenPipelineParam {
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * ECS application name
   */
  appName: string;
  /**
   * ECS cluster properties
   */
  ecsCluster: ecs.Cluster;
  /**
   * ECS application service name
   */
  ecsServiceName: string;
  /**
   * ECS application security group
   */
  securityGroup: ec2.SecurityGroup;
  /**
   * ECS application VPC
   */
  vpc: ec2.IVpc;
  /**
   * FireLens Container Image Name
   */
  fireLensImage: string;
  /**
   * Firehose Stream of FireLens Destination
   */
  firehoseStream: firehose.CfnDeliveryStream;
  /**
   * Flag to determine the filename of image.zip
   */
  imageZipFlag: 'front' | 'back';
  /**
   * Pipeline bucket Removal policy params
   */
  pipelineBucketRemovalPolicyParam?: IRemovalPolicyParam;
  /**
   * SecretsManager
   */
  secret: secrets.ISecret;
  /**
   * Project EnvName
   */
  EnvName: string;
  /**
   * Pipeline source bucket lifecycle rules
   */
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  /**
   * serviceId
   */
  serviceId: string;
  /**
   * Whether the application is backend
   */
  blueTargetGroup: elbv2.ApplicationTargetGroup;
  greenTargetGroup: elbv2.ApplicationTargetGroup;
  listener: elbv2.ApplicationListener;
  testListener: elbv2.ApplicationListener;
  /**
   * ECS Deploy Configuration.
   *
   * @default cdk.aws_codedeploy.EcsDeploymentConfig.ALL_AT_ONCE
   */
  deploymentConfig?: codedeploy.IEcsDeploymentConfig;
}

export class PipelineBlueGreenConstruct extends Construct {
  public readonly sourceBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props: PipelineBlueGreenConstructProps) {
    super(scope, id);

    const eventBusArn =
      props.eventBusArn ??
      `arn:aws:events:${cdk.Stack.of(this).region}:${props.crossAccessAccountId}:event-bus/${props.crossAccessEnvName}${
        props.crossAccessPjPrefix
      }-CrossAccountEventBus`;

    //Dev環境のみ空のタグを設定
    const BillingTagKye = props.EnvName.startsWith('Dev') ? ' ' : 'CmBillingGroup';
    const BillingTagValue = props.EnvName.startsWith('Dev') ? ' ' : props.prefix;
    const PropaGateTags = props.EnvName.startsWith('Dev') ? 'NONE' : 'SERVICE';

    // クロスアカウントアクセス用 IAM Role
    const crossAccessRole = new iam.Role(this, 'CrossAccessRole', {
      roleName: `${props.prefix}${props.appName}-CrossAccessRole`,
      assumedBy: new iam.AnyPrincipal().withConditions({
        ArnLike: {
          'aws:PrincipalArn': [
            `arn:aws:iam::${props.crossAccessAccountId}:role/${props.crossAccessEnvName}${props.crossAccessPjPrefix}${props.appName}-PipelineRole`,
            `arn:aws:iam::${props.crossAccessAccountId}:role/${props.crossAccessEnvName}${props.crossAccessPjPrefix}${props.appName}-LambdaRole`,
          ],
        },
      }),
      inlinePolicies: {
        allowSsmPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['ssm:GetParameter'],
              effect: iam.Effect.ALLOW,
              resources: [
                `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.serviceId}/*`,
                `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.prefix}/*`,
                `arn:aws:ssm:ap-northeast-1:${cdk.Stack.of(this).account}:parameter/${props.prefix}/${props.appName}/*`,
              ],
            }),
          ],
        }),
        // Ecspresso コマンド実行用権限
        // https://zenn.dev/fujiwara/books/ecspresso-handbook-v2/viewer/reference
        allowedEcspressoPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: [
                'ecs:*',
                'iam:PassRole',
                'application-autoscaling:Describe*',
                'codedeploy:List*',
                'codedeploy:CreateDeployment',
                'codedeploy:BatchGet*',
                'codedeploy:GetDeploymentConfig',
                'codedeploy:RegisterApplicationRevision',
              ],
              effect: iam.Effect.ALLOW,
              resources: ['*'],
            }),
            new iam.PolicyStatement({
              actions: [
                'codedeploy:ListDeployments',
                'codedeploy:GetDeployment',
                'codedeploy:ContinueDeployment',
                'codedeploy:StopDeployment',
              ],
              effect: iam.Effect.ALLOW,
              resources: ['*'],
            }),
          ],
        }),
        // config.py実行用権限
        allowedConfigPolicy: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: [
                'application-autoscaling:DescribeScalableTargets',
                'application-autoscaling:RegisterScalableTarget',
                'application-autoscaling:DeregisterScalableTarget',
                'application-autoscaling:PutScalingPolicy',
                'application-autoscaling:DeleteScalingPolicy',
                'application-autoscaling:DescribeScalingPolicies',
                'elasticloadbalancing:ModifyTargetGroup',
              ],
              effect: iam.Effect.ALLOW,
              resources: ['*'],
            }),
          ],
        }),
      },
    });

    // Pipeline SourceAction 用バケット
    const sourceBucket = new s3.Bucket(this, 'PipelineSourceBucket', {
      bucketName: `${props.prefix.toLocaleLowerCase()}${props.appName.toLocaleLowerCase()}-sourcebucket`,
      versioned: true,
      eventBridgeEnabled: true,
      removalPolicy: props.pipelineBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.pipelineBucketRemovalPolicyParam?.autoDeleteObjects ?? false,
      lifecycleRules: props.pipelineSourceBucketLifeCycleRules,
    });
    sourceBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        principals: [
          new iam.AnyPrincipal().withConditions({
            ArnLike: {
              'aws:PrincipalArn': [
                `arn:aws:iam::${props.crossAccessAccountId}:role/${props.crossAccessEnvName}${props.crossAccessPjPrefix}${props.appName}-PipelineRole`,
                `arn:aws:iam::${props.crossAccessAccountId}:role/${props.crossAccessEnvName}${props.crossAccessPjPrefix}${props.appName}-LambdaRole`,
              ],
            },
          }),
        ],
        actions: ['s3:*'],
        resources: [`${sourceBucket.bucketArn}`, `${sourceBucket.bucketArn}/*`],
      }),
    );
    this.sourceBucket = sourceBucket;

    // Set the name of the file that will trigger the pipeline
    let imageZipName;
    if (props.imageZipFlag == 'front') {
      imageZipName = 'image_bg_front.zip';
    } else {
      imageZipName = 'image_bg_backend.zip';
    }

    const crossaccountEventBus = events.EventBus.fromEventBusArn(this, 'CrossAccountEventBus', eventBusArn);

    new events.Rule(this, 'PipelineTriggerEventRule', {
      eventPattern: {
        source: ['aws.s3'],
        detailType: ['Object Created'],
        detail: {
          bucket: {
            name: [sourceBucket.bucketName],
          },
          object: {
            key: [imageZipName],
          },
        },
      },
      targets: [new eventsTarget.EventBus(crossaccountEventBus)],
    });

    if (props.isEcsServiceDeployed) {
      const service = ecs.FargateService.fromFargateServiceAttributes(this, 'Service', {
        cluster: props.ecsCluster,
        serviceName: props.ecsServiceName,
      });

      const application = new codedeploy.EcsApplication(this, 'CodeDeployApplication', {
        applicationName: `${props.prefix}${props.appName}-Application`,
      });

      const deploymentGroup = new codedeploy.EcsDeploymentGroup(this, 'BlueGreenDG', {
        deploymentGroupName: `${props.prefix}${props.appName}-DeploymentGroup`,
        service: service,
        blueGreenDeploymentConfig: {
          blueTargetGroup: props.blueTargetGroup,
          greenTargetGroup: props.greenTargetGroup,
          listener: props.listener,
          testListener: props.testListener,
          deploymentApprovalWaitTime: cdk.Duration.hours(1),
          terminationWaitTime: cdk.Duration.hours(1),
        },
        deploymentConfig: props.deploymentConfig ?? codedeploy.EcsDeploymentConfig.ALL_AT_ONCE,
        application: application,
        autoRollback: {
          failedDeployment: true,
        },
      });
    }

    new ssm.StringParameter(this, 'SSMTaskFamily', {
      parameterName: `/${props.prefix}/${props.appName}/TaskFamily`,
      stringValue: `${props.prefix}-${props.appName}-Taskdef`,
    });

    new ssm.StringParameter(this, 'SSMFirelensImageName', {
      parameterName: `/${props.prefix}/${props.appName}/FirelensImageName`,
      stringValue: props.fireLensImage,
    });

    new ssm.StringParameter(this, 'SSMStreamName', {
      parameterName: `/${props.prefix}/${props.appName}/StreamName`,
      stringValue: props.firehoseStream.deliveryStreamName ?? '',
    });

    const ecrTag = new ssm.StringParameter(this, 'ecrTag', {
      parameterName: `/${props.prefix}/${props.appName}/ecrTag`,
      stringValue: 'sample',
    });

    new ssm.StringParameter(this, 'SSMEcrTag', {
      parameterName: `/${props.prefix}/${props.appName}/EcrTag`,
      stringValue: ecrTag.parameterName,
    });

    new ssm.StringParameter(this, 'SSMCmBillingGroupTagKey', {
      parameterName: `/${props.prefix}/${props.appName}/CmBillingGroupTagKey`,
      stringValue: BillingTagKye,
    });

    new ssm.StringParameter(this, 'SSMCmBillingGroupTag', {
      parameterName: `/${props.prefix}/${props.appName}/CmBillingGroupTag`,
      stringValue: BillingTagValue,
    });

    new ssm.StringParameter(this, 'SSMServiceIdTag', {
      parameterName: `/${props.prefix}/${props.appName}/ServiceIdTag`,
      stringValue: props.serviceId,
    });

    new ssm.StringParameter(this, 'SSMPropagateTag', {
      parameterName: `/${props.prefix}/${props.appName}/PropagateTag`,
      stringValue: PropaGateTags,
    });

    new ssm.StringParameter(this, 'SSMSourceBucketName', {
      parameterName: `/${props.prefix}/${props.appName}/SourceBucketName`,
      stringValue: sourceBucket.bucketName,
    });
  }
}
