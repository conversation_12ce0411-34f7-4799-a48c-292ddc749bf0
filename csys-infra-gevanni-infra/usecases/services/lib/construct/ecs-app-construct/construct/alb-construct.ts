import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { aws_elasticloadbalancingv2 as elbv2 } from 'aws-cdk-lib';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import { aws_s3_notifications as s3n } from 'aws-cdk-lib';
import { aws_iam as iam } from 'aws-cdk-lib';
import { ITopic } from 'aws-cdk-lib/aws-sns';
import { aws_cloudwatch as cw } from 'aws-cdk-lib';
import { aws_cloudwatch_actions as cw_actions } from 'aws-cdk-lib';
import { aws_ssm as ssm } from 'aws-cdk-lib';
import { region_info as ri } from 'aws-cdk-lib';
import { aws_certificatemanager as acm } from 'aws-cdk-lib';
import { EcsappConstruct } from './ecs-app-construct';
import { AlbtgConstruct } from './alb-target-group-construct';
import { IEcsAlbParam } from '../../../../params/interface';
import { NewRelicLogIngestionS3Construct } from './lambda-newrelic-log-s3-construct';
import { ILayerVersion } from 'aws-cdk-lib/aws-lambda';

interface AlbConstructProps extends cdk.StackProps {
  /**
   * Application Load Balancer's VPC
   */
  vpc: IVpc;
  /**
   * SNS topic ARN for sending alarm
   */
  alarmTopic: ITopic;
  /**
   * ECS applications mapping to Application Load Balancer target groups
   */
  ecsApps: IEcsAlbParam;
  /**
   * ACM ARN
   */
  AcmArn?: string;
  /**
   * Lifecycle rules of ALB access log bucket
   */
  accessLogBucketLifecycleRules: s3.LifecycleRule[];

  /**
   * ALB log removal policy parameter
   */
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  /**
   * Service Prefix
   */
  prefix: string;
  /**
   * New Relic Lisense Key
   */
  newrelicSecretArn: string;
  /**
   * New Relic log ingestion s3 lambda layer
   */
  newrelicLayer: ILayerVersion;
  /*
   * Enable alarm
   */
  enableAlarm: boolean;
  appName: string;
}

export class AlbConstruct extends Construct {
  public readonly appAlb: elbv2.ApplicationLoadBalancer;
  public readonly appAlbListerner: elbv2.ApplicationListener;
  public readonly appAlbSecurityGroup: ec2.SecurityGroup;
  public readonly webContentsBucket: s3.Bucket;
  // public readonly ecsAlbApps: EcsappConstruct;
  public readonly AlbTg: AlbtgConstruct;

  constructor(scope: Construct, id: string, props: AlbConstructProps) {
    super(scope, id);

    // --- Security Groups ---

    //Security Group of ALB for App
    const securityGroupForAlb = new ec2.SecurityGroup(this, 'SgAlb', {
      vpc: props.vpc,
      allowAllOutbound: true,
    });
    this.appAlbSecurityGroup = securityGroupForAlb;

    // ------------ Application LoadBalancer ---------------

    // ALB for App Server
    const lbForApp = new elbv2.ApplicationLoadBalancer(this, 'Alb', {
      vpc: props.vpc,
      internetFacing: true,
      securityGroup: securityGroupForAlb,
      vpcSubnets: props.vpc.selectSubnets({
        subnetGroupName: 'Public',
      }),
    });
    this.appAlb = lbForApp;

    let lbForAppListener: elbv2.ApplicationListener;
    if (props.AcmArn) {
      const albCert = acm.Certificate.fromCertificateArn(this, 'albCertificate', props.AcmArn);
      lbForAppListener = lbForApp.addListener('app', {
        port: 443,
        certificates: [
          {
            certificateArn: albCert.certificateArn,
          },
        ],
        sslPolicy: elbv2.SslPolicy.RECOMMENDED_TLS,
      });

      // (mynavi mod) create redirect listener.
      const redirectListener = lbForApp.addListener('redirect', {
        port: 80,
        defaultAction: elbv2.ListenerAction.redirect({
          port: '443',
          protocol: 'HTTPS',
          permanent: true,
        }),
      });
      redirectListener.node.addDependency(lbForAppListener);
    } else {
      lbForAppListener = lbForApp.addListener('app', {
        port: 80,
        protocol: elbv2.ApplicationProtocol.HTTP,
        open: true,
      });
    }
    this.appAlbListerner = lbForAppListener;

    // Enable ALB Access Logging
    //
    // This bucket can not be encrypted with KMS CMK
    // See: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-access-logs.html#access-logging-bucket-permissions
    //
    const albLogBucket = new s3.Bucket(this, 'alb-log-bucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
      enforceSSL: true,
      lifecycleRules: props.accessLogBucketLifecycleRules,
    });

    lbForApp.setAttribute('access_logs.s3.enabled', 'true');
    lbForApp.setAttribute('access_logs.s3.bucket', albLogBucket.bucketName);

    // Permissions for Access Logging
    //    Why don't use bForApp.logAccessLogs(albLogBucket); ?
    //    Because logAccessLogs add wider permission to other account (PutObject*). S3 will become Noncompliant on Security Hub [S3.6]
    //    See: https://docs.aws.amazon.com/securityhub/latest/userguide/securityhub-standards-fsbp-controls.html#fsbp-s3-6
    //    See: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/load-balancer-access-logs.html#access-logging-bucket-permissions
    albLogBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['s3:PutObject'],
        // ALB access logging needs S3 put permission from ALB service account for the region
        principals: [new iam.AccountPrincipal(ri.RegionInfo.get(cdk.Stack.of(this).region).elbv2Account)],
        resources: [albLogBucket.arnForObjects(`AWSLogs/${cdk.Stack.of(this).account}/*`)],
      }),
    );
    albLogBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['s3:PutObject'],
        principals: [new iam.ServicePrincipal('delivery.logs.amazonaws.com')],
        resources: [albLogBucket.arnForObjects(`AWSLogs/${cdk.Stack.of(this).account}/*`)],
        conditions: {
          StringEquals: {
            's3:x-amz-acl': 'bucket-owner-full-control',
          },
        },
      }),
    );

    albLogBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['s3:GetBucketAcl'],
        principals: [new iam.ServicePrincipal('delivery.logs.amazonaws.com')],
        resources: [albLogBucket.bucketArn],
      }),
    );

    // New Relic integration of access logs
    const accessLogIngestion = new NewRelicLogIngestionS3Construct(this, 'NewRelicLogIngestion', {
      prefix: props.prefix,
      newrelicSecretArn: props.newrelicSecretArn,
      albLogBucket: albLogBucket,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy,
      layer: props.newrelicLayer,
      appName: props.appName,
    });

    albLogBucket.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['s3:GetObject', 's3:ListBucket'],
        principals: [new iam.ArnPrincipal(accessLogIngestion.function.role?.roleArn || '')],
        resources: [albLogBucket.bucketArn, `${albLogBucket.bucketArn}/*`],
      }),
    );

    albLogBucket.addEventNotification(
      s3.EventType.OBJECT_CREATED_PUT,
      new s3n.LambdaDestination(accessLogIngestion.function),
    );

    // for newrelic
    new ssm.StringParameter(this, 'SSMAlbName', {
      parameterName: `/${props.prefix}/LoadBalancerName`,
      stringValue: lbForApp.loadBalancerName,
    });

    // Alarm for ALB - ResponseTime
    if (props.enableAlarm) {
      lbForApp.metrics
        .targetResponseTime({
          period: cdk.Duration.minutes(1),
          statistic: cw.Stats.AVERAGE,
        })
        .createAlarm(this, 'AlbResponseTime', {
          evaluationPeriods: 3,
          threshold: 100,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(props.alarmTopic));

      // Alarm for ALB - HTTP 4XX Count
      lbForApp.metrics
        .httpCodeElb(elbv2.HttpCodeElb.ELB_4XX_COUNT, {
          period: cdk.Duration.minutes(1),
          statistic: cw.Stats.SUM,
        })
        .createAlarm(this, 'AlbHttp4xx', {
          evaluationPeriods: 3,
          threshold: 10,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(props.alarmTopic));

      // Alarm for ALB - HTTP 5XX Count
      lbForApp.metrics
        .httpCodeElb(elbv2.HttpCodeElb.ELB_5XX_COUNT, {
          period: cdk.Duration.minutes(1),
          statistic: cw.Stats.SUM,
        })
        .createAlarm(this, 'AlbHttp5xx', {
          evaluationPeriods: 3,
          threshold: 10,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(props.alarmTopic));
    }

    const AlbTg = new AlbtgConstruct(this, `${props.ecsApps.appName}-TG`, {
      prefix: props.prefix,
      vpc: props.vpc,
      alarmTopic: props.alarmTopic,
      appAlbListener: lbForAppListener,
      targetGroupPort: props.ecsApps.portNumber,
      enableAlarm: props.enableAlarm,
    });
    this.AlbTg = AlbTg;

    new ssm.StringParameter(this, 'SSMTargetGroupArn', {
      parameterName: `/${props.prefix}/${props.appName}/TargetGroupArn`,
      stringValue: AlbTg.lbForAppTargetGroup.targetGroupArn,
    });
  }
}
