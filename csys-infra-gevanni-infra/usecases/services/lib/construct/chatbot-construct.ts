import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_chatbot as cb } from 'aws-cdk-lib';
import { aws_iam as iam } from 'aws-cdk-lib';

export interface ChatbotProps extends cdk.StackProps {
  /**
   * AWS Chatbot uses SNS topics to send event and alarm notifications.
   */
  topicArn: string;
  /**
   * Slack Channel Configuration
   */
  slackChannelConfiguration?: {
    /**
     * Slack Channel ID
     *
     * @example - C01YYYYYYYY
     */
    channelId: string;
    /**
     * Slack Workspace ID
     *
     * @example - TMZU8DSDV
     */
    workspaceId: string;
  };

  /**
   * Teams Channel Configuration
   */
  teamsChannelConfiguration?: {
    /**
     * Teams Channel Name
     *
     * @example - MyChannel
     */
    channelName: string;
    /**
     * Team ID
     *
     * @example - 12345678-1234-1234-1234-123456789012
     */
    teamId: string;
    /**
     * Teams Tenant ID
     *
     * @example - 12345678-1234-1234-1234-123456789012
     */
    tenantId: string;
    /**
     * Teams Channel ID
     *
     * @example - 19%3ab6ef35dc342d56ba5654e6fc6d25a071%40thread.tacv2
     */
    teamsChannelId: string;
  };
}

// NOTICE: AWS Chatbot can send events from supported services only.
// See: https://docs.aws.amazon.com/ja_jp/chatbot/latest/adminguide/related-services.html
export class Chatbot extends Construct {
  constructor(scope: Construct, id: string, props: ChatbotProps) {
    super(scope, id);

    // AWS Chatbot configuration for sending message
    const chatbotRole = new iam.Role(this, 'ChatbotRole', {
      assumedBy: new iam.ServicePrincipal('chatbot.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('ReadOnlyAccess'),
        iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchReadOnlyAccess'),
      ],
    });

    if (props.slackChannelConfiguration) {
      // !!! Create SlackChannel and add aws chatbot app to the room
      new cb.CfnSlackChannelConfiguration(this, 'ChatbotChannel', {
        configurationName: `${id}-${props.slackChannelConfiguration.workspaceId}`,
        slackChannelId: props.slackChannelConfiguration.channelId,
        iamRoleArn: chatbotRole.roleArn,
        slackWorkspaceId: props.slackChannelConfiguration.workspaceId,
        snsTopicArns: [props.topicArn],
      });
    }

    if (props.teamsChannelConfiguration) {
      new cb.CfnMicrosoftTeamsChannelConfiguration(this, 'ChatbotTeamsChannel', {
        configurationName: `${id}-${props.teamsChannelConfiguration.channelName}`,
        teamId: props.teamsChannelConfiguration.teamId,
        teamsChannelId: props.teamsChannelConfiguration.teamsChannelId,
        teamsTenantId: props.teamsChannelConfiguration.tenantId,
        snsTopicArns: [props.topicArn],
        iamRoleArn: chatbotRole.roleArn,
      });
    }
  }
}
