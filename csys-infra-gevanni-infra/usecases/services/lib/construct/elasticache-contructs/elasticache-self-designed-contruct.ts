import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as elasticache from 'aws-cdk-lib/aws-elasticache';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { IElastiCacheParam, IRemovalPolicyParam } from 'params/interface';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { DataFirehose } from '../data-firehose-construct';
import { aws_s3 as s3 } from 'aws-cdk-lib';

export interface ElastiCacheProps extends cdk.StackProps {
  pjPrefix: string;
  myVpc: ec2.IVpc;
  appKey: kms.IKey;
  alarmTopic: sns.ITopic;
  ElastiCacheParam: IElastiCacheParam['ElastiCacheSelfDesignedParam'];
  secret: secretsmanager.CfnSecret;
  subnetgroup: elasticache.CfnSubnetGroup;
  securityGroup: ec2.SecurityGroup;
  newrelicSecretArn: string;
  logRemovalPolicyParam?: IRemovalPolicyParam;
  LogBucketLifecycleRules: s3.LifecycleRule[];
}

export class ElastiCache extends Construct {
  constructor(scope: Construct, id: string, props: ElastiCacheProps) {
    super(scope, id);

    const slowLogFirehose = new DataFirehose(this, `SlowLogDatFirehose`, {
      firehoseStreamName: `${props.pjPrefix}-ElastiCache-SlowLog`,
      firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/elasticache/slow-log`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'AllData',
      secretArn: props.newrelicSecretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.LogBucketLifecycleRules,
    });
    const engineLogFirehose = new DataFirehose(this, `EngineLogDatFirehose`, {
      firehoseStreamName: `${props.pjPrefix}-ElastiCache-EngineLog`,
      firehoseLogGroupName: `/aws/firehose/${props.pjPrefix}/elasticache/engine-log`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/firehose/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'AllData',
      secretArn: props.newrelicSecretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.LogBucketLifecycleRules,
    });

    // カスタマイズする場合は../elasticache-param-groupparams/config.tsファイルを修正していく。
    const CustomParameterGroup = new elasticache.CfnParameterGroup(this, 'ElastiCacheCustomParameterGroup', {
      ...props.ElastiCacheParam.elastiCacheCustomParam,
    });

    const replicationGroup = new elasticache.CfnReplicationGroup(this, `${props.pjPrefix}-ElastiCache`, {
      replicationGroupDescription: 'Gevanni ElastiCache Replication Group',
      atRestEncryptionEnabled: true,
      authToken: props.secret.secretString,
      automaticFailoverEnabled: true,
      cacheSubnetGroupName: props.subnetgroup.cacheSubnetGroupName,
      engine: props.ElastiCacheParam.engine,
      engineVersion: props.ElastiCacheParam.engineVersion,
      kmsKeyId: props.appKey.keyId,
      logDeliveryConfigurations: [
        {
          logType: 'slow-log',
          destinationType: 'kinesis-firehose',
          destinationDetails: {
            kinesisFirehoseDetails: {
              deliveryStream: slowLogFirehose.stream.deliveryStreamName || '',
            },
          },
          logFormat: 'json',
        },
        {
          logType: 'engine-log',
          destinationType: 'kinesis-firehose',
          destinationDetails: {
            kinesisFirehoseDetails: {
              deliveryStream: engineLogFirehose.stream.deliveryStreamName || '',
            },
          },
          logFormat: 'json',
        },
      ],
      multiAzEnabled: true,
      notificationTopicArn: props.alarmTopic.topicArn,
      numNodeGroups: props.ElastiCacheParam.numNodeGroups,
      replicasPerNodeGroup: props.ElastiCacheParam.replicasPerNodeGroup,
      replicationGroupId: props.ElastiCacheParam.replicationGroupId,
      securityGroupIds: [props.securityGroup.securityGroupId],
      transitEncryptionEnabled: true,
      cacheParameterGroupName: CustomParameterGroup.ref,
      preferredMaintenanceWindow: props.ElastiCacheParam.preferredMaintenanceWindow,
      autoMinorVersionUpgrade: props.ElastiCacheParam.autoMinorVersionUpgrade,
    });

    replicationGroup.cfnOptions.updatePolicy = {
      useOnlineResharding: true,
    };

    if (props.ElastiCacheParam.enableAutoScale) {
      //オートスケール有効時のインスタンスタイプを設定
      replicationGroup.cacheNodeType = props.ElastiCacheParam.cacheNodeTypeEnableAutoScale;
      //オートスケールの設定
      const ScalableTarget = new appscaling.ScalableTarget(this, 'ElastiCacheShardsScalableTarget', {
        serviceNamespace: appscaling.ServiceNamespace.ELASTICACHE,
        scalableDimension: 'elasticache:replication-group:NodeGroups',
        minCapacity: props.ElastiCacheParam.minCapacity,
        maxCapacity: props.ElastiCacheParam.maxCapacity,
        resourceId: `replication-group/${replicationGroup.replicationGroupId}`,
      });

      cdk.Aspects.of(ScalableTarget).add({
        visit(node) {
          if (node instanceof appscaling.CfnScalableTarget) {
            node.addDependency(replicationGroup);
          }
        },
      });

      ScalableTarget.scaleToTrackMetric('ElastiCacheShardsCPUUtilization', {
        targetValue: props.ElastiCacheParam.targetValueToScale,
        predefinedMetric: props.ElastiCacheParam.predefinedMetricToScale,
      });
    } else {
      //オートスケール無効時のインスタンスタイプを設定
      replicationGroup.cacheNodeType = props.ElastiCacheParam.cacheNodeTypeDisableAutoScale;
    }

    // クラスターグループによって出力されるエンドポイントが異なるため、分岐処理を行う。
    // 参考：https://docs.aws.amazon.com/ja_jp/AmazonElastiCache/latest/dg/Replication.Endpoints.html
    if (props.ElastiCacheParam.numNodeGroups > 1) {
      // numNodeGroups が 1 以上の時、クラスターモードが有効となるため、configuration endopoint を SSM に保存する。
      new ssm.StringParameter(this, 'SSMElastiCacheRepolicationGroupEndPoint', {
        parameterName: `/${props.pjPrefix}/${props.ElastiCacheParam.engine}/endpoint`,
        stringValue: replicationGroup.attrConfigurationEndPointAddress,
      });
    } else {
      new ssm.StringParameter(this, 'SSMElastiCacheRepolicationGroupEndPoint', {
        parameterName: `/${props.pjPrefix}/${props.ElastiCacheParam.engine}/endpoint`,
        stringValue: replicationGroup.attrPrimaryEndPointAddress,
      });
    }
  }
}
