// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "ElastiCacheRedisLoggroupFB0C3E33": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ElastiCacheRedisSecret": Object {
      "Type": "AWS::SecretsManager::Secret",
    },
    "ElastiCacheRedisSecuritygGroup20195709": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ElastiCacheRedis/ElastiCacheRedisSecuritygGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ElastiCacheRedisSecuritygGroupfromGEVANNIserviceECSECSAppBastionECSAPPSGCE65230D637957F380CF": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppBastionECSAPPSGCE65230D:6379",
        "FromPort": 6379,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheRedisSecuritygGroup20195709",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppBastionECSAPPSGEAB03032GroupId846779B4",
        },
        "ToPort": 6379,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ElastiCacheRedisSecuritygGroupfromGEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B6379A333C5A7": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B:6379",
        "FromPort": 6379,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheRedisSecuritygGroup20195709",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsAppFrontAppEcsResourcesSg52129FB0GroupId9ADCF5E9",
        },
        "ToPort": 6379,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ElastiCacheRedisSecuritygGroupfromGEVANNIserviceECSECSAppEcsBackendBackAppEcsResourcesSg7B0FB0D06379DC0CCDFE": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppEcsBackendBackAppEcsResourcesSg7B0FB0D0:6379",
        "FromPort": 6379,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheRedisSecuritygGroup20195709",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27GroupId7DECC85F",
        },
        "ToPort": 6379,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ElastiCacheRedisServerlessGEVANNIserviceElastiCacheRedisServerlessE3AF238E": Object {
      "Properties": Object {
        "CacheUsageLimits": Object {
          "DataStorage": Object {
            "Maximum": 123,
            "Minimum": 0,
            "Unit": "GB",
          },
          "ECPUPerSecond": Object {
            "Maximum": 1000,
            "Minimum": 0,
          },
        },
        "Engine": "redis",
        "KmsKeyId": Object {
          "Fn::Select": Array [
            1,
            Object {
              "Fn::Split": Array [
                "/",
                Object {
                  "Fn::Select": Array [
                    5,
                    Object {
                      "Fn::Split": Array [
                        ":",
                        Object {
                          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
        "MajorEngineVersion": "7",
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "ElastiCacheRedisSecuritygGroup20195709",
              "GroupId",
            ],
          },
        ],
        "ServerlessCacheName": "sample01-dev01-ab-RedisServerless",
        "SubnetIds": Array [
          "p-12345",
          "p-67890",
        ],
      },
      "Type": "AWS::ElastiCache::ServerlessCache",
    },
    "ElastiCacheRedisServerlessSSMRedisEndPoint83E14A29": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/redis/endpoint",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "ElastiCacheRedisServerlessGEVANNIserviceElastiCacheRedisServerlessE3AF238E",
            "Endpoint.Address",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ElastiCacheRedisSubnetGroup": Object {
      "Properties": Object {
        "CacheSubnetGroupName": "GEVANNI-service-ElastiCacheRedis-Subnetgroup",
        "Description": "for redis",
        "SubnetIds": Array [
          "p-12345",
          "p-67890",
        ],
      },
      "Type": "AWS::ElastiCache::SubnetGroup",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 2`] = `
Object {
  "Outputs": Object {
    "ExportsOutputRefimportAcmServiceTokenParameter0F18C120": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportAcmServiceTokenParameter0F18C120",
      },
      "Value": Object {
        "Ref": "importAcmServiceTokenParameter",
      },
    },
    "ExportsOutputRefimportAlarmTopicArnParameterE7221DC7": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
      },
      "Value": Object {
        "Ref": "importAlarmTopicArnParameter",
      },
    },
    "ExportsOutputRefimportAliasServiceTokenParameter23FE9DAA": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportAliasServiceTokenParameter23FE9DAA",
      },
      "Value": Object {
        "Ref": "importAliasServiceTokenParameter",
      },
    },
    "ExportsOutputRefimportAppKeyArnParameterD7B87911": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
      },
      "Value": Object {
        "Ref": "importAppKeyArnParameter",
      },
    },
    "ExportsOutputRefimportFluentbitImageUriParameterD37471DB": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportFluentbitImageUriParameterD37471DB",
      },
      "Value": Object {
        "Ref": "importFluentbitImageUriParameter",
      },
    },
    "ExportsOutputRefimportNamespaceArnParameter5DBCE7D2": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceArnParameter5DBCE7D2",
      },
      "Value": Object {
        "Ref": "importNamespaceArnParameter",
      },
    },
    "ExportsOutputRefimportNamespaceIdParameter58DE8EFB": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceIdParameter58DE8EFB",
      },
      "Value": Object {
        "Ref": "importNamespaceIdParameter",
      },
    },
    "ExportsOutputRefimportNamespaceNameParameter45864180": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceNameParameter45864180",
      },
      "Value": Object {
        "Ref": "importNamespaceNameParameter",
      },
    },
    "ExportsOutputRefimportNewRelicLayerParameter4A54F195": Object {
      "Export": Object {
        "Name": "GEVANNI-common-ImportResources:ExportsOutputRefimportNewRelicLayerParameter4A54F195",
      },
      "Value": Object {
        "Ref": "importNewRelicLayerParameter",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAcmFunctionlambdaRoleArnParameter": Object {
      "Default": "/GEVANNI-common/AcmFunctionLambdaRoleArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAcmServiceTokenParameter": Object {
      "Default": "/GEVANNI-common/AcmServiceToken",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAlarmTopicArnParameter": Object {
      "Default": "/GEVANNI-common/alarmTopicArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAliasServiceTokenParameter": Object {
      "Default": "/GEVANNI-common/AliasServiceToken",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAppKeyArnParameter": Object {
      "Default": "/GEVANNI-common/appKeyArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importBatchMasterBucketParameter": Object {
      "Default": "/GEVANNI-common/BatchMasterBucketName",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importConfMasterBucketParameter": Object {
      "Default": "/GEVANNI-common/ConfMasterBucketName",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importFluentbitImageUriParameter": Object {
      "Default": "/GEVANNI-common/fluentbit/ecr/repository-uri",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importNamespaceArnParameter": Object {
      "Default": "/GEVANNI-common/servicediscovery/NamespaceArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importNamespaceIdParameter": Object {
      "Default": "/GEVANNI-common/servicediscovery/NamespaceId",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importNamespaceNameParameter": Object {
      "Default": "/GEVANNI-common/servicediscovery/NamespaceName",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importNewRelicLayerParameter": Object {
      "Default": "/GEVANNI-common/NewRelicLayerArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importOidcProviderArnParameter": Object {
      "Default": "/GEVANNI-common/oidcProviderArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 3`] = `
Object {
  "Outputs": Object {
    "ECSAppEcsAppFrontAppEcsTaskRoleEcsAppServiceEcsTaskRoleArnFCE092DD": Object {
      "Export": Object {
        "Name": "GEVANNI-service-EcsApp-Service-ecs-task-role-arn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsAppFrontAppEcsTaskRoleA963C904",
          "Arn",
        ],
      },
    },
    "ECSAppEcsBackendBackAppEcsTaskRoleEcsBackendServiceEcsTaskRoleArn3358B96B": Object {
      "Export": Object {
        "Name": "GEVANNI-service-EcsBackend-Service-ecs-task-role-arn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsBackendBackAppEcsTaskRoleD052B233",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppBastionECSAPPSGEAB03032GroupId846779B4": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppBastionECSAPPSGEAB03032GroupId846779B4",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppBastionECSAPPSGEAB03032",
          "GroupId",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AEArn1BD4E09F": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AEArn1BD4E09F",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AE",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppEcsAppFrontAppEcsResourcesSg52129FB0GroupId9ADCF5E9": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsAppFrontAppEcsResourcesSg52129FB0GroupId9ADCF5E9",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0",
          "GroupId",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0ArnC627144E": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0ArnC627144E",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27GroupId7DECC85F": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27GroupId7DECC85F",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
          "GroupId",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45Arn1EE6453D": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45Arn1EE6453D",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
          "Arn",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppFrontAlb124B346FCanonicalHostedZoneID903D1DDC": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FCanonicalHostedZoneID903D1DDC",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppFrontAlb124B346F",
          "CanonicalHostedZoneID",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppFrontAlb124B346FDNSName46A20BE3": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FDNSName46A20BE3",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppFrontAlb124B346F",
          "DNSName",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppFrontAlb124B346F",
          "LoadBalancerFullName",
        ],
      },
    },
    "ExportsOutputFnGetAttECSAppFrontAlbEcsAppTGTargetGroupE49C9484TargetGroupName917B386E": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlbEcsAppTGTargetGroupE49C9484TargetGroupName917B386E",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "ECSAppFrontAlbEcsAppTGTargetGroupE49C9484",
          "TargetGroupName",
        ],
      },
    },
    "ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
      },
      "Value": Object {
        "Ref": "ECSAppECSCommonCluster9D6395A7",
      },
    },
    "ExportsOutputRefECSAppEcsAppFrontAppEcsResourcesSecretA041C38CFFA5248B": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputRefECSAppEcsAppFrontAppEcsResourcesSecretA041C38CFFA5248B",
      },
      "Value": Object {
        "Ref": "ECSAppEcsAppFrontAppEcsResourcesSecretA041C38C",
      },
    },
    "ExportsOutputRefECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C2CB0BDCE": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputRefECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C2CB0BDCE",
      },
      "Value": Object {
        "Ref": "ECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C",
      },
    },
    "ExportsOutputRefECSAppFrontAlb124B346FE426DA64": Object {
      "Export": Object {
        "Name": "GEVANNI-service-ECS:ExportsOutputRefECSAppFrontAlb124B346FE426DA64",
      },
      "Value": Object {
        "Ref": "ECSAppFrontAlb124B346F",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "SsmParameterValuesample02dev01cdEcsAppEcsSGIdC96584B6F00A464EAD1953AFF4B05118Parameter": Object {
      "Default": "/sample02-dev01-cd/EcsApp/EcsSGId",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importAcmArnParameter": Object {
      "Default": "/GEVANNI-service/AcmArn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importBastionEcrRepositoryNameParameter": Object {
      "Default": "/GEVANNI-common/bastion/ecr/repository-name",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importBastionEcsTaskExecutionRoleArnParameter": Object {
      "Default": "/GEVANNI-common/bastion/ecs/task-execution-role-arn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "BatchSecurityGroup77EC865F": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ECS/BatchSecurityGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691": Object {
      "DependsOn": Array [
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
      ],
      "Properties": Object {
        "Code": Object {
          "ZipFile": "import boto3  # type: ignore
import json
import logging
import urllib.request

s3 = boto3.client(\\"s3\\")

EVENTBRIDGE_CONFIGURATION = 'EventBridgeConfiguration'
CONFIGURATION_TYPES = [\\"TopicConfigurations\\", \\"QueueConfigurations\\", \\"LambdaFunctionConfigurations\\"]

def handler(event: dict, context):
  response_status = \\"SUCCESS\\"
  error_message = \\"\\"
  try:
    props = event[\\"ResourceProperties\\"]
    notification_configuration = props[\\"NotificationConfiguration\\"]
    managed = props.get('Managed', 'true').lower() == 'true'
    skipDestinationValidation = props.get('SkipDestinationValidation', 'false').lower() == 'true'
    stack_id = event['StackId']
    old = event.get(\\"OldResourceProperties\\", {}).get(\\"NotificationConfiguration\\", {})
    if managed:
      config = handle_managed(event[\\"RequestType\\"], notification_configuration)
    else:
      config = handle_unmanaged(props[\\"BucketName\\"], stack_id, event[\\"RequestType\\"], notification_configuration, old)
    s3.put_bucket_notification_configuration(Bucket=props[\\"BucketName\\"], NotificationConfiguration=config, SkipDestinationValidation=skipDestinationValidation)
  except Exception as e:
    logging.exception(\\"Failed to put bucket notification configuration\\")
    response_status = \\"FAILED\\"
    error_message = f\\"Error: {str(e)}. \\"
  finally:
    submit_response(event, context, response_status, error_message)

def handle_managed(request_type, notification_configuration):
  if request_type == 'Delete':
    return {}
  return notification_configuration

def handle_unmanaged(bucket, stack_id, request_type, notification_configuration, old):
  def get_id(n):
    n['Id'] = ''
    sorted_notifications = sort_filter_rules(n)
    strToHash=json.dumps(sorted_notifications, sort_keys=True).replace('\\"Name\\": \\"prefix\\"', '\\"Name\\": \\"Prefix\\"').replace('\\"Name\\": \\"suffix\\"', '\\"Name\\": \\"Suffix\\"')
    return f\\"{stack_id}-{hash(strToHash)}\\"
  def with_id(n):
    n['Id'] = get_id(n)
    return n

  external_notifications = {}
  existing_notifications = s3.get_bucket_notification_configuration(Bucket=bucket)
  for t in CONFIGURATION_TYPES:
    if request_type == 'Update':
        old_incoming_ids = [get_id(n) for n in old.get(t, [])]
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not get_id(n) in old_incoming_ids]      
    elif request_type == 'Delete':
        external_notifications[t] = [n for n in existing_notifications.get(t, []) if not n['Id'].startswith(f\\"{stack_id}-\\")]
    elif request_type == 'Create':
        external_notifications[t] = [n for n in existing_notifications.get(t, [])]
  if EVENTBRIDGE_CONFIGURATION in existing_notifications:
    external_notifications[EVENTBRIDGE_CONFIGURATION] = existing_notifications[EVENTBRIDGE_CONFIGURATION]

  if request_type == 'Delete':
    return external_notifications

  notifications = {}
  for t in CONFIGURATION_TYPES:
    external = external_notifications.get(t, [])
    incoming = [with_id(n) for n in notification_configuration.get(t, [])]
    notifications[t] = external + incoming

  if EVENTBRIDGE_CONFIGURATION in notification_configuration:
    notifications[EVENTBRIDGE_CONFIGURATION] = notification_configuration[EVENTBRIDGE_CONFIGURATION]
  elif EVENTBRIDGE_CONFIGURATION in external_notifications:
    notifications[EVENTBRIDGE_CONFIGURATION] = external_notifications[EVENTBRIDGE_CONFIGURATION]

  return notifications

def submit_response(event: dict, context, response_status: str, error_message: str):
  response_body = json.dumps(
    {
      \\"Status\\": response_status,
      \\"Reason\\": f\\"{error_message}See the details in CloudWatch Log Stream: {context.log_stream_name}\\",
      \\"PhysicalResourceId\\": event.get(\\"PhysicalResourceId\\") or event[\\"LogicalResourceId\\"],
      \\"StackId\\": event[\\"StackId\\"],
      \\"RequestId\\": event[\\"RequestId\\"],
      \\"LogicalResourceId\\": event[\\"LogicalResourceId\\"],
      \\"NoEcho\\": False,
    }
  ).encode(\\"utf-8\\")
  headers = {\\"content-type\\": \\"\\", \\"content-length\\": str(len(response_body))}
  try:
    req = urllib.request.Request(url=event[\\"ResponseURL\\"], headers=headers, data=response_body, method=\\"PUT\\")
    with urllib.request.urlopen(req) as response:
      print(response.read().decode(\\"utf-8\\"))
    print(\\"Status code: \\" + response.reason)
  except Exception as e:
      print(\\"send(..) failed executing request.urlopen(..): \\" + str(e))

def sort_filter_rules(json_obj):
  if not isinstance(json_obj, dict):
      return json_obj
  for key, value in json_obj.items():
      if isinstance(value, dict):
          json_obj[key] = sort_filter_rules(value)
      elif isinstance(value, list):
          json_obj[key] = [sort_filter_rules(item) for item in value]
  if \\"Filter\\" in json_obj and \\"Key\\" in json_obj[\\"Filter\\"] and \\"FilterRules\\" in json_obj[\\"Filter\\"][\\"Key\\"]:
      filter_rules = json_obj[\\"Filter\\"][\\"Key\\"][\\"FilterRules\\"]
      sorted_filter_rules = sorted(filter_rules, key=lambda x: x[\\"Name\\"])
      json_obj[\\"Filter\\"][\\"Key\\"][\\"FilterRules\\"] = sorted_filter_rules
  return json_obj",
        },
        "Description": "AWS CloudFormation handler for \\"Custom::S3BucketNotifications\\" resources (@aws-cdk/aws-s3)",
        "Handler": "index.handler",
        "Role": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
            "Arn",
          ],
        },
        "Runtime": "python3.11",
        "Timeout": 300,
      },
      "Type": "AWS::Lambda::Function",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:PutBucketNotification",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleDefaultPolicy2CF63D36",
        "Roles": Array [
          Object {
            "Ref": "BucketNotificationsHandler050a0587b7544547bf325f094a3db834RoleB6FB88EC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppAppLogDatFirehoseCwLogGroup850A6FD8": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/firehose/GEVANNI-service/applogs",
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppAppLogDatFirehoseCwLogStream2DB8E55E": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": Object {
          "Ref": "ECSAppAppLogDatFirehoseCwLogGroup850A6FD8",
        },
      },
      "Type": "AWS::Logs::LogStream",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppAppLogDatFirehoseFirehoseLogBucketAutoDeleteObjectsCustomResource4BD98CB1": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppAppLogDatFirehoseFirehoseLogBucketPolicyD9C0E45F",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppAppLogDatFirehoseFirehoseLogBucketPolicyD9C0E45F": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppAppLogDatFirehoseFirehoseRole75D07A5F": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppAppLogDatFirehoseFirehoseRoleDefaultPolicyB5AE02AF": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutObject",
                "s3:GetBucketLocation",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "secretsmanager:GetSecretValue",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
              },
            },
            Object {
              "Action": "logs:PutLogEvents",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "ECSAppAppLogDatFirehoseCwLogGroup850A6FD8",
                        "Arn",
                      ],
                    },
                    ":*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppAppLogDatFirehoseFirehoseRoleDefaultPolicyB5AE02AF",
        "Roles": Array [
          Object {
            "Ref": "ECSAppAppLogDatFirehoseFirehoseRole75D07A5F",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppAppLogDatFirehoseFirehoseStream12B655F1": Object {
      "Properties": Object {
        "DeliveryStreamName": "GEVANNI-service-AppLog-Stream",
        "DeliveryStreamType": "DirectPut",
        "HttpEndpointDestinationConfiguration": Object {
          "BufferingHints": Object {
            "IntervalInSeconds": 60,
            "SizeInMBs": 1,
          },
          "CloudWatchLoggingOptions": Object {
            "Enabled": true,
            "LogGroupName": Object {
              "Ref": "ECSAppAppLogDatFirehoseCwLogGroup850A6FD8",
            },
            "LogStreamName": Object {
              "Ref": "ECSAppAppLogDatFirehoseCwLogStream2DB8E55E",
            },
          },
          "EndpointConfiguration": Object {
            "Name": "New Relic",
            "Url": "https://aws-api.newrelic.com/firehose/v1",
          },
          "RequestConfiguration": Object {
            "ContentEncoding": "GZIP",
          },
          "RetryOptions": Object {
            "DurationInSeconds": 60,
          },
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "ECSAppAppLogDatFirehoseFirehoseRole75D07A5F",
              "Arn",
            ],
          },
          "S3BackupMode": "AllData",
          "S3Configuration": Object {
            "BucketARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppAppLogDatFirehoseFirehoseLogBucket84BF433A",
                "Arn",
              ],
            },
            "CompressionFormat": "GZIP",
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppAppLogDatFirehoseFirehoseRole75D07A5F",
                "Arn",
              ],
            },
          },
          "SecretsManagerConfiguration": Object {
            "Enabled": true,
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppAppLogDatFirehoseFirehoseRole75D07A5F",
                "Arn",
              ],
            },
            "SecretARN": Object {
              "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
            },
          },
        },
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "ECSAppBastionECSAPPGEVANNIserviceBastionEcsTaskDefNameParameter227F325B": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/bastion/ecs/task-def-name",
        "Type": "String",
        "Value": "GEVANNIserviceECSECSAppBastionECSAPPTaskDefBF0E5A05",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppBastionECSAPPGEVANNIserviceBastionSgIdParameter20C936FC": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/bastion/sg-id",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "ECSAppBastionECSAPPSGEAB03032",
            "GroupId",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppBastionECSAPPLogGroupB2AE2196": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppBastionECSAPPSGEAB03032": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ECS/ECSApp/Bastion-ECSAPP/SG",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ECSAppBastionECSAPPTaskDef94347AA2": Object {
      "Properties": Object {
        "ContainerDefinitions": Array [
          Object {
            "Essential": true,
            "Image": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "************.dkr.ecr.ap-northeast-1.",
                  Object {
                    "Ref": "AWS::URLSuffix",
                  },
                  "/",
                  Object {
                    "Ref": "importBastionEcrRepositoryNameParameter",
                  },
                  ":bastionimage",
                ],
              ],
            },
            "LogConfiguration": Object {
              "LogDriver": "awslogs",
              "Options": Object {
                "awslogs-group": Object {
                  "Ref": "ECSAppBastionECSAPPLogGroupB2AE2196",
                },
                "awslogs-region": "ap-northeast-1",
                "awslogs-stream-prefix": "Bastion-ECSAPP-ECSApp-",
              },
            },
            "Name": "Bastion-ECSAPP",
          },
        ],
        "Cpu": "256",
        "ExecutionRoleArn": Object {
          "Ref": "importBastionEcsTaskExecutionRoleArnParameter",
        },
        "Family": "GEVANNIserviceECSECSAppBastionECSAPPTaskDefBF0E5A05",
        "Memory": "512",
        "NetworkMode": "awsvpc",
        "RequiresCompatibilities": Array [
          "FARGATE",
        ],
        "TaskRoleArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AE",
            "Arn",
          ],
        },
      },
      "Type": "AWS::ECS::TaskDefinition",
    },
    "ECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AE": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppBastionECSAPPTaskServiceTaskRoleDefaultPolicy68ADF241": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ssmmessages:CreateControlChannel",
                "ssmmessages:CreateDataChannel",
                "ssmmessages:OpenControlChannel",
                "ssmmessages:OpenDataChannel",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppBastionECSAPPTaskServiceTaskRoleDefaultPolicy68ADF241",
        "Roles": Array [
          Object {
            "Ref": "ECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AE",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppECSCommonCluster3A9B923D": Object {
      "Properties": Object {
        "CapacityProviders": Array [
          "FARGATE",
          "FARGATE_SPOT",
        ],
        "Cluster": Object {
          "Ref": "ECSAppECSCommonCluster9D6395A7",
        },
        "DefaultCapacityProviderStrategy": Array [],
      },
      "Type": "AWS::ECS::ClusterCapacityProviderAssociations",
    },
    "ECSAppECSCommonCluster9D6395A7": Object {
      "Properties": Object {
        "ClusterSettings": Array [
          Object {
            "Name": "containerInsights",
            "Value": "enabled",
          },
        ],
      },
      "Type": "AWS::ECS::Cluster",
    },
    "ECSAppECSCommonECSServiceActionEventRule59270CC1": Object {
      "Properties": Object {
        "Description": "CloudWatch Event Rule to send notification on ECS Service action events.",
        "EventPattern": Object {
          "detail": Object {
            "clusterArn": Array [
              Object {
                "Fn::GetAtt": Array [
                  "ECSAppECSCommonCluster9D6395A7",
                  "Arn",
                ],
              },
            ],
            "eventType": Array [
              "WARN",
              "ERROR",
            ],
          },
          "detail-type": Array [
            "ECS Service Action",
          ],
          "source": Array [
            "aws.ecs",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppECSCommonECSServiceDeploymentEventRuleBEEAB187": Object {
      "Properties": Object {
        "Description": "CloudWatch Event Rule to send notification on ECS Service deployment events.",
        "EventPattern": Object {
          "detail": Object {
            "clusterArn": Array [
              Object {
                "Fn::GetAtt": Array [
                  "ECSAppECSCommonCluster9D6395A7",
                  "Arn",
                ],
              },
            ],
            "eventType": Array [
              "WARN",
              "ERROR",
            ],
          },
          "detail-type": Array [
            "ECS Deployment State Change",
          ],
          "source": Array [
            "aws.ecs",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppECSCommonSSMBackPortNumberDAC497CA": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/PortNumber",
        "Type": "String",
        "Value": "8080",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppECSCommonSSMDnsRecordName1AAEF065": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/DnsRecordName",
        "Type": "String",
        "Value": Object {
          "Fn::Join": Array [
            "",
            Array [
              Object {
                "Fn::GetAtt": Array [
                  "ECSAppECSCommoncloudMapService3E00FCE0",
                  "Name",
                ],
              },
              ".",
              Object {
                "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceNameParameter45864180",
              },
            ],
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppECSCommonSSMEcsClusterName6140311F": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/ECSClusterName",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppECSCommonCluster9D6395A7",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppECSCommoncloudMapService3E00FCE0": Object {
      "Properties": Object {
        "DnsConfig": Object {
          "DnsRecords": Array [
            Object {
              "TTL": 60,
              "Type": "A",
            },
          ],
          "NamespaceId": Object {
            "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceIdParameter58DE8EFB",
          },
          "RoutingPolicy": "MULTIVALUE",
        },
        "Name": "backend-sd-sample01-dev01-ab",
        "NamespaceId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceIdParameter58DE8EFB",
        },
      },
      "Type": "AWS::ServiceDiscovery::Service",
    },
    "ECSAppEcsAppFrontAppEcsResourcesLogF07550D2": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppEcsResourcesLogforSCBB7306E9": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 1827,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "EmptyOnDelete": true,
        "ImageScanningConfiguration": Object {
          "ScanOnPush": true,
        },
        "LifecyclePolicy": Object {
          "LifecyclePolicyText": "{\\"rules\\":[{\\"rulePriority\\":1,\\"description\\":\\"Keep last 10 images\\",\\"selection\\":{\\"tagStatus\\":\\"any\\",\\"countType\\":\\"imageCountMoreThan\\",\\"countNumber\\":10},\\"action\\":{\\"type\\":\\"expire\\"}}]}",
        },
      },
      "Type": "AWS::ECR::Repository",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppEcsResourcesRepoImageScanComplete31C07476": Object {
      "Properties": Object {
        "EventPattern": Object {
          "detail": Object {
            "repository-name": Array [
              Object {
                "Ref": "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D",
              },
            ],
            "scan-status": Array [
              "COMPLETE",
            ],
          },
          "detail-type": Array [
            "ECR Image Scan",
          ],
          "source": Array [
            "aws.ecr",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSSMEcrRepoCE45ED3E": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsApp/EcrRepo",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSSMEcsSGIdFD066CD1": Object {
      "Properties": Object {
        "Name": "/sample01-dev01-ab/EcsApp/EcsSGId",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0",
            "GroupId",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSSMSecretArn3683404A": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsApp/SecretArn",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsAppFrontAppEcsResourcesSecretA041C38C",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSecretA041C38C": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Name": "GEVANNI-service/EcsApp",
        "SecretString": "{}",
      },
      "Type": "AWS::SecretsManager::Secret",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ECS/ECSApp/EcsApp-FrontApp-Ecs-Resources/Sg",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ECSAppEcsAppFrontAppEcsResourcesSgfromGEVANNIserviceECSECSAppFrontAlbSgAlbC707F5CE8025727065": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppFrontAlbSgAlbC707F5CE:80",
        "FromPort": 80,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppFrontAlbSgAlb5FBE35E9",
            "GroupId",
          ],
        },
        "ToPort": 80,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ECSAppEcsAppFrontAppEcsTaskRoleA963C904": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Description": "GEVANNI-service EcsApp-Service Task Role",
        "RoleName": "GEVANNI-service-EcsApp-Service-taskrole",
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppEcsTaskRoleDefaultPolicy2BB10A7F": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ssmmessages:CreateControlChannel",
                "ssmmessages:CreateDataChannel",
                "ssmmessages:OpenControlChannel",
                "ssmmessages:OpenDataChannel",
                "logs:CreateLogGroup",
                "logs:DescribeLogStreams",
                "logs:DescribeLogGroups",
                "logs:PutLogEvents",
                "firehose:PutRecord",
                "firehose:PutRecordBatch",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppEcsTaskRoleDefaultPolicy2BB10A7F",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppEcsTaskRoleA963C904",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppEcsTaskRoleEcsTaskExecutionRole5BB7D322": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
              ],
            ],
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "logs:CreateLogGroup",
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "createLogs",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "secretsmanager:GetSecretValue",
                  "Effect": "Allow",
                  "Resource": Object {
                    "Ref": "ECSAppEcsAppFrontAppEcsResourcesSecretA041C38C",
                  },
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "getSecret",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipeline183937E4": Object {
      "DependsOn": Array [
        "ECSAppEcsAppFrontAppPipelineRoleDefaultPolicy52E69197",
        "ECSAppEcsAppFrontAppPipelineRole708EFBCB",
      ],
      "Properties": Object {
        "ArtifactStore": Object {
          "EncryptionKey": Object {
            "Id": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                "Arn",
              ],
            },
            "Type": "KMS",
          },
          "Location": Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
          },
          "Type": "S3",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineRole708EFBCB",
            "Arn",
          ],
        },
        "Stages": Array [
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Source",
                  "Owner": "AWS",
                  "Provider": "S3",
                  "Version": "1",
                },
                "Configuration": Object {
                  "PollForSourceChanges": false,
                  "S3Bucket": Object {
                    "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                  },
                  "S3ObjectKey": "image_front.zip",
                },
                "Name": "SourceBucket",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Source_SourceBucket",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleD9E59FD0",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Source",
          },
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Build",
                  "Owner": "AWS",
                  "Provider": "CodeBuild",
                  "Version": "1",
                },
                "Configuration": Object {
                  "ProjectName": Object {
                    "Ref": "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79",
                  },
                },
                "InputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Source_SourceBucket",
                  },
                ],
                "Name": "DeployProject",
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRole13D6B23E",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Deploy",
          },
        ],
      },
      "Type": "AWS::CodePipeline::Pipeline",
    },
    "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyAlias6D602C65": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AliasName": "alias/codepipeline-gevanniserviceecsecsappecsappfrontapppipeline438cc05a",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineArtifactsBucketPolicy554C25DE": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRole13D6B23E": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRoleDefaultPolicy93B2D5EE": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "codebuild:BatchGetBuilds",
                "codebuild:StartBuild",
                "codebuild:StopBuild",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRoleDefaultPolicy93B2D5EE",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRole13D6B23E",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79": Object {
      "Properties": Object {
        "Artifacts": Object {
          "Type": "CODEPIPELINE",
        },
        "Cache": Object {
          "Type": "NO_CACHE",
        },
        "EncryptionKey": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
            "Arn",
          ],
        },
        "Environment": Object {
          "ComputeType": "BUILD_GENERAL1_SMALL",
          "EnvironmentVariables": Array [
            Object {
              "Name": "ECS_CLUSTER",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppECSCommonCluster9D6395A7",
              },
            },
            Object {
              "Name": "ECS_SERVICE",
              "Type": "PLAINTEXT",
              "Value": "EcsApp-Service",
            },
            Object {
              "Name": "TARGET_GROUP_ARN",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppFrontAlbEcsAppTGTargetGroupE49C9484",
              },
            },
            Object {
              "Name": "SECURITY_GROUP",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0",
                  "GroupId",
                ],
              },
            },
            Object {
              "Name": "SUBNET_1",
              "Type": "PLAINTEXT",
              "Value": "p-12345",
            },
            Object {
              "Name": "SUBNET_2",
              "Type": "PLAINTEXT",
              "Value": "p-67890",
            },
            Object {
              "Name": "SUBNET_3",
              "Type": "PLAINTEXT",
              "Value": "",
            },
            Object {
              "Name": "FIRELENS_IMAGE_NAME",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportFluentbitImageUriParameterD37471DB",
                    },
                    ":fluentbitimageruby",
                  ],
                ],
              },
            },
            Object {
              "Name": "STREAM_NAME",
              "Type": "PLAINTEXT",
              "Value": "GEVANNI-service-AppLog-Stream",
            },
            Object {
              "Name": "LOG_GROUP_FIRELENS",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsAppFrontAppEcsResourcesLogF07550D2",
              },
            },
            Object {
              "Name": "LOG_GROUP_SERVICE_CONNECT",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsAppFrontAppEcsResourcesLogforSCBB7306E9",
              },
            },
            Object {
              "Name": "EXECUTION_ROLE_ARN",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppEcsTaskRoleEcsTaskExecutionRole5BB7D322",
                  "Arn",
                ],
              },
            },
            Object {
              "Name": "TASK_ROLE",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppEcsTaskRoleA963C904",
                  "Arn",
                ],
              },
            },
            Object {
              "Name": "FAMILY",
              "Type": "PLAINTEXT",
              "Value": "GEVANNI-service-EcsApp-Taskdef",
            },
            Object {
              "Name": "NAMESPACE",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceArnParameter5DBCE7D2",
              },
            },
            Object {
              "Name": "CLOUDMAP_SERVICE_ARN",
              "Type": "PLAINTEXT",
              "Value": "",
            },
            Object {
              "Name": "DISCOVERY_NAME",
              "Type": "PLAINTEXT",
              "Value": "",
            },
            Object {
              "Name": "PORT_NUMBER",
              "Type": "PLAINTEXT",
              "Value": "80",
            },
            Object {
              "Name": "PARAMETER_NAME",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsAppFrontAppPipelineecrTagFCA4A81B",
              },
            },
            Object {
              "Name": "IMAGE_URI",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::Select": Array [
                        4,
                        Object {
                          "Fn::Split": Array [
                            ":",
                            Object {
                              "Fn::GetAtt": Array [
                                "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D",
                                "Arn",
                              ],
                            },
                          ],
                        },
                      ],
                    },
                    ".dkr.ecr.",
                    Object {
                      "Fn::Select": Array [
                        3,
                        Object {
                          "Fn::Split": Array [
                            ":",
                            Object {
                              "Fn::GetAtt": Array [
                                "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D",
                                "Arn",
                              ],
                            },
                          ],
                        },
                      ],
                    },
                    ".",
                    Object {
                      "Ref": "AWS::URLSuffix",
                    },
                    "/",
                    Object {
                      "Ref": "ECSAppEcsAppFrontAppEcsResourcesRepo7F69438D",
                    },
                  ],
                ],
              },
            },
            Object {
              "Name": "ECR_TAG",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsAppFrontAppPipelineecrTagFCA4A81B",
              },
            },
            Object {
              "Name": "CM_BILLING_GROUP_TAG_KEY",
              "Type": "PLAINTEXT",
              "Value": " ",
            },
            Object {
              "Name": "CM_BILLING_GROUP_TAG",
              "Type": "PLAINTEXT",
              "Value": " ",
            },
            Object {
              "Name": "PROPAGATE_TAG",
              "Type": "PLAINTEXT",
              "Value": "NONE",
            },
          ],
          "Image": "aws/codebuild/amazonlinux2-x86_64-standard:2.0",
          "ImagePullCredentialsType": "CODEBUILD",
          "PrivilegedMode": false,
          "Type": "LINUX_CONTAINER",
        },
        "LogsConfig": Object {
          "CloudWatchLogs": Object {
            "GroupName": Object {
              "Ref": "ECSAppEcsAppFrontAppPipelineLog5B95FF8E",
            },
            "Status": "ENABLED",
          },
        },
        "ServiceRole": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineDeployProjectRole615E8B5F",
            "Arn",
          ],
        },
        "Source": Object {
          "BuildSpec": "{
  \\"version\\": \\"0.2\\",
  \\"phases\\": {
    \\"pre_build\\": {
      \\"commands\\": [
        \\"echo \\\\\\"The latest version of ecspresso is (It only shows up the log) :\\\\\\"\\",
        \\"curl -s https://api.github.com/repos/kayac/ecspresso/releases/latest | jq .tag_name\\",
        \\"curl -sL -o ecspresso-v2.3.5-linux-amd64.tar.gz https://github.com/kayac/ecspresso/releases/download/v2.3.5/ecspresso_2.3.5_linux_amd64.tar.gz\\",
        \\"tar -zxf ecspresso-v2.3.5-linux-amd64.tar.gz\\",
        \\"sudo install ecspresso /usr/local/bin/ecspresso\\",
        \\"ecspresso version\\"
      ]
    },
    \\"build\\": {
      \\"commands\\": [
        \\"export IMAGE_TAG=$(aws ssm get-parameter --name \${PARAMETER_NAME} --query \\\\\\"Parameter.Value\\\\\\" --output text)\\",
        \\"export IMAGE1_NAME=\${IMAGE_URI}:\${IMAGE_TAG}\\",
        \\"ls -lR\\",
        \\"ecspresso deploy --config ecspresso.yml\\",
        \\"python ./config.py\\"
      ]
    }
  }
}",
          "Type": "CODEPIPELINE",
        },
      },
      "Type": "AWS::CodeBuild::Project",
    },
    "ECSAppEcsAppFrontAppPipelineDeployProjectRole615E8B5F": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codebuild.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineDeployProjectRoleDefaultPolicy772B92AF": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineLog5B95FF8E",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79",
                      },
                    ],
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79",
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "codebuild:CreateReportGroup",
                "codebuild:CreateReport",
                "codebuild:UpdateReport",
                "codebuild:BatchPutTestCases",
                "codebuild:BatchPutCodeCoverages",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codebuild:ap-northeast-1:************:report-group/",
                    Object {
                      "Ref": "ECSAppEcsAppFrontAppPipelineDeployProject8DDC0F79",
                    },
                    "-*",
                  ],
                ],
              },
            },
            Object {
              "Action": Array [
                "ecs:RegisterTaskDefinition",
                "ecs:ListTaskDefinitions",
                "ecs:DescribeTaskDefinition",
                "application-autoscaling:DescribeScalableTargets",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "ecs:TagResource",
                "application-autoscaling:RegisterScalableTarget",
                "application-autoscaling:DeregisterScalableTarget",
                "application-autoscaling:PutScalingPolicy",
                "application-autoscaling:DeleteScalingPolicy",
                "application-autoscaling:DescribeScalingPolicies",
                "elasticloadbalancing:ModifyTargetGroup",
                "servicediscovery:GetNamespace",
                "iam:CreateServiceLinkedRole",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "ecs:CreateService",
                "ecs:UpdateService",
                "ecs:DescribeServices",
                "ssm:GetParameter",
                "secretsmanager:GetSecretValue",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:aws:ecs:ap-northeast-1:************:service/",
                      Object {
                        "Ref": "ECSAppECSCommonCluster9D6395A7",
                      },
                      "/EcsApp-Service",
                    ],
                  ],
                },
                "arn:aws:ssm:ap-northeast-1:************:parameter:parameter/GEVANNI-common/*",
                "arn:aws:ssm:ap-northeast-1:************:parameter/GEVANNI-service/*",
                Object {
                  "Ref": "ECSAppEcsAppFrontAppEcsResourcesSecretA041C38C",
                },
              ],
            },
            Object {
              "Action": "elasticloadbalancing:ModifyTargetGroup",
              "Effect": "Allow",
              "Resource": Object {
                "Ref": "ECSAppFrontAlbEcsAppTGTargetGroupE49C9484",
              },
            },
            Object {
              "Action": "iam:PassRole",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppEcsTaskRoleEcsTaskExecutionRole5BB7D322",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppEcsTaskRoleA963C904",
                    "Arn",
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineDeployProjectRoleDefaultPolicy772B92AF",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineDeployProjectRole615E8B5F",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineEventsRoleDefaultPolicyAC19EDB9": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "codepipeline:StartPipelineExecution",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codepipeline:ap-northeast-1:************:",
                    Object {
                      "Ref": "ECSAppEcsAppFrontAppPipeline183937E4",
                    },
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineEventsRoleDefaultPolicyAC19EDB9",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineEventsRoleFF68B870",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineEventsRoleFF68B870": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "events.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineLog5B95FF8E": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 30,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseCfnSubscriptionFilterB6249A8E": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseStreamBFAEF8ED",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelineLog5B95FF8E",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppPipelineMetricFirehoseToKinesisFirehoseRole01052B52",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogGroup43DE47EB": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/firehose/GEVANNI-service/EcsApp/buildlogs",
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogStreamBBC42DC0": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogGroup43DE47EB",
        },
      },
      "Type": "AWS::Logs::LogStream",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucketAutoDeleteObjectsCustomResourceBDC4057C": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucketPolicy79C5D0C2",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucketPolicy79C5D0C2": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRole3CA43015": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRoleDefaultPolicyC6C30888": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutObject",
                "s3:GetBucketLocation",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "secretsmanager:GetSecretValue",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
              },
            },
            Object {
              "Action": "logs:PutLogEvents",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogGroup43DE47EB",
                        "Arn",
                      ],
                    },
                    ":*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRoleDefaultPolicyC6C30888",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRole3CA43015",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseStreamBFAEF8ED": Object {
      "Properties": Object {
        "DeliveryStreamName": "GEVANNI-service-EcsApp-BuildLog-Stream",
        "DeliveryStreamType": "DirectPut",
        "HttpEndpointDestinationConfiguration": Object {
          "BufferingHints": Object {
            "IntervalInSeconds": 60,
            "SizeInMBs": 1,
          },
          "CloudWatchLoggingOptions": Object {
            "Enabled": true,
            "LogGroupName": Object {
              "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogGroup43DE47EB",
            },
            "LogStreamName": Object {
              "Ref": "ECSAppEcsAppFrontAppPipelineMetricFirehoseCwLogStreamBBC42DC0",
            },
          },
          "EndpointConfiguration": Object {
            "Name": "New Relic",
            "Url": "https://aws-api.newrelic.com/firehose/v1",
          },
          "RequestConfiguration": Object {
            "ContentEncoding": "GZIP",
          },
          "RetryOptions": Object {
            "DurationInSeconds": 60,
          },
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRole3CA43015",
              "Arn",
            ],
          },
          "S3BackupMode": "FailedDataOnly",
          "S3Configuration": Object {
            "BucketARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseLogBucket7062B20F",
                "Arn",
              ],
            },
            "CompressionFormat": "GZIP",
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRole3CA43015",
                "Arn",
              ],
            },
          },
          "SecretsManagerConfiguration": Object {
            "Enabled": true,
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsAppFrontAppPipelineMetricFirehoseFirehoseRole3CA43015",
                "Arn",
              ],
            },
            "SecretARN": Object {
              "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
            },
          },
        },
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "ECSAppEcsAppFrontAppPipelineMetricFirehoseToKinesisFirehoseRole01052B52": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/AmazonKinesisFirehoseFullAccess",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineNotifications486223C6": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codepipeline-pipeline-pipeline-execution-started",
          "codepipeline-pipeline-pipeline-execution-succeeded",
          "codepipeline-pipeline-pipeline-execution-failed",
        ],
        "Name": "viceECSECSAppEcsAppFrontAppPipelinePipelineNotifications3F8D99B8",
        "Resource": Object {
          "Fn::Join": Array [
            "",
            Array [
              "arn:",
              Object {
                "Ref": "AWS::Partition",
              },
              ":codepipeline:ap-northeast-1:************:",
              Object {
                "Ref": "ECSAppEcsAppFrontAppPipeline183937E4",
              },
            ],
          ],
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Fn::ImportValue": "GEVANNI-service-Notifier:ExportsOutputRefGEVANNIserviceAlarmSNSTopicA34366179411277C",
            },
            "TargetType": "SNS",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucketAutoDeleteObjectsCustomResource5DD478E9": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppEcsAppFrontAppPipelinePipelineSourceBucketPolicyA190CB9D",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucketNotificationsC5FC784B": Object {
      "DependsOn": Array [
        "ECSAppEcsAppFrontAppPipelinePipelineSourceBucketPolicyA190CB9D",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
        },
        "Managed": true,
        "NotificationConfiguration": Object {
          "EventBridgeConfiguration": Object {},
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691",
            "Arn",
          ],
        },
        "SkipDestinationValidation": false,
      },
      "Type": "Custom::S3BucketNotifications",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucketPolicyA190CB9D": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsAppFrontAppPipelinePipelineTrigerEventRuleBB9A9A8D": Object {
      "Properties": Object {
        "EventPattern": Object {
          "detail": Object {
            "bucket": Object {
              "name": Array [
                Object {
                  "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                },
              ],
            },
            "object": Object {
              "key": Array [
                "image_front.zip",
              ],
            },
          },
          "detail-type": Array [
            "Object Created",
          ],
          "source": Array [
            "aws.s3",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "arn:",
                  Object {
                    "Ref": "AWS::Partition",
                  },
                  ":codepipeline:ap-northeast-1:************:",
                  Object {
                    "Ref": "ECSAppEcsAppFrontAppPipeline183937E4",
                  },
                ],
              ],
            },
            "Id": "Target0",
            "RoleArn": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsAppFrontAppPipelineEventsRoleFF68B870",
                "Arn",
              ],
            },
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppEcsAppFrontAppPipelineRole708EFBCB": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codepipeline.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineRoleDefaultPolicy52E69197": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleD9E59FD0",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineDeployDeployProjectCodePipelineActionRole13D6B23E",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineRoleDefaultPolicy52E69197",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineRole708EFBCB",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineSSMSourceBucketName6DCC7849": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsApp/SourceBucketName",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleD9E59FD0": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleDefaultPolicy23F9AE7B": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0",
                          "Arn",
                        ],
                      },
                      "/image_front.zip",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsAppFrontAppPipelineArtifactsBucket8D7183C0",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Decrypt",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsAppFrontAppPipelineArtifactsBucketEncryptionKeyA7256398",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleDefaultPolicy23F9AE7B",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsAppFrontAppPipelineSourceSourceBucketCodePipelineActionRoleD9E59FD0",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsAppFrontAppPipelineecrTagFCA4A81B": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsApp/ecrTag",
        "Type": "String",
        "Value": "sample",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsBackendBackAppEcsResourcesLogD49A8E5A": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppEcsResourcesLogforSCE9878013": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 1827,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "EmptyOnDelete": true,
        "ImageScanningConfiguration": Object {
          "ScanOnPush": true,
        },
        "LifecyclePolicy": Object {
          "LifecyclePolicyText": "{\\"rules\\":[{\\"rulePriority\\":1,\\"description\\":\\"Keep last 10 images\\",\\"selection\\":{\\"tagStatus\\":\\"any\\",\\"countType\\":\\"imageCountMoreThan\\",\\"countNumber\\":10},\\"action\\":{\\"type\\":\\"expire\\"}}]}",
        },
      },
      "Type": "AWS::ECR::Repository",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppEcsResourcesRepoImageScanCompleteAD5995A4": Object {
      "Properties": Object {
        "EventPattern": Object {
          "detail": Object {
            "repository-name": Array [
              Object {
                "Ref": "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B",
              },
            ],
            "scan-status": Array [
              "COMPLETE",
            ],
          },
          "detail-type": Array [
            "ECR Image Scan",
          ],
          "source": Array [
            "aws.ecr",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAlarmTopicArnParameterE7221DC7",
            },
            "Id": "Target0",
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSSMEcrRepo5EF0688C": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/EcrRepo",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSSMEcsSGIdD7D0D2DC": Object {
      "Properties": Object {
        "Name": "/sample01-dev01-ab/EcsBackend/EcsSGId",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
            "GroupId",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSSMSecretArn8F5D8EAD": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/SecretArn",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "Name": "GEVANNI-service/EcsBackend",
        "SecretString": "{}",
      },
      "Type": "AWS::SecretsManager::Secret",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ECS/ECSApp/EcsBackend-BackApp-Ecs-Resources/Sg",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSgfromGEVANNIserviceECSBatchSecurityGroupF0B026998080A7863281": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSBatchSecurityGroupF0B02699:8080",
        "FromPort": 8080,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::GetAtt": Array [
            "BatchSecurityGroup77EC865F",
            "GroupId",
          ],
        },
        "ToPort": 8080,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSgfromGEVANNIserviceECSECSAppBastionECSAPPSGCE65230D8080D6570750": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppBastionECSAPPSGCE65230D:8080",
        "FromPort": 8080,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppBastionECSAPPSGEAB03032",
            "GroupId",
          ],
        },
        "ToPort": 8080,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSgfromGEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B80808FE8824D": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B:8080",
        "FromPort": 8080,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsAppFrontAppEcsResourcesSg52129FB0",
            "GroupId",
          ],
        },
        "ToPort": 8080,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ECSAppEcsBackendBackAppEcsResourcesSgfromGEVANNIserviceECSECSAppSGsample02dev01cdB0FD168C8080C0325DA3": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppSGsample02dev01cdB0FD168C:8080",
        "FromPort": 8080,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Ref": "SsmParameterValuesample02dev01cdEcsAppEcsSGIdC96584B6F00A464EAD1953AFF4B05118Parameter",
        },
        "ToPort": 8080,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "ECSAppEcsBackendBackAppEcsTaskRoleD052B233": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Description": "GEVANNI-service EcsBackend-Service Task Role",
        "RoleName": "GEVANNI-service-EcsBackend-Service-taskrole",
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppEcsTaskRoleDefaultPolicyB6158135": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ssmmessages:CreateControlChannel",
                "ssmmessages:CreateDataChannel",
                "ssmmessages:OpenControlChannel",
                "ssmmessages:OpenDataChannel",
                "logs:CreateLogGroup",
                "logs:DescribeLogStreams",
                "logs:DescribeLogGroups",
                "logs:PutLogEvents",
                "firehose:PutRecord",
                "firehose:PutRecordBatch",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppEcsTaskRoleDefaultPolicyB6158135",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppEcsTaskRoleD052B233",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppEcsTaskRoleEcsTaskExecutionRole76623661": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "ecs-tasks.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
              ],
            ],
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "logs:CreateLogGroup",
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "createLogs",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "secretsmanager:GetSecretValue",
                  "Effect": "Allow",
                  "Resource": Object {
                    "Ref": "ECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C",
                  },
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "getSecret",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipeline7C151C88": Object {
      "DependsOn": Array [
        "ECSAppEcsBackendBackAppPipelineRoleDefaultPolicyF19F074E",
        "ECSAppEcsBackendBackAppPipelineRoleAC8D5C2A",
      ],
      "Properties": Object {
        "ArtifactStore": Object {
          "EncryptionKey": Object {
            "Id": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                "Arn",
              ],
            },
            "Type": "KMS",
          },
          "Location": Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
          },
          "Type": "S3",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineRoleAC8D5C2A",
            "Arn",
          ],
        },
        "Stages": Array [
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Source",
                  "Owner": "AWS",
                  "Provider": "S3",
                  "Version": "1",
                },
                "Configuration": Object {
                  "PollForSourceChanges": false,
                  "S3Bucket": Object {
                    "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                  },
                  "S3ObjectKey": "image_backend.zip",
                },
                "Name": "SourceBucket",
                "OutputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Source_SourceBucket",
                  },
                ],
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleA991578A",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Source",
          },
          Object {
            "Actions": Array [
              Object {
                "ActionTypeId": Object {
                  "Category": "Build",
                  "Owner": "AWS",
                  "Provider": "CodeBuild",
                  "Version": "1",
                },
                "Configuration": Object {
                  "ProjectName": Object {
                    "Ref": "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1",
                  },
                },
                "InputArtifacts": Array [
                  Object {
                    "Name": "Artifact_Source_SourceBucket",
                  },
                ],
                "Name": "DeployProject",
                "RoleArn": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleC99E52FE",
                    "Arn",
                  ],
                },
                "RunOrder": 1,
              },
            ],
            "Name": "Deploy",
          },
        ],
      },
      "Type": "AWS::CodePipeline::Pipeline",
    },
    "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "KMSMasterKeyID": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                    "Arn",
                  ],
                },
                "SSEAlgorithm": "aws:kms",
              },
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "KeyPolicy": Object {
          "Statement": Array [
            Object {
              "Action": "kms:*",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::KMS::Key",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyAlias9700FF3D": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AliasName": "alias/codepipeline-gevanniserviceecsecsappecsbackendbackapppipeline4884a44a",
        "TargetKeyId": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
            "Arn",
          ],
        },
      },
      "Type": "AWS::KMS::Alias",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineArtifactsBucketPolicy67C5E8B8": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleC99E52FE": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleDefaultPolicyA661FEA5": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "codebuild:BatchGetBuilds",
                "codebuild:StartBuild",
                "codebuild:StopBuild",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleDefaultPolicyA661FEA5",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleC99E52FE",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1": Object {
      "Properties": Object {
        "Artifacts": Object {
          "Type": "CODEPIPELINE",
        },
        "Cache": Object {
          "Type": "NO_CACHE",
        },
        "EncryptionKey": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
            "Arn",
          ],
        },
        "Environment": Object {
          "ComputeType": "BUILD_GENERAL1_SMALL",
          "EnvironmentVariables": Array [
            Object {
              "Name": "ECS_CLUSTER",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppECSCommonCluster9D6395A7",
              },
            },
            Object {
              "Name": "ECS_SERVICE",
              "Type": "PLAINTEXT",
              "Value": "EcsBackend-Service",
            },
            Object {
              "Name": "TARGET_GROUP_ARN",
              "Type": "PLAINTEXT",
              "Value": "",
            },
            Object {
              "Name": "SECURITY_GROUP",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27",
                  "GroupId",
                ],
              },
            },
            Object {
              "Name": "SUBNET_1",
              "Type": "PLAINTEXT",
              "Value": "p-12345",
            },
            Object {
              "Name": "SUBNET_2",
              "Type": "PLAINTEXT",
              "Value": "p-67890",
            },
            Object {
              "Name": "SUBNET_3",
              "Type": "PLAINTEXT",
              "Value": "",
            },
            Object {
              "Name": "FIRELENS_IMAGE_NAME",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportFluentbitImageUriParameterD37471DB",
                    },
                    ":fluentbitimageruby",
                  ],
                ],
              },
            },
            Object {
              "Name": "STREAM_NAME",
              "Type": "PLAINTEXT",
              "Value": "GEVANNI-service-AppLog-Stream",
            },
            Object {
              "Name": "LOG_GROUP_FIRELENS",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsBackendBackAppEcsResourcesLogD49A8E5A",
              },
            },
            Object {
              "Name": "LOG_GROUP_SERVICE_CONNECT",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsBackendBackAppEcsResourcesLogforSCE9878013",
              },
            },
            Object {
              "Name": "EXECUTION_ROLE_ARN",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppEcsTaskRoleEcsTaskExecutionRole76623661",
                  "Arn",
                ],
              },
            },
            Object {
              "Name": "TASK_ROLE",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppEcsTaskRoleD052B233",
                  "Arn",
                ],
              },
            },
            Object {
              "Name": "FAMILY",
              "Type": "PLAINTEXT",
              "Value": "GEVANNI-service-EcsBackend-Taskdef",
            },
            Object {
              "Name": "NAMESPACE",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNamespaceArnParameter5DBCE7D2",
              },
            },
            Object {
              "Name": "CLOUDMAP_SERVICE_ARN",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::GetAtt": Array [
                  "ECSAppECSCommoncloudMapService3E00FCE0",
                  "Arn",
                ],
              },
            },
            Object {
              "Name": "DISCOVERY_NAME",
              "Type": "PLAINTEXT",
              "Value": "backend-sample01-dev01-ab",
            },
            Object {
              "Name": "PORT_NUMBER",
              "Type": "PLAINTEXT",
              "Value": "8080",
            },
            Object {
              "Name": "PARAMETER_NAME",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsBackendBackAppPipelineecrTag913C9965",
              },
            },
            Object {
              "Name": "IMAGE_URI",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::Select": Array [
                        4,
                        Object {
                          "Fn::Split": Array [
                            ":",
                            Object {
                              "Fn::GetAtt": Array [
                                "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B",
                                "Arn",
                              ],
                            },
                          ],
                        },
                      ],
                    },
                    ".dkr.ecr.",
                    Object {
                      "Fn::Select": Array [
                        3,
                        Object {
                          "Fn::Split": Array [
                            ":",
                            Object {
                              "Fn::GetAtt": Array [
                                "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B",
                                "Arn",
                              ],
                            },
                          ],
                        },
                      ],
                    },
                    ".",
                    Object {
                      "Ref": "AWS::URLSuffix",
                    },
                    "/",
                    Object {
                      "Ref": "ECSAppEcsBackendBackAppEcsResourcesRepo24214D8B",
                    },
                  ],
                ],
              },
            },
            Object {
              "Name": "ECR_TAG",
              "Type": "PLAINTEXT",
              "Value": Object {
                "Ref": "ECSAppEcsBackendBackAppPipelineecrTag913C9965",
              },
            },
            Object {
              "Name": "CM_BILLING_GROUP_TAG_KEY",
              "Type": "PLAINTEXT",
              "Value": " ",
            },
            Object {
              "Name": "CM_BILLING_GROUP_TAG",
              "Type": "PLAINTEXT",
              "Value": " ",
            },
            Object {
              "Name": "PROPAGATE_TAG",
              "Type": "PLAINTEXT",
              "Value": "NONE",
            },
          ],
          "Image": "aws/codebuild/amazonlinux2-x86_64-standard:2.0",
          "ImagePullCredentialsType": "CODEBUILD",
          "PrivilegedMode": false,
          "Type": "LINUX_CONTAINER",
        },
        "LogsConfig": Object {
          "CloudWatchLogs": Object {
            "GroupName": Object {
              "Ref": "ECSAppEcsBackendBackAppPipelineLogA47520C1",
            },
            "Status": "ENABLED",
          },
        },
        "ServiceRole": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineDeployProjectRole8BECA536",
            "Arn",
          ],
        },
        "Source": Object {
          "BuildSpec": "{
  \\"version\\": \\"0.2\\",
  \\"phases\\": {
    \\"pre_build\\": {
      \\"commands\\": [
        \\"echo \\\\\\"The latest version of ecspresso is (It only shows up the log) :\\\\\\"\\",
        \\"curl -s https://api.github.com/repos/kayac/ecspresso/releases/latest | jq .tag_name\\",
        \\"curl -sL -o ecspresso-v2.3.5-linux-amd64.tar.gz https://github.com/kayac/ecspresso/releases/download/v2.3.5/ecspresso_2.3.5_linux_amd64.tar.gz\\",
        \\"tar -zxf ecspresso-v2.3.5-linux-amd64.tar.gz\\",
        \\"sudo install ecspresso /usr/local/bin/ecspresso\\",
        \\"ecspresso version\\"
      ]
    },
    \\"build\\": {
      \\"commands\\": [
        \\"export IMAGE_TAG=$(aws ssm get-parameter --name \${PARAMETER_NAME} --query \\\\\\"Parameter.Value\\\\\\" --output text)\\",
        \\"export IMAGE1_NAME=\${IMAGE_URI}:\${IMAGE_TAG}\\",
        \\"ls -lR\\",
        \\"ecspresso deploy --config ecspresso.yml\\",
        \\"python ./config.py\\"
      ]
    }
  }
}",
          "Type": "CODEPIPELINE",
        },
      },
      "Type": "AWS::CodeBuild::Project",
    },
    "ECSAppEcsBackendBackAppPipelineDeployProjectRole8BECA536": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codebuild.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineDeployProjectRoleDefaultPolicyFB0B73AC": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineLogA47520C1",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1",
                      },
                    ],
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":logs:ap-northeast-1:************:log-group:/aws/codebuild/",
                      Object {
                        "Ref": "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1",
                      },
                      ":*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "codebuild:CreateReportGroup",
                "codebuild:CreateReport",
                "codebuild:UpdateReport",
                "codebuild:BatchPutTestCases",
                "codebuild:BatchPutCodeCoverages",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codebuild:ap-northeast-1:************:report-group/",
                    Object {
                      "Ref": "ECSAppEcsBackendBackAppPipelineDeployProjectA1B4EAF1",
                    },
                    "-*",
                  ],
                ],
              },
            },
            Object {
              "Action": Array [
                "ecs:RegisterTaskDefinition",
                "ecs:ListTaskDefinitions",
                "ecs:DescribeTaskDefinition",
                "application-autoscaling:DescribeScalableTargets",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "ecs:TagResource",
                "application-autoscaling:RegisterScalableTarget",
                "application-autoscaling:DeregisterScalableTarget",
                "application-autoscaling:PutScalingPolicy",
                "application-autoscaling:DeleteScalingPolicy",
                "application-autoscaling:DescribeScalingPolicies",
                "elasticloadbalancing:ModifyTargetGroup",
                "servicediscovery:GetNamespace",
                "iam:CreateServiceLinkedRole",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "ecs:CreateService",
                "ecs:UpdateService",
                "ecs:DescribeServices",
                "ssm:GetParameter",
                "secretsmanager:GetSecretValue",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:aws:ecs:ap-northeast-1:************:service/",
                      Object {
                        "Ref": "ECSAppECSCommonCluster9D6395A7",
                      },
                      "/EcsBackend-Service",
                    ],
                  ],
                },
                "arn:aws:ssm:ap-northeast-1:************:parameter:parameter/GEVANNI-common/*",
                "arn:aws:ssm:ap-northeast-1:************:parameter/GEVANNI-service/*",
                Object {
                  "Ref": "ECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C",
                },
              ],
            },
            Object {
              "Action": "iam:PassRole",
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppEcsTaskRoleEcsTaskExecutionRole76623661",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppEcsTaskRoleD052B233",
                    "Arn",
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineDeployProjectRoleDefaultPolicyFB0B73AC",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineDeployProjectRole8BECA536",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineEventsRole39069B06": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "events.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineEventsRoleDefaultPolicy469874FA": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "codepipeline:StartPipelineExecution",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":codepipeline:ap-northeast-1:************:",
                    Object {
                      "Ref": "ECSAppEcsBackendBackAppPipeline7C151C88",
                    },
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineEventsRoleDefaultPolicy469874FA",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineEventsRole39069B06",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineLogA47520C1": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "KmsKeyId": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAppKeyArnParameterD7B87911",
        },
        "RetentionInDays": 30,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Retain",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseCfnSubscriptionFilter89203982": Object {
      "Properties": Object {
        "DestinationArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseStreamE0A90253",
            "Arn",
          ],
        },
        "FilterPattern": "",
        "LogGroupName": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelineLogA47520C1",
        },
        "RoleArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppEcsBackendBackAppPipelineMetricFirehoseToKinesisFirehoseRole323B0512",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Logs::SubscriptionFilter",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogGroup510696B2": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/firehose/GEVANNI-service/EcsBackend/buildlogs",
        "RetentionInDays": 90,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogStreamE674FD02": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogGroup510696B2",
        },
      },
      "Type": "AWS::Logs::LogStream",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucketAutoDeleteObjectsCustomResource757E96C3": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucketPolicyBEED9090",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucketPolicyBEED9090": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRole32DE668A": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "firehose.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRoleDefaultPolicy4D739544": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutObject",
                "s3:GetBucketLocation",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "secretsmanager:GetSecretValue",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
              },
            },
            Object {
              "Action": "logs:PutLogEvents",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogGroup510696B2",
                        "Arn",
                      ],
                    },
                    ":*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRoleDefaultPolicy4D739544",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRole32DE668A",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseStreamE0A90253": Object {
      "Properties": Object {
        "DeliveryStreamName": "GEVANNI-service-EcsBackend-BuildLog-Stream",
        "DeliveryStreamType": "DirectPut",
        "HttpEndpointDestinationConfiguration": Object {
          "BufferingHints": Object {
            "IntervalInSeconds": 60,
            "SizeInMBs": 1,
          },
          "CloudWatchLoggingOptions": Object {
            "Enabled": true,
            "LogGroupName": Object {
              "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogGroup510696B2",
            },
            "LogStreamName": Object {
              "Ref": "ECSAppEcsBackendBackAppPipelineMetricFirehoseCwLogStreamE674FD02",
            },
          },
          "EndpointConfiguration": Object {
            "Name": "New Relic",
            "Url": "https://aws-api.newrelic.com/firehose/v1",
          },
          "RequestConfiguration": Object {
            "ContentEncoding": "GZIP",
          },
          "RetryOptions": Object {
            "DurationInSeconds": 60,
          },
          "RoleARN": Object {
            "Fn::GetAtt": Array [
              "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRole32DE668A",
              "Arn",
            ],
          },
          "S3BackupMode": "FailedDataOnly",
          "S3Configuration": Object {
            "BucketARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseLogBucket436F2A23",
                "Arn",
              ],
            },
            "CompressionFormat": "GZIP",
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRole32DE668A",
                "Arn",
              ],
            },
          },
          "SecretsManagerConfiguration": Object {
            "Enabled": true,
            "RoleARN": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsBackendBackAppPipelineMetricFirehoseFirehoseRole32DE668A",
                "Arn",
              ],
            },
            "SecretARN": Object {
              "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
            },
          },
        },
      },
      "Type": "AWS::KinesisFirehose::DeliveryStream",
    },
    "ECSAppEcsBackendBackAppPipelineMetricFirehoseToKinesisFirehoseRole323B0512": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "logs.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/AmazonKinesisFirehoseFullAccess",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineNotifications36CDE10B": Object {
      "Properties": Object {
        "DetailType": "FULL",
        "EventTypeIds": Array [
          "codepipeline-pipeline-pipeline-execution-started",
          "codepipeline-pipeline-pipeline-execution-succeeded",
          "codepipeline-pipeline-pipeline-execution-failed",
        ],
        "Name": "eECSECSAppEcsBackendBackAppPipelinePipelineNotificationsDB39BE64",
        "Resource": Object {
          "Fn::Join": Array [
            "",
            Array [
              "arn:",
              Object {
                "Ref": "AWS::Partition",
              },
              ":codepipeline:ap-northeast-1:************:",
              Object {
                "Ref": "ECSAppEcsBackendBackAppPipeline7C151C88",
              },
            ],
          ],
        },
        "Targets": Array [
          Object {
            "TargetAddress": Object {
              "Fn::ImportValue": "GEVANNI-service-Notifier:ExportsOutputRefGEVANNIserviceAlarmSNSTopicA34366179411277C",
            },
            "TargetType": "SNS",
          },
        ],
      },
      "Type": "AWS::CodeStarNotifications::NotificationRule",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NewerNoncurrentVersions": 20,
                "NoncurrentDays": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucketAutoDeleteObjectsCustomResourceFA00E0F3": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppEcsBackendBackAppPipelinePipelineSourceBucketPolicy5D220165",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucketNotifications00D494B4": Object {
      "DependsOn": Array [
        "ECSAppEcsBackendBackAppPipelinePipelineSourceBucketPolicy5D220165",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
        },
        "Managed": true,
        "NotificationConfiguration": Object {
          "EventBridgeConfiguration": Object {},
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691",
            "Arn",
          ],
        },
        "SkipDestinationValidation": false,
      },
      "Type": "Custom::S3BucketNotifications",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucketPolicy5D220165": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppEcsBackendBackAppPipelinePipelineTrigerEventRule726B805F": Object {
      "Properties": Object {
        "EventPattern": Object {
          "detail": Object {
            "bucket": Object {
              "name": Array [
                Object {
                  "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                },
              ],
            },
            "object": Object {
              "key": Array [
                "image_backend.zip",
              ],
            },
          },
          "detail-type": Array [
            "Object Created",
          ],
          "source": Array [
            "aws.s3",
          ],
        },
        "State": "ENABLED",
        "Targets": Array [
          Object {
            "Arn": Object {
              "Fn::Join": Array [
                "",
                Array [
                  "arn:",
                  Object {
                    "Ref": "AWS::Partition",
                  },
                  ":codepipeline:ap-northeast-1:************:",
                  Object {
                    "Ref": "ECSAppEcsBackendBackAppPipeline7C151C88",
                  },
                ],
              ],
            },
            "Id": "Target0",
            "RoleArn": Object {
              "Fn::GetAtt": Array [
                "ECSAppEcsBackendBackAppPipelineEventsRole39069B06",
                "Arn",
              ],
            },
          },
        ],
      },
      "Type": "AWS::Events::Rule",
    },
    "ECSAppEcsBackendBackAppPipelineRoleAC8D5C2A": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "codepipeline.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineRoleDefaultPolicyF19F074E": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Decrypt",
                "kms:DescribeKey",
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleA991578A",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineDeployDeployProjectCodePipelineActionRoleC99E52FE",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineRoleDefaultPolicyF19F074E",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineRoleAC8D5C2A",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineSSMSourceBucketNameF1F82B8E": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/SourceBucketName",
        "Type": "String",
        "Value": Object {
          "Ref": "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleA991578A": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleDefaultPolicy4C0ADD8B": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject*",
                "s3:GetBucket*",
                "s3:List*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45",
                          "Arn",
                        ],
                      },
                      "/image_backend.zip",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:DeleteObject*",
                "s3:PutObject",
                "s3:PutObjectLegalHold",
                "s3:PutObjectRetention",
                "s3:PutObjectTagging",
                "s3:PutObjectVersionTagging",
                "s3:Abort*",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppEcsBackendBackAppPipelineArtifactsBucketD8E4D0C7",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "kms:Encrypt",
                "kms:ReEncrypt*",
                "kms:GenerateDataKey*",
                "kms:Decrypt",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppEcsBackendBackAppPipelineArtifactsBucketEncryptionKeyA483BF18",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleDefaultPolicy4C0ADD8B",
        "Roles": Array [
          Object {
            "Ref": "ECSAppEcsBackendBackAppPipelineSourceSourceBucketCodePipelineActionRoleA991578A",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppEcsBackendBackAppPipelineecrTag913C9965": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/EcsBackend/ecrTag",
        "Type": "String",
        "Value": "sample",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppFrontAlb124B346F": Object {
      "Properties": Object {
        "LoadBalancerAttributes": Array [
          Object {
            "Key": "deletion_protection.enabled",
            "Value": "false",
          },
          Object {
            "Key": "access_logs.s3.enabled",
            "Value": "true",
          },
          Object {
            "Key": "access_logs.s3.bucket",
            "Value": Object {
              "Ref": "ECSAppFrontAlbalblogbucketFC4C6490",
            },
          },
        ],
        "Scheme": "internet-facing",
        "SecurityGroups": Array [
          Object {
            "Fn::GetAtt": Array [
              "ECSAppFrontAlbSgAlb5FBE35E9",
              "GroupId",
            ],
          },
        ],
        "Subnets": Array [
          "s-12345",
          "s-67890",
        ],
        "Type": "application",
      },
      "Type": "AWS::ElasticLoadBalancingV2::LoadBalancer",
    },
    "ECSAppFrontAlbEcsAppTGTargetGroupE49C9484": Object {
      "Properties": Object {
        "Port": 80,
        "Protocol": "HTTP",
        "TargetGroupAttributes": Array [
          Object {
            "Key": "deregistration_delay.timeout_seconds",
            "Value": "30",
          },
          Object {
            "Key": "stickiness.enabled",
            "Value": "false",
          },
        ],
        "TargetType": "ip",
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::ElasticLoadBalancingV2::TargetGroup",
    },
    "ECSAppFrontAlbNewRelicLogIngestionFunctionLogGroup5254F969": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "LogGroupName": "/aws/lambda/GEVANNI-service/NewRelicFunctionLog",
        "RetentionInDays": 365,
      },
      "Type": "AWS::Logs::LogGroup",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppFrontAlbNewRelicLogIngestionfunctionC421A1E7": Object {
      "DependsOn": Array [
        "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleDefaultPolicy3F38D687",
        "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleB9D8F189",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": "cdk-hnb659fds-assets-************-ap-northeast-1",
          "S3Key": "06a116cb1fd32616c0031de06fa5fff9b9b84b2b97a2b6d7684a9282e317e542.zip",
        },
        "Environment": Object {
          "Variables": Object {
            "DEBUG_ENABLED": "false",
            "LICENSE_KEY_ARN": Object {
              "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
            },
            "LOG_TYPE": "alb",
          },
        },
        "FunctionName": "GEVANNI-service-nr-log",
        "Handler": "NewRelicLogIngestionS3.lambda_handler",
        "Layers": Array [
          Object {
            "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportNewRelicLayerParameter4A54F195",
          },
        ],
        "LoggingConfig": Object {
          "LogGroup": Object {
            "Ref": "ECSAppFrontAlbNewRelicLogIngestionFunctionLogGroup5254F969",
          },
        },
        "MemorySize": 256,
        "Role": Object {
          "Fn::GetAtt": Array [
            "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleB9D8F189",
            "Arn",
          ],
        },
        "Runtime": "python3.12",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleB9D8F189": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Join": Array [
              "",
              Array [
                "arn:",
                Object {
                  "Ref": "AWS::Partition",
                },
                ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
              ],
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleDefaultPolicy3F38D687": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "s3:GetObject",
                "s3:ListBucket",
                "secretsmanager:GetSecretValue",
              ],
              "Effect": "Allow",
              "Resource": Array [
                Object {
                  "Fn::ImportValue": "GEVANNI-service-Secrets:ExportsOutputFnGetAttGEVANNIserviceNewRelicSecretIdF8C92B1E",
                },
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppFrontAlbalblogbucketFC4C6490",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppFrontAlbalblogbucketFC4C6490",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleDefaultPolicy3F38D687",
        "Roles": Array [
          Object {
            "Ref": "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleB9D8F189",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "ECSAppFrontAlbSSMAlbName1188EA17": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/LoadBalancerName",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "ECSAppFrontAlb124B346F",
            "LoadBalancerName",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "ECSAppFrontAlbSgAlb5FBE35E9": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-ECS/ECSApp/FrontAlb/SgAlb",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "SecurityGroupIngress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow from anyone on port 443",
            "FromPort": 443,
            "IpProtocol": "tcp",
            "ToPort": 443,
          },
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow from anyone on port 80",
            "FromPort": 80,
            "IpProtocol": "tcp",
            "ToPort": 80,
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "ECSAppFrontAlbalblogbucketAllowBucketNotificationsToGEVANNIserviceECSECSAppFrontAlbNewRelicLogIngestionfunctionB2C80EC34734DE2F": Object {
      "Properties": Object {
        "Action": "lambda:InvokeFunction",
        "FunctionName": Object {
          "Fn::GetAtt": Array [
            "ECSAppFrontAlbNewRelicLogIngestionfunctionC421A1E7",
            "Arn",
          ],
        },
        "Principal": "s3.amazonaws.com",
        "SourceAccount": "************",
        "SourceArn": Object {
          "Fn::GetAtt": Array [
            "ECSAppFrontAlbalblogbucketFC4C6490",
            "Arn",
          ],
        },
      },
      "Type": "AWS::Lambda::Permission",
    },
    "ECSAppFrontAlbalblogbucketAutoDeleteObjectsCustomResource9ACCBC41": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "ECSAppFrontAlbalblogbucketPolicyC34EE4F7",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppFrontAlbalblogbucketFC4C6490",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppFrontAlbalblogbucketFC4C6490": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "ECSAppFrontAlbalblogbucketNotifications9A7593BC": Object {
      "DependsOn": Array [
        "ECSAppFrontAlbalblogbucketAllowBucketNotificationsToGEVANNIserviceECSECSAppFrontAlbNewRelicLogIngestionfunctionB2C80EC34734DE2F",
        "ECSAppFrontAlbalblogbucketPolicyC34EE4F7",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "ECSAppFrontAlbalblogbucketFC4C6490",
        },
        "Managed": true,
        "NotificationConfiguration": Object {
          "LambdaFunctionConfigurations": Array [
            Object {
              "Events": Array [
                "s3:ObjectCreated:Put",
              ],
              "LambdaFunctionArn": Object {
                "Fn::GetAtt": Array [
                  "ECSAppFrontAlbNewRelicLogIngestionfunctionC421A1E7",
                  "Arn",
                ],
              },
            },
          ],
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "BucketNotificationsHandler050a0587b7544547bf325f094a3db8347ECC3691",
            "Arn",
          ],
        },
        "SkipDestinationValidation": false,
      },
      "Type": "Custom::S3BucketNotifications",
    },
    "ECSAppFrontAlbalblogbucketPolicyC34EE4F7": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "ECSAppFrontAlbalblogbucketFC4C6490",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppFrontAlbalblogbucketFC4C6490",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppFrontAlbalblogbucketFC4C6490",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppFrontAlbalblogbucketFC4C6490",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppFrontAlbalblogbucketFC4C6490",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "s3:PutObject",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::582318560864:root",
                    ],
                  ],
                },
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "ECSAppFrontAlbalblogbucketFC4C6490",
                        "Arn",
                      ],
                    },
                    "/AWSLogs/************/*",
                  ],
                ],
              },
            },
            Object {
              "Action": "s3:PutObject",
              "Condition": Object {
                "StringEquals": Object {
                  "s3:x-amz-acl": "bucket-owner-full-control",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "delivery.logs.amazonaws.com",
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "ECSAppFrontAlbalblogbucketFC4C6490",
                        "Arn",
                      ],
                    },
                    "/AWSLogs/************/*",
                  ],
                ],
              },
            },
            Object {
              "Action": "s3:GetBucketAcl",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "delivery.logs.amazonaws.com",
              },
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppFrontAlbalblogbucketFC4C6490",
                  "Arn",
                ],
              },
            },
            Object {
              "Action": Array [
                "s3:GetObject",
                "s3:ListBucket",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "ECSAppFrontAlbNewRelicLogIngestionfunctionServiceRoleB9D8F189",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "ECSAppFrontAlbalblogbucketFC4C6490",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "ECSAppFrontAlbalblogbucketFC4C6490",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "ECSAppFrontAlbapp43A777AC": Object {
      "Properties": Object {
        "Certificates": Array [
          Object {
            "CertificateArn": Object {
              "Ref": "importAcmArnParameter",
            },
          },
        ],
        "DefaultActions": Array [
          Object {
            "TargetGroupArn": Object {
              "Ref": "ECSAppFrontAlbEcsAppTGTargetGroupE49C9484",
            },
            "Type": "forward",
          },
        ],
        "LoadBalancerArn": Object {
          "Ref": "ECSAppFrontAlb124B346F",
        },
        "Port": 443,
        "Protocol": "HTTPS",
        "SslPolicy": "ELBSecurityPolicy-TLS13-1-2-2021-06",
      },
      "Type": "AWS::ElasticLoadBalancingV2::Listener",
    },
    "ECSAppFrontAlbredirectEA0D4959": Object {
      "DependsOn": Array [
        "ECSAppFrontAlbapp43A777AC",
      ],
      "Properties": Object {
        "DefaultActions": Array [
          Object {
            "RedirectConfig": Object {
              "Port": "443",
              "Protocol": "HTTPS",
              "StatusCode": "HTTP_301",
            },
            "Type": "redirect",
          },
        ],
        "LoadBalancerArn": Object {
          "Ref": "ECSAppFrontAlb124B346F",
        },
        "Port": 80,
        "Protocol": "HTTP",
      },
      "Type": "AWS::ElasticLoadBalancingV2::Listener",
    },
    "importBastionEcsTaskExecutionRolePolicy0ADF768E": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    "arn:",
                    Object {
                      "Ref": "AWS::Partition",
                    },
                    ":ecr:ap-northeast-1:************:repository/",
                    Object {
                      "Ref": "importBastionEcrRepositoryNameParameter",
                    },
                  ],
                ],
              },
            },
            Object {
              "Action": "ecr:GetAuthorizationToken",
              "Effect": "Allow",
              "Resource": "*",
            },
            Object {
              "Action": Array [
                "logs:CreateLogStream",
                "logs:PutLogEvents",
              ],
              "Effect": "Allow",
              "Resource": Object {
                "Fn::GetAtt": Array [
                  "ECSAppBastionECSAPPLogGroupB2AE2196",
                  "Arn",
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "importBastionEcsTaskExecutionRolePolicy0ADF768E",
        "Roles": Array [
          Object {
            "Fn::Select": Array [
              1,
              Object {
                "Fn::Split": Array [
                  "/",
                  Object {
                    "Fn::Select": Array [
                      5,
                      Object {
                        "Fn::Split": Array [
                          ":",
                          Object {
                            "Ref": "importBastionEcsTaskExecutionRoleArnParameter",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 4`] = `
Object {
  "Outputs": Object {
    "PreSharedKey0": Object {
      "Description": "Pre-shared key for WAF",
      "Value": "b799d457",
    },
    "PreSharedKey1": Object {
      "Description": "Pre-shared key for WAF",
      "Value": "b9a0ace7",
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "WafAlbGEVANNIserviceWafAlbWebAclE762DD25": Object {
      "Properties": Object {
        "DefaultAction": Object {
          "Block": Object {},
        },
        "Rules": Array [
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "IPset",
            "Priority": 11,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": Object {
                  "Fn::GetAtt": Array [
                    "WafAlbIPset08CA3181",
                    "Arn",
                  ],
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "preSharedKey",
            "Priority": 12,
            "Statement": Object {
              "OrStatement": Object {
                "Statements": Array [
                  Object {
                    "ByteMatchStatement": Object {
                      "FieldToMatch": Object {
                        "SingleHeader": Object {
                          "name": "x-pre-shared-key",
                        },
                      },
                      "PositionalConstraint": "EXACTLY",
                      "SearchString": "b799d457",
                      "TextTransformations": Array [
                        Object {
                          "Priority": 0,
                          "Type": "NONE",
                        },
                      ],
                    },
                  },
                  Object {
                    "ByteMatchStatement": Object {
                      "FieldToMatch": Object {
                        "SingleHeader": Object {
                          "name": "x-pre-shared-key",
                        },
                      },
                      "PositionalConstraint": "EXACTLY",
                      "SearchString": "b9a0ace7",
                      "TextTransformations": Array [
                        Object {
                          "Priority": 0,
                          "Type": "NONE",
                        },
                      ],
                    },
                  },
                ],
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
        ],
        "Scope": "REGIONAL",
        "VisibilityConfig": Object {
          "CloudWatchMetricsEnabled": true,
          "MetricName": "WafAcl",
          "SampledRequestsEnabled": true,
        },
      },
      "Type": "AWS::WAFv2::WebACL",
    },
    "WafAlbIPset08CA3181": Object {
      "Properties": Object {
        "Addresses": Array [
          "***************/25",
        ],
        "IPAddressVersion": "IPV4",
        "Scope": "REGIONAL",
      },
      "Type": "AWS::WAFv2::IPSet",
    },
    "WafAlbWebAclAssociation07344E7B9": Object {
      "Properties": Object {
        "ResourceArn": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppFrontAlb124B346FE426DA64",
        },
        "WebACLArn": Object {
          "Fn::GetAtt": Array [
            "WafAlbGEVANNIserviceWafAlbWebAclE762DD25",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::WebACLAssociation",
    },
    "WafAlbWebAclAssociation1A699A768": Object {
      "Properties": Object {
        "ResourceArn": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppFrontAlb124B346FE426DA64",
        },
        "WebACLArn": Object {
          "Fn::GetAtt": Array [
            "WafAlbGEVANNIserviceWafAlbWebAclE762DD25",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::WebACLAssociation",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 5`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "GEVANNIserviceDashboard70CFD7E4": Object {
      "Properties": Object {
        "DashboardBody": Object {
          "Fn::Join": Array [
            "",
            Array [
              "{\\"widgets\\":[{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":0,\\"properties\\":{\\"markdown\\":\\"# Requests\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ALB Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"RequestCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"NewConnectionCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"RejectedConnectionCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_2XX_Count\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":7,\\"properties\\":{\\"markdown\\":\\"# Response Time\\"}},{\\"type\\":\\"metric\\",\\"width\\":8,\\"height\\":6,\\"x\\":0,\\"y\\":8,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Response Time\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"TargetResponseTime\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":14,\\"properties\\":{\\"markdown\\":\\"# Errors\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ALB Errors\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"ClientTLSNegotiationErrorCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_ELB_5XX_Count\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_ELB_4XX_Count\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":15,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"Target Group Errors\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_5XX_Count\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"HTTPCode_Target_4XX_Count\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"TargetConnectionErrorCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"AWS/ApplicationELB\\",\\"TargetTLSNegotiationErrorCount\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":21,\\"properties\\":{\\"markdown\\":\\"# Resources\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":22,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":22,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Memory Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"MemoryUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":22,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Desired Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"DesiredTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":22,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":0,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with Requests per tasks\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ApplicationELB\\",\\"RequestCountPerTarget\\",\\"LoadBalancer\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FLoadBalancerFullNameA61F8DDE",
              },
              "\\",\\"TargetGroup\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlbEcsAppTGTargetGroupE49C9484TargetGroupName917B386E",
              },
              "\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":50,\\"label\\":\\"Threshold: Requests per tasks\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":12,\\"y\\":28,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":10000,\\"label\\":\\"Threshold: CPU Utilization\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":34,\\"properties\\":{\\"markdown\\":\\"# Resources\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":0,\\"y\\":35,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":6,\\"y\\":35,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Memory Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"MemoryUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":12,\\"y\\":35,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Desired Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"DesiredTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":6,\\"x\\":18,\\"y\\":35,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Task Count\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":0,\\"y\\":41,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":10000,\\"label\\":\\"Threshold: CPU Utilization\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}},{\\"type\\":\\"metric\\",\\"width\\":12,\\"height\\":6,\\"x\\":0,\\"y\\":47,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"ECS Auto Scaling with CPU Utilization\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/ECS\\",\\"CPUUtilization\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsApp-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60}],[\\"ECS/ContainerInsights\\",\\"RunningTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}],[\\"ECS/ContainerInsights\\",\\"PendingTaskCount\\",\\"ClusterName\\",\\"",
              Object {
                "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
              },
              "\\",\\"ServiceName\\",\\"EcsBackend-Service\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"period\\":60,\\"yAxis\\":\\"right\\"}]],\\"annotations\\":{\\"horizontal\\":[{\\"value\\":10000,\\"label\\":\\"Threshold: CPU Utilization\\",\\"color\\":\\"#aec7e8\\",\\"fill\\":\\"below\\",\\"yAxis\\":\\"left\\"}]},\\"yAxis\\":{}}}]}",
            ],
          ],
        },
        "DashboardName": "GEVANNI-service-ECSApp",
      },
      "Type": "AWS::CloudWatch::Dashboard",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 6`] = `
Object {
  "Outputs": Object {
    "RoleForAppTeamGEVANNIserviceRoleForAppTeamArn42DC40BA": Object {
      "Export": Object {
        "Name": "GEVANNI-service-RoleForAppTeamArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "RoleForAppTeamGEVANNIserviceRoleForAppTeam4CD9AE91",
          "Arn",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
    "importBastionExecutionRoleArnParameter": Object {
      "Default": "/GEVANNI-common/bastion/ecs/task-execution-role-arn",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "RoleForAppTeamGEVANNIserviceRoleForAppTeam4CD9AE91": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Condition": Object {
                "StringEquals": Object {
                  "aws:PrincipalTag/SsoUserName": Array [
                    "<EMAIL>",
                    "<EMAIL>",
                  ],
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::************:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "ssm:GetParameter",
                  "Effect": "Allow",
                  "Resource": Array [
                    "arn:aws:ssm:ap-northeast-1:************:parameter/GEVANNI-common/*",
                    "arn:aws:ssm:ap-northeast-1:************:parameter/GEVANNI-service/*",
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-ssm-parameter",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "secretsmanager:GetSecretValue",
                    "secretsmanager:PutSecretValue",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppEcsAppFrontAppEcsResourcesSecretA041C38CFFA5248B",
                    },
                    Object {
                      "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppEcsBackendBackAppEcsResourcesSecret2DE9941C2CB0BDCE",
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-secret-manager",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "ecs:DescribeTaskDefinition",
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-ecs-task-definition",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ecs:DescribeTasks",
                    "ecs:RunTask",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    "arn:aws:ecs:ap-northeast-1:************:task/*",
                    "arn:aws:ecs:ap-northeast-1:************:task-definition/GEVANNIserviceECSECSAppBastionECSAPPTaskDefBF0E5A05:*",
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-ecs-task",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ssm:StartSession",
                    "ssm:TerminateSession",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    "arn:aws:ecs:ap-northeast-1:************:task/*",
                    "arn:aws:ssm:ap-northeast-1:************:session/*",
                    "arn:aws:ssm:ap-northeast-1:************:document/*",
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-session-manager",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "s3:GetObject",
                    "s3:PutObject",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          Object {
                            "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsAppFrontAppPipelinePipelineSourceBucket1D02D2D0ArnC627144E",
                          },
                          "/*",
                        ],
                      ],
                    },
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          Object {
                            "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsBackendBackAppPipelinePipelineSourceBucket9D0D8D45Arn1EE6453D",
                          },
                          "/*",
                        ],
                      ],
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-s3-bucket",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "iam:PassRole",
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Ref": "importBastionExecutionRoleArnParameter",
                    },
                    Object {
                      "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppBastionECSAPPTaskServiceTaskRoleBFDBD8AEArn1BD4E09F",
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-iam-role",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ecs:ListServices",
                    "ecs:ListTasks",
                  ],
                  "Condition": Object {
                    "StringEquals": Object {
                      "ecs:cluster": Object {
                        "Fn::Join": Array [
                          "",
                          Array [
                            "arn:aws:ecs:ap-northeast-1:************:cluster/",
                            Object {
                              "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
                            },
                          ],
                        ],
                      },
                    },
                  },
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-ecs-list-services-tasks",
          },
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": "ecs:ExecuteCommand",
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          "arn:aws:ecs:ap-northeast-1:************:cluster/",
                          Object {
                            "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
                          },
                        ],
                      ],
                    },
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          "arn:aws:ecs:ap-northeast-1:************:task/",
                          Object {
                            "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputRefECSAppECSCommonCluster9D6395A7BD8894E3",
                          },
                          "/*",
                        ],
                      ],
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "allow-ecs-execute-command",
          },
        ],
        "RoleName": "GEVANNI-service-RoleForAppTeam",
      },
      "Type": "AWS::IAM::Role",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 7`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "AcmArnAD233D92": Object {
      "Properties": Object {
        "Name": "/GEVANNI-service/AcmArn",
        "Type": "String",
        "Value": Object {
          "Fn::GetAtt": Array [
            "CustomResource",
            "ACMARN",
          ],
        },
      },
      "Type": "AWS::SSM::Parameter",
    },
    "CustomResource": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "DOMAIN_NAME": "test01.dev.gevanni.mynv.jp",
        "HOSTED_ZONE_ID": "Z08220741D6FCGRR7P5PL",
        "ROLE_ARN": "arn:aws:iam::************:role/LambdaCrossAccountRole",
        "ServiceToken": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAcmServiceTokenParameter0F18C120",
        },
      },
      "Type": "AWS::CloudFormation::CustomResource",
      "UpdateReplacePolicy": "Delete",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 8`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomResource": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ALB_DNS_ID": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FDNSName46A20BE3",
        },
        "ALB_HOSTED_ZONE_ID": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppFrontAlb124B346FCanonicalHostedZoneID903D1DDC",
        },
        "DOMAIN_NAME": "test01.dev.gevanni.mynv.jp",
        "HOSTED_ZONE_ID": "Z08220741D6FCGRR7P5PL",
        "ROLE_ARN": "arn:aws:iam::************:role/LambdaCrossAccountRole",
        "ServiceToken": Object {
          "Fn::ImportValue": "GEVANNI-common-ImportResources:ExportsOutputRefimportAliasServiceTokenParameter23FE9DAA",
        },
      },
      "Type": "AWS::CloudFormation::CustomResource",
      "UpdateReplacePolicy": "Delete",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`GEVANNI-service Guest Stacks GuestAccount ECS App Stacks 9`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "OpenSearchSecuritygGroup1541F2BE": Object {
      "Properties": Object {
        "GroupDescription": "GEVANNI-service-Opensearch/OpenSearchSecuritygGroup",
        "SecurityGroupEgress": Array [
          Object {
            "CidrIp": "0.0.0.0/0",
            "Description": "Allow all outbound traffic by default",
            "IpProtocol": "-1",
          },
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::EC2::SecurityGroup",
    },
    "OpenSearchSecuritygGroupfromGEVANNIserviceECSECSAppBastionECSAPPSGCE65230D443FFA0365A": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppBastionECSAPPSGCE65230D:443",
        "FromPort": 443,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "OpenSearchSecuritygGroup1541F2BE",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppBastionECSAPPSGEAB03032GroupId846779B4",
        },
        "ToPort": 443,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "OpenSearchSecuritygGroupfromGEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B4439A425D6A": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppEcsAppFrontAppEcsResourcesSg3FE40E0B:443",
        "FromPort": 443,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "OpenSearchSecuritygGroup1541F2BE",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsAppFrontAppEcsResourcesSg52129FB0GroupId9ADCF5E9",
        },
        "ToPort": 443,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "OpenSearchSecuritygGroupfromGEVANNIserviceECSECSAppEcsBackendBackAppEcsResourcesSg7B0FB0D0443AF934EAA": Object {
      "Properties": Object {
        "Description": "from GEVANNIserviceECSECSAppEcsBackendBackAppEcsResourcesSg7B0FB0D0:443",
        "FromPort": 443,
        "GroupId": Object {
          "Fn::GetAtt": Array [
            "OpenSearchSecuritygGroup1541F2BE",
            "GroupId",
          ],
        },
        "IpProtocol": "tcp",
        "SourceSecurityGroupId": Object {
          "Fn::ImportValue": "GEVANNI-service-ECS:ExportsOutputFnGetAttECSAppEcsBackendBackAppEcsResourcesSgDAEDEC27GroupId7DECC85F",
        },
        "ToPort": 443,
      },
      "Type": "AWS::EC2::SecurityGroupIngress",
    },
    "OpensearchVpcEndpoint": Object {
      "Properties": Object {
        "Name": "opensearch-vpcendpoint",
        "SecurityGroupIds": Array [
          Object {
            "Fn::GetAtt": Array [
              "OpenSearchSecuritygGroup1541F2BE",
              "GroupId",
            ],
          },
        ],
        "SubnetIds": Array [
          "p-12345",
          "p-67890",
        ],
        "VpcId": "vpc-12345",
      },
      "Type": "AWS::OpenSearchServerless::VpcEndpoint",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
