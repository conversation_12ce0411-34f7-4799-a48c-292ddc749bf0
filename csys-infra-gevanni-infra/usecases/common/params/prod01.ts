import * as cdk from 'aws-cdk-lib';
import * as inf from './interface';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export const lambdaIamParam: inf.IlambdaIamParam = {
  CrossAccountRoleArn: 'arn:aws:iam::************:role/LambdaCrossAccountRole',
};

export const OidcParam: inf.IOidcParam = {
  oidcProviderArn: '',
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    InfraRepositoryName: 'csys-infra-gevanni-infra',
    MasterBucketRepositoryName: 'csys-infra-gevanni-infra',
  },
  stackTerminationProtection: false,
};

export const VpcParam: inf.IVpcParam = {
  cidr: '10.101.0.0/16',
  maxAzs: 3,
  natGateways: 3,
};

export const NotifierParam: inf.INotifierParam = {
  isCreate: true,
  workspaceId: 'T62SR7W07',
  channelIdMon: 'C06RV814SVB',
  monitoringNotifyEmail: '<EMAIL>', // change after
};

export const Env: inf.IEnv = {
  envName: 'Prod01',
  prefix: 'GEVANNI',
  account: '************',
  region: 'ap-northeast-1',
};

// CodeBuild完了後にSlackへのステータス通知を行う際に必要な情報
// slackChannelNameはSlackチャンネル名を入力
// slackWorkspaceIdはslackのワークスペースIDを入力
// slackChannelIdはSlackのチャンネルIDを入力
export const InfraResourcesPipelineParam: inf.IInfraResourcesPipelineParam = {
  slackChannelName: 'sys_infra_alert_gevanni_info',
  slackWorkspaceId: 'T62SR7W07',
  slackChannelId: 'C06RV9V73HT',
  stackTerminationProtection: false,
};

// Check the availability zone from Zone ID of a region
// use aws cli, ex: aws ec2 describe-availability-zones --region ap-northeast-1
// see: https://docs.aws.amazon.com/ram/latest/userguide/working-with-az-ids.html
export const VpcForTiDBParam: inf.IVpcForTiDBParam = {
  tidbEndpointServiceName: 'com.amazonaws.vpce.ap-northeast-1.vpce-svc-0d5e3d6da8b5b03aa',
  tidbEndpointServicePort: 4000,
  tidbClusterAvailabilityZones: ['ap-northeast-1a'], // same ZoneId=apne1-az4
};

export const RegionalVpcForTiDBParam: inf.IVpcForTiDBParam = {
  tidbEndpointServiceName: 'com.amazonaws.vpce.ap-northeast-1.vpce-svc-03046ae70ad31ec70',
  tidbEndpointServicePort: 4000,
  tidbClusterAvailabilityZones: ['ap-northeast-1a', 'ap-northeast-1c'], // ZoneId=apne1-az4, apne1-az1
};

export const TiDBEndpoint: inf.ITiDBEndpointParam = {
  tidbEndpoint: 'gateway01-privatelink.ap-northeast-1.prod.aws.tidbcloud.com',
};

export const BastionParam: inf.IBastionParam = {
  lifecycleRules: [
    {
      description: 'Keep last 5 images',
      maxImageCount: 5,
    },
  ],
  stackTerminationProtection: false,
};

export const FluentbitParam: inf.IFluentbitParam = {
  lifecycleRules: [
    {
      description: 'Keep last 5 images',
      maxImageCount: 5,
    },
  ],
  repositoryName: 'fluentbitrepo',
  repositoryDefault: 'public.ecr.aws/aws-observability/aws-for-fluent-bit:stable',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];

export const s3BuildLogLifecycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const KmsKeyParam: inf.IKmsKeyParam = {
  pendingWindow: cdk.Duration.days(7),
};

export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;

export const CustomResourceAcmParam: inf.ICustomResourceAcmParam = {
  lambdaRuntime: lambda.Runtime.PYTHON_3_12,
  stackTerminationProtection: false,
};

export const CustomResourceAliasParam: inf.ICustomResourceAliasParam = {
  lambdaRuntime: lambda.Runtime.PYTHON_3_12,
  stackTerminationProtection: false,
};

export const NewRelicParam: inf.INewRelicParam = {
  ExternalId: '6561399',
};

export const RoleForExternalTeamParam: inf.IRoleForExternalTeamParam = {
  externalBastionAccountId: '************',
  externalTeamName: 'Msp',
  stackTerminationProtection: false,
};

export const TiDBCostMetricsParam: inf.ITiDBCostMetricsParam = {
  stackTerminationProtection: false,
};

export const CloudMapParam: inf.ICloudMapParam = {
  stackTerminationProtection: false,
};

export const ShareResourcesParam: inf.IShareResourcesParam = {
  stackTerminationProtection: false,
};

export const SecretParam: inf.ISecretParam = {
  stackTerminationProtection: false,
};
