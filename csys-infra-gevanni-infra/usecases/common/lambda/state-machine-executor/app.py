import json
from logging import INFO, basicConfig, getLogger

import boto3
from mypy_boto3_stepfunctions.type_defs import StartExecutionInputRequestTypeDef
from type import SchedulerEvent

logger = getLogger(__name__)
basicConfig(level=INFO)
logger.setLevel(INFO)


def lambda_handler(event: SchedulerEvent, context):
    key = event["Id"]
    state_machine_arn = event["StateMachineArn"]
    body = event.get("Body", None)
    stepfunctions = boto3.client("stepfunctions")

    start_execution_input: StartExecutionInputRequestTypeDef = {
        "stateMachineArn": state_machine_arn,
        "name": key,
    }

    if body is not None:
        start_execution_input["input"] = json.dumps(body)

    logger.info(start_execution_input)

    try:
        stepfunctions.start_execution(**start_execution_input)
    except stepfunctions.exceptions.ExecutionAlreadyExists:
        logger.warning(f"Duplicate execution for {key} already exists")
    except Exception:
        raise
