import os
import boto3
import time


def alias_handler(event, context):
    request_type = event['RequestType']
    resource_properties = event.get("ResourceProperties")

    domain_name = resource_properties.get("DOMAIN_NAME")
    host_zone_id = resource_properties.get("HOSTED_ZONE_ID")
    sts_arn = resource_properties.get("ROLE_ARN")
    alb_dns = resource_properties.get("ALB_DNS_ID")
    alb_host_zone_id = resource_properties.get("ALB_HOSTED_ZONE_ID")

    #イベントタイプがCreateの時create_resourceを実行
    if request_type == 'Create':
        return create_resource(domain_name, host_zone_id, sts_arn, alb_dns, alb_host_zone_id)

    #イベントタイプがDeleteの時delete_resourceを実行
    if request_type == 'Delete':
        return delete_resource(domain_name, host_zone_id, sts_arn)

    #ALBのドメインを変更する場合、別サービスとしてALBを新たに作成する。
    #その為イベントタイプがupdateの時はステータスコードを返却して処理を終了する。
    if request_type == 'Update':
        return {'statusCode': 200}

def create_resource(domain_name, host_zone_id, sts_arn, alb_dns, alb_host_zone_id):
    try:
        #Aliasレコードの設定
        change_batch = {
            'Changes': [
                {
                    'Action': 'UPSERT',
                    'ResourceRecordSet': {
                        'Name': domain_name,
                        'Type': 'A',
                        'AliasTarget':{
                            'HostedZoneId': alb_host_zone_id,
                            'DNSName': alb_dns,
                            'EvaluateTargetHealth':False
                        }
                    },
                },
            ],
        }

        #一時認証取得
        sts = boto3.client('sts')
        access_info = sts.assume_role(
            RoleArn = sts_arn,
            RoleSessionName = 'cross_acct_lambda'
            )
        ACCESS_KEY = access_info['Credentials']['AccessKeyId']
        SECRET_KEY = access_info['Credentials']['SecretAccessKey']
        SESSION_TOKEN = access_info['Credentials']['SessionToken']
        route53 = boto3.client('route53',aws_access_key_id=ACCESS_KEY,aws_secret_access_key=SECRET_KEY,aws_session_token=SESSION_TOKEN,)

        #Route53にALBのAliasレコードを登録
        route53.change_resource_record_sets(HostedZoneId=host_zone_id, ChangeBatch=change_batch)

        print('Aliasレコード登録が完了しました。')
        return {'statusCode': 200, 'body': 'Aliasレコード登録が完了しました。'}

    except Exception as e:
        print(f'Error: {e}')
        return {'statusCode': 500, 'body': '処理が失敗しました。'}

def delete_resource(domain_name, host_zone_id, sts_arn):
    try:
        #一時認証取得
        sts = boto3.client('sts')
        access_info = sts.assume_role(
            RoleArn = sts_arn,
            RoleSessionName = 'cross_acct_lambda'
            )
        ACCESS_KEY = access_info['Credentials']['AccessKeyId']
        SECRET_KEY = access_info['Credentials']['SecretAccessKey']
        SESSION_TOKEN = access_info['Credentials']['SessionToken']
        route53 = boto3.client('route53',aws_access_key_id=ACCESS_KEY,aws_secret_access_key=SECRET_KEY,aws_session_token=SESSION_TOKEN,)

        #Route53からAliasレコードを削除
        record_domain_name = domain_name + '.'
        route53_respons = route53.list_resource_record_sets(HostedZoneId=host_zone_id)
        for record in route53_respons['ResourceRecordSets']:
            if record_domain_name == record['Name'] and 'A' == record['Type']:
                route53.change_resource_record_sets(
                    HostedZoneId=host_zone_id,
                    ChangeBatch={
                        'Changes': [
                            {
                                'Action': 'DELETE',
                                'ResourceRecordSet':record,
                            }
                        ]
                    }
                )

        return {'statusCode': 200, 'body': 'Aliasレコードの削除が完了しました。'}
    except Exception as e:
        print(f'Error: {e}')
        return {'statusCode': 500, 'body': 'Aliasレコード削除処理に失敗しました。'}
