import * as cdk from 'aws-cdk-lib';
import * as fs from 'fs';
import { IConfig } from '../params/interface';
import { OidcStack } from '../lib/stack/oidc-stack';
import { InfraResourcesPipelineStack } from '../lib/stack/pipeline-infraresources-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { BastionEcsCommonStack } from '../lib/stack/bastion-ecs-common-stack';
import { AcmStack } from '../lib/stack/acm-stack';
import { AlbAliasStack } from '../lib/stack/alb-aliasrecord-stack';
import { NewRelicMetricsStack } from '../lib/stack/newrelic-metrics-stack';
import { SecretStack } from '../lib/stack/secret-stack';
import { CloudMapStack } from '../lib/stack/cloudmap-stack';
import { TiDBCostMetricsStack } from '../lib/stack/tidb-cost-metrics-stack';
import { RoleForExternalTeamStack } from '../lib/stack/role-for-external-team-stack';

const app = new cdk.App();

// ----------------------- Load context variables ------------------------------
// This context need to be specified in args
const argContext = 'environment';
const envKey = app.node.tryGetContext(argContext);
if (envKey == undefined) {
  throw new Error(`Please specify environment with context option. ex) cdk deploy -c ${argContext}=dev01`);
}
//Read Typescript Environment file
const TsEnvPath = './params/' + envKey + '.ts';
if (!fs.existsSync(TsEnvPath)) throw new Error(`Can't find a ts environment file [../params/` + envKey + `.ts]`);

//ESLintではrequireの利用が禁止されているため除外コメントを追加
//https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/issues/29#issuecomment-**********
const config: IConfig = require('../params/' + envKey);

// Add envName to Stack for avoiding duplication of Stack names.
const pjPrefix = `${config.Env.envName}-${config.Env.prefix}-common`;

// ----------------------- Environment variables for stack ------------------------------
// Default environment
const procEnvDefault = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

// Define account id and region from context.
// If "env" isn't defined on the environment variable in context, use account and region specified by "--profile".
function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnvDefault;
  }
}

// ----------------------- Guest System Stacks ------------------------------

// Empty Secret Manager
const secrets = new SecretStack(app, `${pjPrefix}-Secrets`, {
  prefix: pjPrefix,
  env: getProcEnv(),
  terminationProtection: config.SecretParam.stackTerminationProtection ?? false,
});

const shareResourcesStack = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
  pjPrefix,
  isCreateChatbot: config.NotifierParam.isCreate,
  notifyEmail: config.NotifierParam.monitoringNotifyEmail,
  workspaceId: config.NotifierParam.workspaceId,
  channelId: config.NotifierParam.channelIdMon,
  myVpcCidr: config.VpcParam.cidr,
  myVpcMaxAzs: config.VpcParam.maxAzs,
  natGateways: config.VpcParam.natGateways,
  ...config.VpcForTiDBParam,
  env: getProcEnv(),
  CrossAccountRoleArn: config.lambdaIamParam.CrossAccountRoleArn,
  myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
  kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
  fluentbitRepositoryName: config.FluentbitParam.repositoryName,
  fluentbitEcrLifecycleRules: config.FluentbitParam.lifecycleRules,
  fluentbitEcrRemovalPolicyParam: config.OtherRemovalPolicyParam,
  fluentbitRepositoryDefault: config.FluentbitParam.repositoryDefault,
  regionalVpcForTiDBParam: config.RegionalVpcForTiDBParam,
  terminationProtection: config.ShareResourcesParam.stackTerminationProtection ?? false,
});

new InfraResourcesPipelineStack(app, `${pjPrefix}-Pipeline`, {
  ...config.InfraResourcesPipelineParam,
  pjPrefix,
  envKey,
  env: getProcEnv(),
  appKey: shareResourcesStack.appKey,
  pipelineBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
  newrelicSecretArn: secrets.nrSecret.secretArn,
  buildLogBucketLifecycleRules: config.s3BuildLogLifecycleRules,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  terminationProtection: config.InfraResourcesPipelineParam.stackTerminationProtection ?? false,
});

new OidcStack(app, `${pjPrefix}-OIDC`, {
  oidcProviderArn: config.OidcParam.oidcProviderArn,
  OrganizationName: config.OidcParam.OrganizationName,
  RepositoryNames: config.OidcParam.RepositoryNames,
  env: getProcEnv(),
  pjPrefix,
  terminationProtection: config.OidcParam.stackTerminationProtection ?? false,
});

new BastionEcsCommonStack(app, `${pjPrefix}-BastionEcsCommon`, {
  pjPrefix,
  vpc: shareResourcesStack.vpc.myVpc,
  alarmTopic: shareResourcesStack.alarmTopic.topic,
  repositoryName: 'bastionrepo',
  ecrLifecycleRules: config.BastionParam.lifecycleRules,
  env: getProcEnv(),
  ecrRemovalPolicyParam: config.OtherRemovalPolicyParam,
  tidbEndpoint: config.TiDBEndpoint.tidbEndpoint,
  terminationProtection: config.BastionParam.stackTerminationProtection ?? false,
});

// Lambda function create custom resources
new AcmStack(app, `${pjPrefix}-acm`, {
  prefix: pjPrefix,
  AcmFunctionlambdaRole: shareResourcesStack.lambdaRole.AcmFunctionlambdaRole,
  env: getProcEnv(),
  logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
  LambdaRuntime: config.CustomResourceAcmParam.lambdaRuntime,
  terminationProtection: config.CustomResourceAcmParam.stackTerminationProtection ?? false,
});

new AlbAliasStack(app, `${pjPrefix}-ALbAlias`, {
  AliasFunctionlambdaRole: shareResourcesStack.lambdaRole.AcmFunctionlambdaRole,
  prefix: pjPrefix,
  env: getProcEnv(),
  crossRegionReferences: true,
  logRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
  LambdaRuntime: config.CustomResourceAliasParam.lambdaRuntime,
  terminationProtection: config.CustomResourceAliasParam.stackTerminationProtection ?? false,
});

new TiDBCostMetricsStack(app, `${pjPrefix}-TiDBCostMetrics`, {
  prefix: pjPrefix,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  env: getProcEnv(),
  terminationProtection: config.TiDBCostMetricsParam.stackTerminationProtection ?? false,
});

new NewRelicMetricsStack(app, `${pjPrefix}-NewRelicMetrics`, {
  prefix: pjPrefix,
  newrelicExternalId: config.NewRelicParam.ExternalId,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  metricBucketLifecycleRules: config.s3AuditLogLifecycleRules,
  terminationProtection: config.NewRelicParam.stackTerminationProtection ?? false,
});

const cloudmap = new CloudMapStack(app, `${pjPrefix}-CloudMap`, {
  prefix: pjPrefix,
  vpc: shareResourcesStack.vpc.myVpc,
  env: getProcEnv(),
  terminationProtection: config.CloudMapParam.stackTerminationProtection ?? false,
});

cloudmap.node.addDependency(shareResourcesStack);

const roleForExternalTeam = new RoleForExternalTeamStack(app, `${pjPrefix}-RoleForExternalTeam`, {
  pjPrefix,
  ...config.RoleForExternalTeamParam,
  env: getProcEnv(),
  terminationProtection: config.RoleForExternalTeamParam.stackTerminationProtection ?? false,
});

// --------------------------------- Tagging  -------------------------------------

// Tagging "Environment" tag to all resources in this app
const envTagName = 'Environment';
cdk.Tags.of(app).add(envTagName, config.Env.envName);
