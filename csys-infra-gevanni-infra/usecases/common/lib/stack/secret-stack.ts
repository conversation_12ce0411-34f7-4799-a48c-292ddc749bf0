import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';

export interface SecretStackProps extends cdk.StackProps {
  prefix: string;
}

export class SecretStack extends cdk.Stack {
  public readonly nrSecret: secretsmanager.Secret;
  constructor(scope: Construct, id: string, props: SecretStackProps) {
    super(scope, id, props);
    //  NewRelic Secret
    const newrelicSecret = new secretsmanager.Secret(this, `NewRelicSecret`, {
      secretName: `${props.prefix}/NewRelic`,
      secretObjectValue: {},
    });

    new cdk.CfnOutput(this, `${props.prefix}NewRelicSecretArn`, {
      value: newrelicSecret.secretArn,
      description: 'ARN of the SecretManager for New Relic LisenseKey',
    });
    this.nrSecret = newrelicSecret;
  }
}
