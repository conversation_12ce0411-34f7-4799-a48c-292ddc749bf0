import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { RoleForExternalTeam } from '../construct/role-for-external-team-construct';

export interface RoleForExternalTeamStackProps extends cdk.StackProps {
  pjPrefix: string;
  externalBastionAccountId: string;
  externalTeamName: string;
}

export class RoleForExternalTeamStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: RoleForExternalTeamStackProps) {
    super(scope, id, props);

    new RoleForExternalTeam(this, `RoleFor${props.externalTeamName}Team`, {
      pjPrefix: props.pjPrefix,
      externalBastionAccountId: props.externalBastionAccountId,
      externalTeamName: props.externalTeamName,
    });
  }
}
