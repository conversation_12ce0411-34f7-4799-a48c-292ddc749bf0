import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Chatbot } from '../construct/chatbot-construct';
import { K<PERSON><PERSON><PERSON> } from '../construct/kms-key-construct';
import { Vpc } from '../construct/vpc-construct';
import { SNS } from '../construct/sns-construct';
import { VPCForTiDB } from '../construct/vpc-for-tidb-construct';
import { IRemovalPolicyParam, IVpcForTiDBParam } from '../../params/interface';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { AcmLambdaIamRole } from '../construct/lambda-iamrole-construct';
import * as kms from 'aws-cdk-lib/aws-kms';
import { LambdaLayer } from '../construct/lambda-layer-construct';
import { BatchSharedResourcesConstruct } from '../construct/batch-shared-resources-construct';
import { FluentbitDockerConstruct } from '../construct/fluentbit-docker-construct';
import * as ecr from 'aws-cdk-lib/aws-ecr';

export interface ShareResourcesStackProps extends cdk.StackProps, IVpcForTiDBParam {
  pjPrefix: string;
  isCreateChatbot: boolean;
  notifyEmail: string;
  channelId: string;
  workspaceId: string;
  myVpcCidr: string;
  myVpcMaxAzs: number;
  natGateways?: number;
  CrossAccountRoleArn: string;
  myFlowLogBucketLifecycleRule: s3.LifecycleRule[];
  logRemovalPolicyParam?: IRemovalPolicyParam;
  otherRemovalPolicyParam?: IRemovalPolicyParam;
  kmsPendingWindow?: cdk.Duration;
  fluentbitRepositoryName: string;
  fluentbitEcrLifecycleRules?: ecr.LifecycleRule[];
  fluentbitEcrRemovalPolicyParam?: {
    removalPolicy?: cdk.RemovalPolicy;
    emptyOnDelete?: boolean;
  };
  fluentbitRepositoryDefault: string;
  regionalVpcForTiDBParam?: IVpcForTiDBParam;
}

export class ShareResourcesStack extends cdk.Stack {
  public readonly vpc: Vpc;
  public readonly alarmTopic: SNS;
  public readonly appKey: kms.Key;
  public readonly lambdaRole: AcmLambdaIamRole;

  constructor(scope: Construct, id: string, props: ShareResourcesStackProps) {
    super(scope, id, props);

    //lambda execution role for Acm Function
    const lambdaRole = new AcmLambdaIamRole(this, `${props.pjPrefix}-LambdaRole`, {
      pjPrefix: props.pjPrefix,
      CrossAccountRoleArn: props.CrossAccountRoleArn,
    });
    this.lambdaRole = lambdaRole;

    const alarmTopic = new SNS(this, `${props.pjPrefix}-Alarm`, {
      notifyEmail: props.notifyEmail,
    });
    this.alarmTopic = alarmTopic;

    if (props.isCreateChatbot)
      new Chatbot(this, `${props.pjPrefix}-Chatbot`, {
        topicArn: alarmTopic.topic.topicArn,
        workspaceId: props.workspaceId,
        channelId: props.channelId,
      });

    // CMK for Apps
    const appKey = new KMSKey(this, `${props.pjPrefix}-AppKey`, {
      pjPrefix: props.pjPrefix,
      removalPolicy: props.otherRemovalPolicyParam?.removalPolicy,
      pendingWindow: props.kmsPendingWindow,
    });

    this.appKey = appKey.kmsKey;

    // Networking
    const vpc = new Vpc(this, `${props.pjPrefix}-Vpc`, {
      myVpcCidr: props.myVpcCidr,
      myVpcMaxAzs: props.myVpcMaxAzs,
      natGateways: props.natGateways,
      myFlowLogBucketLifecycleRule: props.myFlowLogBucketLifecycleRule,
      flowLogRemovalPolicyParam: props.logRemovalPolicyParam,
      kmsPendingWindow: props.kmsPendingWindow,
    });
    this.vpc = vpc;

    // Get Private Subnet CIDR Blocks
    const privateSubnets = vpc.myVpc.selectSubnets({
      subnetGroupName: 'Private',
    }).subnets;
    const privateSubnetCidrBlocks = privateSubnets.map((subnet) => subnet.ipv4CidrBlock);

    // VPC for TiDB
    const vpcForTidb = new VPCForTiDB(this, `${props.pjPrefix}-VPCForTiDB`, {
      vpc: vpc.myVpc,
      allowCidrBlocks: privateSubnetCidrBlocks,
      tidbEndpointServiceName: props.tidbEndpointServiceName,
      tidbEndpointServicePort: props.tidbEndpointServicePort,
      tidbClusterAvailabilityZones: props.tidbClusterAvailabilityZones,
    });

    // Regional VPC for TiDB - only create if regionalVpcForTiDBParam is provided
    if (props.regionalVpcForTiDBParam) {
      const regionalVpcForTidb = new VPCForTiDB(this, `${props.pjPrefix}-Regional-VPCForTiDB`, {
        vpc: vpc.myVpc,
        allowCidrBlocks: privateSubnetCidrBlocks,
        tidbEndpointServiceName: props.regionalVpcForTiDBParam.tidbEndpointServiceName,
        tidbEndpointServicePort: props.regionalVpcForTiDBParam.tidbEndpointServicePort,
        tidbClusterAvailabilityZones: props.regionalVpcForTiDBParam.tidbClusterAvailabilityZones,
      });

      new cdk.CfnOutput(this, 'RegionalTiDBVpcEndpointSgId', {
        value: regionalVpcForTidb.securityGroup.securityGroupId,
        description: 'The security group ID of the Regional HA TiDB VPC endpoint',
        exportName: `${props.pjPrefix}-Regional-TiDBVpcEndpointSgId`,
      });

      new ssm.StringParameter(this, 'SSMRegionalTiDBVpcEndpointSgId', {
        parameterName: `/${props.pjPrefix}/RegionalTiDBVpcEndpointSgId`,
        stringValue: regionalVpcForTidb.securityGroup.securityGroupId,
      });
    }

    // S3 for ecspresso master files
    const confMasterBucket = new s3.Bucket(this, 'ConfMasterBucket', {
      versioned: true,
      removalPolicy: props.otherRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.otherRemovalPolicyParam?.autoDeleteObjects ?? false,
    });

    // S3 for batch master files
    const batchMasterBucket = new s3.Bucket(this, 'BatchMasterBucket', {
      versioned: true,
      removalPolicy: props.otherRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.otherRemovalPolicyParam?.autoDeleteObjects ?? false,
    });

    // Commonly used LambdaLayer
    const lambdaLayer = new LambdaLayer(this, 'LambdaLayer', {
      pjPrefix: props.pjPrefix,
    });

    const { stateMachineExecutor, eventBridgeServiceRole } = new BatchSharedResourcesConstruct(
      this,
      'BatchSharedResources',
    );

    // Flentbit Image container
    const fluentbit = new FluentbitDockerConstruct(this, 'FluentbitDocker', {
      repositoryName: props.fluentbitRepositoryName,
      ecrLifecycleRules: props.fluentbitEcrLifecycleRules,
      prefix: props.pjPrefix,
      ecrRemovalPolicyParam: props.fluentbitEcrRemovalPolicyParam,
      repositoryDefault: props.fluentbitRepositoryDefault,
    });

    // Sharing output
    new cdk.CfnOutput(this, 'VpcId', {
      key: 'vpcId',
      value: vpc.myVpc.vpcId,
      exportName: `${props.pjPrefix}-VpcId`,
    });

    new cdk.CfnOutput(this, 'TiDBVpcEndpointSgId', {
      value: vpcForTidb.securityGroup.securityGroupId,
      description: 'The security group ID of the TiDB VPC endpoint',
      exportName: `${props.pjPrefix}-TiDBVpcEndpointSgId`,
    });

    new cdk.CfnOutput(this, 'AlarmTopicArn', {
      key: 'alarmTopicArn',
      value: alarmTopic.topic.topicArn,
      exportName: `${props.pjPrefix}-AlarmTopicArn`,
    });

    new cdk.CfnOutput(this, 'AppKeyArn', {
      key: 'appKeyArn',
      value: appKey.kmsKey.keyArn,
      exportName: `${props.pjPrefix}-AppKeyArn`,
    });

    new cdk.CfnOutput(this, 'FluentbitEcrRepositoryUri', {
      key: 'FluentbitEcrRepositoryUri',
      value: fluentbit.ecrRepository.repositoryUri,
      exportName: `${props.pjPrefix}-FluentbitEcrRepositoryUri`,
    });

    new ssm.StringParameter(this, 'SSMConfMasterBucketName', {
      parameterName: `/${props.pjPrefix}/ConfMasterBucketName`,
      stringValue: confMasterBucket.bucketName,
    });

    new ssm.StringParameter(this, 'SSMBatchMasterBucketName', {
      parameterName: `/${props.pjPrefix}/BatchMasterBucketName`,
      stringValue: batchMasterBucket.bucketName,
    });

    new cdk.CfnOutput(this, 'AcmFunctionLambdaRoleArn', {
      key: 'AcmFunctionLambdaRoleArn',
      value: lambdaRole.AcmFunctionlambdaRole.roleArn,
      exportName: `${props.pjPrefix}-AcmFunctionLambdaRoleArn`,
    });

    new ssm.StringParameter(this, 'SSMVpcId', {
      parameterName: `/${props.pjPrefix}/vpcId`,
      stringValue: vpc.myVpc.vpcId,
    });

    new ssm.StringParameter(this, 'SSMTiDBVpcEndpointSgId', {
      parameterName: `/${props.pjPrefix}/TiDBVpcEndpointSgId`,
      stringValue: vpcForTidb.securityGroup.securityGroupId,
    });

    // Private Subnet to SSM Parameter store（run_task.shが参照）
    new ssm.StringParameter(this, 'SSMPrivateSubnetId', {
      parameterName: `/${props.pjPrefix}/privateSubnetId`,
      stringValue: vpc.myVpc.selectSubnets({ subnetGroupName: 'Private' }).subnetIds[0],
    });

    new ssm.StringParameter(this, 'SSMAlarmTopicArn', {
      parameterName: `/${props.pjPrefix}/alarmTopicArn`,
      stringValue: alarmTopic.topic.topicArn,
    });

    new ssm.StringParameter(this, 'SSMAppKeyArn', {
      parameterName: `/${props.pjPrefix}/appKeyArn`,
      stringValue: appKey.kmsKey.keyArn,
    });

    new ssm.StringParameter(this, 'SSMAcmFunctionLambdaRoleArn', {
      parameterName: `/${props.pjPrefix}/AcmFunctionLambdaRoleArn`,
      stringValue: lambdaRole.AcmFunctionlambdaRole.roleArn,
    });

    new ssm.StringParameter(this, 'SSMNewRelicLayerArn', {
      parameterName: `/${props.pjPrefix}/NewRelicLayerArn`,
      stringValue: lambdaLayer.nrLayer.layerVersionArn,
    });

    new ssm.StringParameter(this, 'SSMStateMachineExecutorArn', {
      parameterName: `/${props.pjPrefix}/lambda/stateMachineExecutorArn`,
      stringValue: stateMachineExecutor.functionArn,
    });

    new ssm.StringParameter(this, 'SSMEventBridgeServiceRoleArn', {
      parameterName: `/${props.pjPrefix}/role/eventBridgeServiceRoleArn`,
      stringValue: eventBridgeServiceRole.roleArn,
    });
    new ssm.StringParameter(this, 'SSMFluentbitEcrRepositoryUri', {
      parameterName: `/${props.pjPrefix}/fluentbit/ecr/repository-uri`,
      stringValue: fluentbit.ecrRepository.repositoryUri,
    });
  }
}
