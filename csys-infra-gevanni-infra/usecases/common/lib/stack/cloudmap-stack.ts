import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import { aws_ec2 as ec2 } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface CloudMapStackProps extends cdk.StackProps {
  prefix: string;
  vpc: ec2.Vpc;
}

export class CloudMapStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: CloudMapStackProps) {
    super(scope, id, props);

    const namespace = new servicediscovery.PrivateDnsNamespace(this, `${props.prefix}-namespace`, {
      name: `${props.prefix}`,
      vpc: props.vpc,
    });

    new ssm.StringParameter(this, 'NamespaceArn', {
      parameterName: `/${props.prefix}/servicediscovery/NamespaceArn`,
      stringValue: namespace.namespaceArn,
    });

    new ssm.StringParameter(this, 'NamespaceId', {
      parameterName: `/${props.prefix}/servicediscovery/NamespaceId`,
      stringValue: namespace.namespaceId,
    });

    new ssm.StringParameter(this, 'NamespaceName', {
      parameterName: `/${props.prefix}/servicediscovery/NamespaceName`,
      stringValue: namespace.namespaceName,
    });
  }
}
