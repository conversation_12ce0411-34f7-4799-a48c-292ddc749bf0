import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { CustomResourceAcmConstruct } from '../construct/lambda-customresource-acm-construct';
import { IRole } from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as lambda from 'aws-cdk-lib/aws-lambda';

export interface AcmProps extends cdk.StackProps {
  prefix: string;
  AcmFunctionlambdaRole: IRole;
  logRemovalPolicy?: cdk.RemovalPolicy;
  LambdaRuntime: lambda.Runtime;
}

export class AcmStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: AcmProps) {
    super(scope, id, props);

    // CustomResourceログ格納場所
    const LambdaLogGroup = new logs.LogGroup(this, 'function-logs', {
      logGroupName: `/aws/lambda/${props.prefix}/ACM-functions-log`,
      retention: logs.RetentionDays.ONE_YEAR,
      removalPolicy: props.logRemovalPolicy ?? cdk.RemovalPolicy.DESTROY,
    });

    new CustomResourceAcmConstruct(this, `${props.prefix}-CustomResource-Acm`, {
      prefix: props.prefix,
      AcmFunctionlambdaRole: props.AcmFunctionlambdaRole,
      LambdaLogGroup: LambdaLogGroup,
      LambdaRuntime: props.LambdaRuntime,
    });
  }
}
