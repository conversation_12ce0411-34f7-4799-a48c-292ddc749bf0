import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { BastionEcsCommonConstruct } from '../construct/bastion-ecs-common-construct';
import { IRemovalPolicyParam } from '../../params/interface';
export interface BastionEcsCommonStackProps extends cdk.StackProps {
  readonly pjPrefix: string;
  readonly vpc: ec2.Vpc;
  readonly alarmTopic: sns.Topic;
  readonly repositoryName: string;
  readonly ecrLifecycleRules?: ecr.LifecycleRule[];
  readonly ecrRemovalPolicyParam?: IRemovalPolicyParam;
  readonly tidbEndpoint: string;
}

export class BastionEcsCommonStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: BastionEcsCommonStackProps) {
    super(scope, id, props);

    const bastionEcsCommon = new BastionEcsCommonConstruct(this, 'BastionEcsCommonConstruct', {
      vpc: props.vpc,
      alarmTopic: props.alarmTopic,
      repositoryName: props.repositoryName,
      ecrLifecycleRules: props.ecrLifecycleRules,
      prefix: props.pjPrefix,
      ecrRemovalPolicyParam: props.ecrRemovalPolicyParam,
    });

    // Sharing output
    new cdk.CfnOutput(this, 'BastionEcrRepositoryName', {
      key: 'bastionEcrRepositoryName',
      value: bastionEcsCommon.ecrRepository.repositoryName,
      exportName: `${props.pjPrefix}-BastionEcrRepositoryName`,
    });

    new cdk.CfnOutput(this, 'BastionEcsClusterName', {
      key: 'bastionEcsClusterName',
      value: bastionEcsCommon.ecsCluster.clusterName,
      exportName: `${props.pjPrefix}-BastionEcsClusterName`,
    });

    new cdk.CfnOutput(this, 'BastionEcsTaskExecutionRoleArn', {
      key: 'bastionEcsTaskExecutionRoleArn',
      value: bastionEcsCommon.ecsTaskExecutionRole.roleArn,
      exportName: `${props.pjPrefix}-BastionEcsTaskExecutionRoleArn`,
    });

    new ssm.StringParameter(this, 'BastionEcrRepositoryNameParameter', {
      parameterName: `/${props.pjPrefix}/bastion/ecr/repository-name`,
      stringValue: bastionEcsCommon.ecrRepository.repositoryName,
    });

    new ssm.StringParameter(this, 'BastionEcsClusterNameParameter', {
      parameterName: `/${props.pjPrefix}/bastion/ecs/cluster-name`,
      stringValue: bastionEcsCommon.ecsCluster.clusterName,
    });

    new ssm.StringParameter(this, 'BastionEcsTaskExecutionRoleArnParameter', {
      parameterName: `/${props.pjPrefix}/bastion/ecs/task-execution-role-arn`,
      stringValue: bastionEcsCommon.ecsTaskExecutionRole.roleArn,
    });

    new ssm.StringParameter(this, 'BastionTiDBEndpointParameter', {
      parameterName: `/${props.pjPrefix}/bastion/tidb/endpoint`,
      stringValue: props.tidbEndpoint,
    });
  }
}
