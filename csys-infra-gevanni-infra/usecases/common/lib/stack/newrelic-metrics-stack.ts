import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { CwMetricStreams } from '../construct/cw-metric-streams-construct';
import { DataFirehose } from '../construct/data-firehose-construct';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';

interface NewRelicMetricsStackProps extends cdk.StackProps {
  prefix: string;
  newrelicExternalId: string;
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  metricBucketLifecycleRules: s3.LifecycleRule[];
}

export class NewRelicMetricsStack extends cdk.Stack {
  public readonly lambdaFunction: lambda.Function;
  constructor(scope: Construct, id: string, props: NewRelicMetricsStackProps) {
    super(scope, id, props);
    // ######## Secret Manager ############
    // データ連携用NewRelicアカウントのLicenseキー
    const newrelicCommonSecret = new secretsmanager.Secret(this, 'NewRelicCommonSecret', {
      secretName: `${props.prefix}/NewRelicCommon`,
      secretObjectValue: {},
    });

    // ######## CloudWatch Metrics ########
    const metricFirehose = new DataFirehose(this, 'MetricFirehose', {
      firehoseStreamName: `${props.prefix}-Metric-Stream`,
      firehoseLogGroupName: `/aws/firehose/${props.prefix}/metrics`,
      httpEndpointUrl: 'https://aws-api.newrelic.com/cloudwatch-metrics/v1',
      httpEndpointName: 'New Relic',
      s3BackupMode: 'FailedDataOnly',
      secretArn: newrelicCommonSecret.secretArn,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      firehoseBucketLifecycleRules: props.metricBucketLifecycleRules,
    });

    new CwMetricStreams(this, 'CwMetricStreams', {
      prefix: props.prefix,
      firehoseStream: metricFirehose.stream,
    });

    const newrelicRole = new iam.Role(this, 'NewRelicRole', {
      // new relicのAWSアカウントIDは固定
      assumedBy: new iam.AccountPrincipal('************').withConditions({
        StringEquals: { 'sts:ExternalId': props.newrelicExternalId },
      }),
      roleName: `${props.prefix}-NewRelicInfrastructure-Integrations`, // new relic推奨のIAMロール名
    });

    newrelicRole.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName('ReadOnlyAccess'));

    new cdk.CfnOutput(this, 'NewRelicInfrastructureIntegrationsRoleArn', {
      value: newrelicRole.roleArn,
      description: 'ARN of the IAM Role for New Relic AWS Metric Streams',
    });
  }
}
