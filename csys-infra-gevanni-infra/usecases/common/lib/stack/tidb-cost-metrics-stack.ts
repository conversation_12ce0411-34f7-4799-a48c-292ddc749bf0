import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { LambdaTidbCostMetricsConstruct } from '../construct/lambda-tidb-cost-metrics-construct';
import { IRemovalPolicyParam } from 'params/interface';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';

export interface TiDBCostMetricsStackProps extends cdk.StackProps {
  /**
   * Project and environment prefix
   */
  prefix: string;
  /**
   * Log Removal Policy Param
   */
  logRemovalPolicyParam?: IRemovalPolicyParam;
}

export class TiDBCostMetricsStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: TiDBCostMetricsStackProps) {
    super(scope, id, props);

    // TiDB Secret
    const tidbCommonSecret = new secretsmanager.Secret(this, `TiDBCommonSecret`, {
      secretName: `${props.prefix}/TiDB`,
      secretObjectValue: {},
    });

    new LambdaTidbCostMetricsConstruct(this, 'LambdaTidbCostMetrics', {
      prefix: props.prefix,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy,
      tidbSecretArn: tidbCommonSecret.secretArn,
    });
  }
}
