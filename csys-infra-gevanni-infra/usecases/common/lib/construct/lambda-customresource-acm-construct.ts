import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { IRole } from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as cr from 'aws-cdk-lib/custom-resources';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface CustomResourceAcmStackProps {
  prefix: string;
  AcmFunctionlambdaRole: IRole;
  LambdaLogGroup: logs.LogGroup;
  LambdaRuntime: lambda.Runtime;
}

export class CustomResourceAcmConstruct extends Construct {
  constructor(scope: Construct, id: string, props: CustomResourceAcmStackProps) {
    super(scope, id);

    //lambda作成
    const lambdaFunction = new lambda.Function(this, 'function', {
      functionName: `${props.prefix}-ACM-functions`,
      runtime: props.LambdaRuntime,
      handler: 'AlbAcm.acm_handler',
      code: lambda.Code.fromAsset('lambda/CustomResource/Acm'),
      logGroup: props.LambdaLogGroup,
      role: props.AcmFunctionlambdaRole,
      timeout: cdk.Duration.seconds(300),
    });

    // カスタムリソースプロバイダーを作成
    const provider = new cr.Provider(this, 'Provider', {
      onEventHandler: lambdaFunction,
    });

    //SSMパラメータストアにAcmServiceTokenを格納
    new ssm.StringParameter(this, 'AcmServiceToken', {
      parameterName: `/${props.prefix}/AcmServiceToken`,
      stringValue: provider.serviceToken,
    });
  }
}
