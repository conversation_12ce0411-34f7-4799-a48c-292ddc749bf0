import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';

interface LambdaLayerProps {
  pjPrefix: string;
}

export class LambdaLayer extends Construct {
  public readonly nrLayer: lambda.LayerVersion;

  constructor(scope: Construct, id: string, props: LambdaLayerProps) {
    super(scope, id);

    const nrLayer = new lambda.LayerVersion(this, 'NewRelicLayer', {
      layerVersionName: `${props.pjPrefix}-NewRelicLayer`,
      code: lambda.Code.fromAsset('lambda/layers/NewRelic', {
        bundling: {
          image: cdk.DockerImage.fromRegistry('public.ecr.aws/sam/build-python3.12'),
          command: [
            'bash',
            '-c',
            'pip install -r requirements.txt -t /asset-output/python && cp -au . /asset-output/python',
          ],
        },
      }),
      compatibleRuntimes: [lambda.Runtime.PYTHON_3_12],
    });
    this.nrLayer = nrLayer;
  }
}
