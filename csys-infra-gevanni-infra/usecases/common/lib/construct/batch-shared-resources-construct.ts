import * as cdk from 'aws-cdk-lib';
import { Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as path from 'path';

export class BatchSharedResourcesConstruct extends Construct {
  public readonly stateMachineExecutor: lambda.IFunction;
  public readonly eventBridgeServiceRole: iam.IRole;

  constructor(scope: Construct, id: string) {
    super(scope, id);

    const latestRuntime = lambda.Runtime.PYTHON_3_12;
    const lambdaFunction = new lambda.Function(this, 'StateMachineExecutor', {
      runtime: latestRuntime,
      handler: 'app.lambda_handler',
      code: lambda.Code.fromAsset(path.join(__dirname, `../../lambda/state-machine-executor`), {
        bundling: {
          image: latestRuntime.bundlingImage,
          command: ['bash', '-c', 'pip install -r requirements.txt -t /asset-output && cp -au . /asset-output'],
        },
      }),
      role: new iam.Role(this, 'StateMachineExecutorRole', {
        assumedBy: new ServicePrincipal('lambda.amazonaws.com'),
        managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        inlinePolicies: {
          stateMachineExecutionPolicy: new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                actions: ['states:StartExecution'],
                resources: ['*'],
              }),
            ],
          }),
        },
      }),
    });

    const eventBridgeSchedulerServiceRole = new Role(this, 'EventBridgeSchedulerServiceRole', {
      assumedBy: new ServicePrincipal('scheduler.amazonaws.com', {
        conditions: {
          StringEquals: { 'aws:SourceAccount': `${cdk.Stack.of(this).account}` },
        },
      }),
      path: '/service-role/',
    });

    lambdaFunction.grantInvoke(eventBridgeSchedulerServiceRole);
    this.stateMachineExecutor = lambdaFunction;
    this.eventBridgeServiceRole = eventBridgeSchedulerServiceRole;
  }
}
