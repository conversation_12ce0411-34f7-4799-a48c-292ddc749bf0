import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';
import { IVpcForTiDBParam } from '../../params/interface';

interface VPCForTiDBProps extends IVpcForTiDBParam {
  /**
   * The VPC.
   */
  readonly vpc: ec2.IVpc;
  /**
   * The CIDR blocks that are allowed to access the TiDB VPC endpoint.
   */
  readonly allowCidrBlocks: string[];
}

export class VPCForTiDB extends Construct {
  public readonly securityGroup: ec2.SecurityGroup;

  constructor(scope: Construct, id: string, props: VPCForTiDBProps) {
    super(scope, id);

    // Create a security group for TiDB VPC endpoint
    this.securityGroup = new ec2.SecurityGroup(this, 'SecurityGroup', {
      vpc: props.vpc,
      description: 'Security group for TiDB VPC endpoint',
    });
    // Allow traffic to TiDB VPC endpoint
    props.allowCidrBlocks.forEach((cidrBlock) => {
      this.securityGroup.addIngressRule(
        ec2.Peer.ipv4(cidrBlock),
        ec2.Port.tcp(props.tidbEndpointServicePort || 4000),
        'Allow traffic to TiDB VPC endpoint',
      );
    });

    // Create a VPC endpoint for TiDB
    const vpcEndpoint = new ec2.InterfaceVpcEndpoint(this, 'TiDBVpcEndpoint', {
      vpc: props.vpc,
      service: {
        name: props.tidbEndpointServiceName,
        port: props.tidbEndpointServicePort || 4000,
      },
      securityGroups: [this.securityGroup],
      subnets: {
        subnetGroupName: 'Private',
        availabilityZones: props.tidbClusterAvailabilityZones,
      },
    });

    vpcEndpoint.node.addDependency(props.vpc);
  }
}
