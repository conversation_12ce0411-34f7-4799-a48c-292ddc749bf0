import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as s3 from 'aws-cdk-lib/aws-s3';

interface DataFirehoseProps {
  firehoseStreamName: string;
  firehoseLogGroupName: string;
  httpEndpointUrl: string;
  httpEndpointName: string;
  s3BackupMode: 'AllData' | 'FailedDataOnly';
  logBucket?: s3.Bucket;
  secretArn: string;
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  firehoseBucketLifecycleRules: s3.LifecycleRule[];
}

export class DataFirehose extends Construct {
  public readonly stream: firehose.CfnDeliveryStream;
  public readonly bucket: s3.Bucket;
  constructor(scope: Construct, id: string, props: DataFirehoseProps) {
    super(scope, id);
    let firehoseLogBucket: s3.Bucket;
    if (props.logBucket) {
      firehoseLogBucket = props.logBucket;
    } else {
      firehoseLogBucket = new s3.Bucket(this, 'FirehoseLogBucket', {
        accessControl: s3.BucketAccessControl.PRIVATE,
        encryption: s3.BucketEncryption.S3_MANAGED,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
        autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
        enforceSSL: true,
        lifecycleRules: props.firehoseBucketLifecycleRules,
      });
    }
    this.bucket = firehoseLogBucket;

    const firehoseRole = new iam.Role(this, 'FirehoseRole', {
      assumedBy: new iam.ServicePrincipal('firehose.amazonaws.com'),
    });

    firehoseRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['s3:PutObject', 's3:GetBucketLocation'],
        resources: [firehoseLogBucket.bucketArn, firehoseLogBucket.bucketArn + '/*'],
      }),
    );

    firehoseRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['secretsmanager:GetSecretValue'],
        resources: [props.secretArn],
      }),
    );

    const cwlogGroup = new logs.LogGroup(this, 'CwLogGroup', {
      logGroupName: props.firehoseLogGroupName,
      retention: logs.RetentionDays.THREE_MONTHS,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    firehoseRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ['logs:PutLogEvents'],
        resources: [`${cwlogGroup.logGroupArn}:*`],
      }),
    );

    const cwlogStream = new logs.LogStream(this, 'CwLogStream', {
      logGroup: cwlogGroup,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });

    const firehoseStream = new firehose.CfnDeliveryStream(this, 'FirehoseStream', {
      deliveryStreamName: props.firehoseStreamName,
      deliveryStreamType: 'DirectPut',
      httpEndpointDestinationConfiguration: {
        roleArn: firehoseRole.roleArn,
        endpointConfiguration: {
          url: props.httpEndpointUrl,
          name: props.httpEndpointName,
        },
        bufferingHints: {
          intervalInSeconds: 60,
          sizeInMBs: 1,
        },
        retryOptions: {
          durationInSeconds: 60,
        },
        s3BackupMode: props.s3BackupMode,
        s3Configuration: {
          roleArn: firehoseRole.roleArn,
          bucketArn: firehoseLogBucket.bucketArn,
          compressionFormat: 'GZIP',
        },
        requestConfiguration: {
          contentEncoding: 'GZIP',
        },
        cloudWatchLoggingOptions: {
          enabled: true,
          logGroupName: cwlogGroup.logGroupName,
          logStreamName: cwlogStream.logStreamName,
        },
        secretsManagerConfiguration: {
          enabled: true,
          roleArn: firehoseRole.roleArn,
          secretArn: props.secretArn,
        },
      },
    });
    this.stream = firehoseStream;
  }
}
