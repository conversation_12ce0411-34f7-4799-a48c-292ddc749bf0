import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface RoleForExternalTeamProps {
  /**
   * The prefix of the project
   */
  readonly pjPrefix: string;
  /**
   * The bastion account ID
   */
  readonly externalBastionAccountId: string;
  /**
   * The Team name of the external team
   */
  readonly externalTeamName: string;
}

export class RoleForExternalTeam extends Construct {
  constructor(scope: Construct, id: string, props: RoleForExternalTeamProps) {
    super(scope, id);

    const inlinePolicies = {
      'allow-ecs-force-new-deployment': new iam.PolicyDocument({
        statements: [
          new iam.PolicyStatement({
            actions: ['ecs:UpdateService'],
            resources: ['*'],
          }),
        ],
      }),
    };

    const roleForExternal = new iam.Role(this, `${props.pjPrefix}-RoleFor${props.externalTeamName}`, {
      roleName: `${props.pjPrefix}-RoleFor${props.externalTeamName}`,
      assumedBy: new iam.AccountPrincipal(props.externalBastionAccountId).withConditions({
        Bool: {
          'aws:MultiFactorAuthPresent': true,
        },
      }),
      inlinePolicies,
      managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('ReadOnlyAccess')],
    });

    new cdk.CfnOutput(this, `${props.pjPrefix}-RoleFor${props.externalTeamName}Arn`, {
      value: roleForExternal.roleArn,
      exportName: `${props.pjPrefix}-RoleFor${props.externalTeamName}Arn`,
    });
  }
}
