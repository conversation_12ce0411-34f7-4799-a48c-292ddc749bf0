import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { IRole } from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as cr from 'aws-cdk-lib/custom-resources';
import * as ssm from 'aws-cdk-lib/aws-ssm';

export interface CustomResourceAliasStackProps {
  prefix: string;
  AliasFunctionlambdaRole: IRole;
  LambdaLogGroup: logs.LogGroup;
  LambdaRuntime: lambda.Runtime;
}

export class CustomResourceAliasConstruct extends Construct {
  constructor(scope: Construct, id: string, props: CustomResourceAliasStackProps) {
    super(scope, id);

    //lambda作成
    const lambdaFunction = new lambda.Function(this, 'function', {
      functionName: `${props.prefix}-Al<PERSON>-<PERSON><PERSON>`,
      runtime: props.LambdaRuntime,
      handler: 'AlbAliasRecode.alias_handler',
      code: lambda.Code.fromAsset('lambda/CustomResource/Alias'),
      logGroup: props.LambdaLogGroup,
      role: props.AliasFunctionlambdaRole,
      timeout: cdk.Duration.seconds(300),
    });

    // カスタムリソースプロバイダーを作成
    const provider = new cr.Provider(this, 'Provider', {
      onEventHandler: lambdaFunction,
    });

    //SSMパラメータストアにAliasServiceTokenを格納
    new ssm.StringParameter(this, 'AliasServiceToken', {
      parameterName: `/${props.prefix}/AliasServiceToken`,
      stringValue: provider.serviceToken,
    });
  }
}
