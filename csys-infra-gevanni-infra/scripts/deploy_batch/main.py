import json
import logging
import os
import re
import tomllib
from pathlib import Path
from typing import Literal, cast

import boto3
import jinja2
from mypy_boto3_ecs import ECSClient
from mypy_boto3_ecs.type_defs import RegisterTaskDefinitionRequestTypeDef
from mypy_boto3_scheduler.client import EventBridgeSchedulerClient
from mypy_boto3_scheduler.type_defs import (
    CreateScheduleInputRequestTypeDef,
    UpdateScheduleInputRequestTypeDef,
)
from mypy_boto3_scheduler.type_defs import (
    TagTypeDef as SchedulerTagTypeDef,
)
from mypy_boto3_stepfunctions import SFNClient
from mypy_boto3_stepfunctions.type_defs import TagTypeDef
from type import Config, Cron, Input, Triggers

# ----- logging settings -----
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
logger.setLevel(logging.INFO)
# --------------------------

# ----- environment variables -----
env_vars: dict[str, str | None] = {
    "AWS_REGION": os.getenv("AWS_REGION"),
    "EXECUTION_ROLE_ARN": os.getenv("EXECUTION_ROLE_ARN"),
    "CLUSTER_NAME": os.getenv("CLUSTER_NAME"),
    "SUBNET_ID_0": os.getenv("SUBNET_ID_0"),
    "SUBNET_ID_1": os.getenv("SUBNET_ID_1"),
    "SUBNET_ID_2": os.getenv("SUBNET_ID_2"),
    "SECURITY_GROUP_ID": os.getenv("SECURITY_GROUP_ID"),
    "STATE_MACHINE_ROLE_ARN": os.getenv("STATE_MACHINE_ROLE_ARN"),
    "STATE_MACHINE_EXECUTOR_ARN": os.getenv("STATE_MACHINE_EXECUTOR_ARN"),
    "STATE_MACHINE_EXECUTOR_ROLE_ARN": os.getenv("STATE_MACHINE_EXECUTOR_ROLE_ARN"),
    "BATCH_NAME": os.getenv("BATCH_NAME"),
    "LOG_GROUP": os.getenv("LOG_GROUP"),
    "GEVANNI_SERVICE_NAME": os.getenv("GEVANNI_SERVICE_NAME"),
    "FIRELENS_IMAGE_URI": os.getenv("FIRELENS_IMAGE_URI"),
    "LOG_GROUP_FIRELENS": os.getenv("LOG_GROUP_FIRELENS"),
    "STREAM_NAME": os.getenv("STREAM_NAME"),
    "REST_API_ID": os.getenv("REST_API_ID"),
    "ROOT_RESOURCE_ID": os.getenv("ROOT_RESOURCE_ID"),
    "STATE_MACHINE_EXECUTOR_ARN_FOR_API": os.getenv("STATE_MACHINE_EXECUTOR_ARN_FOR_API"),
    "API_ROLE": os.getenv("API_ROLE"),
    "BATCH_TRIGGER_BODY_MODEL": os.getenv("BATCH_TRIGGER_BODY_MODEL"),
    "API_STAGE_NAME": os.getenv("API_STAGE_NAME"),
    "SNS_TOPIC_ARN": os.getenv("SNS_TOPIC_ARN"),
}
# ---------------------------------

# ----- boto3 client settings -----
# dict.get の第2引数を指定しても型推論では str とならないため短絡評価を行う
region: str = env_vars.get("AWS_REGION") or "ap-northeast-1"
sfn = boto3.client("stepfunctions", region_name=region)
# ---------------------------------


class AwsCron:
    minute: str = "*"
    hour: str = "*"
    date: str = "*"
    month: str = "*"
    dayOfWeek: str = "*"
    year: str = "*"

    def __init__(self, cron: Cron) -> None:
        for key, value in cron.items():
            setattr(self, key, value)
        if self.date == "*":
            self.dayOfWeek = "?"

    def to_string(self):
        return f"{self.minute} {self.hour} {self.date} {self.month} {self.dayOfWeek} {self.year}"


def render_template(template_path: Path, props: dict):
    with open(template_path) as f:
        text: str = f.read()
    template = jinja2.Template(text)
    rendered: str = template.render(props)

    return json.loads(rendered)


def tag_resource(
    client: ECSClient | SFNClient | EventBridgeSchedulerClient, resource_arn: str, additional_tags: list[TagTypeDef]
):
    current_tags: list[TagTypeDef]
    # EventBridgeSchedulerClient はタグに関して ECSClient と SFNClient と同様のメソッドを持つが引数が異なる
    match client.meta.service_model.service_name:
        case "ecs":
            current_tags = cast(ECSClient, client).list_tags_for_resource(resourceArn=resource_arn)["tags"]
        case "stepfunctions":
            current_tags = cast(SFNClient, client).list_tags_for_resource(resourceArn=resource_arn)["tags"]
        case "scheduler":
            scheduler_tags: list[SchedulerTagTypeDef] = cast(EventBridgeSchedulerClient, client).list_tags_for_resource(
                ResourceArn=resource_arn
            )["Tags"]
            # SchedulerTagTypeDef は、キーの先頭文字が大文字になっているため、TagTypeDef に変換する
            current_tags = [{"key": tag["Key"], "value": tag["Value"]} for tag in scheduler_tags]
        case _:
            # この分岐は実行されないが、念のためエラーを出す
            raise ValueError(f"Unsupported service name: {client.meta.service_model.service_name}")

    # TagTypeDef の値をハッシュ化可能な形式にするため、tuple に変換して重複を除去
    tags_set: set[tuple[tuple[str, object], ...]] = {tuple(dict_.items()) for dict_ in current_tags + additional_tags}

    # 元に戻すとき、各要素の型は dict だが、正確には TagTypeDef であるため、キャストする
    tags: list[TagTypeDef] = [cast(TagTypeDef, dict(elem)) for elem in tags_set]

    match client.meta.service_model.service_name:
        case "ecs":
            cast(ECSClient, client).tag_resource(resourceArn=resource_arn, tags=tags)
        case "stepfunctions":
            cast(SFNClient, client).tag_resource(resourceArn=resource_arn, tags=tags)
        case "scheduler":
            # タグを取得したときと逆に、SchedulerTagTypeDef に変換してから渡す
            scheduler_tags = [{"Key": tag["key"], "Value": tag["value"]} for tag in tags]
            cast(EventBridgeSchedulerClient, client).tag_resource(ResourceArn=resource_arn, Tags=scheduler_tags)
        case _:
            # この分岐は実行されないが、念のためエラーを出す
            raise ValueError(f"Unsupported service name: {client.meta.service_model.service_name}")


def deploy_task_definition(
    service_name: str, batch_name: str, file_path: Path, param: dict[str, object], pj_prefix: str
):
    ecs = boto3.client("ecs", region_name=region)

    task_definition_name_max_length = 255
    # 拡張子を除いたファイル名をタスク定義名とする。
    # タスク定義名には、英数字、アンダースコア、ハイフン以外の文字は使用できない
    task_definition_name: str = re.sub(r"[^\w-]", "_", file_path.stem)[:task_definition_name_max_length]
    rendered: RegisterTaskDefinitionRequestTypeDef = render_template(
        file_path, {**env_vars, "family": task_definition_name, **param}
    )

    task_definition_arn: str = ecs.register_task_definition(**rendered)["taskDefinition"]["taskDefinitionArn"]
    additional_tags: list[TagTypeDef] = [
        {"key": "ServiceName", "value": service_name},
        {"key": "BatchName", "value": batch_name},
        {"key": "CmBillingGroup", "value": pj_prefix},
    ]
    tag_resource(ecs, task_definition_arn, additional_tags)
    logger.info(f"Registered task definition: {task_definition_arn}")

    return task_definition_arn


def deploy_state_machine(
    batch_name: str,
    task_definitions: dict[str, str],
    tasks: dict[str, str],
    container_overrides_postions: dict[str, int],
):
    file_path: Path = (Path(__file__).parent / f"{batch_name}.asl.json").resolve()
    state_machine_definition: dict = render_template(
        file_path, {**env_vars, **task_definitions, **tasks, **container_overrides_postions}
    )

    index = json.dumps(state_machine_definition).find('"PropagateTags": "TASK_DEFINITION"')
    if index == -1:
        raise ValueError("PropagateTags is not found in state machine definition or that value is not TASK_DEFINITION.")

    account_id: str = boto3.client("sts").get_caller_identity()["Account"]
    state_machine_name = f"{env_vars['GEVANNI_SERVICE_NAME']}-{batch_name}"
    state_machine_arn = f"arn:aws:states:{region}:{account_id}:stateMachine:{state_machine_name}"

    state_machine_role_arn: str | None = env_vars["STATE_MACHINE_ROLE_ARN"]
    if state_machine_role_arn is None:
        raise ValueError("STATE_MACHINE_ROLE_ARN is not set at environment variables")

    try:
        current_state_machine_arn: str = sfn.describe_state_machine(stateMachineArn=state_machine_arn)[
            "stateMachineArn"
        ]

        sfn.update_state_machine(
            stateMachineArn=current_state_machine_arn,
            definition=json.dumps(state_machine_definition),
            roleArn=state_machine_role_arn,
        )
        logger.info(f"Updated state machine: {current_state_machine_arn}")

        return current_state_machine_arn
    except sfn.exceptions.StateMachineDoesNotExist:
        logger.warning(f"State machine {state_machine_arn} does not exist. Creating new state machine.")

        created_state_machine_arn: str = sfn.create_state_machine(
            name=state_machine_name,
            definition=json.dumps(state_machine_definition),
            roleArn=state_machine_role_arn,
        )["stateMachineArn"]
        logger.info(f"Create state machine: {created_state_machine_arn}")

        return created_state_machine_arn


def build_container_overrides_option(input: dict[str, Input], tasks: dict[str, int]):
    def build_command_option(commands: list):
        return [{"Name": command["containerName"], "Command": command["passedCommand"]} for command in commands]

    positions = {key.split("_")[0]: value for key, value in tasks.items()}
    return [
        build_command_option(input[task_name]["commands"])
        for task_name, _ in sorted(positions.items(), key=lambda item: item[1])
    ]


def deploy_scheduler(
    service_name: str,
    batch_name: str,
    schedule_name: str,
    cron: AwsCron,
    state: Literal["ENABLED", "DISABLED"],
    tasks: dict[str, int],
    state_machine_arn: str,
    pj_prefix: str,
    input: dict[str, Input] | None = None,
):
    state_machine_executor_lambda_payload: dict = {
        "Id": "<aws.scheduler.execution-id>",
        "StateMachineArn": state_machine_arn,
    }
    if input is not None:
        # ステートマシンを起動する Lambda では、Body の値をステートマシンの入力に設定する
        state_machine_executor_lambda_payload["Body"] = {
            "containerOverrides": build_container_overrides_option(input, tasks)
        }

    state_machine_executor_role_arn: str | None = env_vars["STATE_MACHINE_EXECUTOR_ROLE_ARN"]
    if state_machine_executor_role_arn is None:
        raise ValueError("STATE_MACHINE_EXECUTOR_ROLE_ARN is not set at environment variables")

    schedule_expression = cron.to_string()
    deploy_scheduler_input: CreateScheduleInputRequestTypeDef | UpdateScheduleInputRequestTypeDef = {
        "Name": schedule_name,
        "Description": f"Schedule for {batch_name}",
        "FlexibleTimeWindow": {"Mode": "OFF"},
        "ScheduleExpression": f"cron({schedule_expression})",
        "GroupName": service_name,
        "State": state,
        "Target": {
            "Arn": str(env_vars["STATE_MACHINE_EXECUTOR_ARN"]),
            "RoleArn": state_machine_executor_role_arn,
            "Input": json.dumps(state_machine_executor_lambda_payload),
        },
    }
    scheduler = boto3.client("scheduler", region_name=region)
    tags: list[TagTypeDef] = [
        {"key": "ServiceName", "value": service_name},
        {"key": "CmBillingGroup", "value": pj_prefix},
    ]
    try:
        scheduler.get_schedule(Name=schedule_name, GroupName=service_name)
        scheduler.update_schedule(**deploy_scheduler_input)
        logger.info(f"Updated schedule: {batch_name}")
        group_arn = scheduler.get_schedule_group(Name=service_name)["Arn"]
        tag_resource(scheduler, group_arn, tags)
    except scheduler.exceptions.ResourceNotFoundException:
        logger.warning(f"Schedule {schedule_name} does not exist. Creating new schedule.")

        try:
            # 削除時に Gevanni サービス名でスケジュールをフィルタリングするために、スケジュールグループを作成する
            group_arn = scheduler.create_schedule_group(Name=service_name)["ScheduleGroupArn"]
            # EventBridgeScheduler は、スケジュールグループのみタグ付けが可能
            tag_resource(scheduler, group_arn, tags)
        except scheduler.exceptions.ConflictException:
            # すでにグループが存在する場合は何もしない
            logger.info("Schedule group already exists.")

        scheduler.create_schedule(**deploy_scheduler_input)
        logger.info(f"Created schedule: {schedule_name}")


def deploy_api_resource(path: str):
    apigw = boto3.client("apigateway", region_name=region)
    rest_api_id = env_vars["REST_API_ID"]
    root_resource_id = env_vars["ROOT_RESOURCE_ID"]
    batch_trigger_body_model = env_vars["BATCH_TRIGGER_BODY_MODEL"]
    state_machine_executor_arn_for_api = env_vars["STATE_MACHINE_EXECUTOR_ARN_FOR_API"]
    api_role = env_vars["API_ROLE"]
    stage_name = env_vars["API_STAGE_NAME"]

    if (
        rest_api_id is None
        or root_resource_id is None
        or batch_trigger_body_model is None
        or state_machine_executor_arn_for_api is None
        or api_role is None
        or stage_name is None
    ):
        raise ValueError("Required environment variables are not set")

    http_method = "POST"
    authorization_type = "AWS_IAM"
    try:
        resp = apigw.create_resource(restApiId=rest_api_id, parentId=root_resource_id, pathPart=path[1:])
        resource_id = resp["id"]
    except apigw.exceptions.ConflictException:
        items = apigw.get_resources(restApiId=rest_api_id)["items"]
        for item in items:
            if item["path"] == path:
                resource = item
        resource_id = resource["id"]

    try:
        apigw.put_method(
            resourceId=resource_id,
            restApiId=rest_api_id,
            httpMethod=http_method,
            authorizationType=authorization_type,
            requestModels={"application/json": batch_trigger_body_model},
        )
    except apigw.exceptions.ConflictException:
        apigw.update_method(
            resourceId=resource_id,
            restApiId=rest_api_id,
            httpMethod=http_method,
            patchOperations=[
                {
                    "op": "add",
                    "path": "/requestModels/application~1json",
                    "value": batch_trigger_body_model,
                },
            ],
        )

    integration_uri_prefix = "arn:aws:apigateway:ap-northeast-1:lambda:path/2015-03-31/functions"
    integration_uri = f"{integration_uri_prefix}/{state_machine_executor_arn_for_api}/invocations"
    apigw.put_integration(
        restApiId=rest_api_id,
        resourceId=resource_id,
        # at the lambda integration, the httpMethod must be POST
        httpMethod=http_method,
        type="AWS_PROXY",
        integrationHttpMethod=http_method,
        uri=integration_uri,
        credentials=api_role,
        passthroughBehavior="NEVER",
    )
    print("Integration created")

    deployment_id = apigw.create_deployment(restApiId=rest_api_id, stageName=stage_name)["id"]
    apigw.update_stage(
        restApiId=rest_api_id,
        stageName=stage_name,
        patchOperations=[{"op": "replace", "path": "/deploymentId", "value": deployment_id}],
    )
    print("Deployment created")


def deploy_batch_app(
    service_name: str,
    batch_name: str,
    triggers: Triggers,
    params: Config,
    repo_uri_map: dict[str, str],
):
    ssm = boto3.client("ssm", region_name=env_vars["AWS_REGION"])
    task_definition_arns: dict[str, str] = {}
    pj_prefix = f"{params['env']['name']}-GEVANNI-{params['env']['service_prefix']}"
    parameter_prefix = f"/{pj_prefix}/batch/{batch_name}"
    tasks: dict[str, str] = {}
    for task_name, task_param in params["tasks"].items():
        tasks[task_name] = task_name
        spec = task_param["definition"]["spec"]
        task_role_arn = ssm.get_parameter(Name=f"{parameter_prefix}/role/task/{task_name}")["Parameter"]["Value"]
        image_placeholders: dict[str, str] = {}
        for container_name, container_param in task_param["build"].items():
            image_placeholder: str = container_param["image_placeholder"]
            repo_uri = repo_uri_map[f"{task_name}{container_name}"]
            image_placeholders[image_placeholder] = repo_uri
        arn = deploy_task_definition(
            service_name,
            batch_name,
            (Path(__file__).parent / task_param["definition"]["name"]).resolve(),
            {
                f"{task_name}_cpu": spec["cpu"],
                f"{task_name}_memory": spec["memory"],
                f"{task_name}_taskRoleArn": task_role_arn,
            }
            | image_placeholders,
            pj_prefix,
        )
        task_definition_arns[f"{task_name}_task_def"] = arn

    container_overrides_postions = {f"{key}_commands": i for i, key in enumerate(tasks.keys())}
    state_machine_arn: str = deploy_state_machine(batch_name, task_definition_arns, tasks, container_overrides_postions)

    for trigger_name, trigger_def in triggers["triggers"].items():
        commands: dict[str, Input] | None = cast(dict[str, Input] | None, trigger_def.get("inputs", None))
        trigger_type: Literal["CRON", "API"] = "CRON"

        if trigger_def["type"] == "cron":
            deploy_scheduler(
                service_name,
                batch_name,
                trigger_name,
                AwsCron(trigger_def["cron"]),
                trigger_def["state"],
                container_overrides_postions,
                state_machine_arn,
                pj_prefix,
                commands,
            )
        else:
            path = trigger_def["apiPath"]
            trigger_type = "API"
            deploy_api_resource(path)

        batch_tag: list[TagTypeDef] = [
            {"key": "ServiceName", "value": service_name},
            {"key": "BatchName", "value": batch_name},
            {"key": "TriggerType", "value": trigger_type},
            {"key": "CmBillingGroup", "value": pj_prefix},
        ]
        tag_resource(sfn, state_machine_arn, batch_tag)


def load_parameters(definition_file_dir_path: Path):
    param_file: Path = next(definition_file_dir_path.glob("*.toml")).resolve()
    if not param_file.is_file():
        raise Exception("paramuration file '`env`.toml' not found.")

    with open(param_file, "rb") as f:
        obj = tomllib.load(f)

    return obj


def main():
    service_name: str | None = env_vars["GEVANNI_SERVICE_NAME"]
    if service_name is None:
        raise ValueError("GEVANNI_SERVICE_NAME is not set at environment variables")

    batch_name: str | None = env_vars["BATCH_NAME"]
    if batch_name is None:
        raise ValueError("BATCH_NAME is not set at environment variables")

    trigger_file_path: Path = (Path(__file__).parent / f"{batch_name}.trigger.json").resolve()
    with open(trigger_file_path) as f:
        triggers: Triggers = json.load(f)

    repo_uri_map_path: Path = (Path(__file__).parent / "repo_uri_map.json").resolve()
    with open(repo_uri_map_path) as f:
        repo_uri_map: dict[str, str] = json.load(f)

    params: Config = load_parameters(Path(__file__).parent)

    logger.info(f"deploy {batch_name}")
    deploy_batch_app(service_name, batch_name, triggers, params, repo_uri_map)


if __name__ == "__main__":
    main()
