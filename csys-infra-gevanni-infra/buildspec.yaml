version: 0.2

phases:
  install:
    commands:
      # requires npm >=8.1.0
      - npm i -g npm
  pre_build:
    commands:
      - npm ci
  build:
    commands:
      - npm audit
      - npm run lint
      - npm run build --workspace usecases/guest-webapp-sample
      - npm run test --workspace usecases/guest-webapp-sample
      # You can specify CDK deployment commands.
      # Usually, you may want to deploy all of resources in the app.
      # If you want to do so, please specify `"*"`
      - cd usecases/guest-webapp-sample
      - npx cdk deploy BLEA-MonitorAlarm -c environment=dev --require-approval never
