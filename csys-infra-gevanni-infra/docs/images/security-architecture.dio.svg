<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1341px" height="841px" viewBox="-0.5 -0.5 1341 841" content="&lt;mxfile&gt;&lt;diagram id=&quot;kTgIIZ1JuWM4HKAjfQyZ&quot; name=&quot;250128&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 180 430 L 1100 430 L 1100 800 L 180 800 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 180 430 L 205 430 L 205 455 L 180 455 Z M 192.52 433.21 C 191.4 433.21 190.31 433.63 189.49 434.39 C 188.67 435.11 188.2 436.15 188.2 437.24 L 188.2 439.78 L 185.89 439.78 C 185.8 439.78 185.7 439.82 185.64 439.89 C 185.57 439.95 185.54 440.04 185.54 440.13 L 185.54 451.43 C 185.54 451.63 185.7 451.79 185.89 451.79 L 199.11 451.79 C 199.3 451.79 199.46 451.63 199.46 451.43 L 199.46 440.15 C 199.47 440.06 199.43 439.97 199.36 439.9 C 199.3 439.83 199.21 439.79 199.11 439.79 L 196.81 439.79 L 196.81 437.29 C 196.8 436.21 196.35 435.18 195.56 434.44 C 194.74 433.65 193.65 433.22 192.52 433.21 Z M 192.51 433.93 C 193.46 433.92 194.37 434.28 195.06 434.93 C 195.72 435.54 196.1 436.4 196.1 437.29 L 196.1 439.79 L 188.88 439.79 L 188.89 437.26 C 188.9 436.36 189.28 435.51 189.95 434.91 C 190.65 434.27 191.57 433.92 192.51 433.93 Z M 186.24 440.5 L 198.76 440.5 L 198.75 451.07 L 186.24 451.07 Z M 192.51 442.74 C 191.48 442.73 190.61 443.51 190.51 444.53 C 190.42 445.56 191.13 446.48 192.14 446.66 L 192.14 449.44 L 192.86 449.44 L 192.86 446.66 C 193.79 446.49 194.47 445.67 194.48 444.72 C 194.48 443.63 193.6 442.75 192.51 442.74 Z M 192.39 443.45 C 192.43 443.45 192.47 443.45 192.51 443.46 C 192.84 443.46 193.16 443.59 193.4 443.83 C 193.64 444.07 193.77 444.39 193.76 444.72 C 193.77 445.06 193.64 445.38 193.4 445.61 C 193.16 445.85 192.84 445.98 192.51 445.98 C 192.04 446.02 191.6 445.8 191.34 445.42 C 191.08 445.03 191.06 444.53 191.28 444.12 C 191.5 443.71 191.93 443.46 192.39 443.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 888px; height: 1px; padding-top: 437px; margin-left: 212px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="212" y="449" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <path d="M 180 270 L 1080 270 L 1080 390 L 180 390 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 180 270 L 205 270 L 205 295 L 180 295 Z M 192.52 273.21 C 191.4 273.21 190.31 273.63 189.49 274.39 C 188.67 275.11 188.2 276.15 188.2 277.24 L 188.2 279.78 L 185.89 279.78 C 185.8 279.78 185.7 279.82 185.64 279.89 C 185.57 279.95 185.54 280.04 185.54 280.13 L 185.54 291.43 C 185.54 291.63 185.7 291.79 185.89 291.79 L 199.11 291.79 C 199.3 291.79 199.46 291.63 199.46 291.43 L 199.46 280.15 C 199.47 280.06 199.43 279.97 199.36 279.9 C 199.3 279.83 199.21 279.79 199.11 279.79 L 196.81 279.79 L 196.81 277.29 C 196.8 276.21 196.35 275.18 195.56 274.44 C 194.74 273.65 193.65 273.22 192.52 273.21 Z M 192.51 273.93 C 193.46 273.92 194.37 274.28 195.06 274.93 C 195.72 275.54 196.1 276.4 196.1 277.29 L 196.1 279.79 L 188.88 279.79 L 188.89 277.26 C 188.9 276.36 189.28 275.51 189.95 274.91 C 190.65 274.27 191.57 273.92 192.51 273.93 Z M 186.24 280.5 L 198.76 280.5 L 198.75 291.07 L 186.24 291.07 Z M 192.51 282.74 C 191.48 282.73 190.61 283.51 190.51 284.53 C 190.42 285.56 191.13 286.48 192.14 286.66 L 192.14 289.44 L 192.86 289.44 L 192.86 286.66 C 193.79 286.49 194.47 285.67 194.48 284.72 C 194.48 283.63 193.6 282.75 192.51 282.74 Z M 192.39 283.45 C 192.43 283.45 192.47 283.45 192.51 283.46 C 192.84 283.46 193.16 283.59 193.4 283.83 C 193.64 284.07 193.77 284.39 193.76 284.72 C 193.77 285.06 193.64 285.38 193.4 285.61 C 193.16 285.85 192.84 285.98 192.51 285.98 C 192.04 286.02 191.6 285.8 191.34 285.42 C 191.08 285.03 191.06 284.53 191.28 284.12 C 191.5 283.71 191.93 283.46 192.39 283.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 868px; height: 1px; padding-top: 277px; margin-left: 212px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="212" y="289" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="140" y="60" width="300" height="780" rx="45" ry="45" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 298px; height: 1px; padding-top: 57px; margin-left: 141px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                サービス A
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="290" y="57" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    サービス A
                </text>
            </switch>
        </g>
        <rect x="840" y="60" width="280" height="780" rx="42" ry="42" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 57px; margin-left: 841px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                サービス C
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="980" y="57" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    サービス C
                </text>
            </switch>
        </g>
        <rect x="520" y="60" width="280" height="780" rx="42" ry="42" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 57px; margin-left: 521px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                サービス B
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="57" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    サービス B
                </text>
            </switch>
        </g>
        <path d="M 160 230 L 1340 230 L 1340 820 L 160 820 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 166.09 237.18 C 166.01 237.18 165.93 237.19 165.85 237.19 C 165.5 237.19 165.15 237.23 164.81 237.32 C 164.53 237.39 164.25 237.49 163.98 237.62 C 163.9 237.65 163.84 237.7 163.79 237.76 C 163.75 237.83 163.74 237.91 163.74 237.99 L 163.74 238.32 C 163.74 238.46 163.78 238.53 163.88 238.53 L 163.99 238.53 L 164.22 238.44 C 164.45 238.35 164.69 238.27 164.94 238.21 C 165.17 238.16 165.41 238.13 165.65 238.13 C 166.04 238.09 166.43 238.2 166.74 238.44 C 166.97 238.74 167.09 239.12 167.05 239.5 L 167.05 239.99 C 166.78 239.93 166.54 239.88 166.29 239.84 C 166.05 239.81 165.81 239.79 165.57 239.79 C 164.98 239.76 164.4 239.94 163.94 240.31 C 163.54 240.65 163.32 241.15 163.34 241.68 C 163.31 242.15 163.49 242.62 163.82 242.96 C 164.18 243.29 164.66 243.46 165.15 243.44 C 165.91 243.45 166.63 243.11 167.11 242.51 C 167.18 242.66 167.24 242.79 167.31 242.91 C 167.38 243.02 167.46 243.12 167.55 243.21 C 167.6 243.27 167.67 243.31 167.75 243.31 C 167.81 243.31 167.87 243.29 167.92 243.25 L 168.34 242.97 C 168.41 242.93 168.46 242.86 168.47 242.77 C 168.47 242.72 168.45 242.67 168.42 242.62 C 168.34 242.47 168.26 242.31 168.21 242.14 C 168.15 241.95 168.12 241.75 168.13 241.55 L 168.14 239.37 C 168.2 238.77 168 238.18 167.59 237.74 C 167.17 237.39 166.64 237.19 166.09 237.18 Z M 179.89 237.19 C 179.78 237.19 179.68 237.19 179.57 237.2 C 179.29 237.2 179 237.24 178.73 237.31 C 178.47 237.38 178.23 237.5 178.01 237.66 C 177.82 237.81 177.66 237.99 177.54 238.21 C 177.42 238.43 177.35 238.67 177.36 238.92 C 177.36 239.27 177.48 239.61 177.69 239.89 C 177.97 240.22 178.34 240.46 178.76 240.56 L 179.72 240.87 C 179.97 240.93 180.2 241.05 180.39 241.22 C 180.51 241.35 180.58 241.51 180.57 241.69 C 180.58 241.94 180.45 242.18 180.23 242.31 C 179.93 242.48 179.6 242.56 179.26 242.54 C 178.99 242.54 178.72 242.51 178.46 242.45 C 178.22 242.4 177.98 242.32 177.75 242.22 L 177.59 242.15 C 177.54 242.14 177.5 242.14 177.46 242.15 C 177.36 242.15 177.31 242.22 177.31 242.36 L 177.31 242.69 C 177.31 242.76 177.32 242.82 177.35 242.88 C 177.4 242.97 177.47 243.03 177.56 243.07 C 177.8 243.19 178.06 243.28 178.32 243.34 C 178.66 243.41 179 243.45 179.35 243.45 L 179.33 243.46 C 179.66 243.45 179.98 243.4 180.29 243.3 C 180.55 243.22 180.8 243.09 181.01 242.92 C 181.21 242.77 181.38 242.57 181.49 242.34 C 181.61 242.1 181.67 241.83 181.66 241.56 C 181.67 241.23 181.56 240.9 181.36 240.63 C 181.09 240.32 180.73 240.09 180.33 239.99 L 179.39 239.69 C 179.13 239.61 178.88 239.49 178.67 239.32 C 178.54 239.2 178.47 239.03 178.47 238.85 C 178.46 238.61 178.58 238.38 178.79 238.25 C 179.06 238.11 179.36 238.05 179.67 238.06 C 180.11 238.06 180.55 238.14 180.96 238.32 C 181.04 238.37 181.12 238.4 181.21 238.41 C 181.31 238.41 181.36 238.34 181.36 238.19 L 181.36 237.88 C 181.37 237.8 181.35 237.72 181.31 237.66 C 181.25 237.59 181.18 237.54 181.11 237.49 L 180.83 237.38 L 180.45 237.27 L 180.01 237.2 C 179.97 237.2 179.93 237.19 179.89 237.19 Z M 176.02 237.35 C 175.94 237.35 175.86 237.38 175.79 237.42 C 175.72 237.5 175.68 237.59 175.66 237.69 L 174.51 242.14 L 173.47 237.71 C 173.45 237.61 173.41 237.52 173.34 237.44 C 173.26 237.39 173.17 237.37 173.07 237.38 L 172.54 237.38 C 172.44 237.37 172.35 237.39 172.27 237.44 C 172.2 237.51 172.15 237.61 172.14 237.71 L 171.09 242.14 L 169.97 237.7 C 169.95 237.6 169.91 237.51 169.84 237.44 C 169.76 237.39 169.67 237.36 169.58 237.37 L 168.92 237.37 C 168.81 237.37 168.76 237.43 168.76 237.54 C 168.77 237.63 168.79 237.72 168.82 237.81 L 170.38 242.95 C 170.4 243.05 170.45 243.14 170.52 243.21 C 170.6 243.26 170.69 243.29 170.78 243.28 L 171.36 243.26 C 171.46 243.27 171.55 243.25 171.63 243.19 C 171.7 243.12 171.74 243.03 171.76 242.93 L 172.79 238.64 L 173.82 242.93 C 173.83 243.03 173.88 243.12 173.95 243.19 C 174.03 243.25 174.12 243.27 174.21 243.26 L 174.78 243.26 C 174.88 243.27 174.97 243.25 175.04 243.2 C 175.11 243.13 175.16 243.03 175.18 242.94 L 176.79 237.79 C 176.84 237.72 176.84 237.63 176.84 237.63 C 176.84 237.59 176.84 237.56 176.84 237.52 C 176.84 237.48 176.82 237.43 176.79 237.4 C 176.76 237.37 176.72 237.35 176.67 237.36 L 176.05 237.36 C 176.04 237.36 176.03 237.36 176.02 237.35 Z M 165.65 240.62 C 165.7 240.62 165.75 240.62 165.8 240.62 L 166.43 240.62 C 166.64 240.64 166.85 240.67 167.06 240.71 L 167.06 241.01 C 167.07 241.21 167.05 241.4 167 241.59 C 166.96 241.75 166.88 241.9 166.77 242.01 C 166.61 242.21 166.39 242.36 166.14 242.44 C 165.91 242.52 165.67 242.56 165.43 242.56 C 165.18 242.6 164.93 242.53 164.73 242.37 C 164.55 242.18 164.46 241.92 164.49 241.66 C 164.47 241.36 164.59 241.08 164.81 240.89 C 165.06 240.72 165.35 240.62 165.65 240.62 Z M 181.04 244.72 C 180.34 244.73 179.51 244.89 178.88 245.33 C 178.69 245.47 178.72 245.63 178.94 245.63 C 179.64 245.54 181.21 245.35 181.5 245.71 C 181.78 246.06 181.19 247.54 180.94 248.21 C 180.86 248.41 181.03 248.49 181.21 248.34 C 182.39 247.36 182.72 245.3 182.46 245 C 182.32 244.85 181.74 244.71 181.04 244.72 Z M 162.65 245.1 C 162.5 245.12 162.42 245.3 162.58 245.44 C 165.29 247.89 168.82 249.23 172.48 249.21 C 175.37 249.22 178.2 248.36 180.59 246.74 C 180.95 246.47 180.63 246.07 180.26 246.23 C 177.87 247.24 175.3 247.76 172.71 247.77 C 169.23 247.78 165.82 246.87 162.81 245.14 C 162.75 245.11 162.69 245.1 162.65 245.1 Z M 160 230 L 185 230 L 185 255 L 160 255 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1148px; height: 1px; padding-top: 237px; margin-left: 192px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Gevanni Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="192" y="249" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Gevanni Account
                </text>
            </switch>
        </g>
        <rect x="240" y="520" width="160" height="110" fill="none" stroke="#f78e04" stroke-width="2" pointer-events="all"/>
        <rect x="240" y="520" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 252.99 544.69 L 261.68 544.69 L 261.68 543.33 L 252.99 543.33 Z M 252.99 537.93 L 261.68 537.93 L 261.68 536.57 L 252.99 536.57 Z M 252.99 531.18 L 261.68 531.18 L 261.68 529.81 L 252.99 529.81 Z M 249.33 544.78 L 249.33 543.24 L 250.86 543.24 L 250.86 544.78 Z M 248.65 546.14 L 251.55 546.14 C 251.92 546.14 252.23 545.83 252.23 545.46 L 252.23 542.56 C 252.23 542.18 251.92 541.88 251.55 541.88 L 248.65 541.88 C 248.27 541.88 247.97 542.18 247.97 542.56 L 247.97 545.46 C 247.97 545.83 248.27 546.14 248.65 546.14 Z M 249.33 538.02 L 249.33 536.49 L 250.86 536.49 L 250.86 538.02 Z M 248.65 539.38 L 251.55 539.38 C 251.92 539.38 252.23 539.08 252.23 538.7 L 252.23 535.8 C 252.23 535.43 251.92 535.12 251.55 535.12 L 248.65 535.12 C 248.27 535.12 247.97 535.43 247.97 535.8 L 247.97 538.7 C 247.97 539.08 248.27 539.38 248.65 539.38 Z M 249.33 531.26 L 249.33 529.73 L 250.86 529.73 L 250.86 531.26 Z M 248.65 532.63 L 251.55 532.63 C 251.92 532.63 252.23 532.32 252.23 531.94 L 252.23 529.05 C 252.23 528.67 251.92 528.37 251.55 528.37 L 248.65 528.37 C 248.27 528.37 247.97 528.67 247.97 529.05 L 247.97 531.94 C 247.97 532.32 248.27 532.63 248.65 532.63 Z M 246.92 548.64 L 246.92 526.83 L 262.93 526.83 L 262.93 548.64 Z M 245.56 526.15 L 245.56 545.74 L 244.02 545.74 L 244.02 523.94 L 260.04 523.94 L 260.04 525.47 L 246.24 525.47 C 245.86 525.47 245.56 525.78 245.56 526.15 Z M 242.66 523.26 L 242.66 543.17 L 241.45 543.17 L 241.45 521.36 L 257.46 521.36 L 257.46 522.57 L 243.34 522.57 C 242.96 522.57 242.66 522.88 242.66 523.26 Z M 263.61 525.47 L 261.4 525.47 L 261.4 523.26 C 261.4 522.88 261.09 522.57 260.72 522.57 L 258.82 522.57 L 258.82 520.68 C 258.82 520.31 258.52 520 258.14 520 L 240.77 520 C 240.39 520 240.09 520.31 240.09 520.68 L 240.09 543.85 C 240.09 544.23 240.39 544.53 240.77 544.53 L 242.66 544.53 L 242.66 546.42 C 242.66 546.8 242.96 547.1 243.34 547.1 L 245.56 547.1 L 245.56 549.32 C 245.56 549.7 245.86 550 246.24 550 L 263.61 550 C 263.99 550 264.29 549.7 264.29 549.32 L 264.29 526.15 C 264.29 525.78 263.99 525.47 263.61 525.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 535px; margin-left: 266px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="266" y="539" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS S...
                </text>
            </switch>
        </g>
        <rect x="240" y="650" width="160" height="110" fill="none" stroke="#f78e04" stroke-width="2" pointer-events="all"/>
        <rect x="240" y="650" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 252.99 674.69 L 261.68 674.69 L 261.68 673.33 L 252.99 673.33 Z M 252.99 667.93 L 261.68 667.93 L 261.68 666.57 L 252.99 666.57 Z M 252.99 661.18 L 261.68 661.18 L 261.68 659.81 L 252.99 659.81 Z M 249.33 674.78 L 249.33 673.24 L 250.86 673.24 L 250.86 674.78 Z M 248.65 676.14 L 251.55 676.14 C 251.92 676.14 252.23 675.83 252.23 675.46 L 252.23 672.56 C 252.23 672.18 251.92 671.88 251.55 671.88 L 248.65 671.88 C 248.27 671.88 247.97 672.18 247.97 672.56 L 247.97 675.46 C 247.97 675.83 248.27 676.14 248.65 676.14 Z M 249.33 668.02 L 249.33 666.49 L 250.86 666.49 L 250.86 668.02 Z M 248.65 669.38 L 251.55 669.38 C 251.92 669.38 252.23 669.08 252.23 668.7 L 252.23 665.8 C 252.23 665.43 251.92 665.12 251.55 665.12 L 248.65 665.12 C 248.27 665.12 247.97 665.43 247.97 665.8 L 247.97 668.7 C 247.97 669.08 248.27 669.38 248.65 669.38 Z M 249.33 661.26 L 249.33 659.73 L 250.86 659.73 L 250.86 661.26 Z M 248.65 662.63 L 251.55 662.63 C 251.92 662.63 252.23 662.32 252.23 661.94 L 252.23 659.05 C 252.23 658.67 251.92 658.37 251.55 658.37 L 248.65 658.37 C 248.27 658.37 247.97 658.67 247.97 659.05 L 247.97 661.94 C 247.97 662.32 248.27 662.63 248.65 662.63 Z M 246.92 678.64 L 246.92 656.83 L 262.93 656.83 L 262.93 678.64 Z M 245.56 656.15 L 245.56 675.74 L 244.02 675.74 L 244.02 653.94 L 260.04 653.94 L 260.04 655.47 L 246.24 655.47 C 245.86 655.47 245.56 655.78 245.56 656.15 Z M 242.66 653.26 L 242.66 673.17 L 241.45 673.17 L 241.45 651.36 L 257.46 651.36 L 257.46 652.57 L 243.34 652.57 C 242.96 652.57 242.66 652.88 242.66 653.26 Z M 263.61 655.47 L 261.4 655.47 L 261.4 653.26 C 261.4 652.88 261.09 652.57 260.72 652.57 L 258.82 652.57 L 258.82 650.68 C 258.82 650.31 258.52 650 258.14 650 L 240.77 650 C 240.39 650 240.09 650.31 240.09 650.68 L 240.09 673.85 C 240.09 674.23 240.39 674.53 240.77 674.53 L 242.66 674.53 L 242.66 676.42 C 242.66 676.8 242.96 677.1 243.34 677.1 L 245.56 677.1 L 245.56 679.32 C 245.56 679.7 245.86 680 246.24 680 L 263.61 680 C 263.99 680 264.29 679.7 264.29 679.32 L 264.29 656.15 C 264.29 655.78 263.99 655.47 263.61 655.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 665px; margin-left: 266px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="266" y="669" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS S...
                </text>
            </switch>
        </g>
        <path d="M 160 80 L 1100 80 L 1100 190 L 160 190 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 166.09 87.18 C 166.01 87.18 165.93 87.19 165.85 87.19 C 165.5 87.19 165.15 87.23 164.81 87.32 C 164.53 87.39 164.25 87.49 163.98 87.62 C 163.9 87.65 163.84 87.7 163.79 87.76 C 163.75 87.83 163.74 87.91 163.74 87.99 L 163.74 88.32 C 163.74 88.46 163.78 88.53 163.88 88.53 L 163.99 88.53 L 164.22 88.44 C 164.45 88.35 164.69 88.27 164.94 88.21 C 165.17 88.16 165.41 88.13 165.65 88.13 C 166.04 88.09 166.43 88.2 166.74 88.44 C 166.97 88.74 167.09 89.12 167.05 89.5 L 167.05 89.99 C 166.78 89.93 166.54 89.88 166.29 89.84 C 166.05 89.81 165.81 89.79 165.57 89.79 C 164.98 89.76 164.4 89.94 163.94 90.31 C 163.54 90.65 163.32 91.15 163.34 91.68 C 163.31 92.15 163.49 92.62 163.82 92.96 C 164.18 93.29 164.66 93.46 165.15 93.44 C 165.91 93.45 166.63 93.11 167.11 92.51 C 167.18 92.66 167.24 92.79 167.31 92.91 C 167.38 93.02 167.46 93.12 167.55 93.21 C 167.6 93.27 167.67 93.31 167.75 93.31 C 167.81 93.31 167.87 93.29 167.92 93.25 L 168.34 92.97 C 168.41 92.93 168.46 92.86 168.47 92.77 C 168.47 92.72 168.45 92.67 168.42 92.62 C 168.34 92.47 168.26 92.31 168.21 92.14 C 168.15 91.95 168.12 91.75 168.13 91.55 L 168.14 89.37 C 168.2 88.77 168 88.18 167.59 87.74 C 167.17 87.39 166.64 87.19 166.09 87.18 Z M 179.89 87.19 C 179.78 87.19 179.68 87.19 179.57 87.2 C 179.29 87.2 179 87.24 178.73 87.31 C 178.47 87.38 178.23 87.5 178.01 87.66 C 177.82 87.81 177.66 87.99 177.54 88.21 C 177.42 88.43 177.35 88.67 177.36 88.92 C 177.36 89.27 177.48 89.61 177.69 89.89 C 177.97 90.22 178.34 90.46 178.76 90.56 L 179.72 90.87 C 179.97 90.93 180.2 91.05 180.39 91.22 C 180.51 91.35 180.58 91.51 180.57 91.69 C 180.58 91.94 180.45 92.18 180.23 92.31 C 179.93 92.48 179.6 92.56 179.26 92.54 C 178.99 92.54 178.72 92.51 178.46 92.45 C 178.22 92.4 177.98 92.32 177.75 92.22 L 177.59 92.15 C 177.54 92.14 177.5 92.14 177.46 92.15 C 177.36 92.15 177.31 92.22 177.31 92.36 L 177.31 92.69 C 177.31 92.76 177.32 92.82 177.35 92.89 C 177.4 92.97 177.47 93.03 177.56 93.07 C 177.8 93.19 178.06 93.28 178.32 93.34 C 178.66 93.41 179 93.45 179.35 93.45 L 179.33 93.46 C 179.66 93.45 179.98 93.4 180.29 93.3 C 180.55 93.22 180.8 93.09 181.01 92.92 C 181.21 92.77 181.38 92.57 181.49 92.34 C 181.61 92.1 181.67 91.83 181.66 91.56 C 181.67 91.23 181.56 90.9 181.36 90.63 C 181.09 90.32 180.73 90.09 180.33 89.99 L 179.39 89.69 C 179.13 89.61 178.88 89.49 178.67 89.32 C 178.54 89.2 178.47 89.03 178.47 88.85 C 178.46 88.61 178.58 88.38 178.79 88.25 C 179.06 88.11 179.36 88.05 179.67 88.06 C 180.11 88.06 180.55 88.14 180.96 88.32 C 181.04 88.37 181.12 88.4 181.21 88.41 C 181.31 88.41 181.36 88.34 181.36 88.19 L 181.36 87.88 C 181.37 87.8 181.35 87.72 181.31 87.66 C 181.25 87.59 181.18 87.54 181.11 87.49 L 180.83 87.38 L 180.45 87.27 L 180.01 87.2 C 179.97 87.2 179.93 87.19 179.89 87.19 Z M 176.02 87.36 C 175.94 87.35 175.86 87.38 175.79 87.42 C 175.72 87.5 175.68 87.59 175.66 87.69 L 174.51 92.14 L 173.47 87.71 C 173.45 87.61 173.41 87.52 173.34 87.44 C 173.26 87.39 173.17 87.37 173.07 87.38 L 172.54 87.38 C 172.44 87.37 172.35 87.39 172.27 87.44 C 172.2 87.51 172.15 87.61 172.14 87.71 L 171.09 92.14 L 169.97 87.7 C 169.95 87.6 169.91 87.51 169.84 87.44 C 169.76 87.39 169.67 87.36 169.58 87.37 L 168.92 87.37 C 168.81 87.37 168.76 87.43 168.76 87.54 C 168.77 87.63 168.79 87.72 168.82 87.81 L 170.38 92.95 C 170.4 93.05 170.45 93.14 170.52 93.21 C 170.6 93.26 170.69 93.29 170.78 93.28 L 171.36 93.26 C 171.46 93.27 171.55 93.25 171.63 93.19 C 171.7 93.12 171.74 93.03 171.76 92.93 L 172.79 88.64 L 173.82 92.93 C 173.83 93.03 173.88 93.12 173.95 93.19 C 174.03 93.25 174.12 93.27 174.21 93.26 L 174.78 93.26 C 174.88 93.27 174.97 93.25 175.04 93.2 C 175.11 93.13 175.16 93.03 175.18 92.94 L 176.79 87.79 C 176.84 87.72 176.84 87.63 176.84 87.63 C 176.84 87.59 176.84 87.56 176.84 87.52 C 176.84 87.48 176.82 87.43 176.79 87.4 C 176.76 87.37 176.72 87.35 176.67 87.36 L 176.05 87.36 C 176.04 87.36 176.03 87.36 176.02 87.36 Z M 165.65 90.62 C 165.7 90.62 165.75 90.62 165.8 90.62 L 166.43 90.62 C 166.64 90.64 166.85 90.67 167.06 90.71 L 167.06 91.01 C 167.07 91.21 167.05 91.4 167 91.59 C 166.96 91.75 166.88 91.9 166.77 92.01 C 166.61 92.21 166.39 92.36 166.14 92.44 C 165.91 92.52 165.67 92.56 165.43 92.56 C 165.18 92.6 164.93 92.53 164.73 92.37 C 164.55 92.18 164.46 91.92 164.49 91.66 C 164.47 91.36 164.59 91.08 164.81 90.89 C 165.06 90.72 165.35 90.62 165.65 90.62 Z M 181.04 94.72 C 180.34 94.73 179.51 94.89 178.88 95.33 C 178.69 95.47 178.72 95.63 178.94 95.63 C 179.64 95.54 181.21 95.35 181.5 95.71 C 181.78 96.06 181.19 97.54 180.94 98.21 C 180.86 98.41 181.03 98.49 181.21 98.34 C 182.39 97.36 182.72 95.3 182.46 95 C 182.32 94.85 181.74 94.71 181.04 94.72 Z M 162.65 95.1 C 162.5 95.12 162.42 95.3 162.58 95.44 C 165.29 97.89 168.82 99.23 172.48 99.21 C 175.37 99.22 178.2 98.36 180.59 96.74 C 180.95 96.47 180.63 96.07 180.26 96.23 C 177.87 97.24 175.3 97.76 172.71 97.77 C 169.23 97.78 165.82 96.87 162.81 95.14 C 162.75 95.11 162.69 95.1 162.65 95.1 Z M 160 80 L 185 80 L 185 105 L 160 105 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 908px; height: 1px; padding-top: 87px; margin-left: 192px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                App Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="192" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    App Account
                </text>
            </switch>
        </g>
        <rect x="200" y="470" width="880" height="310" fill="none" stroke="#b266ff" stroke-width="2" pointer-events="all"/>
        <path d="M 200 470 L 230 470 L 230 500 L 200 500 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 220.57 486.48 C 220.71 486.48 220.84 486.41 220.92 486.29 C 221.19 485.9 223.57 482.41 223.57 480.84 C 223.57 479.11 222.28 477.8 220.57 477.8 C 218.86 477.8 217.57 479.11 217.57 480.84 C 217.57 482.41 219.95 485.9 220.22 486.29 C 220.3 486.41 220.43 486.48 220.57 486.48 Z M 220.57 478.67 C 221.79 478.67 222.71 479.6 222.71 480.84 C 222.71 481.79 221.39 484.02 220.57 485.27 C 219.76 484.02 218.43 481.79 218.43 480.84 C 218.43 479.6 219.35 478.67 220.57 478.67 Z M 222.29 480.84 C 222.29 479.93 221.47 479.11 220.57 479.11 C 219.68 479.11 218.86 479.93 218.86 480.84 C 218.86 481.75 219.68 482.57 220.57 482.57 C 221.47 482.57 222.29 481.75 222.29 480.84 Z M 219.72 480.84 C 219.72 480.41 220.15 479.97 220.57 479.97 C 221 479.97 221.43 480.41 221.43 480.84 C 221.43 481.27 221 481.71 220.57 481.71 C 220.15 481.71 219.72 481.27 219.72 480.84 Z M 223.71 484.12 L 223.43 484.94 C 224.15 485.2 225.21 485.75 225.77 486.91 L 218.86 486.91 C 218.62 486.91 218.43 487.1 218.43 487.34 L 218.43 492.54 L 217.14 492.54 L 217.14 490.81 C 217.14 490.57 216.95 490.38 216.72 490.38 L 213.72 490.38 C 213.48 490.38 213.29 490.57 213.29 490.81 L 213.29 492.54 L 212 492.53 L 212 487.78 C 212 487.61 211.9 487.45 211.75 487.38 C 211.59 487.31 211.41 487.34 211.28 487.46 L 206.3 492.08 C 206.01 491.93 205.74 491.76 205.5 491.57 L 211.86 485.94 C 211.95 485.85 212 485.73 212 485.61 L 212 484.31 C 212 484.07 211.81 483.87 211.57 483.87 L 206.85 483.87 C 206.85 483.84 206.85 483.81 206.85 483.77 C 206.86 483.68 206.86 483.59 206.86 483.5 C 206.86 483.05 206.94 482.59 207.08 482.14 L 211.57 482.14 C 211.81 482.14 212 481.95 212 481.71 L 212 478.01 C 212.43 477.99 212.86 478.02 213.29 478.1 L 213.29 485.18 C 213.29 485.41 213.48 485.61 213.72 485.61 L 216.72 485.61 C 216.95 485.61 217.14 485.41 217.14 485.18 L 217.14 482.57 L 216.29 482.57 L 216.29 484.74 L 214.15 484.74 L 214.15 478.34 C 214.96 478.65 215.73 479.15 216.41 479.84 L 217.02 479.23 C 215 477.18 212.33 476.59 209.88 477.64 C 207.71 478.57 206 481.14 206 483.5 C 206 483.58 206 483.66 206 483.75 C 206 483.82 205.99 483.91 205.99 483.98 C 204.75 484.4 203 485.49 203 488.38 L 203 488.47 C 203 488.53 203 488.58 203.01 488.67 C 203.15 491.01 205.15 493.04 207.65 493.39 C 207.67 493.4 207.69 493.4 207.71 493.4 L 213.67 493.4 C 213.69 493.41 213.7 493.41 213.72 493.41 L 216.72 493.41 C 216.73 493.41 216.74 493.41 216.75 493.41 L 222.71 493.41 L 222.71 493.41 C 222.72 493.41 222.74 493.41 222.75 493.41 C 222.79 493.41 227 492.92 227 488.64 C 227 485.61 224.7 484.47 223.71 484.12 Z M 214.15 491.25 L 216.29 491.25 L 216.29 492.54 L 214.15 492.54 Z M 210.21 478.44 C 210.52 478.3 210.83 478.21 211.15 478.13 L 211.15 481.27 L 207.41 481.27 C 208.01 480.03 209.04 478.94 210.21 478.44 Z M 203.86 488.5 L 203.86 488.38 C 203.86 486.44 204.75 485.21 206.49 484.74 L 211.15 484.74 L 211.15 485.41 L 204.87 490.97 C 204.29 490.31 203.91 489.49 203.86 488.62 C 203.86 488.56 203.86 488.54 203.86 488.5 Z M 207.21 492.42 L 211.15 488.76 L 211.15 492.53 L 207.74 492.53 C 207.56 492.5 207.38 492.46 207.21 492.42 Z M 222.68 492.55 L 219.29 492.54 L 219.29 487.78 L 226.06 487.78 C 226.11 488.04 226.14 488.33 226.14 488.64 C 226.14 492.03 222.99 492.51 222.68 492.55 Z M 216.72 486.04 L 213.68 486.04 C 213.45 486.04 213.26 486.24 213.26 486.48 L 213.26 489.51 C 213.26 489.75 213.45 489.94 213.68 489.94 L 216.72 489.94 C 216.95 489.94 217.14 489.75 217.14 489.51 L 217.14 486.48 C 217.14 486.24 216.95 486.04 216.72 486.04 Z M 214.11 489.08 L 214.11 486.91 L 216.29 486.91 L 216.29 489.08 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 485px; margin-left: 232px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Cloud Map
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="489" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Cloud...
                </text>
            </switch>
        </g>
        <rect x="580" y="520" width="160" height="110" fill="none" stroke="#f78e04" stroke-width="2" pointer-events="all"/>
        <rect x="580" y="520" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 592.99 544.69 L 601.68 544.69 L 601.68 543.33 L 592.99 543.33 Z M 592.99 537.93 L 601.68 537.93 L 601.68 536.57 L 592.99 536.57 Z M 592.99 531.18 L 601.68 531.18 L 601.68 529.81 L 592.99 529.81 Z M 589.33 544.78 L 589.33 543.24 L 590.86 543.24 L 590.86 544.78 Z M 588.65 546.14 L 591.55 546.14 C 591.92 546.14 592.23 545.83 592.23 545.46 L 592.23 542.56 C 592.23 542.18 591.92 541.88 591.55 541.88 L 588.65 541.88 C 588.27 541.88 587.97 542.18 587.97 542.56 L 587.97 545.46 C 587.97 545.83 588.27 546.14 588.65 546.14 Z M 589.33 538.02 L 589.33 536.49 L 590.86 536.49 L 590.86 538.02 Z M 588.65 539.38 L 591.55 539.38 C 591.92 539.38 592.23 539.08 592.23 538.7 L 592.23 535.8 C 592.23 535.43 591.92 535.12 591.55 535.12 L 588.65 535.12 C 588.27 535.12 587.97 535.43 587.97 535.8 L 587.97 538.7 C 587.97 539.08 588.27 539.38 588.65 539.38 Z M 589.33 531.26 L 589.33 529.73 L 590.86 529.73 L 590.86 531.26 Z M 588.65 532.63 L 591.55 532.63 C 591.92 532.63 592.23 532.32 592.23 531.94 L 592.23 529.05 C 592.23 528.67 591.92 528.37 591.55 528.37 L 588.65 528.37 C 588.27 528.37 587.97 528.67 587.97 529.05 L 587.97 531.94 C 587.97 532.32 588.27 532.63 588.65 532.63 Z M 586.92 548.64 L 586.92 526.83 L 602.93 526.83 L 602.93 548.64 Z M 585.56 526.15 L 585.56 545.74 L 584.02 545.74 L 584.02 523.94 L 600.04 523.94 L 600.04 525.47 L 586.24 525.47 C 585.86 525.47 585.56 525.78 585.56 526.15 Z M 582.66 523.26 L 582.66 543.17 L 581.45 543.17 L 581.45 521.36 L 597.46 521.36 L 597.46 522.57 L 583.34 522.57 C 582.96 522.57 582.66 522.88 582.66 523.26 Z M 603.61 525.47 L 601.4 525.47 L 601.4 523.26 C 601.4 522.88 601.09 522.57 600.72 522.57 L 598.82 522.57 L 598.82 520.68 C 598.82 520.31 598.52 520 598.14 520 L 580.77 520 C 580.39 520 580.09 520.31 580.09 520.68 L 580.09 543.85 C 580.09 544.23 580.39 544.53 580.77 544.53 L 582.66 544.53 L 582.66 546.42 C 582.66 546.8 582.96 547.1 583.34 547.1 L 585.56 547.1 L 585.56 549.32 C 585.56 549.7 585.86 550 586.24 550 L 603.61 550 C 603.99 550 604.29 549.7 604.29 549.32 L 604.29 526.15 C 604.29 525.78 603.99 525.47 603.61 525.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 535px; margin-left: 606px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="606" y="539" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS S...
                </text>
            </switch>
        </g>
        <rect x="881" y="520" width="160" height="110" fill="none" stroke="#f78e04" stroke-width="2" pointer-events="all"/>
        <rect x="881" y="520" width="24.38" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 893.99 544.69 L 902.68 544.69 L 902.68 543.33 L 893.99 543.33 Z M 893.99 537.93 L 902.68 537.93 L 902.68 536.57 L 893.99 536.57 Z M 893.99 531.18 L 902.68 531.18 L 902.68 529.81 L 893.99 529.81 Z M 890.33 544.78 L 890.33 543.24 L 891.86 543.24 L 891.86 544.78 Z M 889.65 546.14 L 892.55 546.14 C 892.92 546.14 893.23 545.83 893.23 545.46 L 893.23 542.56 C 893.23 542.18 892.92 541.88 892.55 541.88 L 889.65 541.88 C 889.27 541.88 888.97 542.18 888.97 542.56 L 888.97 545.46 C 888.97 545.83 889.27 546.14 889.65 546.14 Z M 890.33 538.02 L 890.33 536.49 L 891.86 536.49 L 891.86 538.02 Z M 889.65 539.38 L 892.55 539.38 C 892.92 539.38 893.23 539.08 893.23 538.7 L 893.23 535.8 C 893.23 535.43 892.92 535.12 892.55 535.12 L 889.65 535.12 C 889.27 535.12 888.97 535.43 888.97 535.8 L 888.97 538.7 C 888.97 539.08 889.27 539.38 889.65 539.38 Z M 890.33 531.26 L 890.33 529.73 L 891.86 529.73 L 891.86 531.26 Z M 889.65 532.63 L 892.55 532.63 C 892.92 532.63 893.23 532.32 893.23 531.94 L 893.23 529.05 C 893.23 528.67 892.92 528.37 892.55 528.37 L 889.65 528.37 C 889.27 528.37 888.97 528.67 888.97 529.05 L 888.97 531.94 C 888.97 532.32 889.27 532.63 889.65 532.63 Z M 887.92 548.64 L 887.92 526.83 L 903.93 526.83 L 903.93 548.64 Z M 886.56 526.15 L 886.56 545.74 L 885.02 545.74 L 885.02 523.94 L 901.04 523.94 L 901.04 525.47 L 887.24 525.47 C 886.86 525.47 886.56 525.78 886.56 526.15 Z M 883.66 523.26 L 883.66 543.17 L 882.45 543.17 L 882.45 521.36 L 898.46 521.36 L 898.46 522.57 L 884.34 522.57 C 883.96 522.57 883.66 522.88 883.66 523.26 Z M 904.61 525.47 L 902.4 525.47 L 902.4 523.26 C 902.4 522.88 902.09 522.57 901.72 522.57 L 899.82 522.57 L 899.82 520.68 C 899.82 520.31 899.52 520 899.14 520 L 881.77 520 C 881.39 520 881.09 520.31 881.09 520.68 L 881.09 543.85 C 881.09 544.23 881.39 544.53 881.77 544.53 L 883.66 544.53 L 883.66 546.42 C 883.66 546.8 883.96 547.1 884.34 547.1 L 886.56 547.1 L 886.56 549.32 C 886.56 549.7 886.86 550 887.24 550 L 904.61 550 C 904.99 550 905.29 549.7 905.29 549.32 L 905.29 526.15 C 905.29 525.78 904.99 525.47 904.61 525.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 535px; margin-left: 907px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="907" y="539" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS S...
                </text>
            </switch>
        </g>
        <rect x="120" y="20" width="340" height="770" fill="none" stroke="#6c8ebf" stroke-width="4" stroke-dasharray="12 12" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 338px; height: 1px; padding-top: 17px; margin-left: 121px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                内製案件 ①
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="290" y="17" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    内製案件 ①
                </text>
            </switch>
        </g>
        <rect x="500" y="20" width="640" height="770" fill="none" stroke="#b85450" stroke-width="4" stroke-dasharray="12 12" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 638px; height: 1px; padding-top: 17px; margin-left: 501px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                内製案件 ②
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="820" y="17" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    内製案件 ②
                </text>
            </switch>
        </g>
        <path d="M 640 100 L 680 100 L 680 140 L 640 140 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 668.64 126.93 C 668.64 125.98 667.87 125.21 666.93 125.21 C 665.98 125.21 665.21 125.98 665.21 126.93 C 665.21 127.87 665.98 128.64 666.93 128.64 C 667.87 128.64 668.64 127.87 668.64 126.93 Z M 669.78 126.93 C 669.78 128.5 668.5 129.78 666.93 129.78 C 665.35 129.78 664.07 128.5 664.07 126.93 C 664.07 125.35 665.35 124.07 666.93 124.07 C 668.5 124.07 669.78 125.35 669.78 126.93 Z M 654.51 118.68 C 654.51 117.74 653.74 116.97 652.8 116.97 C 651.85 116.97 651.08 117.74 651.08 118.68 C 651.08 119.63 651.85 120.4 652.8 120.4 C 653.74 120.4 654.51 119.63 654.51 118.68 Z M 655.65 118.68 C 655.65 120.26 654.37 121.54 652.8 121.54 C 651.22 121.54 649.94 120.26 649.94 118.68 C 649.94 117.11 651.22 115.82 652.8 115.82 C 654.37 115.82 655.65 117.11 655.65 118.68 Z M 659.85 109.72 C 659.85 110.66 660.61 111.43 661.56 111.43 C 662.51 111.43 663.27 110.66 663.27 109.72 C 663.27 108.77 662.51 108 661.56 108 C 660.61 108 659.85 108.77 659.85 109.72 Z M 658.7 109.72 C 658.7 108.14 659.99 106.86 661.56 106.86 C 663.14 106.86 664.42 108.14 664.42 109.72 C 664.42 111.29 663.14 112.58 661.56 112.58 C 659.99 112.58 658.7 111.29 658.7 109.72 Z M 674.86 120 C 674.86 114.7 672.02 109.8 667.44 107.15 C 666.61 107.31 665.82 107.54 664.83 107.9 L 664.44 106.82 C 664.96 106.64 665.42 106.49 665.87 106.36 C 664.03 105.56 662.03 105.14 660 105.14 C 659.03 105.14 658.09 105.24 657.16 105.42 C 657.83 105.82 658.43 106.21 659 106.65 L 658.31 107.56 C 657.5 106.94 656.65 106.42 655.54 105.83 C 649.93 107.6 645.89 112.55 645.25 118.36 C 646.42 118.12 647.55 117.99 648.81 117.96 L 648.84 119.1 C 647.52 119.13 646.39 119.27 645.16 119.55 C 645.15 119.7 645.14 119.85 645.14 120 C 645.14 124.95 647.59 129.51 651.62 132.26 C 650.9 130.12 650.54 128.11 650.54 126.14 C 650.54 125.02 650.74 124.1 650.94 123.12 C 650.99 122.9 651.04 122.67 651.08 122.43 L 652.2 122.65 C 652.16 122.89 652.11 123.13 652.06 123.36 C 651.86 124.31 651.69 125.14 651.69 126.14 C 651.69 128.37 652.18 130.68 653.17 133.19 C 655.3 134.29 657.59 134.86 660 134.86 C 661.57 134.86 663.11 134.61 664.58 134.12 C 665.15 132.99 665.58 131.92 665.94 130.69 L 667.03 131 C 666.77 131.9 666.48 132.72 666.12 133.53 C 667.04 133.11 667.91 132.6 668.73 132 C 668.54 131.52 668.32 131.04 668.09 130.57 L 669.11 130.06 C 669.31 130.46 669.49 130.86 669.67 131.27 C 672.97 128.44 674.86 124.38 674.86 120 Z M 676 120 C 676 124.99 673.73 129.6 669.78 132.66 C 668.81 133.42 667.74 134.05 666.62 134.56 C 666.15 134.77 665.66 134.97 665.16 135.14 C 663.52 135.71 661.78 136 660 136 C 657.37 136 654.76 135.35 652.45 134.11 C 647.24 131.32 644 125.91 644 120 C 644 119.61 644.01 119.31 644.03 119.03 C 644.42 112.36 649 106.58 655.43 104.67 C 656.89 104.22 658.43 104 660 104 C 662.75 104 665.45 104.71 667.82 106.05 C 672.86 108.87 676 114.22 676 120 Z M 658.47 111.69 L 657.71 110.83 C 656.43 111.95 655.44 113.14 654.27 114.93 L 655.23 115.55 C 656.33 113.85 657.27 112.74 658.47 111.69 Z M 656.73 119.1 L 656.36 120.18 C 658.98 121.08 661.27 122.52 663.56 124.71 L 664.35 123.88 C 661.94 121.58 659.51 120.06 656.73 119.1 Z M 664.37 112.38 C 666.51 115.65 667.72 119.24 667.96 123.05 L 666.82 123.12 C 666.59 119.51 665.45 116.11 663.41 113.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 660px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudF...
                </text>
            </switch>
        </g>
        <rect x="640" y="300" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 660 300 C 648.97 300 640 308.97 640 320 C 640 331.03 648.97 340 660 340 C 671.03 340 680 331.03 680 320 C 680 308.97 671.03 300 660 300 Z M 660 338.18 C 649.97 338.18 641.82 330.03 641.82 320 C 641.82 309.97 649.97 301.82 660 301.82 C 670.03 301.82 678.18 309.97 678.18 320 C 678.18 330.03 670.03 338.18 660 338.18 Z M 671.85 325.45 L 670.45 325.45 L 670.45 322.39 C 670.45 321.88 670.05 321.48 669.55 321.48 L 667.27 321.48 L 667.27 318.41 C 667.27 317.91 666.87 317.5 666.36 317.5 L 660.91 317.5 L 660.91 315.34 L 666.36 315.34 C 666.87 315.34 667.27 314.93 667.27 314.43 L 667.27 307.27 C 667.27 306.77 666.87 306.36 666.36 306.36 L 653.64 306.36 C 653.13 306.36 652.73 306.77 652.73 307.27 L 652.73 314.43 C 652.73 314.93 653.13 315.34 653.64 315.34 L 659.09 315.34 L 659.09 317.5 L 653.64 317.5 C 653.13 317.5 652.73 317.91 652.73 318.41 L 652.73 321.48 L 650.46 321.48 C 649.95 321.48 649.55 321.88 649.55 322.39 L 649.55 325.45 L 648.15 325.45 C 647.65 325.45 647.24 325.86 647.24 326.36 L 647.24 330.34 C 647.24 330.84 647.65 331.25 648.15 331.25 L 652.05 331.25 C 652.55 331.25 652.96 330.84 652.96 330.34 L 652.96 326.36 C 652.96 325.86 652.55 325.45 652.05 325.45 L 651.36 325.45 L 651.36 323.3 L 655.11 323.3 L 655.11 325.45 L 654.43 325.45 C 653.93 325.45 653.52 325.86 653.52 326.36 L 653.52 330.34 C 653.52 330.84 653.93 331.25 654.43 331.25 L 658.41 331.25 C 658.91 331.25 659.32 330.84 659.32 330.34 L 659.32 326.36 C 659.32 325.86 658.91 325.45 658.41 325.45 L 656.93 325.45 L 656.93 322.39 C 656.93 321.88 656.53 321.48 656.02 321.48 L 654.55 321.48 L 654.55 319.32 L 665.45 319.32 L 665.45 321.48 L 663.98 321.48 C 663.47 321.48 663.07 321.88 663.07 322.39 L 663.07 325.45 L 661.59 325.45 C 661.09 325.45 660.68 325.86 660.68 326.36 L 660.68 330.34 C 660.68 330.84 661.09 331.25 661.59 331.25 L 665.57 331.25 C 666.07 331.25 666.48 330.84 666.48 330.34 L 666.48 326.36 C 666.48 325.86 666.07 325.45 665.57 325.45 L 664.89 325.45 L 664.89 323.3 L 668.64 323.3 L 668.64 325.45 L 667.9 325.45 C 667.4 325.45 666.99 325.86 666.99 326.36 L 666.99 330.34 C 666.99 330.84 667.4 331.25 667.9 331.25 L 671.85 331.25 C 672.35 331.25 672.76 330.84 672.76 330.34 L 672.76 326.36 C 672.76 325.86 672.35 325.45 671.85 325.45 Z M 654.55 313.52 L 654.55 308.18 L 665.45 308.18 L 665.45 313.52 Z M 649.06 329.43 L 649.06 327.27 L 651.14 327.27 L 651.14 329.43 Z M 655.34 329.43 L 655.34 327.27 L 657.5 327.27 L 657.5 329.43 Z M 662.5 329.43 L 662.5 327.27 L 664.66 327.27 L 664.66 329.43 Z M 668.81 329.43 L 668.81 327.27 L 670.94 327.27 L 670.94 329.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 347px; margin-left: 660px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="359" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="940" y="560" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 979.09 560.07 L 940.91 560.07 C 940.41 560.07 940 560.48 940 560.98 L 940 584.85 C 940 585.35 940.41 585.76 940.91 585.76 L 979.09 585.76 C 979.59 585.76 980 585.35 980 584.85 L 980 560.98 C 980 560.48 979.59 560.07 979.09 560.07 Z M 941.82 583.94 L 941.82 561.89 L 978.18 561.89 L 978.18 583.94 Z M 944.77 581.67 L 946.59 581.67 L 946.59 564.17 L 944.77 564.17 Z M 949.55 581.67 L 951.36 581.67 L 951.36 564.17 L 949.55 564.17 Z M 954.32 581.67 L 956.14 581.67 L 956.14 564.17 L 954.32 564.17 Z M 959.09 581.67 L 960.91 581.67 L 960.91 564.17 L 959.09 564.17 Z M 963.86 581.67 L 965.68 581.67 L 965.68 564.17 L 963.86 564.17 Z M 968.64 581.67 L 970.45 581.67 L 970.45 564.17 L 968.64 564.17 Z M 973.41 581.67 L 975.23 581.67 L 975.23 564.17 L 973.41 564.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 593px; margin-left: 960px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Front Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="960" y="605" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Front...
                </text>
            </switch>
        </g>
        <path d="M 940 100 L 980 100 L 980 140 L 940 140 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 968.64 126.93 C 968.64 125.98 967.87 125.21 966.93 125.21 C 965.98 125.21 965.21 125.98 965.21 126.93 C 965.21 127.87 965.98 128.64 966.93 128.64 C 967.87 128.64 968.64 127.87 968.64 126.93 Z M 969.78 126.93 C 969.78 128.5 968.5 129.78 966.93 129.78 C 965.35 129.78 964.07 128.5 964.07 126.93 C 964.07 125.35 965.35 124.07 966.93 124.07 C 968.5 124.07 969.78 125.35 969.78 126.93 Z M 954.51 118.68 C 954.51 117.74 953.74 116.97 952.8 116.97 C 951.85 116.97 951.08 117.74 951.08 118.68 C 951.08 119.63 951.85 120.4 952.8 120.4 C 953.74 120.4 954.51 119.63 954.51 118.68 Z M 955.65 118.68 C 955.65 120.26 954.37 121.54 952.8 121.54 C 951.22 121.54 949.94 120.26 949.94 118.68 C 949.94 117.11 951.22 115.82 952.8 115.82 C 954.37 115.82 955.65 117.11 955.65 118.68 Z M 959.85 109.72 C 959.85 110.66 960.61 111.43 961.56 111.43 C 962.51 111.43 963.27 110.66 963.27 109.72 C 963.27 108.77 962.51 108 961.56 108 C 960.61 108 959.85 108.77 959.85 109.72 Z M 958.7 109.72 C 958.7 108.14 959.99 106.86 961.56 106.86 C 963.14 106.86 964.42 108.14 964.42 109.72 C 964.42 111.29 963.14 112.58 961.56 112.58 C 959.99 112.58 958.7 111.29 958.7 109.72 Z M 974.86 120 C 974.86 114.7 972.02 109.8 967.44 107.15 C 966.61 107.31 965.82 107.54 964.83 107.9 L 964.44 106.82 C 964.96 106.64 965.42 106.49 965.87 106.36 C 964.03 105.56 962.03 105.14 960 105.14 C 959.03 105.14 958.09 105.24 957.16 105.42 C 957.83 105.82 958.43 106.21 959 106.65 L 958.31 107.56 C 957.5 106.94 956.65 106.42 955.54 105.83 C 949.93 107.6 945.89 112.55 945.25 118.36 C 946.42 118.12 947.55 117.99 948.81 117.96 L 948.84 119.1 C 947.52 119.13 946.39 119.27 945.16 119.55 C 945.15 119.7 945.14 119.85 945.14 120 C 945.14 124.95 947.59 129.51 951.62 132.26 C 950.9 130.12 950.54 128.11 950.54 126.14 C 950.54 125.02 950.74 124.1 950.94 123.12 C 950.99 122.9 951.04 122.67 951.08 122.43 L 952.2 122.65 C 952.16 122.89 952.11 123.13 952.06 123.36 C 951.86 124.31 951.69 125.14 951.69 126.14 C 951.69 128.37 952.18 130.68 953.17 133.19 C 955.3 134.29 957.59 134.86 960 134.86 C 961.57 134.86 963.11 134.61 964.58 134.12 C 965.15 132.99 965.58 131.92 965.94 130.69 L 967.03 131 C 966.77 131.9 966.48 132.72 966.12 133.53 C 967.04 133.11 967.91 132.6 968.73 132 C 968.54 131.52 968.32 131.04 968.09 130.57 L 969.11 130.06 C 969.31 130.46 969.49 130.86 969.67 131.27 C 972.97 128.44 974.86 124.38 974.86 120 Z M 976 120 C 976 124.99 973.73 129.6 969.78 132.66 C 968.81 133.42 967.74 134.05 966.62 134.56 C 966.15 134.77 965.66 134.97 965.16 135.14 C 963.52 135.71 961.78 136 960 136 C 957.37 136 954.76 135.35 952.45 134.11 C 947.24 131.32 944 125.91 944 120 C 944 119.61 944.01 119.31 944.03 119.03 C 944.42 112.36 949 106.58 955.43 104.67 C 956.89 104.22 958.43 104 960 104 C 962.75 104 965.45 104.71 967.82 106.05 C 972.86 108.87 976 114.22 976 120 Z M 958.47 111.69 L 957.71 110.83 C 956.43 111.95 955.44 113.14 954.27 114.93 L 955.23 115.55 C 956.33 113.85 957.27 112.74 958.47 111.69 Z M 956.73 119.1 L 956.36 120.18 C 958.98 121.08 961.27 122.52 963.56 124.71 L 964.35 123.88 C 961.94 121.58 959.51 120.06 956.73 119.1 Z M 964.37 112.38 C 966.51 115.65 967.72 119.24 967.96 123.05 L 966.82 123.12 C 966.59 119.51 965.45 116.11 963.41 113.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 960px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="960" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudF...
                </text>
            </switch>
        </g>
        <rect x="940" y="300" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 960 300 C 948.97 300 940 308.97 940 320 C 940 331.03 948.97 340 960 340 C 971.03 340 980 331.03 980 320 C 980 308.97 971.03 300 960 300 Z M 960 338.18 C 949.97 338.18 941.82 330.03 941.82 320 C 941.82 309.97 949.97 301.82 960 301.82 C 970.03 301.82 978.18 309.97 978.18 320 C 978.18 330.03 970.03 338.18 960 338.18 Z M 971.85 325.45 L 970.45 325.45 L 970.45 322.39 C 970.45 321.88 970.05 321.48 969.55 321.48 L 967.27 321.48 L 967.27 318.41 C 967.27 317.91 966.87 317.5 966.36 317.5 L 960.91 317.5 L 960.91 315.34 L 966.36 315.34 C 966.87 315.34 967.27 314.93 967.27 314.43 L 967.27 307.27 C 967.27 306.77 966.87 306.36 966.36 306.36 L 953.64 306.36 C 953.13 306.36 952.73 306.77 952.73 307.27 L 952.73 314.43 C 952.73 314.93 953.13 315.34 953.64 315.34 L 959.09 315.34 L 959.09 317.5 L 953.64 317.5 C 953.13 317.5 952.73 317.91 952.73 318.41 L 952.73 321.48 L 950.46 321.48 C 949.95 321.48 949.55 321.88 949.55 322.39 L 949.55 325.45 L 948.15 325.45 C 947.65 325.45 947.24 325.86 947.24 326.36 L 947.24 330.34 C 947.24 330.84 947.65 331.25 948.15 331.25 L 952.05 331.25 C 952.55 331.25 952.96 330.84 952.96 330.34 L 952.96 326.36 C 952.96 325.86 952.55 325.45 952.05 325.45 L 951.36 325.45 L 951.36 323.3 L 955.11 323.3 L 955.11 325.45 L 954.43 325.45 C 953.93 325.45 953.52 325.86 953.52 326.36 L 953.52 330.34 C 953.52 330.84 953.93 331.25 954.43 331.25 L 958.41 331.25 C 958.91 331.25 959.32 330.84 959.32 330.34 L 959.32 326.36 C 959.32 325.86 958.91 325.45 958.41 325.45 L 956.93 325.45 L 956.93 322.39 C 956.93 321.88 956.53 321.48 956.02 321.48 L 954.55 321.48 L 954.55 319.32 L 965.45 319.32 L 965.45 321.48 L 963.98 321.48 C 963.47 321.48 963.07 321.88 963.07 322.39 L 963.07 325.45 L 961.59 325.45 C 961.09 325.45 960.68 325.86 960.68 326.36 L 960.68 330.34 C 960.68 330.84 961.09 331.25 961.59 331.25 L 965.57 331.25 C 966.07 331.25 966.48 330.84 966.48 330.34 L 966.48 326.36 C 966.48 325.86 966.07 325.45 965.57 325.45 L 964.89 325.45 L 964.89 323.3 L 968.64 323.3 L 968.64 325.45 L 967.9 325.45 C 967.4 325.45 966.99 325.86 966.99 326.36 L 966.99 330.34 C 966.99 330.84 967.4 331.25 967.9 331.25 L 971.85 331.25 C 972.35 331.25 972.76 330.84 972.76 330.34 L 972.76 326.36 C 972.76 325.86 972.35 325.45 971.85 325.45 Z M 954.55 313.52 L 954.55 308.18 L 965.45 308.18 L 965.45 313.52 Z M 949.06 329.43 L 949.06 327.27 L 951.14 327.27 L 951.14 329.43 Z M 955.34 329.43 L 955.34 327.27 L 957.5 327.27 L 957.5 329.43 Z M 962.5 329.43 L 962.5 327.27 L 964.66 327.27 L 964.66 329.43 Z M 968.81 329.43 L 968.81 327.27 L 970.94 327.27 L 970.94 329.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 347px; margin-left: 960px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="960" y="359" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="640" y="560" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 679.09 560.07 L 640.91 560.07 C 640.41 560.07 640 560.48 640 560.98 L 640 584.85 C 640 585.35 640.41 585.76 640.91 585.76 L 679.09 585.76 C 679.59 585.76 680 585.35 680 584.85 L 680 560.98 C 680 560.48 679.59 560.07 679.09 560.07 Z M 641.82 583.94 L 641.82 561.89 L 678.18 561.89 L 678.18 583.94 Z M 644.77 581.67 L 646.59 581.67 L 646.59 564.17 L 644.77 564.17 Z M 649.55 581.67 L 651.36 581.67 L 651.36 564.17 L 649.55 564.17 Z M 654.32 581.67 L 656.14 581.67 L 656.14 564.17 L 654.32 564.17 Z M 659.09 581.67 L 660.91 581.67 L 660.91 564.17 L 659.09 564.17 Z M 663.86 581.67 L 665.68 581.67 L 665.68 564.17 L 663.86 564.17 Z M 668.64 581.67 L 670.45 581.67 L 670.45 564.17 L 668.64 564.17 Z M 673.41 581.67 L 675.23 581.67 L 675.23 564.17 L 673.41 564.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 593px; margin-left: 660px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Front Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="605" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Front...
                </text>
            </switch>
        </g>
        <path d="M 300 100 L 340 100 L 340 140 L 300 140 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 328.64 126.93 C 328.64 125.98 327.87 125.21 326.93 125.21 C 325.98 125.21 325.21 125.98 325.21 126.93 C 325.21 127.87 325.98 128.64 326.93 128.64 C 327.87 128.64 328.64 127.87 328.64 126.93 Z M 329.78 126.93 C 329.78 128.5 328.5 129.78 326.93 129.78 C 325.35 129.78 324.07 128.5 324.07 126.93 C 324.07 125.35 325.35 124.07 326.93 124.07 C 328.5 124.07 329.78 125.35 329.78 126.93 Z M 314.51 118.68 C 314.51 117.74 313.74 116.97 312.8 116.97 C 311.85 116.97 311.08 117.74 311.08 118.68 C 311.08 119.63 311.85 120.4 312.8 120.4 C 313.74 120.4 314.51 119.63 314.51 118.68 Z M 315.65 118.68 C 315.65 120.26 314.37 121.54 312.8 121.54 C 311.22 121.54 309.94 120.26 309.94 118.68 C 309.94 117.11 311.22 115.82 312.8 115.82 C 314.37 115.82 315.65 117.11 315.65 118.68 Z M 319.85 109.72 C 319.85 110.66 320.61 111.43 321.56 111.43 C 322.51 111.43 323.27 110.66 323.27 109.72 C 323.27 108.77 322.51 108 321.56 108 C 320.61 108 319.85 108.77 319.85 109.72 Z M 318.7 109.72 C 318.7 108.14 319.99 106.86 321.56 106.86 C 323.14 106.86 324.42 108.14 324.42 109.72 C 324.42 111.29 323.14 112.58 321.56 112.58 C 319.99 112.58 318.7 111.29 318.7 109.72 Z M 334.86 120 C 334.86 114.7 332.02 109.8 327.44 107.15 C 326.61 107.31 325.82 107.54 324.83 107.9 L 324.44 106.82 C 324.96 106.64 325.42 106.49 325.87 106.36 C 324.03 105.56 322.03 105.14 320 105.14 C 319.03 105.14 318.09 105.24 317.16 105.42 C 317.83 105.82 318.43 106.21 319 106.65 L 318.31 107.56 C 317.5 106.94 316.65 106.42 315.54 105.83 C 309.93 107.6 305.89 112.55 305.25 118.36 C 306.42 118.12 307.55 117.99 308.81 117.96 L 308.84 119.1 C 307.52 119.13 306.39 119.27 305.16 119.55 C 305.15 119.7 305.14 119.85 305.14 120 C 305.14 124.95 307.59 129.51 311.62 132.26 C 310.9 130.12 310.54 128.11 310.54 126.14 C 310.54 125.02 310.74 124.1 310.94 123.12 C 310.99 122.9 311.04 122.67 311.08 122.43 L 312.2 122.65 C 312.16 122.89 312.11 123.13 312.06 123.36 C 311.86 124.31 311.69 125.14 311.69 126.14 C 311.69 128.37 312.18 130.68 313.17 133.19 C 315.3 134.29 317.59 134.86 320 134.86 C 321.57 134.86 323.11 134.61 324.58 134.12 C 325.15 132.99 325.58 131.92 325.94 130.69 L 327.03 131 C 326.77 131.9 326.48 132.72 326.12 133.53 C 327.04 133.11 327.91 132.6 328.73 132 C 328.54 131.52 328.32 131.04 328.09 130.57 L 329.11 130.06 C 329.31 130.46 329.49 130.86 329.67 131.27 C 332.97 128.44 334.86 124.38 334.86 120 Z M 336 120 C 336 124.99 333.73 129.6 329.78 132.66 C 328.81 133.42 327.74 134.05 326.62 134.56 C 326.15 134.77 325.66 134.97 325.16 135.14 C 323.52 135.71 321.78 136 320 136 C 317.37 136 314.76 135.35 312.45 134.11 C 307.24 131.32 304 125.91 304 120 C 304 119.61 304.01 119.31 304.03 119.03 C 304.42 112.36 309 106.58 315.43 104.67 C 316.89 104.22 318.43 104 320 104 C 322.75 104 325.45 104.71 327.82 106.05 C 332.86 108.87 336 114.22 336 120 Z M 318.47 111.69 L 317.71 110.83 C 316.43 111.95 315.44 113.14 314.27 114.93 L 315.23 115.55 C 316.33 113.85 317.27 112.74 318.47 111.69 Z M 316.73 119.1 L 316.36 120.18 C 318.98 121.08 321.27 122.52 323.56 124.71 L 324.35 123.88 C 321.94 121.58 319.51 120.06 316.73 119.1 Z M 324.37 112.38 C 326.51 115.65 327.72 119.24 327.96 123.05 L 326.82 123.12 C 326.59 119.51 325.45 116.11 323.41 113.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 320px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudF...
                </text>
            </switch>
        </g>
        <rect x="300" y="300" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 320 300 C 308.97 300 300 308.97 300 320 C 300 331.03 308.97 340 320 340 C 331.03 340 340 331.03 340 320 C 340 308.97 331.03 300 320 300 Z M 320 338.18 C 309.97 338.18 301.82 330.03 301.82 320 C 301.82 309.97 309.97 301.82 320 301.82 C 330.03 301.82 338.18 309.97 338.18 320 C 338.18 330.03 330.03 338.18 320 338.18 Z M 331.85 325.45 L 330.45 325.45 L 330.45 322.39 C 330.45 321.88 330.05 321.48 329.55 321.48 L 327.27 321.48 L 327.27 318.41 C 327.27 317.91 326.87 317.5 326.36 317.5 L 320.91 317.5 L 320.91 315.34 L 326.36 315.34 C 326.87 315.34 327.27 314.93 327.27 314.43 L 327.27 307.27 C 327.27 306.77 326.87 306.36 326.36 306.36 L 313.64 306.36 C 313.13 306.36 312.73 306.77 312.73 307.27 L 312.73 314.43 C 312.73 314.93 313.13 315.34 313.64 315.34 L 319.09 315.34 L 319.09 317.5 L 313.64 317.5 C 313.13 317.5 312.73 317.91 312.73 318.41 L 312.73 321.48 L 310.46 321.48 C 309.95 321.48 309.55 321.88 309.55 322.39 L 309.55 325.45 L 308.15 325.45 C 307.65 325.45 307.24 325.86 307.24 326.36 L 307.24 330.34 C 307.24 330.84 307.65 331.25 308.15 331.25 L 312.05 331.25 C 312.55 331.25 312.96 330.84 312.96 330.34 L 312.96 326.36 C 312.96 325.86 312.55 325.45 312.05 325.45 L 311.36 325.45 L 311.36 323.3 L 315.11 323.3 L 315.11 325.45 L 314.43 325.45 C 313.93 325.45 313.52 325.86 313.52 326.36 L 313.52 330.34 C 313.52 330.84 313.93 331.25 314.43 331.25 L 318.41 331.25 C 318.91 331.25 319.32 330.84 319.32 330.34 L 319.32 326.36 C 319.32 325.86 318.91 325.45 318.41 325.45 L 316.93 325.45 L 316.93 322.39 C 316.93 321.88 316.53 321.48 316.02 321.48 L 314.55 321.48 L 314.55 319.32 L 325.45 319.32 L 325.45 321.48 L 323.98 321.48 C 323.47 321.48 323.07 321.88 323.07 322.39 L 323.07 325.45 L 321.59 325.45 C 321.09 325.45 320.68 325.86 320.68 326.36 L 320.68 330.34 C 320.68 330.84 321.09 331.25 321.59 331.25 L 325.57 331.25 C 326.07 331.25 326.48 330.84 326.48 330.34 L 326.48 326.36 C 326.48 325.86 326.07 325.45 325.57 325.45 L 324.89 325.45 L 324.89 323.3 L 328.64 323.3 L 328.64 325.45 L 327.9 325.45 C 327.4 325.45 326.99 325.86 326.99 326.36 L 326.99 330.34 C 326.99 330.84 327.4 331.25 327.9 331.25 L 331.85 331.25 C 332.35 331.25 332.76 330.84 332.76 330.34 L 332.76 326.36 C 332.76 325.86 332.35 325.45 331.85 325.45 Z M 314.55 313.52 L 314.55 308.18 L 325.45 308.18 L 325.45 313.52 Z M 309.06 329.43 L 309.06 327.27 L 311.14 327.27 L 311.14 329.43 Z M 315.34 329.43 L 315.34 327.27 L 317.5 327.27 L 317.5 329.43 Z M 322.5 329.43 L 322.5 327.27 L 324.66 327.27 L 324.66 329.43 Z M 328.81 329.43 L 328.81 327.27 L 330.94 327.27 L 330.94 329.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 347px; margin-left: 320px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="359" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="300" y="690" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 339.09 690.07 L 300.91 690.07 C 300.41 690.07 300 690.48 300 690.98 L 300 714.85 C 300 715.35 300.41 715.76 300.91 715.76 L 339.09 715.76 C 339.59 715.76 340 715.35 340 714.85 L 340 690.98 C 340 690.48 339.59 690.07 339.09 690.07 Z M 301.82 713.94 L 301.82 691.89 L 338.18 691.89 L 338.18 713.94 Z M 304.77 711.67 L 306.59 711.67 L 306.59 694.17 L 304.77 694.17 Z M 309.55 711.67 L 311.36 711.67 L 311.36 694.17 L 309.55 694.17 Z M 314.32 711.67 L 316.14 711.67 L 316.14 694.17 L 314.32 694.17 Z M 319.09 711.67 L 320.91 711.67 L 320.91 694.17 L 319.09 694.17 Z M 323.86 711.67 L 325.68 711.67 L 325.68 694.17 L 323.86 694.17 Z M 328.64 711.67 L 330.45 711.67 L 330.45 694.17 L 328.64 694.17 Z M 333.41 711.67 L 335.23 711.67 L 335.23 694.17 L 333.41 694.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 723px; margin-left: 320px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Back Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="735" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Back C...
                </text>
            </switch>
        </g>
        <rect x="300" y="560" width="40" height="25.83" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 339.09 560.07 L 300.91 560.07 C 300.41 560.07 300 560.48 300 560.98 L 300 584.85 C 300 585.35 300.41 585.76 300.91 585.76 L 339.09 585.76 C 339.59 585.76 340 585.35 340 584.85 L 340 560.98 C 340 560.48 339.59 560.07 339.09 560.07 Z M 301.82 583.94 L 301.82 561.89 L 338.18 561.89 L 338.18 583.94 Z M 304.77 581.67 L 306.59 581.67 L 306.59 564.17 L 304.77 564.17 Z M 309.55 581.67 L 311.36 581.67 L 311.36 564.17 L 309.55 564.17 Z M 314.32 581.67 L 316.14 581.67 L 316.14 564.17 L 314.32 564.17 Z M 319.09 581.67 L 320.91 581.67 L 320.91 564.17 L 319.09 564.17 Z M 323.86 581.67 L 325.68 581.67 L 325.68 564.17 L 323.86 564.17 Z M 328.64 581.67 L 330.45 581.67 L 330.45 564.17 L 328.64 564.17 Z M 333.41 581.67 L 335.23 581.67 L 335.23 564.17 L 333.41 564.17 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 593px; margin-left: 320px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Front Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="605" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Front...
                </text>
            </switch>
        </g>
        <ellipse cx="881" cy="575" rx="15" ry="15" fill="rgb(255, 255, 255)" stroke="#b266ff" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 575px; margin-left: 867px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                SG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="881" y="579" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    SG
                </text>
            </switch>
        </g>
        <ellipse cx="400" cy="575" rx="15" ry="15" fill="rgb(255, 255, 255)" stroke="#b266ff" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 28px; height: 1px; padding-top: 575px; margin-left: 386px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                SG
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="579" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    SG
                </text>
            </switch>
        </g>
        <path d="M 1260 660 L 1300 660 L 1300 700 L 1260 700 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 1283.91 688.73 L 1291.19 688.73 L 1291.19 689.85 L 1283.91 689.85 L 1283.91 690.97 L 1282.8 690.97 L 1282.8 689.85 L 1280.56 689.85 L 1280.56 688.73 L 1282.8 688.73 L 1282.8 688.17 L 1283.91 688.17 Z M 1286.15 685.37 L 1291.19 685.37 L 1291.19 686.49 L 1286.15 686.49 L 1286.15 687.61 L 1285.03 687.61 L 1285.03 686.49 L 1280.56 686.49 L 1280.56 685.37 L 1285.03 685.37 L 1285.03 684.81 L 1286.15 684.81 Z M 1288.95 682.02 L 1291.19 682.02 L 1291.19 683.13 L 1288.95 683.13 L 1288.95 684.25 L 1287.83 684.25 L 1287.83 683.13 L 1280.56 683.13 L 1280.56 682.02 L 1287.83 682.02 L 1287.83 681.46 L 1288.95 681.46 Z M 1269.37 684.25 L 1274.41 684.25 L 1274.41 685.37 L 1269.37 685.37 C 1268.67 685.37 1267.92 685.16 1267.37 684.81 C 1266.24 684.09 1264.34 682.42 1264.34 679.25 C 1264.34 675.4 1266.95 673.98 1268.47 673.49 L 1268.44 672.98 C 1268.44 669.81 1270.53 666.56 1273.31 665.38 C 1276.57 664 1280.02 664.69 1282.54 667.22 C 1283.32 668 1283.97 668.96 1284.46 670.07 C 1285.45 669.24 1286.8 668.96 1288.05 669.37 C 1289.65 669.89 1290.64 671.37 1290.75 673.35 C 1292.34 673.73 1295.66 675.03 1295.66 679.3 C 1295.66 679.46 1295.65 679.61 1295.65 679.76 L 1294.53 679.73 C 1294.53 679.58 1294.54 679.44 1294.54 679.3 C 1294.54 675.53 1291.43 674.58 1290.1 674.35 C 1289.95 674.33 1289.81 674.24 1289.73 674.11 C 1289.64 673.99 1289.61 673.83 1289.64 673.68 C 1289.63 672.02 1288.92 670.83 1287.7 670.43 C 1286.61 670.07 1285.41 670.47 1284.72 671.42 C 1284.59 671.58 1284.39 671.67 1284.18 671.64 C 1283.98 671.61 1283.81 671.47 1283.74 671.27 C 1283.27 669.96 1282.6 668.86 1281.75 668.01 C 1279.56 665.81 1276.57 665.22 1273.75 666.41 C 1271.4 667.41 1269.56 670.28 1269.56 672.94 L 1269.61 673.87 C 1269.63 674.14 1269.45 674.38 1269.19 674.44 C 1267.8 674.8 1265.46 675.89 1265.46 679.25 C 1265.46 681.75 1266.82 683.14 1267.97 683.86 C 1268.35 684.1 1268.88 684.25 1269.37 684.25 Z M 1294.54 687.23 L 1293.55 687.17 C 1293.29 687.16 1293.04 687.34 1292.98 687.61 C 1292.79 688.45 1292.46 689.24 1292 689.97 C 1291.85 690.2 1291.9 690.5 1292.1 690.69 L 1292.84 691.34 L 1291 693.18 L 1290.35 692.44 C 1290.17 692.24 1289.86 692.2 1289.63 692.34 C 1288.9 692.8 1288.11 693.13 1287.27 693.32 C 1287 693.38 1286.82 693.63 1286.83 693.9 L 1286.89 694.88 L 1284.3 694.88 L 1284.35 693.9 C 1284.37 693.63 1284.18 693.38 1283.92 693.32 C 1283.08 693.13 1282.28 692.8 1281.55 692.34 C 1281.32 692.19 1281.02 692.24 1280.84 692.44 L 1280.18 693.18 L 1278.35 691.34 L 1279.08 690.69 C 1279.29 690.5 1279.33 690.2 1279.18 689.97 C 1278.72 689.24 1278.4 688.45 1278.21 687.61 C 1278.15 687.34 1277.88 687.16 1277.63 687.17 L 1276.64 687.23 L 1276.64 684.63 L 1277.63 684.69 C 1277.89 684.71 1278.15 684.52 1278.21 684.26 C 1278.4 683.42 1278.73 682.63 1279.19 681.9 C 1279.34 681.67 1279.29 681.36 1279.09 681.18 L 1278.35 680.52 L 1280.18 678.69 L 1280.84 679.43 C 1281.03 679.63 1281.33 679.67 1281.56 679.53 C 1282.29 679.07 1283.08 678.74 1283.92 678.55 C 1284.18 678.49 1284.37 678.25 1284.35 677.97 L 1284.3 676.98 L 1286.89 676.98 L 1286.83 677.97 C 1286.82 678.25 1287 678.49 1287.27 678.55 C 1288.1 678.74 1288.9 679.07 1289.63 679.53 C 1289.86 679.67 1290.16 679.63 1290.34 679.43 L 1291 678.69 L 1292.84 680.52 L 1292.1 681.18 C 1291.89 681.36 1291.85 681.67 1292 681.9 C 1292.46 682.62 1292.79 683.42 1292.98 684.26 C 1293.04 684.52 1293.29 684.71 1293.55 684.69 L 1294.54 684.63 Z M 1295.07 683.48 L 1293.95 683.55 C 1293.77 682.91 1293.51 682.29 1293.19 681.71 L 1294.02 680.96 C 1294.14 680.86 1294.21 680.71 1294.21 680.56 C 1294.22 680.41 1294.16 680.26 1294.05 680.15 L 1291.37 677.48 C 1291.27 677.37 1291.11 677.3 1290.96 677.31 C 1290.81 677.32 1290.66 677.38 1290.56 677.5 L 1289.81 678.34 C 1289.23 678.01 1288.62 677.76 1287.98 677.58 L 1288.04 676.45 C 1288.05 676.3 1287.99 676.15 1287.89 676.04 C 1287.78 675.93 1287.64 675.86 1287.48 675.86 L 1283.7 675.86 C 1283.55 675.86 1283.4 675.93 1283.3 676.04 C 1283.19 676.15 1283.14 676.3 1283.14 676.45 L 1283.21 677.58 C 1282.57 677.76 1281.95 678.01 1281.37 678.34 L 1280.62 677.5 C 1280.52 677.38 1280.38 677.32 1280.22 677.31 C 1280.06 677.31 1279.92 677.37 1279.81 677.48 L 1277.14 680.15 C 1277.03 680.26 1276.97 680.41 1276.97 680.56 C 1276.98 680.71 1277.05 680.86 1277.16 680.96 L 1278 681.71 C 1277.68 682.29 1277.42 682.91 1277.24 683.55 L 1276.12 683.48 C 1275.97 683.47 1275.81 683.53 1275.7 683.63 C 1275.59 683.74 1275.53 683.89 1275.53 684.04 L 1275.53 687.82 C 1275.53 687.98 1275.59 688.12 1275.7 688.23 C 1275.81 688.33 1275.97 688.39 1276.12 688.38 L 1277.23 688.32 C 1277.42 688.96 1277.67 689.57 1278 690.16 L 1277.16 690.9 C 1277.05 691 1276.98 691.15 1276.97 691.3 C 1276.97 691.46 1277.03 691.61 1277.14 691.71 L 1279.81 694.39 C 1279.92 694.5 1280.08 694.56 1280.22 694.55 C 1280.38 694.55 1280.52 694.48 1280.62 694.36 L 1281.37 693.53 C 1281.95 693.86 1282.57 694.11 1283.21 694.29 L 1283.14 695.41 C 1283.14 695.56 1283.19 695.71 1283.3 695.82 C 1283.4 695.94 1283.55 696 1283.7 696 L 1287.48 696 C 1287.64 696 1287.78 695.94 1287.89 695.82 C 1287.99 695.71 1288.05 695.56 1288.04 695.41 L 1287.98 694.3 C 1288.62 694.11 1289.24 693.86 1289.82 693.53 L 1290.56 694.36 C 1290.66 694.48 1290.81 694.55 1290.96 694.55 C 1291.11 694.56 1291.27 694.5 1291.37 694.39 L 1294.05 691.71 C 1294.16 691.61 1294.22 691.46 1294.21 691.3 C 1294.21 691.15 1294.14 691 1294.02 690.9 L 1293.19 690.16 C 1293.51 689.57 1293.77 688.96 1293.95 688.32 L 1295.07 688.38 C 1295.22 688.39 1295.37 688.33 1295.48 688.23 C 1295.6 688.12 1295.66 687.98 1295.66 687.82 L 1295.66 684.04 C 1295.66 683.89 1295.6 683.74 1295.48 683.63 C 1295.37 683.53 1295.22 683.47 1295.07 683.48 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 707px; margin-left: 1280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                SSM
                                <br/>
                                Parameter Store
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1280" y="719" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SSM...
                </text>
            </switch>
        </g>
        <path d="M 1260 535 L 1300 535 L 1300 575 L 1260 575 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 1287.43 556.97 C 1287.62 556.97 1287.79 556.88 1287.9 556.72 C 1288.26 556.2 1291.43 551.55 1291.43 549.45 C 1291.43 547.15 1289.71 545.41 1287.43 545.41 C 1285.15 545.41 1283.43 547.15 1283.43 549.45 C 1283.43 551.55 1286.6 556.2 1286.96 556.72 C 1287.07 556.88 1287.24 556.97 1287.43 556.97 Z M 1287.43 546.56 C 1289.06 546.56 1290.29 547.8 1290.29 549.45 C 1290.29 550.72 1288.52 553.69 1287.43 555.36 C 1286.34 553.7 1284.57 550.72 1284.57 549.45 C 1284.57 547.8 1285.8 546.56 1287.43 546.56 Z M 1289.72 549.45 C 1289.72 548.24 1288.63 547.14 1287.43 547.14 C 1286.23 547.14 1285.14 548.24 1285.14 549.45 C 1285.14 550.66 1286.23 551.77 1287.43 551.77 C 1288.63 551.77 1289.72 550.66 1289.72 549.45 Z M 1286.29 549.45 C 1286.29 548.88 1286.86 548.3 1287.43 548.3 C 1288 548.3 1288.57 548.88 1288.57 549.45 C 1288.57 550.03 1288 550.61 1287.43 550.61 C 1286.86 550.61 1286.29 550.03 1286.29 549.45 Z M 1291.62 553.83 L 1291.24 554.92 C 1292.2 555.26 1293.62 556 1294.36 557.55 L 1285.14 557.55 C 1284.83 557.55 1284.57 557.8 1284.57 558.12 L 1284.57 565.06 L 1282.86 565.05 L 1282.86 562.75 C 1282.86 562.43 1282.6 562.17 1282.29 562.17 L 1278.29 562.17 C 1277.97 562.17 1277.72 562.43 1277.72 562.75 L 1277.72 565.05 L 1276 565.05 L 1276 558.7 C 1276 558.47 1275.87 558.26 1275.66 558.17 C 1275.46 558.08 1275.21 558.12 1275.05 558.28 L 1268.4 564.44 C 1268.02 564.25 1267.66 564.02 1267.33 563.76 L 1275.81 556.25 C 1275.93 556.14 1276 555.98 1276 555.81 L 1276 554.08 C 1276 553.76 1275.75 553.5 1275.43 553.5 L 1269.14 553.5 C 1269.14 553.45 1269.14 553.41 1269.14 553.37 C 1269.14 553.24 1269.15 553.12 1269.15 553 C 1269.15 552.41 1269.25 551.79 1269.43 551.19 L 1275.43 551.19 C 1275.75 551.19 1276 550.93 1276 550.61 L 1276 545.68 C 1276.58 545.65 1277.15 545.69 1277.72 545.8 L 1277.72 555.23 C 1277.72 555.55 1277.97 555.81 1278.29 555.81 L 1282.29 555.81 C 1282.6 555.81 1282.86 555.55 1282.86 555.23 L 1282.86 551.77 L 1281.72 551.77 L 1281.72 554.66 L 1278.86 554.66 L 1278.86 546.12 C 1279.94 546.53 1280.97 547.2 1281.88 548.13 L 1282.69 547.31 C 1280 544.58 1276.44 543.78 1273.17 545.18 C 1270.27 546.43 1268 549.86 1268 553 C 1268 553.1 1268 553.21 1268 553.33 C 1267.99 553.43 1267.99 553.54 1267.99 553.65 C 1266.33 554.2 1264.01 555.65 1264.01 559.51 L 1264 559.63 C 1264 559.71 1264 559.78 1264.01 559.89 C 1264.2 563.02 1266.86 565.73 1270.2 566.19 C 1270.22 566.19 1270.25 566.2 1270.28 566.2 L 1278.23 566.2 C 1278.25 566.21 1278.27 566.22 1278.29 566.22 L 1282.29 566.22 C 1282.3 566.22 1282.32 566.21 1282.33 566.21 L 1290.28 566.22 L 1290.28 566.22 C 1290.3 566.22 1290.32 566.22 1290.34 566.21 C 1290.39 566.21 1296 565.56 1296 559.86 C 1296 555.81 1292.94 554.29 1291.62 553.83 Z M 1278.86 563.33 L 1281.72 563.33 L 1281.72 565.05 L 1278.86 565.05 Z M 1273.62 546.25 C 1274.03 546.07 1274.44 545.94 1274.86 545.85 L 1274.86 550.03 L 1269.88 550.03 C 1270.68 548.37 1272.06 546.92 1273.62 546.25 Z M 1265.15 559.67 L 1265.15 559.51 C 1265.15 556.92 1266.33 555.28 1268.65 554.66 L 1274.86 554.66 L 1274.86 555.55 L 1266.49 562.96 C 1265.71 562.08 1265.22 560.98 1265.15 559.82 C 1265.14 559.75 1265.14 559.71 1265.15 559.67 Z M 1269.61 564.89 L 1274.86 560.02 L 1274.86 565.05 L 1270.32 565.04 C 1270.07 565 1269.84 564.95 1269.61 564.89 Z M 1290.25 565.06 L 1285.72 565.06 L 1285.72 558.7 L 1294.75 558.7 C 1294.82 559.06 1294.86 559.44 1294.86 559.86 C 1294.86 564.38 1290.66 565.01 1290.25 565.06 Z M 1282.29 556.39 L 1278.25 556.39 C 1277.93 556.39 1277.67 556.65 1277.67 556.97 L 1277.67 561.01 C 1277.67 561.33 1277.93 561.59 1278.25 561.59 L 1282.29 561.59 C 1282.6 561.59 1282.86 561.33 1282.86 561.01 L 1282.86 556.97 C 1282.86 556.65 1282.6 556.39 1282.29 556.39 Z M 1278.82 560.44 L 1278.82 557.55 L 1281.72 557.55 L 1281.72 560.44 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 582px; margin-left: 1280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Cloud Map
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1280" y="594" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cloud...
                </text>
            </switch>
        </g>
        <path d="M 330 290 L 350 290 L 350 310 L 330 310 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 332.68 300.29 L 332 300.29 L 332 299.71 L 332.69 299.71 C 332.74 298.27 333.21 296.89 334.06 295.72 L 334.52 296.06 C 333.75 297.13 333.31 298.39 333.26 299.71 L 334 299.71 L 334 300.29 L 333.26 300.29 C 333.31 301.62 333.74 302.88 334.52 303.96 L 334.06 304.3 C 333.21 303.12 332.74 301.74 332.68 300.29 Z M 344.29 305.95 C 343.11 306.8 341.73 307.27 340.29 307.33 L 340.29 308 L 339.71 308 L 339.71 307.33 C 338.27 307.27 336.89 306.8 335.71 305.95 L 336.05 305.49 C 337.12 306.27 338.39 306.7 339.71 306.75 L 339.71 306 L 340.29 306 L 340.29 306.75 C 341.61 306.7 342.88 306.27 343.95 305.49 Z M 335.71 294.07 C 336.89 293.22 338.27 292.75 339.71 292.7 L 339.71 292 L 340.29 292 L 340.29 292.7 C 341.73 292.75 343.11 293.22 344.29 294.07 L 343.95 294.53 C 342.88 293.75 341.61 293.32 340.29 293.27 L 340.29 294 L 339.71 294 L 339.71 293.27 C 338.39 293.32 337.12 293.75 336.05 294.53 Z M 348 299.71 L 348 300.29 L 347.31 300.29 C 347.26 301.74 346.79 303.12 345.94 304.3 L 345.48 303.96 C 346.26 302.88 346.69 301.62 346.74 300.29 L 346 300.29 L 346 299.71 L 346.74 299.71 C 346.69 298.39 346.25 297.13 345.48 296.06 L 345.94 295.72 C 346.79 296.89 347.26 298.27 347.31 299.71 Z M 344.28 295.33 L 346.52 293.09 L 346.92 293.49 L 344.68 295.73 Z M 335.72 304.69 L 333.48 306.93 L 333.08 306.53 L 335.32 304.29 Z M 336.36 296.76 L 332.12 292.52 L 332.52 292.12 L 336.76 296.36 Z M 343.63 303.23 L 347.88 307.48 L 347.48 307.88 L 343.23 303.63 Z M 337.62 300.21 C 337.64 300.17 337.66 300.13 337.69 300.1 C 338.17 299.34 338.03 298.31 337.88 297.69 C 338.29 297.96 338.66 298.52 338.79 298.75 C 338.84 298.85 338.95 298.91 339.06 298.9 C 339.17 298.89 339.27 298.82 339.31 298.71 C 339.73 297.5 339.52 296.6 339.22 296.01 C 339.58 296.22 339.86 296.52 340.05 296.91 C 340.45 297.75 340.36 298.9 339.81 299.91 C 339.06 301.3 339.21 302.74 339.38 303.52 C 338.96 303.34 338.57 303.13 338.24 302.9 C 337.38 302.3 337.1 301.12 337.62 300.21 Z M 341.46 300.11 C 341.44 300.23 341.49 300.35 341.59 300.41 C 341.7 300.47 341.82 300.46 341.92 300.38 C 341.94 300.36 342.41 299.98 342.64 299.04 C 342.91 299.44 343.19 300.24 342.81 301.72 C 342.44 303.17 340.65 303.58 340.01 303.68 C 339.86 303.13 339.55 301.6 340.32 300.19 C 340.86 299.18 341.01 298.05 340.73 297.11 C 341.25 297.72 341.69 298.68 341.46 300.11 Z M 337.12 299.93 C 336.46 301.09 336.81 302.6 337.92 303.37 C 338.42 303.72 339.02 304.02 339.72 304.27 C 339.75 304.28 339.78 304.29 339.81 304.29 C 339.82 304.29 339.82 304.28 339.83 304.28 L 339.83 304.28 C 339.95 304.28 342.81 304.03 343.37 301.86 C 344.09 299.07 342.64 298.24 342.58 298.21 C 342.5 298.16 342.4 298.16 342.31 298.2 C 342.23 298.25 342.17 298.33 342.16 298.43 C 342.14 298.62 342.11 298.79 342.07 298.94 C 341.87 296.75 340.14 295.86 339.79 295.7 C 339.43 295.43 338.99 295.24 338.48 295.15 C 338.35 295.12 338.23 295.19 338.17 295.31 C 338.12 295.42 338.15 295.56 338.25 295.65 C 338.29 295.68 339.18 296.46 338.93 297.93 C 338.6 297.49 338.07 296.97 337.45 296.97 C 337.35 296.97 337.26 297.01 337.21 297.09 C 337.16 297.17 337.15 297.27 337.18 297.35 C 337.19 297.37 337.77 298.9 337.2 299.79 C 337.18 299.84 337.15 299.88 337.12 299.93 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 300px; margin-left: 352px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Web ACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="352" y="304" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Web...
                </text>
            </switch>
        </g>
        <path d="M 670 290 L 690 290 L 690 310 L 670 310 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 672.68 300.29 L 672 300.29 L 672 299.71 L 672.69 299.71 C 672.74 298.27 673.21 296.89 674.06 295.72 L 674.52 296.06 C 673.75 297.13 673.31 298.39 673.26 299.71 L 674 299.71 L 674 300.29 L 673.26 300.29 C 673.31 301.62 673.74 302.88 674.52 303.96 L 674.06 304.3 C 673.21 303.12 672.74 301.74 672.68 300.29 Z M 684.29 305.95 C 683.11 306.8 681.73 307.27 680.29 307.33 L 680.29 308 L 679.71 308 L 679.71 307.33 C 678.27 307.27 676.89 306.8 675.71 305.95 L 676.05 305.49 C 677.12 306.27 678.39 306.7 679.71 306.75 L 679.71 306 L 680.29 306 L 680.29 306.75 C 681.61 306.7 682.88 306.27 683.95 305.49 Z M 675.71 294.07 C 676.89 293.22 678.27 292.75 679.71 292.7 L 679.71 292 L 680.29 292 L 680.29 292.7 C 681.73 292.75 683.11 293.22 684.29 294.07 L 683.95 294.53 C 682.88 293.75 681.61 293.32 680.29 293.27 L 680.29 294 L 679.71 294 L 679.71 293.27 C 678.39 293.32 677.12 293.75 676.05 294.53 Z M 688 299.71 L 688 300.29 L 687.31 300.29 C 687.26 301.74 686.79 303.12 685.94 304.3 L 685.48 303.96 C 686.26 302.88 686.69 301.62 686.74 300.29 L 686 300.29 L 686 299.71 L 686.74 299.71 C 686.69 298.39 686.25 297.13 685.48 296.06 L 685.94 295.72 C 686.79 296.89 687.26 298.27 687.31 299.71 Z M 684.28 295.33 L 686.52 293.09 L 686.92 293.49 L 684.68 295.73 Z M 675.72 304.69 L 673.48 306.93 L 673.08 306.53 L 675.32 304.29 Z M 676.36 296.76 L 672.12 292.52 L 672.52 292.12 L 676.76 296.36 Z M 683.63 303.23 L 687.88 307.48 L 687.48 307.88 L 683.23 303.63 Z M 677.62 300.21 C 677.64 300.17 677.66 300.13 677.69 300.1 C 678.17 299.34 678.03 298.31 677.88 297.69 C 678.29 297.96 678.66 298.52 678.79 298.75 C 678.84 298.85 678.95 298.91 679.06 298.9 C 679.17 298.89 679.27 298.82 679.31 298.71 C 679.73 297.5 679.52 296.6 679.22 296.01 C 679.58 296.22 679.86 296.52 680.05 296.91 C 680.45 297.75 680.36 298.9 679.81 299.91 C 679.06 301.3 679.21 302.74 679.38 303.52 C 678.96 303.34 678.57 303.13 678.24 302.9 C 677.38 302.3 677.1 301.12 677.62 300.21 Z M 681.46 300.11 C 681.44 300.23 681.49 300.35 681.59 300.41 C 681.7 300.47 681.82 300.46 681.92 300.38 C 681.94 300.36 682.41 299.98 682.64 299.04 C 682.91 299.44 683.19 300.24 682.81 301.72 C 682.44 303.17 680.65 303.58 680.01 303.68 C 679.86 303.13 679.55 301.6 680.32 300.19 C 680.86 299.18 681.01 298.05 680.73 297.11 C 681.25 297.72 681.69 298.68 681.46 300.11 Z M 677.12 299.93 C 676.46 301.09 676.81 302.6 677.92 303.37 C 678.42 303.72 679.02 304.02 679.72 304.27 C 679.75 304.28 679.78 304.29 679.81 304.29 C 679.82 304.29 679.82 304.28 679.83 304.28 L 679.83 304.28 C 679.95 304.28 682.81 304.03 683.37 301.86 C 684.09 299.07 682.64 298.24 682.58 298.21 C 682.5 298.16 682.4 298.16 682.31 298.2 C 682.23 298.25 682.17 298.33 682.16 298.43 C 682.14 298.62 682.11 298.79 682.07 298.94 C 681.87 296.75 680.14 295.86 679.79 295.7 C 679.43 295.43 678.99 295.24 678.48 295.15 C 678.35 295.12 678.23 295.19 678.17 295.31 C 678.12 295.42 678.15 295.56 678.25 295.65 C 678.29 295.68 679.18 296.46 678.93 297.93 C 678.6 297.49 678.07 296.97 677.45 296.97 C 677.35 296.97 677.26 297.01 677.21 297.09 C 677.16 297.17 677.15 297.27 677.18 297.35 C 677.19 297.37 677.77 298.9 677.2 299.79 C 677.18 299.84 677.15 299.88 677.12 299.93 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 300px; margin-left: 692px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Web ACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="692" y="304" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Web...
                </text>
            </switch>
        </g>
        <path d="M 970 290 L 990 290 L 990 310 L 970 310 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 972.68 300.29 L 972 300.29 L 972 299.71 L 972.69 299.71 C 972.74 298.27 973.21 296.89 974.06 295.72 L 974.52 296.06 C 973.75 297.13 973.31 298.39 973.26 299.71 L 974 299.71 L 974 300.29 L 973.26 300.29 C 973.31 301.62 973.74 302.88 974.52 303.96 L 974.06 304.3 C 973.21 303.12 972.74 301.74 972.68 300.29 Z M 984.29 305.95 C 983.11 306.8 981.73 307.27 980.29 307.33 L 980.29 308 L 979.71 308 L 979.71 307.33 C 978.27 307.27 976.89 306.8 975.71 305.95 L 976.05 305.49 C 977.12 306.27 978.39 306.7 979.71 306.75 L 979.71 306 L 980.29 306 L 980.29 306.75 C 981.61 306.7 982.88 306.27 983.95 305.49 Z M 975.71 294.07 C 976.89 293.22 978.27 292.75 979.71 292.7 L 979.71 292 L 980.29 292 L 980.29 292.7 C 981.73 292.75 983.11 293.22 984.29 294.07 L 983.95 294.53 C 982.88 293.75 981.61 293.32 980.29 293.27 L 980.29 294 L 979.71 294 L 979.71 293.27 C 978.39 293.32 977.12 293.75 976.05 294.53 Z M 988 299.71 L 988 300.29 L 987.31 300.29 C 987.26 301.74 986.79 303.12 985.94 304.3 L 985.48 303.96 C 986.26 302.88 986.69 301.62 986.74 300.29 L 986 300.29 L 986 299.71 L 986.74 299.71 C 986.69 298.39 986.25 297.13 985.48 296.06 L 985.94 295.72 C 986.79 296.89 987.26 298.27 987.31 299.71 Z M 984.28 295.33 L 986.52 293.09 L 986.92 293.49 L 984.68 295.73 Z M 975.72 304.69 L 973.48 306.93 L 973.08 306.53 L 975.32 304.29 Z M 976.36 296.76 L 972.12 292.52 L 972.52 292.12 L 976.76 296.36 Z M 983.63 303.23 L 987.88 307.48 L 987.48 307.88 L 983.23 303.63 Z M 977.62 300.21 C 977.64 300.17 977.66 300.13 977.69 300.1 C 978.17 299.34 978.03 298.31 977.88 297.69 C 978.29 297.96 978.66 298.52 978.79 298.75 C 978.84 298.85 978.95 298.91 979.06 298.9 C 979.17 298.89 979.27 298.82 979.31 298.71 C 979.73 297.5 979.52 296.6 979.22 296.01 C 979.58 296.22 979.86 296.52 980.05 296.91 C 980.45 297.75 980.36 298.9 979.81 299.91 C 979.06 301.3 979.21 302.74 979.38 303.52 C 978.96 303.34 978.57 303.13 978.24 302.9 C 977.38 302.3 977.1 301.12 977.62 300.21 Z M 981.46 300.11 C 981.44 300.23 981.49 300.35 981.59 300.41 C 981.7 300.47 981.82 300.46 981.92 300.38 C 981.94 300.36 982.41 299.98 982.64 299.04 C 982.91 299.44 983.19 300.24 982.81 301.72 C 982.44 303.17 980.65 303.58 980.01 303.68 C 979.86 303.13 979.55 301.6 980.32 300.19 C 980.86 299.18 981.01 298.05 980.73 297.11 C 981.25 297.72 981.69 298.68 981.46 300.11 Z M 977.12 299.93 C 976.46 301.09 976.81 302.6 977.92 303.37 C 978.42 303.72 979.02 304.02 979.72 304.27 C 979.75 304.28 979.78 304.29 979.81 304.29 C 979.82 304.29 979.82 304.28 979.83 304.28 L 979.83 304.28 C 979.95 304.28 982.81 304.03 983.37 301.86 C 984.09 299.07 982.64 298.24 982.58 298.21 C 982.5 298.16 982.4 298.16 982.31 298.2 C 982.23 298.25 982.17 298.33 982.16 298.43 C 982.14 298.62 982.11 298.79 982.07 298.94 C 981.87 296.75 980.14 295.86 979.79 295.7 C 979.43 295.43 978.99 295.24 978.48 295.15 C 978.35 295.12 978.23 295.19 978.17 295.31 C 978.12 295.42 978.15 295.56 978.25 295.65 C 978.29 295.68 979.18 296.46 978.93 297.93 C 978.6 297.49 978.07 296.97 977.45 296.97 C 977.35 296.97 977.26 297.01 977.21 297.09 C 977.16 297.17 977.15 297.27 977.18 297.35 C 977.19 297.37 977.77 298.9 977.2 299.79 C 977.18 299.84 977.15 299.88 977.12 299.93 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 300px; margin-left: 992px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Web ACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="992" y="304" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Web...
                </text>
            </switch>
        </g>
        <path d="M 660 370 L 660 501.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 660 507.76 L 656 499.76 L 660 501.76 L 664 499.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 660 170 L 660 281.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 660 287.76 L 656 279.76 L 660 281.76 L 664 279.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 860 575 L 758.24 575" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 752.24 575 L 760.24 571 L 758.24 575 L 760.24 579 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 600px; margin-left: 810px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                サービス間通信は
                                <br/>
                                SG によって制御
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="810" y="603" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    サービス間通信は...
                </text>
            </switch>
        </g>
        <path d="M 960 370 L 960 501.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 960 507.76 L 956 499.76 L 960 501.76 L 964 499.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 960 170 L 960 281.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 960 287.76 L 956 279.76 L 960 281.76 L 964 279.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 470 575 L 428.24 575" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 422.24 575 L 430.24 571 L 428.24 575 L 430.24 579 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 320 370 L 320 501.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 320 507.76 L 316 499.76 L 320 501.76 L 324 499.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 320 170 L 320 281.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 320 287.76 L 316 279.76 L 320 281.76 L 324 279.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 1250 680 L 830 680 Q 820 680 820 670 L 820 628.24" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 820 622.24 L 824 630.24 L 820 628.24 L 816 630.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 1195px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                SG ID の連携
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1195" y="664" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    SG ID の連携
                </text>
            </switch>
        </g>
        <path d="M 320 610 L 320 643.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 320 648.88 L 316.5 641.88 L 320 643.63 L 323.5 641.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 1250 555 L 1048.24 555" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 1042.24 555 L 1050.24 551 L 1048.24 555 L 1050.24 559 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 530px; margin-left: 1193px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                名前解決
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1193" y="534" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">
                    名前解決
                </text>
            </switch>
        </g>
        <path d="M 160 320 L 281.76 320" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 287.76 320 L 279.76 324 L 281.76 320 L 279.76 316 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 3.66 338.18 C 4.11 329.17 11.27 321.98 20 321.98 C 22.95 321.98 25.85 322.81 28.37 324.38 C 33.05 327.3 36.05 332.53 36.34 338.18 Z M 11.08 310.8 C 11.08 305.85 15.08 301.82 20 301.82 C 24.92 301.82 28.92 305.85 28.92 310.8 C 28.92 315.75 24.92 319.78 20 319.78 C 15.08 319.78 11.08 315.75 11.08 310.8 Z M 29.33 322.84 C 27.75 321.86 26.05 321.15 24.27 320.7 C 28.07 319.04 30.73 315.23 30.73 310.8 C 30.73 304.84 25.92 300 20 300 C 14.08 300 9.27 304.84 9.27 310.8 C 9.27 315.23 11.94 319.05 15.74 320.71 C 7.77 322.71 1.82 330.18 1.82 339.09 C 1.82 339.59 2.22 340 2.73 340 L 37.27 340 C 37.77 340 38.18 339.59 38.18 339.09 C 38.18 332.47 34.79 326.25 29.33 322.84 Z" fill="#232f3d" stroke="none" pointer-events="none"/>
        <path d="M 50 320 L 140 320" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 150px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                CloudFront からの通信のみ許可
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="150" y="353" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    CloudFront からの通信のみ許可
                </text>
            </switch>
        </g>
        <path d="M 140 310 L 145.83 310 L 150 315.92 L 154.17 310 L 160 310 L 152.92 320 L 160 330 L 154.17 330 L 150 324.08 L 145.83 330 L 140 330 L 146.67 320 Z" fill="#ff0000" stroke="#b85450" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 570 575 L 490 575" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 470 565 L 475.83 565 L 480 570.92 L 484.17 565 L 490 565 L 482.92 575 L 490 585 L 484.17 585 L 480 579.08 L 475.83 585 L 470 585 L 476.67 575 Z" fill="#ff0000" stroke="#b85450" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>