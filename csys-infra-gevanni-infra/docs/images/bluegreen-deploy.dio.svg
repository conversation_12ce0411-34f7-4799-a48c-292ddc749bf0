<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1458px" height="736px" viewBox="-0.5 -0.5 1458 736" content="&lt;mxfile&gt;&lt;diagram id=&quot;t1SR_PXq1gYllvhI1J37&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="0" y="0" width="1457" height="735" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="14" y="12" width="1426" height="311" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1424px; height: 1px; padding-top: 19px; margin-left: 16px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gevanni
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="16" y="31" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gevanni
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="15" y="355" width="1426" height="369" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1424px; height: 1px; padding-top: 362px; margin-left: 17px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    アプリ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="17" y="374" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        アプリ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="51" y="552" width="1374" height="152" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 191 L 76 236.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 241.88 L 72.5 234.88 L 76 236.63 L 79.5 234.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 293 L 76 395.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 400.88 L 72.5 393.88 L 76 395.63 L 79.5 393.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 452 L 76 545.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 550.88 L 72.5 543.88 L 76 545.63 L 79.5 543.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 51 141 L 101 141 L 101 191 L 51 191 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 90.92 168.07 L 91.2 166.13 C 93.73 167.65 93.76 168.28 93.76 168.29 C 93.76 168.3 93.32 168.66 90.92 168.07 Z M 89.54 167.68 C 85.16 166.36 79.08 163.56 76.61 162.4 C 76.61 162.39 76.62 162.38 76.62 162.37 C 76.62 161.43 75.85 160.66 74.9 160.66 C 73.95 160.66 73.18 161.43 73.18 162.37 C 73.18 163.32 73.95 164.09 74.9 164.09 C 75.31 164.09 75.69 163.93 75.99 163.69 C 78.89 165.06 84.93 167.81 89.33 169.11 L 87.59 181.4 C 87.59 181.43 87.58 181.47 87.58 181.5 C 87.58 182.58 82.79 184.57 74.97 184.57 C 67.06 184.57 62.21 182.58 62.21 181.5 C 62.21 181.47 62.21 181.44 62.21 181.4 L 58.57 154.83 C 61.72 157 68.49 158.14 74.97 158.14 C 81.44 158.14 88.2 157 91.36 154.84 Z M 58.18 152.06 C 58.24 151.12 63.64 147.43 74.97 147.43 C 86.3 147.43 91.7 151.12 91.76 152.06 L 91.76 152.38 C 91.13 154.48 84.13 156.71 74.97 156.71 C 65.79 156.71 58.79 154.48 58.18 152.37 Z M 93.18 152.07 C 93.18 149.6 86.09 146 74.97 146 C 63.85 146 56.76 149.6 56.76 152.07 L 56.82 152.61 L 60.79 181.56 C 60.88 184.79 69.51 186 74.97 186 C 81.73 186 88.92 184.44 89.01 181.56 L 90.72 169.49 C 91.68 169.72 92.46 169.83 93.09 169.83 C 93.93 169.83 94.5 169.63 94.85 169.21 C 95.14 168.88 95.24 168.47 95.16 168.03 C 94.98 167.04 93.8 165.97 91.41 164.61 L 93.11 152.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 243 L 101 243 L 101 293 L 51 293 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 85.75 286.57 C 84.22 286.57 82.98 285.32 82.98 283.78 C 82.98 282.24 84.22 280.99 85.75 280.99 C 87.29 280.99 88.53 282.24 88.53 283.78 C 88.53 285.32 87.29 286.57 85.75 286.57 Z M 79.67 274.43 L 72.28 274.43 L 68.59 268 L 72.28 261.57 L 79.67 261.57 L 83.36 268 Z M 67.16 255.01 C 65.63 255.01 64.38 253.76 64.38 252.22 C 64.38 250.68 65.63 249.43 67.16 249.43 C 68.69 249.43 69.94 250.68 69.94 252.22 C 69.94 253.76 68.69 255.01 67.16 255.01 Z M 85.75 279.56 C 85.23 279.56 84.72 279.66 84.26 279.84 L 81.24 274.72 L 81.08 274.82 L 84.79 268.36 C 84.92 268.14 84.92 267.86 84.79 267.64 L 80.69 260.5 C 80.56 260.28 80.33 260.14 80.08 260.14 L 72.61 260.14 L 72.64 260.13 L 69.88 255.43 C 70.78 254.66 71.36 253.51 71.36 252.22 C 71.36 249.89 69.47 248 67.16 248 C 64.84 248 62.96 249.89 62.96 252.22 C 62.96 254.55 64.84 256.44 67.16 256.44 C 67.69 256.44 68.19 256.34 68.65 256.16 L 71.24 260.54 L 67.16 267.64 C 67.03 267.86 67.03 268.14 67.16 268.36 L 71.26 275.5 C 71.39 275.72 71.62 275.86 71.87 275.86 L 80.08 275.86 C 80.13 275.86 80.19 275.85 80.25 275.83 L 83.04 280.57 C 82.13 281.34 81.56 282.49 81.56 283.78 C 81.56 286.11 83.44 288 85.75 288 C 88.07 288 89.95 286.11 89.95 283.78 C 89.95 281.45 88.07 279.56 85.75 279.56 Z M 89.99 262.36 C 88.46 262.36 87.21 261.11 87.21 259.57 C 87.21 258.03 88.46 256.78 89.99 256.78 C 91.52 256.78 92.77 258.03 92.77 259.57 C 92.77 261.11 91.52 262.36 89.99 262.36 Z M 95.19 267.64 L 92.49 262.95 C 93.52 262.18 94.19 260.95 94.19 259.57 C 94.19 257.24 92.3 255.35 89.99 255.35 C 89.4 255.35 88.84 255.47 88.33 255.69 L 86.13 251.86 C 86 251.64 85.77 251.51 85.52 251.51 L 76.71 251.51 L 76.71 252.94 L 85.11 252.94 L 87.14 256.48 C 86.31 257.25 85.79 258.35 85.79 259.57 C 85.79 261.89 87.67 263.79 89.99 263.79 C 90.42 263.79 90.84 263.72 91.23 263.6 L 93.76 268 L 90.21 274.18 L 91.44 274.89 L 95.19 268.36 C 95.32 268.14 95.32 267.86 95.19 267.64 Z M 62.59 278.89 C 61.06 278.89 59.81 277.64 59.81 276.1 C 59.81 274.56 61.06 273.31 62.59 273.31 C 64.12 273.31 65.37 274.56 65.37 276.1 C 65.37 277.64 64.12 278.89 62.59 278.89 Z M 64.91 279.61 C 66.04 278.86 66.79 277.56 66.79 276.1 C 66.79 273.77 64.91 271.88 62.59 271.88 C 61.92 271.88 61.29 272.04 60.73 272.32 L 58.25 268 L 62.36 260.84 L 61.13 260.12 L 56.81 267.64 C 56.68 267.86 56.68 268.14 56.81 268.36 L 59.57 273.17 C 58.85 273.93 58.39 274.96 58.39 276.1 C 58.39 278.43 60.28 280.32 62.59 280.32 C 62.94 280.32 63.28 280.27 63.6 280.19 L 65.87 284.14 C 66 284.36 66.23 284.49 66.48 284.49 L 75.29 284.49 L 75.29 283.06 L 66.89 283.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 402 L 101 402 L 101 452 L 51 452 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 85.75 445.57 C 84.22 445.57 82.98 444.32 82.98 442.78 C 82.98 441.24 84.22 439.99 85.75 439.99 C 87.29 439.99 88.53 441.24 88.53 442.78 C 88.53 444.32 87.29 445.57 85.75 445.57 Z M 79.67 433.43 L 72.28 433.43 L 68.59 427 L 72.28 420.57 L 79.67 420.57 L 83.36 427 Z M 67.16 414.01 C 65.63 414.01 64.38 412.76 64.38 411.22 C 64.38 409.68 65.63 408.43 67.16 408.43 C 68.69 408.43 69.94 409.68 69.94 411.22 C 69.94 412.76 68.69 414.01 67.16 414.01 Z M 85.75 438.56 C 85.23 438.56 84.72 438.66 84.26 438.84 L 81.24 433.72 L 81.08 433.82 L 84.79 427.36 C 84.92 427.14 84.92 426.86 84.79 426.64 L 80.69 419.5 C 80.56 419.28 80.33 419.14 80.08 419.14 L 72.61 419.14 L 72.64 419.13 L 69.88 414.43 C 70.78 413.66 71.36 412.51 71.36 411.22 C 71.36 408.89 69.47 407 67.16 407 C 64.84 407 62.96 408.89 62.96 411.22 C 62.96 413.55 64.84 415.44 67.16 415.44 C 67.69 415.44 68.19 415.34 68.65 415.16 L 71.24 419.54 L 67.16 426.64 C 67.03 426.86 67.03 427.14 67.16 427.36 L 71.26 434.5 C 71.39 434.72 71.62 434.86 71.87 434.86 L 80.08 434.86 C 80.13 434.86 80.19 434.85 80.25 434.83 L 83.04 439.57 C 82.13 440.34 81.56 441.49 81.56 442.78 C 81.56 445.11 83.44 447 85.75 447 C 88.07 447 89.95 445.11 89.95 442.78 C 89.95 440.45 88.07 438.56 85.75 438.56 Z M 89.99 421.36 C 88.46 421.36 87.21 420.11 87.21 418.57 C 87.21 417.03 88.46 415.78 89.99 415.78 C 91.52 415.78 92.77 417.03 92.77 418.57 C 92.77 420.11 91.52 421.36 89.99 421.36 Z M 95.19 426.64 L 92.49 421.95 C 93.52 421.18 94.19 419.95 94.19 418.57 C 94.19 416.24 92.3 414.35 89.99 414.35 C 89.4 414.35 88.84 414.47 88.33 414.69 L 86.13 410.86 C 86 410.64 85.77 410.51 85.52 410.51 L 76.71 410.51 L 76.71 411.94 L 85.11 411.94 L 87.14 415.48 C 86.31 416.25 85.79 417.35 85.79 418.57 C 85.79 420.89 87.67 422.79 89.99 422.79 C 90.42 422.79 90.84 422.72 91.23 422.6 L 93.76 427 L 90.21 433.18 L 91.44 433.89 L 95.19 427.36 C 95.32 427.14 95.32 426.86 95.19 426.64 Z M 62.59 437.89 C 61.06 437.89 59.81 436.64 59.81 435.1 C 59.81 433.56 61.06 432.31 62.59 432.31 C 64.12 432.31 65.37 433.56 65.37 435.1 C 65.37 436.64 64.12 437.89 62.59 437.89 Z M 64.91 438.61 C 66.04 437.86 66.79 436.56 66.79 435.1 C 66.79 432.77 64.91 430.88 62.59 430.88 C 61.92 430.88 61.29 431.04 60.73 431.32 L 58.25 427 L 62.36 419.84 L 61.13 419.12 L 56.81 426.64 C 56.68 426.86 56.68 427.14 56.81 427.36 L 59.57 432.17 C 58.85 432.93 58.39 433.96 58.39 435.1 C 58.39 437.43 60.28 439.32 62.59 439.32 C 62.94 439.32 63.28 439.27 63.6 439.19 L 65.87 443.14 C 66 443.36 66.23 443.49 66.48 443.49 L 75.29 443.49 L 75.29 442.06 L 66.89 442.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 459px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="471" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 552 L 101 552 L 101 602 L 51 602 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 92.03 557 L 59.97 557 C 57.78 557 56 558.78 56 560.97 L 56 570.48 C 56 572.67 57.78 574.45 59.97 574.45 L 61.09 574.45 L 61.09 596.27 C 61.09 596.67 61.42 597 61.82 597 L 89.45 597 C 89.86 597 90.18 596.67 90.18 596.27 L 90.18 574.45 L 92.03 574.45 C 94.22 574.45 96 572.67 96 570.48 L 96 560.97 C 96 558.78 94.22 557 92.03 557 Z M 62.55 595.55 L 62.55 574.45 L 88.73 574.45 L 88.73 595.55 Z M 92.03 573 L 59.97 573 C 58.59 573 57.45 571.87 57.45 570.48 L 57.45 570.09 L 66.18 570.09 L 66.18 568.64 L 57.45 568.64 L 57.45 560.97 C 57.45 559.58 58.59 558.45 59.97 558.45 L 92.03 558.45 C 93.41 558.45 94.55 559.58 94.55 560.97 L 94.55 568.64 L 74.18 568.64 L 74.18 570.09 L 94.55 570.09 L 94.55 570.48 C 94.55 571.87 93.41 573 92.03 573 Z M 65.1 584.67 C 65.1 584.46 65.19 584.26 65.34 584.12 L 69.95 580.05 L 70.91 581.14 L 66.93 584.66 L 70.89 588.08 L 69.94 589.18 L 65.35 585.22 C 65.19 585.08 65.1 584.88 65.1 584.67 Z M 79.69 588.11 L 83.69 584.61 L 79.69 581.14 L 80.64 580.04 L 85.27 584.05 C 85.43 584.19 85.52 584.39 85.52 584.6 C 85.52 584.81 85.43 585.01 85.27 585.15 L 80.65 589.21 Z M 73.21 592.03 L 71.87 591.48 L 77.39 578.01 L 78.73 578.56 Z M 68.36 570.09 L 72 570.09 L 72 568.64 L 68.36 568.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 609px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="621" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 41 L 101 41 L 101 91 L 51 91 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 74.8 63.98 C 74.45 64.18 74.24 64.55 74.24 64.96 L 74.24 84.4 L 59.53 76.18 L 59.53 57.06 L 76.04 47.49 L 90.71 54.76 Z M 92.41 54.74 C 92.41 54.34 92.2 53.96 91.81 53.74 L 76.59 46.2 C 76.25 46 75.81 46 75.47 46.2 L 58.69 55.92 C 58.34 56.12 58.13 56.5 58.13 56.9 L 58.13 76.34 C 58.13 76.74 58.34 77.12 58.7 77.32 L 73.95 85.85 C 74.12 85.95 74.32 86 74.51 86 C 74.71 86 74.9 85.95 75.08 85.85 C 75.42 85.65 75.64 85.27 75.64 84.87 L 75.64 65.12 L 91.85 55.72 C 92.2 55.52 92.41 55.15 92.41 54.74 Z M 92.45 76.22 L 79.14 84.36 L 79.14 77.07 L 86.08 72.59 L 86.13 72.28 C 86.14 72.2 86.15 72.2 86.15 71.82 L 86.17 63.12 L 92.47 59.47 Z M 93.31 58.02 C 92.96 57.81 92.53 57.81 92.18 58.02 L 85.47 61.9 C 85.26 62.02 84.77 62.29 84.77 62.84 L 84.74 71.78 L 78.35 75.91 C 77.97 76.14 77.74 76.5 77.74 76.88 L 77.74 84.81 C 77.74 85.21 77.95 85.57 78.31 85.78 C 78.5 85.89 78.71 85.94 78.91 85.94 C 79.12 85.94 79.32 85.89 79.51 85.78 L 93.29 77.35 C 93.64 77.15 93.85 76.78 93.85 76.38 L 93.87 58.99 C 93.87 58.59 93.66 58.22 93.31 58.02 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 98px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ECR
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="110" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ECR
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 191 561.63 L 191.03 188.49 Q 191.03 178.49 181.03 178.49 L 101 178.5" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 191 566.88 L 187.5 559.88 L 191 561.63 L 194.5 559.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 256 633 L 310.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 315.88 633 L 308.88 636.5 L 310.63 633 L 308.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 828 633 L 881.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 886.88 633 L 879.88 636.5 L 881.63 633 L 879.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1018 633 L 1072.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1077.88 633 L 1070.88 636.5 L 1072.63 633 L 1070.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 637 633 L 691.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 696.88 633 L 689.88 636.5 L 691.63 633 L 689.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 447 633 L 500.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 505.88 633 L 498.88 636.5 L 500.63 633 L 498.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1200.64 435.37 L 1201.15 552" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1200.62 430.12 L 1204.15 437.1 L 1200.64 435.37 L 1197.15 437.13 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1175.5 379 L 1225.5 379 L 1225.5 429 L 1175.5 429 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 1210.25 422.57 C 1208.72 422.57 1207.48 421.32 1207.48 419.78 C 1207.48 418.24 1208.72 416.99 1210.25 416.99 C 1211.79 416.99 1213.03 418.24 1213.03 419.78 C 1213.03 421.32 1211.79 422.57 1210.25 422.57 Z M 1204.17 410.43 L 1196.78 410.43 L 1193.09 404 L 1196.78 397.57 L 1204.17 397.57 L 1207.86 404 Z M 1191.66 391.01 C 1190.13 391.01 1188.88 389.76 1188.88 388.22 C 1188.88 386.68 1190.13 385.43 1191.66 385.43 C 1193.19 385.43 1194.44 386.68 1194.44 388.22 C 1194.44 389.76 1193.19 391.01 1191.66 391.01 Z M 1210.25 415.56 C 1209.73 415.56 1209.22 415.66 1208.76 415.84 L 1205.74 410.72 L 1205.58 410.82 L 1209.29 404.36 C 1209.42 404.14 1209.42 403.86 1209.29 403.64 L 1205.19 396.5 C 1205.06 396.28 1204.83 396.14 1204.58 396.14 L 1197.11 396.14 L 1197.14 396.13 L 1194.38 391.43 C 1195.28 390.66 1195.86 389.51 1195.86 388.22 C 1195.86 385.89 1193.97 384 1191.66 384 C 1189.34 384 1187.46 385.89 1187.46 388.22 C 1187.46 390.55 1189.34 392.44 1191.66 392.44 C 1192.19 392.44 1192.69 392.34 1193.15 392.16 L 1195.74 396.54 L 1191.66 403.64 C 1191.53 403.86 1191.53 404.14 1191.66 404.36 L 1195.76 411.5 C 1195.89 411.72 1196.12 411.86 1196.37 411.86 L 1204.58 411.86 C 1204.63 411.86 1204.69 411.85 1204.75 411.83 L 1207.54 416.57 C 1206.63 417.34 1206.06 418.49 1206.06 419.78 C 1206.06 422.11 1207.94 424 1210.25 424 C 1212.57 424 1214.45 422.11 1214.45 419.78 C 1214.45 417.45 1212.57 415.56 1210.25 415.56 Z M 1214.49 398.36 C 1212.96 398.36 1211.71 397.11 1211.71 395.57 C 1211.71 394.03 1212.96 392.78 1214.49 392.78 C 1216.02 392.78 1217.27 394.03 1217.27 395.57 C 1217.27 397.11 1216.02 398.36 1214.49 398.36 Z M 1219.69 403.64 L 1216.99 398.95 C 1218.02 398.18 1218.69 396.95 1218.69 395.57 C 1218.69 393.24 1216.8 391.35 1214.49 391.35 C 1213.9 391.35 1213.34 391.47 1212.83 391.69 L 1210.63 387.86 C 1210.5 387.64 1210.27 387.51 1210.02 387.51 L 1201.21 387.51 L 1201.21 388.94 L 1209.61 388.94 L 1211.64 392.48 C 1210.81 393.25 1210.29 394.35 1210.29 395.57 C 1210.29 397.89 1212.17 399.79 1214.49 399.79 C 1214.92 399.79 1215.34 399.72 1215.73 399.6 L 1218.26 404 L 1214.71 410.18 L 1215.94 410.89 L 1219.69 404.36 C 1219.82 404.14 1219.82 403.86 1219.69 403.64 Z M 1187.09 414.89 C 1185.56 414.89 1184.31 413.64 1184.31 412.1 C 1184.31 410.56 1185.56 409.31 1187.09 409.31 C 1188.62 409.31 1189.87 410.56 1189.87 412.1 C 1189.87 413.64 1188.62 414.89 1187.09 414.89 Z M 1189.41 415.61 C 1190.54 414.86 1191.29 413.56 1191.29 412.1 C 1191.29 409.77 1189.41 407.88 1187.09 407.88 C 1186.42 407.88 1185.79 408.04 1185.23 408.32 L 1182.75 404 L 1186.86 396.84 L 1185.63 396.12 L 1181.31 403.64 C 1181.18 403.86 1181.18 404.14 1181.31 404.36 L 1184.07 409.17 C 1183.35 409.93 1182.89 410.96 1182.89 412.1 C 1182.89 414.43 1184.78 416.32 1187.09 416.32 C 1187.44 416.32 1187.78 416.27 1188.1 416.19 L 1190.37 420.14 C 1190.5 420.36 1190.73 420.49 1190.98 420.49 L 1199.79 420.49 L 1199.79 419.06 L 1191.39 419.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 436px; margin-left: 1201px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1201" y="448" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1175.5 404 L 1117.87 404" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1112.62 404 L 1119.62 400.5 L 1117.87 404 L 1119.62 407.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 382 568 L 382 511 Q 382 501 392 501 L 675.03 501 Q 685.03 501 685.03 491 L 685 197.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 685 192.12 L 688.5 199.12 L 685 197.37 L 681.5 199.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 501px; margin-left: 479px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ecspresso deploy
                                    <div>
                                        （更新）
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="479" y="504" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ecspresso deploy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 763 568 L 763.02 513 Q 763.03 503 753.03 503 L 695.03 503 Q 685.03 503 685.03 493 L 685 197.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 685 192.12 L 688.5 199.12 L 685 197.37 L 681.5 199.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 528px; margin-left: 762px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CLIコマンド実行
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="762" y="531" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        CLIコマンド実行
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 660 141 L 710 141 L 710 191 L 660 191 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 685.02 146 C 674.03 146 665.43 154.38 665.01 165.5 C 665 165.76 665.15 166 665.37 166.13 L 672.55 170.86 L 673.32 169.67 L 666.52 165.21 C 666.88 163.69 667.98 162.55 669.57 161.98 C 670.33 161.7 671.19 161.55 672.14 161.55 C 673.68 161.55 675.13 162.14 676.21 163.19 L 676.34 163.32 C 677.31 164.26 677.88 165.98 677.88 165.99 C 677.89 166.03 678.84 169.61 678.84 169.61 L 680.2 169.24 L 679.29 165.84 L 679.29 165.76 C 679.63 162.66 682.47 161.55 685.01 161.55 C 686.55 161.55 688 162.14 689.09 163.19 C 689.09 163.19 690.61 164.37 690.73 165.75 L 690.73 165.85 L 689.81 169.23 L 691.17 169.61 L 692.13 166.08 C 692.13 166.05 692.13 166.02 692.14 165.99 C 692.33 163.29 694.58 161.55 697.88 161.55 C 699.42 161.55 700.87 162.14 701.95 163.19 C 702.79 164.02 703.38 164.64 703.55 165.25 L 697.49 169.51 L 698.29 170.67 L 704.7 166.16 C 704.87 166.03 705 165.53 705 165.5 C 704.8 154.39 696.23 146.01 685.02 146 Z M 677.91 162.89 C 677.72 162.7 677.53 162.5 677.32 162.3 L 677.19 162.18 C 675.84 160.86 674.05 160.14 672.14 160.14 C 669.97 160.14 668.14 160.8 666.87 161.94 C 668.54 154.43 674.43 148.84 682 147.66 C 679.74 150.31 678.18 155.93 677.91 162.89 Z M 690.31 162.42 L 690.06 162.18 C 688.71 160.86 686.92 160.14 685.01 160.14 C 682.58 160.14 680.6 160.96 679.34 162.35 C 679.8 153.43 682.54 147.43 685.02 147.42 C 685.02 147.42 685.02 147.42 685.03 147.42 C 687.55 147.44 690.33 153.65 690.72 162.82 C 690.58 162.69 690.45 162.56 690.31 162.42 Z M 702.93 162.18 C 701.58 160.86 699.79 160.14 697.88 160.14 C 695.4 160.14 693.37 161.02 692.11 162.5 C 691.8 155.71 690.25 150.26 688.03 147.66 C 695.97 148.86 701.99 154.72 703.32 162.57 C 703.19 162.44 703.06 162.31 702.93 162.18 Z M 678.61 178.39 C 678.47 178.24 678.39 178.04 678.4 177.84 C 678.42 177.63 678.52 177.44 678.68 177.32 L 682.01 174.8 L 682.85 175.94 L 680.18 177.95 L 682.57 180.3 L 681.59 181.31 Z M 690.64 178.64 L 688.25 176.29 L 689.23 175.27 L 692.21 178.2 C 692.36 178.34 692.43 178.55 692.42 178.75 C 692.41 178.96 692.31 179.15 692.14 179.27 L 688.81 181.79 L 687.97 180.65 Z M 682.62 183.33 L 686.86 173.6 L 688.15 174.17 L 683.9 183.9 Z M 695.18 171.13 L 675.53 171.13 C 675.15 171.13 674.83 171.45 674.83 171.84 L 674.83 185.29 C 674.83 185.68 675.15 186 675.53 186 L 695.18 186 C 695.57 186 695.88 185.68 695.88 185.29 L 695.88 171.84 C 695.88 171.45 695.57 171.13 695.18 171.13 Z M 676.24 184.58 L 676.24 172.55 L 694.48 172.55 L 694.48 184.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 685px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodeDeploy
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="685" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodeDepl...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 660 166.17 L 475.37 167.46" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 470.12 167.49 L 477.09 163.94 L 475.37 167.46 L 477.14 170.94 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 565px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    コンテナの更新
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="565" y="170" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        コンテナの更新
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 191 93 L 191.02 143.51 Q 191.03 153.51 181.03 153.51 L 107.37 153.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 102.12 153.5 L 109.12 150 L 107.37 153.5 L 109.12 157 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 136px; margin-left: 191px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    コピー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="139" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        コピー
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 166 43 L 216 43 L 216 93 L 166 93 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 205.92 70.07 L 206.2 68.14 C 208.73 69.65 208.76 70.28 208.76 70.29 C 208.76 70.3 208.32 70.66 205.92 70.07 Z M 204.54 69.68 C 200.16 68.36 194.08 65.56 191.61 64.4 C 191.61 64.39 191.62 64.38 191.62 64.37 C 191.62 63.42 190.85 62.66 189.9 62.66 C 188.95 62.66 188.18 63.42 188.18 64.37 C 188.18 65.32 188.95 66.09 189.9 66.09 C 190.31 66.09 190.69 65.93 190.99 65.69 C 193.89 67.06 199.93 69.81 204.33 71.11 L 202.59 83.4 C 202.59 83.43 202.58 83.47 202.58 83.5 C 202.58 84.58 197.79 86.57 189.97 86.57 C 182.06 86.57 177.21 84.58 177.21 83.5 C 177.21 83.47 177.21 83.44 177.21 83.4 L 173.57 56.83 C 176.72 59 183.49 60.14 189.97 60.14 C 196.44 60.14 203.2 59 206.36 56.84 Z M 173.18 54.06 C 173.24 53.12 178.64 49.43 189.97 49.43 C 201.3 49.43 206.7 53.12 206.76 54.06 L 206.76 54.38 C 206.13 56.48 199.13 58.71 189.97 58.71 C 180.79 58.71 173.79 56.48 173.18 54.37 Z M 208.18 54.07 C 208.18 51.6 201.09 48 189.97 48 C 178.85 48 171.76 51.6 171.76 54.07 L 171.82 54.61 L 175.79 83.56 C 175.88 86.79 184.51 88 189.97 88 C 196.73 88 203.92 86.44 204.01 83.56 L 205.72 71.49 C 206.68 71.72 207.46 71.83 208.09 71.83 C 208.93 71.83 209.5 71.63 209.85 71.21 C 210.14 70.88 210.24 70.47 210.16 70.03 C 209.98 69.04 208.8 67.97 206.41 66.61 L 208.11 54.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 191px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    マスターバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="112" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        マスターバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1144 568 L 1144 513 Q 1144 503 1134 503 L 694.4 503 Q 684.4 503 684.4 493 L 684.37 195.54" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 684.37 190.29 L 687.87 197.29 L 684.37 195.54 L 680.87 197.29 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 505px; margin-left: 970px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CLIコマンド実行
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="970" y="509" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        CLIコマンド実行
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1209 633 L 1269.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1274.88 633 L 1267.88 636.5 L 1269.63 633 L 1267.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1079" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 1080px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <span style="background-color: transparent;">
                                        元のタスク停止
                                    </span>
                                    <div>
                                        <span style="background-color: transparent;">
                                            ステージ
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1144" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        元のタスク停止
ステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1118 603 L 1168 603 L 1168 653 L 1118 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1160.49 615.6 L 1156.39 611.85 L 1152.29 615.6 Z M 1154.82 611.43 L 1146.5 611.44 L 1150.94 614.97 Z M 1151.07 623.52 L 1144.52 625.93 L 1157.11 625.93 Z M 1141.46 639.71 L 1159.91 639.71 L 1159.91 627.31 L 1141.46 627.31 Z M 1149.53 615.6 L 1144.99 612 L 1140.49 615.6 Z M 1139.05 614.99 L 1143.49 611.45 L 1134.65 611.46 Z M 1137.62 615.6 L 1133.09 611.97 L 1128.56 615.6 L 1135.3 615.6 Z M 1134.62 616.98 L 1129.09 616.98 L 1134.62 622.47 Z M 1134.62 624.61 L 1128.13 630.52 L 1134.62 636.88 Z M 1134.62 639.11 L 1127.79 645.8 L 1127.79 645.91 L 1134.62 645.91 Z M 1127.79 632.11 L 1127.79 643.88 L 1133.8 638 Z M 1127.79 628.98 L 1133.76 623.54 L 1127.79 617.62 Z M 1127.1 615.02 L 1131.53 611.46 L 1127.1 611.47 Z M 1125.73 610.09 L 1124.37 610.09 L 1124.37 616.98 L 1125.73 616.98 L 1125.73 616.29 L 1125.73 610.78 Z M 1162.9 616.54 C 1162.8 616.81 1162.54 616.98 1162.26 616.98 L 1151.71 616.98 L 1151.71 622.49 L 1160.85 625.98 L 1160.85 625.99 C 1161.1 626.09 1161.28 626.33 1161.28 626.62 L 1161.28 640.4 C 1161.28 640.78 1160.97 641.09 1160.6 641.09 L 1140.77 641.09 C 1140.4 641.09 1140.09 640.78 1140.09 640.4 L 1140.09 626.62 C 1140.09 626.33 1140.27 626.08 1140.53 625.98 L 1140.53 625.98 L 1150.34 622.49 L 1150.34 616.98 L 1135.99 616.98 L 1135.99 646.59 C 1135.99 646.97 1135.68 647.28 1135.3 647.28 L 1127.1 647.28 C 1126.72 647.28 1126.42 646.97 1126.42 646.59 L 1126.42 618.36 L 1123.68 618.36 C 1123.31 618.36 1123 618.05 1123 617.67 L 1123 609.41 C 1123 609.03 1123.31 608.72 1123.68 608.72 L 1126.42 608.72 C 1126.8 608.72 1127.1 609.03 1127.1 609.41 L 1127.1 610.09 L 1156.49 610.09 L 1162.72 615.78 C 1162.93 615.97 1163 616.27 1162.9 616.54 Z M 1148.72 638.54 L 1152.69 629.43 L 1151.44 628.88 L 1147.46 637.99 Z M 1152.45 635.5 L 1153.34 636.55 L 1156.29 634.03 C 1156.43 633.91 1156.52 633.73 1156.53 633.54 C 1156.54 633.34 1156.47 633.16 1156.33 633.02 L 1153.87 630.5 L 1152.9 631.47 L 1154.84 633.46 Z M 1143.57 633.5 C 1143.42 633.36 1143.34 633.16 1143.35 632.96 C 1143.36 632.75 1143.46 632.56 1143.63 632.44 L 1146.98 629.94 L 1147.8 631.04 L 1145.11 633.05 L 1147.36 635.13 L 1146.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 1143px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1143" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="888" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 889px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    承認ステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="953" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        承認ステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="908.5" y="608" width="91" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 628px; margin-left: 910px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    手動承認
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="954" y="632" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        手動承認
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="698" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    トラフィック切替
                                    <div>
                                        ステージ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="763" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        トラフィック切替
ステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 736 603 L 786 603 L 786 653 L 736 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 778.49 615.6 L 774.39 611.85 L 770.29 615.6 Z M 772.82 611.43 L 764.5 611.44 L 768.94 614.97 Z M 769.07 623.52 L 762.52 625.93 L 775.11 625.93 Z M 759.46 639.71 L 777.91 639.71 L 777.91 627.31 L 759.46 627.31 Z M 767.53 615.6 L 762.99 612 L 758.49 615.6 Z M 757.05 614.99 L 761.49 611.45 L 752.65 611.46 Z M 755.62 615.6 L 751.09 611.97 L 746.56 615.6 L 753.3 615.6 Z M 752.62 616.98 L 747.09 616.98 L 752.62 622.47 Z M 752.62 624.61 L 746.13 630.52 L 752.62 636.88 Z M 752.62 639.11 L 745.79 645.8 L 745.79 645.91 L 752.62 645.91 Z M 745.79 632.11 L 745.79 643.88 L 751.8 638 Z M 745.79 628.98 L 751.76 623.54 L 745.79 617.62 Z M 745.1 615.02 L 749.53 611.46 L 745.1 611.47 Z M 743.73 610.09 L 742.37 610.09 L 742.37 616.98 L 743.73 616.98 L 743.73 616.29 L 743.73 610.78 Z M 780.9 616.54 C 780.8 616.81 780.54 616.98 780.26 616.98 L 769.71 616.98 L 769.71 622.49 L 778.85 625.98 L 778.85 625.99 C 779.1 626.09 779.28 626.33 779.28 626.62 L 779.28 640.4 C 779.28 640.78 778.97 641.09 778.6 641.09 L 758.77 641.09 C 758.4 641.09 758.09 640.78 758.09 640.4 L 758.09 626.62 C 758.09 626.33 758.27 626.08 758.53 625.98 L 758.53 625.98 L 768.34 622.49 L 768.34 616.98 L 753.99 616.98 L 753.99 646.59 C 753.99 646.97 753.68 647.28 753.3 647.28 L 745.1 647.28 C 744.72 647.28 744.42 646.97 744.42 646.59 L 744.42 618.36 L 741.68 618.36 C 741.31 618.36 741 618.05 741 617.67 L 741 609.41 C 741 609.03 741.31 608.72 741.68 608.72 L 744.42 608.72 C 744.8 608.72 745.1 609.03 745.1 609.41 L 745.1 610.09 L 774.49 610.09 L 780.72 615.78 C 780.93 615.97 781 616.27 780.9 616.54 Z M 766.72 638.54 L 770.69 629.43 L 769.44 628.88 L 765.46 637.99 Z M 770.45 635.5 L 771.34 636.55 L 774.29 634.03 C 774.43 633.91 774.52 633.73 774.53 633.54 C 774.54 633.34 774.47 633.16 774.33 633.02 L 771.87 630.5 L 770.9 631.47 L 772.84 633.46 Z M 761.57 633.5 C 761.42 633.36 761.34 633.16 761.35 632.96 C 761.36 632.75 761.46 632.56 761.63 632.44 L 764.98 629.94 L 765.8 631.04 L 763.11 633.05 L 765.36 635.13 L 764.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 761px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="761" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="507" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 508px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    承認ステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="572" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        承認ステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="527.5" y="608" width="91" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 89px; height: 1px; padding-top: 628px; margin-left: 529px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    手動承認
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="573" y="632" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        手動承認
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 349.5 568 L 351.95 234.87" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 351.99 229.62 L 355.44 236.64 L 351.95 234.87 L 348.44 236.59 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 466px; margin-left: 349px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ecspresso deploy
                                    <div>
                                        (初回)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="349" y="469" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ecspresso deploy...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="317" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 318px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    デプロイステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="382" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        デプロイステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 360 603 L 410 603 L 410 653 L 360 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 402.49 615.6 L 398.39 611.85 L 394.29 615.6 Z M 396.82 611.43 L 388.5 611.44 L 392.94 614.97 Z M 393.07 623.52 L 386.52 625.93 L 399.11 625.93 Z M 383.46 639.71 L 401.91 639.71 L 401.91 627.31 L 383.46 627.31 Z M 391.53 615.6 L 386.99 612 L 382.49 615.6 Z M 381.05 614.99 L 385.49 611.45 L 376.65 611.46 Z M 379.62 615.6 L 375.09 611.97 L 370.56 615.6 L 377.3 615.6 Z M 376.62 616.98 L 371.09 616.98 L 376.62 622.47 Z M 376.62 624.61 L 370.13 630.52 L 376.62 636.88 Z M 376.62 639.11 L 369.79 645.8 L 369.79 645.91 L 376.62 645.91 Z M 369.79 632.11 L 369.79 643.88 L 375.8 638 Z M 369.79 628.98 L 375.76 623.54 L 369.79 617.62 Z M 369.1 615.02 L 373.53 611.46 L 369.1 611.47 Z M 367.73 610.09 L 366.37 610.09 L 366.37 616.98 L 367.73 616.98 L 367.73 616.29 L 367.73 610.78 Z M 404.9 616.54 C 404.8 616.81 404.54 616.98 404.26 616.98 L 393.71 616.98 L 393.71 622.49 L 402.85 625.98 L 402.85 625.99 C 403.1 626.09 403.28 626.33 403.28 626.62 L 403.28 640.4 C 403.28 640.78 402.97 641.09 402.6 641.09 L 382.77 641.09 C 382.4 641.09 382.09 640.78 382.09 640.4 L 382.09 626.62 C 382.09 626.33 382.27 626.08 382.53 625.98 L 382.53 625.98 L 392.34 622.49 L 392.34 616.98 L 377.99 616.98 L 377.99 646.59 C 377.99 646.97 377.68 647.28 377.3 647.28 L 369.1 647.28 C 368.72 647.28 368.42 646.97 368.42 646.59 L 368.42 618.36 L 365.68 618.36 C 365.31 618.36 365 618.05 365 617.67 L 365 609.41 C 365 609.03 365.31 608.72 365.68 608.72 L 368.42 608.72 C 368.8 608.72 369.1 609.03 369.1 609.41 L 369.1 610.09 L 398.49 610.09 L 404.72 615.78 C 404.93 615.97 405 616.27 404.9 616.54 Z M 390.72 638.54 L 394.69 629.43 L 393.44 628.88 L 389.46 637.99 Z M 394.45 635.5 L 395.34 636.55 L 398.29 634.03 C 398.43 633.91 398.52 633.73 398.53 633.54 C 398.54 633.34 398.47 633.16 398.33 633.02 L 395.87 630.5 L 394.9 631.47 L 396.84 633.46 Z M 385.57 633.5 C 385.42 633.36 385.34 633.16 385.35 632.96 C 385.36 632.75 385.46 632.56 385.63 632.44 L 388.98 629.94 L 389.8 631.04 L 387.11 633.05 L 389.36 635.13 L 388.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 385px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="385" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="126" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 127px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ソースステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 166 603 L 216 603 L 216 653 L 166 653 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 205.92 630.07 L 206.2 628.13 C 208.73 629.65 208.76 630.28 208.76 630.29 C 208.76 630.3 208.32 630.66 205.92 630.07 Z M 204.54 629.68 C 200.16 628.36 194.08 625.57 191.61 624.4 C 191.61 624.39 191.62 624.38 191.62 624.37 C 191.62 623.42 190.85 622.65 189.9 622.65 C 188.95 622.65 188.18 623.42 188.18 624.37 C 188.18 625.32 188.95 626.09 189.9 626.09 C 190.31 626.09 190.69 625.93 190.99 625.69 C 193.89 627.06 199.93 629.81 204.33 631.11 L 202.59 643.4 C 202.59 643.43 202.58 643.47 202.58 643.5 C 202.58 644.58 197.79 646.57 189.97 646.57 C 182.06 646.57 177.21 644.58 177.21 643.5 C 177.21 643.47 177.21 643.44 177.21 643.4 L 173.57 616.83 C 176.72 619 183.49 620.14 189.97 620.14 C 196.44 620.14 203.2 619 206.36 616.84 Z M 173.18 614.06 C 173.24 613.12 178.64 609.43 189.97 609.43 C 201.3 609.43 206.7 613.12 206.76 614.06 L 206.76 614.38 C 206.13 616.48 199.13 618.71 189.97 618.71 C 180.79 618.71 173.79 616.48 173.18 614.37 Z M 208.18 614.07 C 208.18 611.6 201.09 608 189.97 608 C 178.85 608 171.76 611.6 171.76 614.07 L 171.82 614.61 L 175.79 643.56 C 175.88 646.79 184.51 648 189.97 648 C 196.73 648 203.92 646.44 204.01 643.56 L 205.72 631.49 C 206.68 631.72 207.46 631.83 208.09 631.83 C 208.93 631.83 209.5 631.63 209.85 631.21 C 210.14 630.88 210.24 630.47 210.16 630.03 C 209.98 629.04 208.8 627.97 206.41 626.61 L 208.11 614.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 191px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                    <div>
                                        （Gevanniアカウント）
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
（Gevanniアカウント）
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="367.5" y="156" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="367" y="210" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="390.5" y="220">
                    コンテナ
                </text>
            </g>
        </g>
        <g>
            <rect x="313" y="106.5" width="156" height="122" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 313 106.5 L 363 106.5 L 363 156.5 L 313 156.5 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 354.48 136.94 L 348.87 133.57 L 348.87 125.56 C 348.87 125.31 348.74 125.08 348.52 124.95 L 340.45 120.25 L 340.45 113.46 L 354.48 121.75 Z M 355.54 120.75 L 340.11 111.63 C 339.89 111.5 339.63 111.5 339.41 111.62 C 339.19 111.75 339.05 111.98 339.05 112.23 L 339.05 120.65 C 339.05 120.9 339.18 121.13 339.4 121.25 L 347.47 125.96 L 347.47 133.97 C 347.47 134.22 347.6 134.44 347.81 134.57 L 354.82 138.78 C 354.93 138.84 355.05 138.88 355.18 138.88 C 355.3 138.88 355.42 138.85 355.52 138.79 C 355.74 138.66 355.88 138.43 355.88 138.18 L 355.88 121.35 C 355.88 121.1 355.75 120.87 355.54 120.75 Z M 337.96 150 L 321.52 141.26 L 321.52 121.75 L 335.55 113.46 L 335.55 120.26 L 328.16 124.96 C 327.96 125.09 327.83 125.32 327.83 125.56 L 327.83 137.48 C 327.83 137.74 327.98 137.98 328.21 138.1 L 337.64 143.01 C 337.85 143.11 338.09 143.11 338.29 143.01 L 347.44 138.28 L 353.07 141.66 Z M 354.84 141.08 L 347.83 136.87 C 347.62 136.75 347.36 136.74 347.14 136.85 L 337.97 141.59 L 329.24 137.05 L 329.24 125.94 L 336.62 121.24 C 336.83 121.11 336.95 120.89 336.95 120.65 L 336.95 112.23 C 336.95 111.98 336.81 111.75 336.59 111.62 C 336.38 111.5 336.11 111.5 335.89 111.63 L 320.46 120.75 C 320.25 120.87 320.12 121.1 320.12 121.35 L 320.12 141.68 C 320.12 141.94 320.26 142.18 320.49 142.3 L 337.64 151.42 C 337.74 151.47 337.85 151.5 337.97 151.5 C 338.08 151.5 338.2 151.47 338.31 151.41 L 354.82 142.3 C 355.04 142.18 355.17 141.95 355.18 141.7 C 355.18 141.44 355.05 141.21 354.84 141.08 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 132px; margin-left: 365px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ECS
                                    <span style="background-color: transparent;">
                                        クラスター
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="365" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        ECSクラスター
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="367.5" y="156" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="367" y="210" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="390.5" y="220">
                    コンテナ
                </text>
            </g>
        </g>
        <g>
            <path d="M 1062.5 379 L 1112.5 379 L 1112.5 429 L 1062.5 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 1078.95 422.57 L 1070.08 422.57 L 1079.89 402.07 L 1084.34 411.22 Z M 1080.53 400.11 C 1080.41 399.86 1080.16 399.71 1079.89 399.71 L 1079.89 399.71 C 1079.61 399.71 1079.36 399.87 1079.24 400.11 L 1068.3 422.98 C 1068.2 423.2 1068.21 423.46 1068.34 423.67 C 1068.47 423.87 1068.7 424 1068.95 424 L 1079.4 424 C 1079.68 424 1079.93 423.84 1080.05 423.59 L 1085.78 411.52 C 1085.87 411.32 1085.87 411.1 1085.77 410.9 Z M 1105.37 422.57 L 1096.56 422.57 L 1082.42 392.98 C 1082.31 392.73 1082.05 392.57 1081.78 392.57 L 1076.01 392.57 L 1076.02 385.43 L 1087.32 385.43 L 1101.39 415.02 C 1101.51 415.27 1101.76 415.43 1102.04 415.43 L 1105.37 415.43 Z M 1106.09 414 L 1102.49 414 L 1088.42 384.41 C 1088.3 384.16 1088.05 384 1087.77 384 L 1075.3 384 C 1074.91 384 1074.59 384.32 1074.59 384.71 L 1074.58 393.29 C 1074.58 393.48 1074.65 393.66 1074.79 393.79 C 1074.92 393.93 1075.1 394 1075.29 394 L 1081.33 394 L 1095.46 423.59 C 1095.58 423.84 1095.83 424 1096.11 424 L 1106.09 424 C 1106.48 424 1106.8 423.68 1106.8 423.29 L 1106.8 414.71 C 1106.8 414.32 1106.48 414 1106.09 414 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 436px; margin-left: 1088px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    Lambda
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1088" y="448" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Lambda
                    </text>
                </switch>
            </g>
        </g>
        <g/>
        <g>
            <rect x="1276" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 1277px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    config.py
                                    <div>
                                        実行
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1341" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        config.py...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1315 605.92 L 1365 605.92 L 1365 655.92 L 1315 655.92 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 1357.49 618.52 L 1353.39 614.77 L 1349.29 618.52 Z M 1351.82 614.35 L 1343.5 614.36 L 1347.94 617.89 Z M 1348.07 626.44 L 1341.52 628.85 L 1354.11 628.85 Z M 1338.46 642.62 L 1356.91 642.62 L 1356.91 630.23 L 1338.46 630.23 Z M 1346.53 618.52 L 1341.99 614.92 L 1337.49 618.52 Z M 1336.05 617.91 L 1340.49 614.36 L 1331.65 614.38 Z M 1334.62 618.52 L 1330.09 614.89 L 1325.56 618.52 L 1332.3 618.52 Z M 1331.62 619.9 L 1326.09 619.9 L 1331.62 625.38 Z M 1331.62 627.53 L 1325.13 633.44 L 1331.62 639.79 Z M 1331.62 642.03 L 1324.79 648.71 L 1324.79 648.82 L 1331.62 648.82 Z M 1324.79 635.02 L 1324.79 646.79 L 1330.8 640.91 Z M 1324.79 631.9 L 1330.76 626.46 L 1324.79 620.54 Z M 1324.1 617.93 L 1328.53 614.38 L 1324.1 614.39 Z M 1322.73 613.01 L 1321.37 613.01 L 1321.37 619.9 L 1322.73 619.9 L 1322.73 619.21 L 1322.73 613.7 Z M 1359.9 619.46 C 1359.8 619.72 1359.54 619.9 1359.26 619.9 L 1348.71 619.9 L 1348.71 625.41 L 1357.85 628.9 L 1357.85 628.9 C 1358.1 629.01 1358.28 629.25 1358.28 629.54 L 1358.28 643.31 C 1358.28 643.69 1357.97 644 1357.6 644 L 1337.77 644 C 1337.4 644 1337.09 643.69 1337.09 643.31 L 1337.09 629.54 C 1337.09 629.24 1337.27 629 1337.53 628.9 L 1337.53 628.89 L 1347.34 625.41 L 1347.34 619.9 L 1332.99 619.9 L 1332.99 649.51 C 1332.99 649.89 1332.68 650.2 1332.3 650.2 L 1324.1 650.2 C 1323.72 650.2 1323.42 649.89 1323.42 649.51 L 1323.42 621.27 L 1320.68 621.27 C 1320.31 621.27 1320 620.97 1320 620.59 L 1320 612.32 C 1320 611.94 1320.31 611.63 1320.68 611.63 L 1323.42 611.63 C 1323.8 611.63 1324.1 611.94 1324.1 612.32 L 1324.1 613.01 L 1353.49 613.01 L 1359.72 618.7 C 1359.93 618.89 1360 619.19 1359.9 619.46 Z M 1345.72 641.46 L 1349.69 632.35 L 1348.44 631.8 L 1344.46 640.9 Z M 1349.45 638.42 L 1350.34 639.47 L 1353.29 636.95 C 1353.43 636.82 1353.52 636.64 1353.53 636.45 C 1353.54 636.26 1353.47 636.07 1353.33 635.94 L 1350.87 633.42 L 1349.9 634.39 L 1351.84 636.38 Z M 1340.57 636.42 C 1340.42 636.28 1340.34 636.08 1340.35 635.87 C 1340.36 635.67 1340.46 635.48 1340.63 635.35 L 1343.98 632.85 L 1344.8 633.96 L 1342.11 635.96 L 1344.36 638.05 L 1343.44 639.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 663px; margin-left: 1340px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1340" y="675" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1316" y="142.5" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1341 142.5 C 1327.21 142.5 1316 153.72 1316 167.5 C 1316 181.28 1327.21 192.5 1341 192.5 C 1354.78 192.5 1366 181.28 1366 167.5 C 1366 153.72 1354.78 142.5 1341 142.5 Z M 1341 190.23 C 1328.47 190.23 1318.27 180.03 1318.27 167.5 C 1318.27 154.97 1328.47 144.77 1341 144.77 C 1353.53 144.77 1363.73 154.97 1363.73 167.5 C 1363.73 180.03 1353.53 190.23 1341 190.23 Z M 1355.81 174.32 L 1354.07 174.32 L 1354.07 170.48 C 1354.07 169.85 1353.56 169.35 1352.93 169.35 L 1350.09 169.35 L 1350.09 165.51 C 1350.09 164.88 1349.58 164.38 1348.95 164.38 L 1342.14 164.38 L 1342.14 161.68 L 1348.95 161.68 C 1349.58 161.68 1350.09 161.17 1350.09 160.54 L 1350.09 151.59 C 1350.09 150.96 1349.58 150.45 1348.95 150.45 L 1333.05 150.45 C 1332.42 150.45 1331.91 150.96 1331.91 151.59 L 1331.91 160.54 C 1331.91 161.17 1332.42 161.68 1333.05 161.68 L 1339.86 161.68 L 1339.86 164.38 L 1333.05 164.38 C 1332.42 164.38 1331.91 164.88 1331.91 165.51 L 1331.91 169.35 L 1329.07 169.35 C 1328.44 169.35 1327.93 169.85 1327.93 170.48 L 1327.93 174.32 L 1326.19 174.32 C 1325.56 174.32 1325.05 174.83 1325.05 175.45 L 1325.05 180.43 C 1325.05 181.05 1325.56 181.56 1326.19 181.56 L 1331.06 181.56 C 1331.68 181.56 1332.19 181.05 1332.19 180.43 L 1332.19 175.45 C 1332.19 174.83 1331.68 174.32 1331.06 174.32 L 1330.21 174.32 L 1330.21 171.62 L 1334.89 171.62 L 1334.89 174.32 L 1334.04 174.32 C 1333.41 174.32 1332.9 174.83 1332.9 175.45 L 1332.9 180.43 C 1332.9 181.05 1333.41 181.56 1334.04 181.56 L 1339.01 181.56 C 1339.64 181.56 1340.15 181.05 1340.15 180.43 L 1340.15 175.45 C 1340.15 174.83 1339.64 174.32 1339.01 174.32 L 1337.17 174.32 L 1337.17 170.48 C 1337.17 169.85 1336.66 169.35 1336.03 169.35 L 1334.18 169.35 L 1334.18 166.65 L 1347.82 166.65 L 1347.82 169.35 L 1345.97 169.35 C 1345.34 169.35 1344.84 169.85 1344.84 170.48 L 1344.84 174.32 L 1342.99 174.32 C 1342.36 174.32 1341.85 174.83 1341.85 175.45 L 1341.85 180.43 C 1341.85 181.05 1342.36 181.56 1342.99 181.56 L 1347.96 181.56 C 1348.59 181.56 1349.1 181.05 1349.1 180.43 L 1349.1 175.45 C 1349.1 174.83 1348.59 174.32 1347.96 174.32 L 1347.11 174.32 L 1347.11 171.62 L 1351.8 171.62 L 1351.8 174.32 L 1350.87 174.32 C 1350.25 174.32 1349.74 174.83 1349.74 175.45 L 1349.74 180.43 C 1349.74 181.05 1350.25 181.56 1350.87 181.56 L 1355.81 181.56 C 1356.44 181.56 1356.95 181.05 1356.95 180.43 L 1356.95 175.45 C 1356.95 174.83 1356.44 174.32 1355.81 174.32 Z M 1334.18 159.4 L 1334.18 152.73 L 1347.82 152.73 L 1347.82 159.4 Z M 1327.32 179.29 L 1327.32 176.59 L 1329.92 176.59 L 1329.92 179.29 Z M 1335.18 179.29 L 1335.18 176.59 L 1337.88 176.59 L 1337.88 179.29 Z M 1344.13 179.29 L 1344.13 176.59 L 1346.82 176.59 L 1346.82 179.29 Z M 1352.01 179.29 L 1352.01 176.59 L 1354.68 176.59 L 1354.68 179.29 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 1341px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1341" y="212" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1341 568 L 1341 198.87" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1341 193.62 L 1344.5 200.62 L 1341 198.87 L 1337.5 200.62 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 524px; margin-left: 1341px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    config.py実行
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1341" y="527" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        config.py実行
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="107.5" y="221.5" width="45" height="45" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOUUeugVe+cVe+gUfOQVeucYeOYWeegVe+cVe+cUfOcVe+cVeucVe+YUe+gVe0z/l+sAAAARdFJOUwAQgO//rzAgUM+fQN9gv3CPCYlhQAAAAAlwSFlzAAAXEQAAFxEByibzPwAAAypJREFUWEftlt2WqjAMha2NRUSF93/ak70JlEILBa/OWvPdTGQ0TXZ+yu2PP07inBk/c/dyN/Nn7iIPM0lozLjAU6Q1k7zkcdlbJ+LNBM6LPM0+jRORhfia8vtMLT7BDKKBxM/uLWdK4V7yNpO0Ip2ZVM+fkEsFSir3FenN5IeXmVUM6uxjtqIf56yC/isGWYMevpBo2WAvka+ZlUBgPxer6cJkX2kISJY9v38nDeGGZCTyfNRZFHzJsl2CirEQtsRDJ+eoJTvNOJ2vPO79PXDlUG4Zjg4EtrRcCp8R5Ce+IsMZ7YGUqYpjfsUJyPSgTsyKaWXoYO3kF6RdD63DL1bYBDm/l5+2tx6VbAh95IMpRSC35dXtTTjLoi0aj0OXD2YbOhBVM9T00FOZmlS9x0kagYCZIQp9kg95otBTK2OQNr2vX0iWG9Eg/NbZLbzmWPRn29Zv4lkzVGedwYj1BtLJtIl2XLpYdQ2THR3RD7klgIIsN2sDWVo0QXllsB/MTsAKif/otLK4mBBcqeEaPb5w3yz7wkqvpYVRmAM9p3QNor5jX7B3PSLrENe2wCTbDxO626iNU0P8EymA0qTvXoOuZVgB0woHOFjzLqTB2+NglXPr0AFFWy8NF579MFYjrVaG2cEo2rKGn+HbjnlbOJpAQUsQHZho4+MRDiKxeuOGLjXMwsEs2gLOlPj2YTmyWsv3riUBbUAHFO2xkuo59PF6JnjvWm0vIzrIqp4DM5brCzrATGxVL5Lvi13Vy+SWKB28sYuiaFVs+4IOvkXVd8ASfQzAliIdUOtZtCPmwG1lTnNOBzDrVe/8tCLQF4QO4JlaR9EO4JnT98KdKSJHOmgxo1G0AxpWehs+HXDAomgH8Is8PYXPlRdFm0XYgfnlzhwdIGQsgZoGLeXHyYcDLIVs2FswNbkv4k4c1wJcHb18GiHNr2nHGLH98bxadZIGpZuCr8n6F0Neq3oOpMYxUZmGRt9a6lTPovcHU5u7/8RaWKPl52LFnUQqVc+A9UX9sC40xLa/7Gq+6jVJvQqu+1HwmnBV6TXaCDuX7Tl0bmr2ZiU/aP3Hf83t9g/IgRlOBBqbEwAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="104" y="275" width="54" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="130" y="284.5">
                    event bus
                </text>
            </g>
        </g>
        <g>
            <image x="107.5" y="376.5" width="45" height="45" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOUUeugVe+cVe+gUfOQVeucYeOYWeegVe+cVe+cUfOcVe+cVeucVe+YUe+gVe0z/l+sAAAARdFJOUwAQgO//rzAgUM+fQN9gv3CPCYlhQAAAAAlwSFlzAAAXEQAAFxEByibzPwAAAypJREFUWEftlt2WqjAMha2NRUSF93/ak70JlEILBa/OWvPdTGQ0TXZ+yu2PP07inBk/c/dyN/Nn7iIPM0lozLjAU6Q1k7zkcdlbJ+LNBM6LPM0+jRORhfia8vtMLT7BDKKBxM/uLWdK4V7yNpO0Ip2ZVM+fkEsFSir3FenN5IeXmVUM6uxjtqIf56yC/isGWYMevpBo2WAvka+ZlUBgPxer6cJkX2kISJY9v38nDeGGZCTyfNRZFHzJsl2CirEQtsRDJ+eoJTvNOJ2vPO79PXDlUG4Zjg4EtrRcCp8R5Ce+IsMZ7YGUqYpjfsUJyPSgTsyKaWXoYO3kF6RdD63DL1bYBDm/l5+2tx6VbAh95IMpRSC35dXtTTjLoi0aj0OXD2YbOhBVM9T00FOZmlS9x0kagYCZIQp9kg95otBTK2OQNr2vX0iWG9Eg/NbZLbzmWPRn29Zv4lkzVGedwYj1BtLJtIl2XLpYdQ2THR3RD7klgIIsN2sDWVo0QXllsB/MTsAKif/otLK4mBBcqeEaPb5w3yz7wkqvpYVRmAM9p3QNor5jX7B3PSLrENe2wCTbDxO626iNU0P8EymA0qTvXoOuZVgB0woHOFjzLqTB2+NglXPr0AFFWy8NF579MFYjrVaG2cEo2rKGn+HbjnlbOJpAQUsQHZho4+MRDiKxeuOGLjXMwsEs2gLOlPj2YTmyWsv3riUBbUAHFO2xkuo59PF6JnjvWm0vIzrIqp4DM5brCzrATGxVL5Lvi13Vy+SWKB28sYuiaFVs+4IOvkXVd8ASfQzAliIdUOtZtCPmwG1lTnNOBzDrVe/8tCLQF4QO4JlaR9EO4JnT98KdKSJHOmgxo1G0AxpWehs+HXDAomgH8Is8PYXPlRdFm0XYgfnlzhwdIGQsgZoGLeXHyYcDLIVs2FswNbkv4k4c1wJcHb18GiHNr2nHGLH98bxadZIGpZuCr8n6F0Neq3oOpMYxUZmGRt9a6lTPovcHU5u7/8RaWKPl52LFnUQqVc+A9UX9sC40xLa/7Gq+6jVJvQqu+1HwmnBV6TXaCDuX7Tl0bmr2ZiU/aP3Hf83t9g/IgRlOBBqbEwAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="104" y="430" width="54" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="130" y="439.5">
                    event bus
                </text>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>