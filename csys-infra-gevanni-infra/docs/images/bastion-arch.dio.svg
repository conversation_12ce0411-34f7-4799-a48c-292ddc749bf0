<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="620px" height="401px" viewBox="-0.5 -0.5 620 401" content="&lt;mxfile&gt;&lt;diagram id=&quot;U4NK6GnbAuaAQ4b66llG&quot; name=&quot;ページ1&quot;&gt;7Vpbb6M4FP41kXZfIsAQwmNDM7MrzUqVKu3OPo1ccIgVwMiYXPbX7zGYcLFp020m7UrttAo+GF++853vHJOZoTA7fuW42P7BYpLOHCs+ztD9zHFs1/LgQ1pOjWVpBY0h4TRWnTrDI/2HKKOlrBWNSTnoKBhLBS2GxojlOYnEwIY5Z4dhtw1Lh7MWOCGa4THCqW79i8Ziq3bh+J39N0KTrZrZW6LmRobbvmoj5RbH7NAzofUMhZwx0Vxlx5CkErsWlua5LxN3FYp7nFZqpTkRB8Z37bo4ycUlA7U76Y3050Oo1ixOLQ7ljohI7siaoRWrREpzEp4xl8aE45jCpCFLGQdbznJ4drUVWQotGy4PWyrIY4EjOeYB+AK2DcuF8rrttG01rRwVYCvkdXZMJMHm+FC684Szqqin/B38brz7Y19E8nHB2Y60S5o5aOkHdrCQE9E0HS11T7ig4Pq7lCZyVMHkJFi1UrIRckRYP82Tb3XrHllqzaYpYlxuSaw2ovuldSHMSo49tik/fSUsI4KfoEsXTc0jKpYWqnnoiOkiZdv2SOlayohVMCTnoTtiwIXixgRPdMYVnAnwP2wRqFI9AQE12oAr8viMgYkAPX6MkDa74xo4+kMcka0DiXwDkGcHvAnIpQFIuseC3BLGcdz9BFiDC1E9a/1bUIVNjFFdp7gUNMTRFpBdpBKLmO7hMpGXvzwSvqeRBP3u1/Y+zNLr8rwIFozmol6xt4Jf2EPY/HnQNZSWueMZjCabrxttvRt82KYZxkaTzdeNtt5NttpVD40mm+/pKx4/bRuetkdPw+9EThkpK/z7It0/EG+wh4Hj1dTX5H5T/4y1uA2Jb/iJpA+spILWaeSJCcGyFzNBBDFC+DDoXspjuCyabW3oUa7DnNg4KVnFI9KktRU0TQmO1MSOamJfJXIRCoaC6OmhuzBE7uIacuhpgavH3QirjEW7qphnmMsPTuLvOic2Gwt+hk4612BTKjrkjyoLrgCwa7tDgB1D6jZm7isooy6M4eNrq0T0spNelaP0+e3XYuqMqiHbgKmRtfZVaKsBspJBCcHqWDW+VphWpZSJzyTy/0gi63vfrgXjjUkko3GcXnyceK8UEgHpVulo6bwJkltFp2cITu8KsYmQFnQkhlO9ajIutixhOU7XnbXvCNg1P31XqNeNv2UDCKia98f+zftTv/VAOIUVy/qgNg5lMar4fnQklEsbgGzAvXGpygmNSWCekLaXY/YFh0pB0P1weBOy9aN3nONTr4PSpm7kB2noVQ3WKKktrZGXmhE7n52XdpEbXU1ieyW7oaI3mJ5aTTaX968v+l8lMBNvRC7THdX5w9WtNSsIX+9Jk7jsKSEC1REYsOI/7FZqVjjaJXVAtLuMyQZX6bU0ZznSHMMJ1F3qmoOeiYyLNce9kuacGx9Jc+zgo4iOi36u6Nj6a65Odf6DxHyKyZXFBN1ITFz7/cSknbpHQlwUMv4IzvS9vXB00wcz0LjUU+uQyuMF9Eg9PsTMkel4EiybKl4/P0AZPz5pLPyJc4UfmA4Ri+Ex4EVqD8KwR+AJyo9pq8XAJTyOaBkxO5hXZf3IKHAty7N8dPkriDNFLib0YsTnZX2iulVJrr+F4FVeZ5Ry92zm1L9Gmgajn7Ja2vdTlmvMfm9IYRdvX3/n/8btP5/BTXj4hhQe3Gr/+peMz2rQRG3+qUHvrkGa3piYN61B6IYiBM3u6/WmFOz+jwJa/ws=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <path d="M 80 0 L 510 0 L 510 400 L 80 400 Z" fill="none" stroke="#879196" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));"/>
            <path d="M 90.59 6.65 C 90.53 6.65 90.48 6.65 90.42 6.65 L 90.42 6.65 C 89.11 6.68 88.03 7.24 87.14 8.25 C 87.13 8.25 87.13 8.25 87.13 8.25 C 86.2 9.36 85.87 10.52 85.96 11.73 C 84.81 12.06 84.12 12.92 83.76 13.74 C 83.75 13.75 83.75 13.76 83.74 13.78 C 83.33 15.05 83.68 16.36 84.24 17.16 C 84.25 17.17 84.25 17.17 84.26 17.18 C 84.94 18.05 85.97 18.53 87.02 18.53 L 98.17 18.53 C 99.19 18.53 100.07 18.16 100.8 17.37 C 101.25 16.94 101.49 16.29 101.58 15.59 C 101.67 14.9 101.61 14.16 101.32 13.55 C 101.31 13.54 101.31 13.53 101.31 13.52 C 100.8 12.62 99.95 11.81 98.76 11.64 C 98.74 10.79 98.28 9.99 97.68 9.56 C 97.67 9.55 97.66 9.55 97.65 9.54 C 97.01 9.18 96.4 9.14 95.91 9.3 C 95.6 9.4 95.36 9.56 95.14 9.74 C 94.51 8.36 93.43 7.18 91.81 6.79 C 91.81 6.79 91.81 6.79 91.81 6.79 C 91.38 6.7 90.97 6.65 90.59 6.65 Z M 90.43 7.38 C 90.8 7.38 91.2 7.43 91.64 7.53 C 93.16 7.89 94.15 9.07 94.66 10.48 C 94.71 10.6 94.81 10.69 94.94 10.72 C 95.07 10.74 95.2 10.7 95.29 10.61 C 95.54 10.34 95.83 10.11 96.14 10.01 C 96.44 9.91 96.78 9.92 97.26 10.18 C 97.67 10.49 98.11 11.31 98.03 11.9 C 98.01 12.01 98.05 12.12 98.12 12.2 C 98.19 12.28 98.29 12.33 98.39 12.33 C 99.46 12.34 100.16 13.02 100.64 13.88 C 100.85 14.3 100.91 14.92 100.84 15.5 C 100.76 16.07 100.53 16.59 100.28 16.83 C 100.27 16.84 100.27 16.85 100.26 16.85 C 99.65 17.53 99.03 17.78 98.17 17.78 L 87.02 17.78 C 86.2 17.78 85.39 17.41 84.85 16.73 C 84.44 16.13 84.14 15.02 84.46 14.02 C 84.79 13.27 85.36 12.55 86.41 12.36 C 86.6 12.32 86.74 12.14 86.71 11.94 C 86.56 10.79 86.8 9.81 87.7 8.74 C 88.49 7.85 89.33 7.39 90.43 7.38 Z M 92.2 10.7 C 91.77 10.7 91.4 10.93 91.13 11.21 C 90.85 11.5 90.64 11.85 90.64 12.25 L 90.64 12.71 L 90.14 12.71 C 90.04 12.71 89.94 12.75 89.87 12.82 C 89.8 12.89 89.76 12.98 89.76 13.08 L 89.76 15.7 C 89.76 15.8 89.8 15.89 89.87 15.96 C 89.94 16.03 90.04 16.07 90.14 16.07 L 94.16 16.07 C 94.26 16.07 94.35 16.03 94.42 15.96 C 94.49 15.89 94.53 15.8 94.53 15.7 L 94.53 13.08 C 94.53 12.98 94.49 12.89 94.42 12.82 C 94.35 12.75 94.26 12.71 94.16 12.71 L 93.68 12.71 L 93.68 12.25 C 93.68 11.84 93.47 11.47 93.21 11.2 C 92.94 10.92 92.61 10.7 92.2 10.7 Z M 92.2 11.45 C 92.29 11.45 92.5 11.54 92.67 11.72 C 92.83 11.89 92.93 12.11 92.93 12.25 L 92.93 12.71 L 91.39 12.71 L 91.39 12.25 C 91.39 12.15 91.49 11.91 91.66 11.74 C 91.83 11.56 92.06 11.45 92.2 11.45 Z M 90.51 13.46 L 93.78 13.46 L 93.78 15.32 L 90.51 15.32 Z M 80 25 L 80 0 L 105 0 L 105 25 Z" fill="#879196" stroke="none" pointer-events="all" style="fill: light-dark(rgb(135, 145, 150), rgb(106, 115, 119));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 398px; height: 1px; padding-top: 7px; margin-left: 112px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #879196; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#879196, #6a7377); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    VPC
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="112" y="19" fill="#879196" font-family="&quot;Helvetica&quot;" font-size="12px">
                        VPC
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="110" y="250" width="370" height="140" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 257px; margin-left: 112px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    protected subnet
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="112" y="269" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        protected subnet
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="110" y="30" width="370" height="210" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 368px; height: 1px; padding-top: 37px; margin-left: 112px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    private subnet
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="112" y="49" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        private subnet
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 279 290 L 339 290 L 339 350 L 279 350 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 318.43 338.05 L 318.43 333.03 C 316.26 334.43 312.35 335.1 308.62 335.1 C 304.56 335.1 301.39 334.39 299.57 333.17 L 299.57 338.05 C 299.57 339.5 302.96 341 308.62 341 C 314.4 341 318.43 339.44 318.43 338.05 Z M 308.62 327.47 C 304.56 327.47 301.39 326.77 299.57 325.55 L 299.57 330.45 C 299.6 331.89 302.98 333.38 308.62 333.38 C 314.38 333.38 318.4 331.84 318.43 330.44 L 318.43 325.41 C 316.26 326.81 312.35 327.47 308.62 327.47 Z M 318.43 322.82 L 318.43 317.02 C 316.26 318.43 312.35 319.09 308.62 319.09 C 304.56 319.09 301.39 318.38 299.57 317.17 L 299.57 322.82 C 299.6 324.27 302.98 325.76 308.62 325.76 C 314.38 325.76 318.4 324.21 318.43 322.82 Z M 299.57 314.42 C 299.57 314.43 299.57 314.43 299.57 314.43 L 299.57 314.43 L 299.57 314.44 C 299.6 315.89 302.98 317.38 308.62 317.38 C 314.91 317.38 318.4 315.64 318.43 314.44 L 318.43 314.43 L 318.43 314.43 C 318.43 314.43 318.43 314.43 318.43 314.42 C 318.43 313.22 314.94 311.47 308.62 311.47 C 302.95 311.47 299.57 312.97 299.57 314.42 Z M 320.14 314.44 L 320.14 322.81 L 320.15 322.81 C 320.15 322.81 320.14 322.82 320.14 322.83 L 320.14 330.43 L 320.15 330.43 C 320.15 330.44 320.14 330.44 320.14 330.45 L 320.14 338.05 C 320.14 341.25 314.17 342.71 308.62 342.71 C 302.08 342.71 297.86 340.88 297.86 338.05 L 297.86 330.46 C 297.86 330.45 297.85 330.44 297.85 330.43 L 297.86 330.43 L 297.86 322.83 C 297.86 322.82 297.85 322.82 297.85 322.81 L 297.86 322.81 L 297.86 314.45 C 297.86 314.44 297.85 314.43 297.85 314.42 C 297.85 311.59 302.08 309.75 308.62 309.75 C 314.17 309.75 320.15 311.22 320.15 314.42 C 320.15 314.43 320.14 314.44 320.14 314.44 Z M 332.14 303.39 C 332.62 303.39 333 303.01 333 302.53 L 333 298.14 C 333 297.67 332.62 297.29 332.14 297.29 L 285.86 297.29 C 285.38 297.29 285 297.67 285 298.14 L 285 302.53 C 285 303.01 285.38 303.39 285.86 303.39 C 286.9 303.39 287.75 304.24 287.75 305.28 C 287.75 306.31 286.9 307.16 285.86 307.16 C 285.38 307.16 285 307.54 285 308.02 L 285 325.57 C 285 326.05 285.38 326.43 285.86 326.43 L 294.43 326.43 L 294.43 324.72 L 290.14 324.72 L 290.14 322.15 L 294.43 322.15 L 294.43 320.43 L 289.29 320.43 C 288.81 320.43 288.43 320.81 288.43 321.29 L 288.43 324.72 L 286.71 324.72 L 286.71 308.77 C 288.29 308.39 289.47 306.97 289.47 305.28 C 289.47 303.58 288.29 302.16 286.71 301.78 L 286.71 299 L 331.29 299 L 331.29 301.78 C 329.71 302.16 328.53 303.58 328.53 305.28 C 328.53 306.97 329.71 308.39 331.29 308.77 L 331.29 324.72 L 329.57 324.72 L 329.57 321.29 C 329.57 320.81 329.19 320.43 328.71 320.43 L 323.57 320.43 L 323.57 322.15 L 327.86 322.15 L 327.86 324.72 L 323.57 324.72 L 323.57 326.43 L 332.14 326.43 C 332.62 326.43 333 326.05 333 325.57 L 333 308.02 C 333 307.54 332.62 307.16 332.14 307.16 C 331.1 307.16 330.25 306.31 330.25 305.28 C 330.25 304.24 331.1 303.39 332.14 303.39 Z M 298.71 309.29 L 298.71 302.43 C 298.71 301.96 298.33 301.57 297.86 301.57 L 292.71 301.57 C 292.24 301.57 291.86 301.96 291.86 302.43 L 291.86 317 C 291.86 317.47 292.24 317.86 292.71 317.86 L 295.29 317.86 L 295.29 316.14 L 293.57 316.14 L 293.57 303.29 L 297 303.29 L 297 309.29 Z M 324.43 316.14 L 323.57 316.14 L 323.57 317.86 L 325.29 317.86 C 325.76 317.86 326.14 317.47 326.14 317 L 326.14 302.43 C 326.14 301.96 325.76 301.57 325.29 301.57 L 320.14 301.57 C 319.67 301.57 319.29 301.96 319.29 302.43 L 319.29 309.29 L 321 309.29 L 321 303.29 L 324.43 303.29 Z M 317.57 308.43 L 317.57 302.43 C 317.57 301.96 317.19 301.57 316.71 301.57 L 310.71 301.57 C 310.24 301.57 309.86 301.96 309.86 302.43 L 309.86 307.57 L 311.57 307.57 L 311.57 303.29 L 315.86 303.29 L 315.86 308.43 Z M 306.43 307.57 L 306.43 303.29 L 302.14 303.29 L 302.14 308.43 L 300.43 308.43 L 300.43 302.43 C 300.43 301.96 300.81 301.57 301.29 301.57 L 307.29 301.57 C 307.76 301.57 308.14 301.96 308.14 302.43 L 308.14 307.57 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 357px; margin-left: 309px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ElastiCache
                                    <div>
                                        (Service A)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="309" y="369" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ElastiCach...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 358 260 L 374 276 L 390 260 L 394 264 L 378 280 L 394 296 L 390 300 L 374 284 L 358 300 L 354 296 L 370 280 L 354 264 Z" fill="#ff0000" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="180" y="60" width="260" height="160" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 180 60 L 230 60 L 230 110 L 180 110 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 221.48 90.44 L 215.87 87.07 L 215.87 79.06 C 215.87 78.81 215.74 78.58 215.52 78.45 L 207.45 73.75 L 207.45 66.96 L 221.48 75.25 Z M 222.54 74.25 L 207.11 65.13 C 206.89 65 206.63 65 206.41 65.12 C 206.19 65.25 206.05 65.48 206.05 65.73 L 206.05 74.15 C 206.05 74.4 206.18 74.63 206.4 74.75 L 214.47 79.46 L 214.47 87.47 C 214.47 87.72 214.6 87.94 214.81 88.07 L 221.82 92.28 C 221.93 92.34 222.05 92.38 222.18 92.38 C 222.3 92.38 222.42 92.35 222.52 92.29 C 222.74 92.16 222.88 91.93 222.88 91.68 L 222.88 74.85 C 222.88 74.6 222.75 74.37 222.54 74.25 Z M 204.96 103.5 L 188.52 94.76 L 188.52 75.25 L 202.55 66.96 L 202.55 73.76 L 195.16 78.46 C 194.96 78.59 194.83 78.82 194.83 79.06 L 194.83 90.98 C 194.83 91.24 194.98 91.48 195.21 91.6 L 204.64 96.51 C 204.85 96.61 205.09 96.61 205.29 96.51 L 214.44 91.78 L 220.07 95.16 Z M 221.84 94.58 L 214.83 90.37 C 214.62 90.25 214.36 90.24 214.14 90.35 L 204.97 95.09 L 196.24 90.55 L 196.24 79.44 L 203.62 74.74 C 203.83 74.61 203.95 74.39 203.95 74.15 L 203.95 65.73 C 203.95 65.48 203.81 65.25 203.59 65.12 C 203.38 65 203.11 65 202.89 65.13 L 187.46 74.25 C 187.25 74.37 187.12 74.6 187.12 74.85 L 187.12 95.18 C 187.12 95.44 187.26 95.68 187.49 95.8 L 204.64 104.92 C 204.74 104.97 204.85 105 204.97 105 C 205.08 105 205.2 104.97 205.31 104.91 L 221.82 95.8 C 222.04 95.68 222.17 95.45 222.18 95.2 C 222.18 94.94 222.05 94.71 221.84 94.58 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 67px; margin-left: 232px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    Bastion ECS Cluster
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="232" y="79" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Bastion E...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 244 160.91 L 244 320 L 272.63 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 277.88 320 L 270.88 323.5 L 272.63 320 L 270.88 316.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="220" y="130" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 266.91 130.09 L 221.09 130.09 C 220.49 130.09 220 130.58 220 131.18 L 220 159.82 C 220 160.42 220.49 160.91 221.09 160.91 L 266.91 160.91 C 267.51 160.91 268 160.42 268 159.82 L 268 131.18 C 268 130.58 267.51 130.09 266.91 130.09 Z M 222.18 158.73 L 222.18 132.27 L 265.82 132.27 L 265.82 158.73 Z M 225.73 156 L 227.91 156 L 227.91 135 L 225.73 135 Z M 231.45 156 L 233.64 156 L 233.64 135 L 231.45 135 Z M 237.18 156 L 239.36 156 L 239.36 135 L 237.18 135 Z M 242.91 156 L 245.09 156 L 245.09 135 L 242.91 135 Z M 248.63 156 L 250.82 156 L 250.82 135 L 248.63 135 Z M 254.36 156 L 256.55 156 L 256.55 135 L 254.36 135 Z M 260.09 156 L 262.27 156 L 262.27 135 L 260.09 135 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 168px; margin-left: 244px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Service A
                                    <div>
                                        <div>
                                            bastion
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="244" y="180" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Service...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 374 160.91 L 374 320 L 345.37 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 340.12 320 L 347.12 316.5 L 345.37 320 L 347.12 323.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="350" y="130" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 376.32 153.68 L 379.32 153.68 L 379.32 151.64 L 376.32 151.64 Z M 366.5 145.36 L 366.5 137.05 L 381.5 137.05 L 381.5 145.36 Z M 367.46 153.95 L 367.46 151.36 L 380.54 151.36 L 380.54 153.95 Z M 365.41 147.55 L 372.91 147.55 L 372.91 149.18 L 366.36 149.18 C 365.76 149.18 365.27 149.67 365.27 150.27 L 365.27 155.05 C 365.27 155.65 365.76 156.14 366.36 156.14 L 381.64 156.14 C 382.24 156.14 382.73 155.65 382.73 155.05 L 382.73 150.27 C 382.73 149.67 382.24 149.18 381.64 149.18 L 375.09 149.18 L 375.09 147.55 L 382.59 147.55 C 383.19 147.55 383.68 147.06 383.68 146.45 L 383.68 135.95 C 383.68 135.35 383.19 134.86 382.59 134.86 L 365.41 134.86 C 364.81 134.86 364.32 135.35 364.32 135.95 L 364.32 146.45 C 364.32 147.06 364.81 147.55 365.41 147.55 Z M 391.04 156 L 393.23 156 L 393.23 135 L 391.04 135 Z M 385.32 156 L 387.5 156 L 387.5 135 L 385.32 135 Z M 360.5 156 L 362.68 156 L 362.68 135 L 360.5 135 Z M 354.77 156 L 356.96 156 L 356.96 135 L 354.77 135 Z M 352.18 158.73 L 352.18 132.27 L 395.82 132.27 L 395.82 158.73 Z M 396.91 130.09 L 351.09 130.09 C 350.49 130.09 350 130.58 350 131.18 L 350 159.82 C 350 160.42 350.49 160.91 351.09 160.91 L 396.91 160.91 C 397.51 160.91 398 160.42 398 159.82 L 398 131.18 C 398 130.58 397.51 130.09 396.91 130.09 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 168px; margin-left: 374px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Service B
                                    <div>
                                        bastion
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="374" y="180" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Service...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="0" y="120.5" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 17.6 120.5 C 12.12 120.5 7.65 124.77 7.65 130.01 C 7.65 133.18 9.28 135.98 11.77 137.71 C 4.35 140.19 0 147.31 0 154.4 L 0 160.2 L 0.69 160.2 L 17.28 160.2 L 17.28 162.66 C 17.28 164.37 18.73 165.76 20.51 165.76 L 30.57 165.76 L 30.57 167.84 L 26.15 167.84 C 25.38 167.83 24.74 168.42 24.74 169.16 C 24.74 169.9 25.38 170.5 26.15 170.49 L 41.13 170.49 C 41.9 170.5 42.53 169.9 42.53 169.16 C 42.53 168.42 41.9 167.83 41.13 167.84 L 36.7 167.84 L 36.7 165.76 L 46.76 165.76 C 48.54 165.76 50 164.37 50 162.66 L 50 148.3 C 50 146.6 48.54 145.2 46.76 145.2 L 32.64 145.2 C 32.15 144.41 31.58 143.62 30.94 142.87 C 29.09 140.7 26.58 138.8 23.4 137.73 C 25.9 136 27.54 133.18 27.54 130.01 C 27.54 124.77 23.08 120.5 17.6 120.5 Z M 17.6 122.09 C 22.18 122.09 25.87 125.63 25.87 130.01 C 25.87 134.39 22.18 137.93 17.6 137.93 C 13.01 137.93 9.32 134.39 9.32 130.01 C 9.32 125.63 13.01 122.09 17.6 122.09 Z M 13.39 138.63 C 14.67 139.2 16.09 139.52 17.6 139.52 C 19.09 139.52 20.51 139.2 21.78 138.63 C 25.27 139.51 27.93 141.44 29.86 143.71 C 30.28 144.19 30.66 144.69 31 145.2 L 20.51 145.2 C 18.73 145.2 17.28 146.6 17.28 148.3 L 17.28 158.88 L 1.39 158.88 L 1.39 154.4 C 1.39 147.45 5.79 140.51 13.39 138.63 Z M 20.51 146.79 L 46.76 146.79 C 47.65 146.79 48.34 147.45 48.34 148.3 L 48.34 162.66 C 48.34 163.51 47.65 164.17 46.76 164.17 L 20.51 164.17 C 19.63 164.17 18.94 163.51 18.94 162.66 L 18.94 148.3 C 18.94 147.45 19.63 146.79 20.51 146.79 Z M 21.21 148.47 L 21.21 162.44 L 46.06 162.44 L 46.06 148.47 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 178px; margin-left: 25px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <div>
                                        service A
                                    </div>
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="25" y="190" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        service...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 50 145.5 L 213.63 145.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 218.88 145.5 L 211.88 149 L 213.63 145.5 L 211.88 142 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 146px; margin-left: 135px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    run task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="135" y="149" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        run task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 570 145.5 L 404.37 145.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 399.12 145.5 L 406.12 142 L 404.37 145.5 L 406.12 149 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 146px; margin-left: 484px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    run task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="484" y="149" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        run task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="570" y="120.5" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 587.6 120.5 C 582.12 120.5 577.65 124.77 577.65 130.01 C 577.65 133.18 579.28 135.98 581.77 137.71 C 574.35 140.19 570 147.31 570 154.4 L 570 160.2 L 570.69 160.2 L 587.28 160.2 L 587.28 162.66 C 587.28 164.37 588.73 165.76 590.51 165.76 L 600.57 165.76 L 600.57 167.84 L 596.15 167.84 C 595.38 167.83 594.74 168.42 594.74 169.16 C 594.74 169.9 595.38 170.5 596.15 170.49 L 611.13 170.49 C 611.9 170.5 612.53 169.9 612.53 169.16 C 612.53 168.42 611.9 167.83 611.13 167.84 L 606.7 167.84 L 606.7 165.76 L 616.76 165.76 C 618.54 165.76 620 164.37 620 162.66 L 620 148.3 C 620 146.6 618.54 145.2 616.76 145.2 L 602.64 145.2 C 602.15 144.41 601.58 143.62 600.94 142.87 C 599.09 140.7 596.58 138.8 593.4 137.73 C 595.9 136 597.54 133.18 597.54 130.01 C 597.54 124.77 593.08 120.5 587.6 120.5 Z M 587.6 122.09 C 592.18 122.09 595.87 125.63 595.87 130.01 C 595.87 134.39 592.18 137.93 587.6 137.93 C 583.01 137.93 579.32 134.39 579.32 130.01 C 579.32 125.63 583.01 122.09 587.6 122.09 Z M 583.39 138.63 C 584.67 139.2 586.09 139.52 587.6 139.52 C 589.09 139.52 590.51 139.2 591.78 138.63 C 595.27 139.51 597.93 141.44 599.86 143.71 C 600.28 144.19 600.66 144.69 601 145.2 L 590.51 145.2 C 588.73 145.2 587.28 146.6 587.28 148.3 L 587.28 158.88 L 571.39 158.88 L 571.39 154.4 C 571.39 147.45 575.79 140.51 583.39 138.63 Z M 590.51 146.79 L 616.76 146.79 C 617.65 146.79 618.34 147.45 618.34 148.3 L 618.34 162.66 C 618.34 163.51 617.65 164.17 616.76 164.17 L 590.51 164.17 C 589.63 164.17 588.94 163.51 588.94 162.66 L 588.94 148.3 C 588.94 147.45 589.63 146.79 590.51 146.79 Z M 591.21 148.47 L 591.21 162.44 L 616.06 162.44 L 616.06 148.47 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 178px; margin-left: 595px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    <div>
                                        service B
                                    </div>
                                    app team
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="595" y="190" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        service...
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>