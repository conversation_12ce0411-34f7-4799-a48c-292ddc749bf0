<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="571px" height="736px" viewBox="-0.5 -0.5 571 736" content="&lt;mxfile&gt;&lt;diagram id=&quot;t1SR_PXq1gYllvhI1J37&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;sUVaOCGj8OUhIo01Ldo2&quot; name=&quot;ページ2&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;OhwhAC8GBpe2jGCgQJW2&quot; name=&quot;ページ3&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="0" y="0" width="570" height="735" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="14" y="12" width="532" height="311" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 530px; height: 1px; padding-top: 19px; margin-left: 16px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gevanni
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="16" y="31" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gevanni
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="15" y="355" width="529" height="369" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 527px; height: 1px; padding-top: 362px; margin-left: 17px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    アプリ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="17" y="374" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        アプリ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="51" y="552" width="465" height="152" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 191 L 76 236.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 241.88 L 72.5 234.88 L 76 236.63 L 79.5 234.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 293 L 76 395.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 400.88 L 72.5 393.88 L 76 395.63 L 79.5 393.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 76 452 L 76 545.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 76 550.88 L 72.5 543.88 L 76 545.63 L 79.5 543.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 51 141 L 101 141 L 101 191 L 51 191 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 90.92 168.07 L 91.2 166.13 C 93.73 167.65 93.76 168.28 93.76 168.29 C 93.76 168.3 93.32 168.66 90.92 168.07 Z M 89.54 167.68 C 85.16 166.36 79.08 163.56 76.61 162.4 C 76.61 162.39 76.62 162.38 76.62 162.37 C 76.62 161.43 75.85 160.66 74.9 160.66 C 73.95 160.66 73.18 161.43 73.18 162.37 C 73.18 163.32 73.95 164.09 74.9 164.09 C 75.31 164.09 75.69 163.93 75.99 163.69 C 78.89 165.06 84.93 167.81 89.33 169.11 L 87.59 181.4 C 87.59 181.43 87.58 181.47 87.58 181.5 C 87.58 182.58 82.79 184.57 74.97 184.57 C 67.06 184.57 62.21 182.58 62.21 181.5 C 62.21 181.47 62.21 181.44 62.21 181.4 L 58.57 154.83 C 61.72 157 68.49 158.14 74.97 158.14 C 81.44 158.14 88.2 157 91.36 154.84 Z M 58.18 152.06 C 58.24 151.12 63.64 147.43 74.97 147.43 C 86.3 147.43 91.7 151.12 91.76 152.06 L 91.76 152.38 C 91.13 154.48 84.13 156.71 74.97 156.71 C 65.79 156.71 58.79 154.48 58.18 152.37 Z M 93.18 152.07 C 93.18 149.6 86.09 146 74.97 146 C 63.85 146 56.76 149.6 56.76 152.07 L 56.82 152.61 L 60.79 181.56 C 60.88 184.79 69.51 186 74.97 186 C 81.73 186 88.92 184.44 89.01 181.56 L 90.72 169.49 C 91.68 169.72 92.46 169.83 93.09 169.83 C 93.93 169.83 94.5 169.63 94.85 169.21 C 95.14 168.88 95.24 168.47 95.16 168.03 C 94.98 167.04 93.8 165.97 91.41 164.61 L 93.11 152.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 198px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="210" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 243 L 101 243 L 101 293 L 51 293 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 85.75 286.57 C 84.22 286.57 82.98 285.32 82.98 283.78 C 82.98 282.24 84.22 280.99 85.75 280.99 C 87.29 280.99 88.53 282.24 88.53 283.78 C 88.53 285.32 87.29 286.57 85.75 286.57 Z M 79.67 274.43 L 72.28 274.43 L 68.59 268 L 72.28 261.57 L 79.67 261.57 L 83.36 268 Z M 67.16 255.01 C 65.63 255.01 64.38 253.76 64.38 252.22 C 64.38 250.68 65.63 249.43 67.16 249.43 C 68.69 249.43 69.94 250.68 69.94 252.22 C 69.94 253.76 68.69 255.01 67.16 255.01 Z M 85.75 279.56 C 85.23 279.56 84.72 279.66 84.26 279.84 L 81.24 274.72 L 81.08 274.82 L 84.79 268.36 C 84.92 268.14 84.92 267.86 84.79 267.64 L 80.69 260.5 C 80.56 260.28 80.33 260.14 80.08 260.14 L 72.61 260.14 L 72.64 260.13 L 69.88 255.43 C 70.78 254.66 71.36 253.51 71.36 252.22 C 71.36 249.89 69.47 248 67.16 248 C 64.84 248 62.96 249.89 62.96 252.22 C 62.96 254.55 64.84 256.44 67.16 256.44 C 67.69 256.44 68.19 256.34 68.65 256.16 L 71.24 260.54 L 67.16 267.64 C 67.03 267.86 67.03 268.14 67.16 268.36 L 71.26 275.5 C 71.39 275.72 71.62 275.86 71.87 275.86 L 80.08 275.86 C 80.13 275.86 80.19 275.85 80.25 275.83 L 83.04 280.57 C 82.13 281.34 81.56 282.49 81.56 283.78 C 81.56 286.11 83.44 288 85.75 288 C 88.07 288 89.95 286.11 89.95 283.78 C 89.95 281.45 88.07 279.56 85.75 279.56 Z M 89.99 262.36 C 88.46 262.36 87.21 261.11 87.21 259.57 C 87.21 258.03 88.46 256.78 89.99 256.78 C 91.52 256.78 92.77 258.03 92.77 259.57 C 92.77 261.11 91.52 262.36 89.99 262.36 Z M 95.19 267.64 L 92.49 262.95 C 93.52 262.18 94.19 260.95 94.19 259.57 C 94.19 257.24 92.3 255.35 89.99 255.35 C 89.4 255.35 88.84 255.47 88.33 255.69 L 86.13 251.86 C 86 251.64 85.77 251.51 85.52 251.51 L 76.71 251.51 L 76.71 252.94 L 85.11 252.94 L 87.14 256.48 C 86.31 257.25 85.79 258.35 85.79 259.57 C 85.79 261.89 87.67 263.79 89.99 263.79 C 90.42 263.79 90.84 263.72 91.23 263.6 L 93.76 268 L 90.21 274.18 L 91.44 274.89 L 95.19 268.36 C 95.32 268.14 95.32 267.86 95.19 267.64 Z M 62.59 278.89 C 61.06 278.89 59.81 277.64 59.81 276.1 C 59.81 274.56 61.06 273.31 62.59 273.31 C 64.12 273.31 65.37 274.56 65.37 276.1 C 65.37 277.64 64.12 278.89 62.59 278.89 Z M 64.91 279.61 C 66.04 278.86 66.79 277.56 66.79 276.1 C 66.79 273.77 64.91 271.88 62.59 271.88 C 61.92 271.88 61.29 272.04 60.73 272.32 L 58.25 268 L 62.36 260.84 L 61.13 260.12 L 56.81 267.64 C 56.68 267.86 56.68 268.14 56.81 268.36 L 59.57 273.17 C 58.85 273.93 58.39 274.96 58.39 276.1 C 58.39 278.43 60.28 280.32 62.59 280.32 C 62.94 280.32 63.28 280.27 63.6 280.19 L 65.87 284.14 C 66 284.36 66.23 284.49 66.48 284.49 L 75.29 284.49 L 75.29 283.06 L 66.89 283.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 300px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="312" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 402 L 101 402 L 101 452 L 51 452 Z" fill="#e7157b" stroke="none" pointer-events="all" style="fill: light-dark(rgb(231, 21, 123), rgb(255, 129, 217));"/>
            <path d="M 85.75 445.57 C 84.22 445.57 82.98 444.32 82.98 442.78 C 82.98 441.24 84.22 439.99 85.75 439.99 C 87.29 439.99 88.53 441.24 88.53 442.78 C 88.53 444.32 87.29 445.57 85.75 445.57 Z M 79.67 433.43 L 72.28 433.43 L 68.59 427 L 72.28 420.57 L 79.67 420.57 L 83.36 427 Z M 67.16 414.01 C 65.63 414.01 64.38 412.76 64.38 411.22 C 64.38 409.68 65.63 408.43 67.16 408.43 C 68.69 408.43 69.94 409.68 69.94 411.22 C 69.94 412.76 68.69 414.01 67.16 414.01 Z M 85.75 438.56 C 85.23 438.56 84.72 438.66 84.26 438.84 L 81.24 433.72 L 81.08 433.82 L 84.79 427.36 C 84.92 427.14 84.92 426.86 84.79 426.64 L 80.69 419.5 C 80.56 419.28 80.33 419.14 80.08 419.14 L 72.61 419.14 L 72.64 419.13 L 69.88 414.43 C 70.78 413.66 71.36 412.51 71.36 411.22 C 71.36 408.89 69.47 407 67.16 407 C 64.84 407 62.96 408.89 62.96 411.22 C 62.96 413.55 64.84 415.44 67.16 415.44 C 67.69 415.44 68.19 415.34 68.65 415.16 L 71.24 419.54 L 67.16 426.64 C 67.03 426.86 67.03 427.14 67.16 427.36 L 71.26 434.5 C 71.39 434.72 71.62 434.86 71.87 434.86 L 80.08 434.86 C 80.13 434.86 80.19 434.85 80.25 434.83 L 83.04 439.57 C 82.13 440.34 81.56 441.49 81.56 442.78 C 81.56 445.11 83.44 447 85.75 447 C 88.07 447 89.95 445.11 89.95 442.78 C 89.95 440.45 88.07 438.56 85.75 438.56 Z M 89.99 421.36 C 88.46 421.36 87.21 420.11 87.21 418.57 C 87.21 417.03 88.46 415.78 89.99 415.78 C 91.52 415.78 92.77 417.03 92.77 418.57 C 92.77 420.11 91.52 421.36 89.99 421.36 Z M 95.19 426.64 L 92.49 421.95 C 93.52 421.18 94.19 419.95 94.19 418.57 C 94.19 416.24 92.3 414.35 89.99 414.35 C 89.4 414.35 88.84 414.47 88.33 414.69 L 86.13 410.86 C 86 410.64 85.77 410.51 85.52 410.51 L 76.71 410.51 L 76.71 411.94 L 85.11 411.94 L 87.14 415.48 C 86.31 416.25 85.79 417.35 85.79 418.57 C 85.79 420.89 87.67 422.79 89.99 422.79 C 90.42 422.79 90.84 422.72 91.23 422.6 L 93.76 427 L 90.21 433.18 L 91.44 433.89 L 95.19 427.36 C 95.32 427.14 95.32 426.86 95.19 426.64 Z M 62.59 437.89 C 61.06 437.89 59.81 436.64 59.81 435.1 C 59.81 433.56 61.06 432.31 62.59 432.31 C 64.12 432.31 65.37 433.56 65.37 435.1 C 65.37 436.64 64.12 437.89 62.59 437.89 Z M 64.91 438.61 C 66.04 437.86 66.79 436.56 66.79 435.1 C 66.79 432.77 64.91 430.88 62.59 430.88 C 61.92 430.88 61.29 431.04 60.73 431.32 L 58.25 427 L 62.36 419.84 L 61.13 419.12 L 56.81 426.64 C 56.68 426.86 56.68 427.14 56.81 427.36 L 59.57 432.17 C 58.85 432.93 58.39 433.96 58.39 435.1 C 58.39 437.43 60.28 439.32 62.59 439.32 C 62.94 439.32 63.28 439.27 63.6 439.19 L 65.87 443.14 C 66 443.36 66.23 443.49 66.48 443.49 L 75.29 443.49 L 75.29 442.06 L 66.89 442.06 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 459px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    EventBridge
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="471" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        EventBri...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 552 L 101 552 L 101 602 L 51 602 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 92.03 557 L 59.97 557 C 57.78 557 56 558.78 56 560.97 L 56 570.48 C 56 572.67 57.78 574.45 59.97 574.45 L 61.09 574.45 L 61.09 596.27 C 61.09 596.67 61.42 597 61.82 597 L 89.45 597 C 89.86 597 90.18 596.67 90.18 596.27 L 90.18 574.45 L 92.03 574.45 C 94.22 574.45 96 572.67 96 570.48 L 96 560.97 C 96 558.78 94.22 557 92.03 557 Z M 62.55 595.55 L 62.55 574.45 L 88.73 574.45 L 88.73 595.55 Z M 92.03 573 L 59.97 573 C 58.59 573 57.45 571.87 57.45 570.48 L 57.45 570.09 L 66.18 570.09 L 66.18 568.64 L 57.45 568.64 L 57.45 560.97 C 57.45 559.58 58.59 558.45 59.97 558.45 L 92.03 558.45 C 93.41 558.45 94.55 559.58 94.55 560.97 L 94.55 568.64 L 74.18 568.64 L 74.18 570.09 L 94.55 570.09 L 94.55 570.48 C 94.55 571.87 93.41 573 92.03 573 Z M 65.1 584.67 C 65.1 584.46 65.19 584.26 65.34 584.12 L 69.95 580.05 L 70.91 581.14 L 66.93 584.66 L 70.89 588.08 L 69.94 589.18 L 65.35 585.22 C 65.19 585.08 65.1 584.88 65.1 584.67 Z M 79.69 588.11 L 83.69 584.61 L 79.69 581.14 L 80.64 580.04 L 85.27 584.05 C 85.43 584.19 85.52 584.39 85.52 584.6 C 85.52 584.81 85.43 585.01 85.27 585.15 L 80.65 589.21 Z M 73.21 592.03 L 71.87 591.48 L 77.39 578.01 L 78.73 578.56 Z M 68.36 570.09 L 72 570.09 L 72 568.64 L 68.36 568.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 609px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    CodePipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="621" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        CodePipe...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 51 41 L 101 41 L 101 91 L 51 91 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 74.8 63.98 C 74.45 64.18 74.24 64.55 74.24 64.96 L 74.24 84.4 L 59.53 76.18 L 59.53 57.06 L 76.04 47.49 L 90.71 54.76 Z M 92.41 54.74 C 92.41 54.34 92.2 53.96 91.81 53.74 L 76.59 46.2 C 76.25 46 75.81 46 75.47 46.2 L 58.69 55.92 C 58.34 56.12 58.13 56.5 58.13 56.9 L 58.13 76.34 C 58.13 76.74 58.34 77.12 58.7 77.32 L 73.95 85.85 C 74.12 85.95 74.32 86 74.51 86 C 74.71 86 74.9 85.95 75.08 85.85 C 75.42 85.65 75.64 85.27 75.64 84.87 L 75.64 65.12 L 91.85 55.72 C 92.2 55.52 92.41 55.15 92.41 54.74 Z M 92.45 76.22 L 79.14 84.36 L 79.14 77.07 L 86.08 72.59 L 86.13 72.28 C 86.14 72.2 86.15 72.2 86.15 71.82 L 86.17 63.12 L 92.47 59.47 Z M 93.31 58.02 C 92.96 57.81 92.53 57.81 92.18 58.02 L 85.47 61.9 C 85.26 62.02 84.77 62.29 84.77 62.84 L 84.74 71.78 L 78.35 75.91 C 77.97 76.14 77.74 76.5 77.74 76.88 L 77.74 84.81 C 77.74 85.21 77.95 85.57 78.31 85.78 C 78.5 85.89 78.71 85.94 78.91 85.94 C 79.12 85.94 79.32 85.89 79.51 85.78 L 93.29 77.35 C 93.64 77.15 93.85 76.78 93.85 76.38 L 93.87 58.99 C 93.87 58.59 93.66 58.22 93.31 58.02 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 98px; margin-left: 76px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ECR
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="76" y="110" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ECR
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 191 561.63 L 191 188.5 Q 191 178.5 181 178.5 L 101 178.5" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 191 566.88 L 187.5 559.88 L 191 561.63 L 194.5 559.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 256 633 L 310.63 633" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 315.88 633 L 308.88 636.5 L 310.63 633 L 308.88 629.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 191 93 L 191 143.5 Q 191 153.5 181 153.5 L 107.37 153.5" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 102.12 153.5 L 109.12 150 L 107.37 153.5 L 109.12 157 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 137px; margin-left: 192px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    コピー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="192" y="140" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        コピー
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 166 43 L 216 43 L 216 93 L 166 93 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 205.92 70.07 L 206.2 68.14 C 208.73 69.65 208.76 70.28 208.76 70.29 C 208.76 70.3 208.32 70.66 205.92 70.07 Z M 204.54 69.68 C 200.16 68.36 194.08 65.56 191.61 64.4 C 191.61 64.39 191.62 64.38 191.62 64.37 C 191.62 63.42 190.85 62.66 189.9 62.66 C 188.95 62.66 188.18 63.42 188.18 64.37 C 188.18 65.32 188.95 66.09 189.9 66.09 C 190.31 66.09 190.69 65.93 190.99 65.69 C 193.89 67.06 199.93 69.81 204.33 71.11 L 202.59 83.4 C 202.59 83.43 202.58 83.47 202.58 83.5 C 202.58 84.58 197.79 86.57 189.97 86.57 C 182.06 86.57 177.21 84.58 177.21 83.5 C 177.21 83.47 177.21 83.44 177.21 83.4 L 173.57 56.83 C 176.72 59 183.49 60.14 189.97 60.14 C 196.44 60.14 203.2 59 206.36 56.84 Z M 173.18 54.06 C 173.24 53.12 178.64 49.43 189.97 49.43 C 201.3 49.43 206.7 53.12 206.76 54.06 L 206.76 54.38 C 206.13 56.48 199.13 58.71 189.97 58.71 C 180.79 58.71 173.79 56.48 173.18 54.37 Z M 208.18 54.07 C 208.18 51.6 201.09 48 189.97 48 C 178.85 48 171.76 51.6 171.76 54.07 L 171.82 54.61 L 175.79 83.56 C 175.88 86.79 184.51 88 189.97 88 C 196.73 88 203.92 86.44 204.01 83.56 L 205.72 71.49 C 206.68 71.72 207.46 71.83 208.09 71.83 C 208.93 71.83 209.5 71.63 209.85 71.21 C 210.14 70.88 210.24 70.47 210.16 70.03 C 209.98 69.04 208.8 67.97 206.41 66.61 L 208.11 54.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 191px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    マスターバケット
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="112" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        マスターバケット
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 382 568 L 382 234.87" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 382 229.62 L 385.5 236.62 L 382 234.87 L 378.5 236.62 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 466px; margin-left: 381px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ecspresso deploy
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="381" y="469" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        ecspresso deploy
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="317" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 318px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    デプロイステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="382" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        デプロイステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 360 603 L 410 603 L 410 653 L 360 653 Z" fill="#c925d1" stroke="none" pointer-events="all" style="fill: light-dark(rgb(201, 37, 209), rgb(255, 124, 255));"/>
            <path d="M 402.49 615.6 L 398.39 611.85 L 394.29 615.6 Z M 396.82 611.43 L 388.5 611.44 L 392.94 614.97 Z M 393.07 623.52 L 386.52 625.93 L 399.11 625.93 Z M 383.46 639.71 L 401.91 639.71 L 401.91 627.31 L 383.46 627.31 Z M 391.53 615.6 L 386.99 612 L 382.49 615.6 Z M 381.05 614.99 L 385.49 611.45 L 376.65 611.46 Z M 379.62 615.6 L 375.09 611.97 L 370.56 615.6 L 377.3 615.6 Z M 376.62 616.98 L 371.09 616.98 L 376.62 622.47 Z M 376.62 624.61 L 370.13 630.52 L 376.62 636.88 Z M 376.62 639.11 L 369.79 645.8 L 369.79 645.91 L 376.62 645.91 Z M 369.79 632.11 L 369.79 643.88 L 375.8 638 Z M 369.79 628.98 L 375.76 623.54 L 369.79 617.62 Z M 369.1 615.02 L 373.53 611.46 L 369.1 611.47 Z M 367.73 610.09 L 366.37 610.09 L 366.37 616.98 L 367.73 616.98 L 367.73 616.29 L 367.73 610.78 Z M 404.9 616.54 C 404.8 616.81 404.54 616.98 404.26 616.98 L 393.71 616.98 L 393.71 622.49 L 402.85 625.98 L 402.85 625.99 C 403.1 626.09 403.28 626.33 403.28 626.62 L 403.28 640.4 C 403.28 640.78 402.97 641.09 402.6 641.09 L 382.77 641.09 C 382.4 641.09 382.09 640.78 382.09 640.4 L 382.09 626.62 C 382.09 626.33 382.27 626.08 382.53 625.98 L 382.53 625.98 L 392.34 622.49 L 392.34 616.98 L 377.99 616.98 L 377.99 646.59 C 377.99 646.97 377.68 647.28 377.3 647.28 L 369.1 647.28 C 368.72 647.28 368.42 646.97 368.42 646.59 L 368.42 618.36 L 365.68 618.36 C 365.31 618.36 365 618.05 365 617.67 L 365 609.41 C 365 609.03 365.31 608.72 365.68 608.72 L 368.42 608.72 C 368.8 608.72 369.1 609.03 369.1 609.41 L 369.1 610.09 L 398.49 610.09 L 404.72 615.78 C 404.93 615.97 405 616.27 404.9 616.54 Z M 390.72 638.54 L 394.69 629.43 L 393.44 628.88 L 389.46 637.99 Z M 394.45 635.5 L 395.34 636.55 L 398.29 634.03 C 398.43 633.91 398.52 633.73 398.53 633.54 C 398.54 633.34 398.47 633.16 398.33 633.02 L 395.87 630.5 L 394.9 631.47 L 396.84 633.46 Z M 385.57 633.5 C 385.42 633.36 385.34 633.16 385.35 632.96 C 385.36 632.75 385.46 632.56 385.63 632.44 L 388.98 629.94 L 389.8 631.04 L 387.11 633.05 L 389.36 635.13 L 388.44 636.15 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 385px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    codebuild
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="385" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        codebuild
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="126" y="568" width="130" height="130" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 575px; margin-left: 127px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ソースステージ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="587" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースステージ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 166 603 L 216 603 L 216 653 L 166 653 Z" fill="#7aa116" stroke="none" pointer-events="all" style="fill: light-dark(rgb(122, 161, 22), rgb(97, 130, 11));"/>
            <path d="M 205.92 630.07 L 206.2 628.13 C 208.73 629.65 208.76 630.28 208.76 630.29 C 208.76 630.3 208.32 630.66 205.92 630.07 Z M 204.54 629.68 C 200.16 628.36 194.08 625.57 191.61 624.4 C 191.61 624.39 191.62 624.38 191.62 624.37 C 191.62 623.42 190.85 622.65 189.9 622.65 C 188.95 622.65 188.18 623.42 188.18 624.37 C 188.18 625.32 188.95 626.09 189.9 626.09 C 190.31 626.09 190.69 625.93 190.99 625.69 C 193.89 627.06 199.93 629.81 204.33 631.11 L 202.59 643.4 C 202.59 643.43 202.58 643.47 202.58 643.5 C 202.58 644.58 197.79 646.57 189.97 646.57 C 182.06 646.57 177.21 644.58 177.21 643.5 C 177.21 643.47 177.21 643.44 177.21 643.4 L 173.57 616.83 C 176.72 619 183.49 620.14 189.97 620.14 C 196.44 620.14 203.2 619 206.36 616.84 Z M 173.18 614.06 C 173.24 613.12 178.64 609.43 189.97 609.43 C 201.3 609.43 206.7 613.12 206.76 614.06 L 206.76 614.38 C 206.13 616.48 199.13 618.71 189.97 618.71 C 180.79 618.71 173.79 616.48 173.18 614.37 Z M 208.18 614.07 C 208.18 611.6 201.09 608 189.97 608 C 178.85 608 171.76 611.6 171.76 614.07 L 171.82 614.61 L 175.79 643.56 C 175.88 646.79 184.51 648 189.97 648 C 196.73 648 203.92 646.44 204.01 643.56 L 205.72 631.49 C 206.68 631.72 207.46 631.83 208.09 631.83 C 208.93 631.83 209.5 631.63 209.85 631.21 C 210.14 630.88 210.24 630.47 210.16 630.03 C 209.98 629.04 208.8 627.97 206.41 626.61 L 208.11 614.64 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 660px; margin-left: 191px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    ソースバケット
                                    <div>
                                        （Gevanniアカウント）
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="191" y="672" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ソースバケット
（Gevanniアカウント）
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="367.5" y="156" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="367" y="210" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="390.5" y="220">
                    コンテナ
                </text>
            </g>
        </g>
        <g/>
        <g>
            <rect x="304" y="106.5" width="156" height="122" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 304 106.5 L 354 106.5 L 354 156.5 L 304 156.5 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
            <path d="M 345.48 136.94 L 339.87 133.57 L 339.87 125.56 C 339.87 125.31 339.74 125.08 339.52 124.95 L 331.45 120.25 L 331.45 113.46 L 345.48 121.75 Z M 346.54 120.75 L 331.11 111.63 C 330.89 111.5 330.63 111.5 330.41 111.62 C 330.19 111.75 330.05 111.98 330.05 112.23 L 330.05 120.65 C 330.05 120.9 330.18 121.13 330.4 121.25 L 338.47 125.96 L 338.47 133.97 C 338.47 134.22 338.6 134.44 338.81 134.57 L 345.82 138.78 C 345.93 138.84 346.05 138.88 346.18 138.88 C 346.3 138.88 346.42 138.85 346.52 138.79 C 346.74 138.66 346.88 138.43 346.88 138.18 L 346.88 121.35 C 346.88 121.1 346.75 120.87 346.54 120.75 Z M 328.96 150 L 312.52 141.26 L 312.52 121.75 L 326.55 113.46 L 326.55 120.26 L 319.16 124.96 C 318.96 125.09 318.83 125.32 318.83 125.56 L 318.83 137.48 C 318.83 137.74 318.98 137.98 319.21 138.1 L 328.64 143.01 C 328.85 143.11 329.09 143.11 329.29 143.01 L 338.44 138.28 L 344.07 141.66 Z M 345.84 141.08 L 338.83 136.87 C 338.62 136.75 338.36 136.74 338.14 136.85 L 328.97 141.59 L 320.24 137.05 L 320.24 125.94 L 327.62 121.24 C 327.83 121.11 327.95 120.89 327.95 120.65 L 327.95 112.23 C 327.95 111.98 327.81 111.75 327.59 111.62 C 327.38 111.5 327.11 111.5 326.89 111.63 L 311.46 120.75 C 311.25 120.87 311.12 121.1 311.12 121.35 L 311.12 141.68 C 311.12 141.94 311.26 142.18 311.49 142.3 L 328.64 151.42 C 328.74 151.47 328.85 151.5 328.97 151.5 C 329.08 151.5 329.2 151.47 329.31 151.41 L 345.82 142.3 C 346.04 142.18 346.17 141.95 346.18 141.7 C 346.18 141.44 346.05 141.21 345.84 141.08 Z" fill="#ffffff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(255, 255, 255), rgb(18, 18, 18));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 132px; margin-left: 356px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ECS
                                    <span style="background-color: transparent;">
                                        クラスター
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="356" y="135" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px">
                        ECSクラスター
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <image x="358.5" y="156" width="46" height="46" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLBAMAAADKYGfZAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAwUExURQAAAO9wAOxwAOxwAOtwAO1yAO1xAO1xAO5xAO5xAO9wAOpwAOtwAO1wAO5yAO1yACH696YAAAAQdFJOUwAQUGBAn//fz78gMIBwr48dwG1XAAAACXBIWXMAABcRAAAXEQHKJvM/AAAAwklEQVRIx+3VPQ4BQRiA4XeXXT+bvQOjQiNxAVG4g1LCrIaoHEGipJtQOII4gUriAmpRuIALMKvfkZBovjeZqZ5JJlPMhyT9Jq/WdtQhB4F2NSKEgpMlb7Y0mS0SSpZ1si/fS+z+e5ZXOyK1p6xeR1TltarQbcChmTL/zQJ9JNZX8nqGpwcwH8J9ApdpykJhwoQJ+57FpoVvjkTmimdW0N/AeQ31bcr+91s+sqfHKbHj48O5UHSysWWRcXWzDyJJXwdPyDmISQ6XpuMAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="358" y="210" width="50" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="381.5" y="220">
                    コンテナ
                </text>
            </g>
        </g>
        <g>
            <image x="107.5" y="221.5" width="45" height="45" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOUUeugVe+cVe+gUfOQVeucYeOYWeegVe+cVe+cUfOcVe+cVeucVe+YUe+gVe0z/l+sAAAARdFJOUwAQgO//rzAgUM+fQN9gv3CPCYlhQAAAAAlwSFlzAAAXEQAAFxEByibzPwAAAypJREFUWEftlt2WqjAMha2NRUSF93/ak70JlEILBa/OWvPdTGQ0TXZ+yu2PP07inBk/c/dyN/Nn7iIPM0lozLjAU6Q1k7zkcdlbJ+LNBM6LPM0+jRORhfia8vtMLT7BDKKBxM/uLWdK4V7yNpO0Ip2ZVM+fkEsFSir3FenN5IeXmVUM6uxjtqIf56yC/isGWYMevpBo2WAvka+ZlUBgPxer6cJkX2kISJY9v38nDeGGZCTyfNRZFHzJsl2CirEQtsRDJ+eoJTvNOJ2vPO79PXDlUG4Zjg4EtrRcCp8R5Ce+IsMZ7YGUqYpjfsUJyPSgTsyKaWXoYO3kF6RdD63DL1bYBDm/l5+2tx6VbAh95IMpRSC35dXtTTjLoi0aj0OXD2YbOhBVM9T00FOZmlS9x0kagYCZIQp9kg95otBTK2OQNr2vX0iWG9Eg/NbZLbzmWPRn29Zv4lkzVGedwYj1BtLJtIl2XLpYdQ2THR3RD7klgIIsN2sDWVo0QXllsB/MTsAKif/otLK4mBBcqeEaPb5w3yz7wkqvpYVRmAM9p3QNor5jX7B3PSLrENe2wCTbDxO626iNU0P8EymA0qTvXoOuZVgB0woHOFjzLqTB2+NglXPr0AFFWy8NF579MFYjrVaG2cEo2rKGn+HbjnlbOJpAQUsQHZho4+MRDiKxeuOGLjXMwsEs2gLOlPj2YTmyWsv3riUBbUAHFO2xkuo59PF6JnjvWm0vIzrIqp4DM5brCzrATGxVL5Lvi13Vy+SWKB28sYuiaFVs+4IOvkXVd8ASfQzAliIdUOtZtCPmwG1lTnNOBzDrVe/8tCLQF4QO4JlaR9EO4JnT98KdKSJHOmgxo1G0AxpWehs+HXDAomgH8Is8PYXPlRdFm0XYgfnlzhwdIGQsgZoGLeXHyYcDLIVs2FswNbkv4k4c1wJcHb18GiHNr2nHGLH98bxadZIGpZuCr8n6F0Neq3oOpMYxUZmGRt9a6lTPovcHU5u7/8RaWKPl52LFnUQqVc+A9UX9sC40xLa/7Gq+6jVJvQqu+1HwmnBV6TXaCDuX7Tl0bmr2ZiU/aP3Hf83t9g/IgRlOBBqbEwAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="104" y="275" width="54" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="130" y="284.5">
                    event bus
                </text>
            </g>
        </g>
        <g>
            <image x="107.5" y="375.5" width="45" height="45" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOUUeugVe+cVe+gUfOQVeucYeOYWeegVe+cVe+cUfOcVe+cVeucVe+YUe+gVe0z/l+sAAAARdFJOUwAQgO//rzAgUM+fQN9gv3CPCYlhQAAAAAlwSFlzAAAXEQAAFxEByibzPwAAAypJREFUWEftlt2WqjAMha2NRUSF93/ak70JlEILBa/OWvPdTGQ0TXZ+yu2PP07inBk/c/dyN/Nn7iIPM0lozLjAU6Q1k7zkcdlbJ+LNBM6LPM0+jRORhfia8vtMLT7BDKKBxM/uLWdK4V7yNpO0Ip2ZVM+fkEsFSir3FenN5IeXmVUM6uxjtqIf56yC/isGWYMevpBo2WAvka+ZlUBgPxer6cJkX2kISJY9v38nDeGGZCTyfNRZFHzJsl2CirEQtsRDJ+eoJTvNOJ2vPO79PXDlUG4Zjg4EtrRcCp8R5Ce+IsMZ7YGUqYpjfsUJyPSgTsyKaWXoYO3kF6RdD63DL1bYBDm/l5+2tx6VbAh95IMpRSC35dXtTTjLoi0aj0OXD2YbOhBVM9T00FOZmlS9x0kagYCZIQp9kg95otBTK2OQNr2vX0iWG9Eg/NbZLbzmWPRn29Zv4lkzVGedwYj1BtLJtIl2XLpYdQ2THR3RD7klgIIsN2sDWVo0QXllsB/MTsAKif/otLK4mBBcqeEaPb5w3yz7wkqvpYVRmAM9p3QNor5jX7B3PSLrENe2wCTbDxO626iNU0P8EymA0qTvXoOuZVgB0woHOFjzLqTB2+NglXPr0AFFWy8NF579MFYjrVaG2cEo2rKGn+HbjnlbOJpAQUsQHZho4+MRDiKxeuOGLjXMwsEs2gLOlPj2YTmyWsv3riUBbUAHFO2xkuo59PF6JnjvWm0vIzrIqp4DM5brCzrATGxVL5Lvi13Vy+SWKB28sYuiaFVs+4IOvkXVd8ASfQzAliIdUOtZtCPmwG1lTnNOBzDrVe/8tCLQF4QO4JlaR9EO4JnT98KdKSJHOmgxo1G0AxpWehs+HXDAomgH8Is8PYXPlRdFm0XYgfnlzhwdIGQsgZoGLeXHyYcDLIVs2FswNbkv4k4c1wJcHb18GiHNr2nHGLH98bxadZIGpZuCr8n6F0Neq3oOpMYxUZmGRt9a6lTPovcHU5u7/8RaWKPl52LFnUQqVc+A9UX9sC40xLa/7Gq+6jVJvQqu+1HwmnBV6TXaCDuX7Tl0bmr2ZiU/aP3Hf83t9g/IgRlOBBqbEwAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        </g>
        <g>
            <g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));">
                <rect fill="#ffffff" stroke="none" x="104" y="429" width="54" height="15" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/>
                <text x="130" y="438.5">
                    event bus
                </text>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>