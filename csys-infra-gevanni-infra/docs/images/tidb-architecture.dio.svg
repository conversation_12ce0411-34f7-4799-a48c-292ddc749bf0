<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="602px" height="201px" viewBox="-0.5 -0.5 602 201" content="&lt;mxfile&gt;&lt;diagram id=&quot;z49tYMhux-z23KhVYUYl&quot; name=&quot;250206&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 0 0 L 200 0 L 200 200 L 0 200 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 6.09 7.18 C 6.01 7.18 5.93 7.19 5.85 7.19 C 5.5 7.19 5.15 7.23 4.81 7.32 C 4.53 7.39 4.25 7.49 3.98 7.62 C 3.9 7.65 3.84 7.7 3.79 7.76 C 3.75 7.83 3.74 7.91 3.74 7.99 L 3.74 8.32 C 3.74 8.46 3.79 8.53 3.89 8.53 L 3.99 8.53 L 4.22 8.44 C 4.45 8.35 4.69 8.27 4.94 8.21 C 5.17 8.16 5.41 8.13 5.65 8.13 C 6.04 8.09 6.43 8.2 6.73 8.44 C 6.97 8.74 7.09 9.12 7.05 9.5 L 7.05 9.99 C 6.79 9.93 6.54 9.88 6.29 9.84 C 6.05 9.81 5.81 9.79 5.57 9.79 C 4.98 9.76 4.4 9.94 3.94 10.31 C 3.54 10.65 3.32 11.15 3.34 11.68 C 3.31 12.15 3.49 12.62 3.82 12.96 C 4.18 13.29 4.66 13.46 5.15 13.44 C 5.91 13.45 6.63 13.11 7.11 12.51 C 7.18 12.66 7.24 12.79 7.31 12.91 C 7.38 13.02 7.46 13.12 7.55 13.21 C 7.6 13.27 7.67 13.31 7.75 13.31 C 7.81 13.31 7.87 13.29 7.92 13.25 L 8.34 12.97 C 8.41 12.93 8.46 12.86 8.47 12.77 C 8.47 12.72 8.45 12.67 8.42 12.62 C 8.34 12.47 8.26 12.31 8.21 12.14 C 8.15 11.95 8.12 11.75 8.13 11.55 L 8.14 9.37 C 8.2 8.77 8 8.18 7.59 7.74 C 7.17 7.39 6.64 7.19 6.09 7.18 Z M 19.89 7.19 C 19.78 7.19 19.68 7.19 19.57 7.2 C 19.29 7.2 19 7.24 18.73 7.31 C 18.47 7.38 18.23 7.5 18.02 7.66 C 17.82 7.81 17.66 7.99 17.54 8.21 C 17.42 8.43 17.35 8.67 17.36 8.92 C 17.36 9.27 17.48 9.61 17.69 9.89 C 17.97 10.22 18.34 10.46 18.76 10.56 L 19.72 10.87 C 19.97 10.93 20.2 11.05 20.39 11.22 C 20.51 11.35 20.58 11.51 20.57 11.69 C 20.58 11.94 20.45 12.18 20.23 12.31 C 19.93 12.48 19.6 12.56 19.26 12.54 C 18.99 12.54 18.72 12.51 18.46 12.45 C 18.22 12.4 17.98 12.32 17.75 12.22 L 17.59 12.15 C 17.54 12.14 17.5 12.14 17.46 12.15 C 17.36 12.15 17.31 12.22 17.31 12.36 L 17.31 12.69 C 17.31 12.76 17.32 12.82 17.35 12.89 C 17.4 12.97 17.47 13.03 17.56 13.07 C 17.8 13.19 18.06 13.28 18.32 13.34 C 18.66 13.41 19 13.45 19.35 13.45 L 19.33 13.46 C 19.66 13.45 19.98 13.4 20.29 13.3 C 20.55 13.22 20.8 13.09 21.01 12.92 C 21.21 12.77 21.38 12.57 21.49 12.34 C 21.61 12.1 21.67 11.83 21.66 11.56 C 21.67 11.23 21.56 10.9 21.36 10.63 C 21.09 10.32 20.73 10.09 20.33 9.99 L 19.39 9.69 C 19.13 9.61 18.88 9.49 18.67 9.32 C 18.54 9.2 18.47 9.03 18.47 8.85 C 18.46 8.61 18.58 8.38 18.79 8.25 C 19.06 8.11 19.36 8.05 19.67 8.06 C 20.11 8.06 20.55 8.14 20.96 8.32 C 21.04 8.37 21.12 8.4 21.21 8.41 C 21.31 8.41 21.36 8.34 21.36 8.19 L 21.36 7.88 C 21.37 7.8 21.35 7.72 21.31 7.66 C 21.25 7.59 21.18 7.54 21.11 7.49 L 20.83 7.38 L 20.45 7.27 L 20.01 7.2 C 19.97 7.2 19.93 7.19 19.89 7.19 Z M 16.02 7.36 C 15.94 7.35 15.86 7.38 15.79 7.42 C 15.72 7.5 15.68 7.59 15.66 7.69 L 14.51 12.14 L 13.47 7.71 C 13.45 7.61 13.41 7.52 13.34 7.44 C 13.26 7.39 13.17 7.37 13.07 7.38 L 12.54 7.38 C 12.44 7.37 12.35 7.39 12.27 7.44 C 12.2 7.51 12.15 7.61 12.14 7.71 L 11.09 12.14 L 9.97 7.7 C 9.95 7.6 9.91 7.51 9.84 7.44 C 9.76 7.39 9.67 7.36 9.58 7.37 L 8.92 7.37 C 8.81 7.37 8.76 7.43 8.76 7.54 C 8.77 7.63 8.79 7.72 8.82 7.81 L 10.38 12.95 C 10.4 13.05 10.45 13.14 10.52 13.21 C 10.6 13.26 10.69 13.29 10.78 13.28 L 11.36 13.26 C 11.46 13.27 11.55 13.25 11.63 13.19 C 11.7 13.12 11.74 13.03 11.76 12.93 L 12.79 8.64 L 13.82 12.93 C 13.83 13.03 13.88 13.12 13.95 13.19 C 14.03 13.25 14.12 13.27 14.21 13.26 L 14.79 13.26 C 14.88 13.27 14.97 13.25 15.04 13.2 C 15.11 13.13 15.16 13.03 15.18 12.94 L 16.79 7.79 C 16.84 7.72 16.84 7.63 16.84 7.63 C 16.84 7.59 16.84 7.56 16.84 7.52 C 16.84 7.48 16.82 7.43 16.79 7.4 C 16.76 7.37 16.72 7.35 16.67 7.36 L 16.05 7.36 C 16.04 7.36 16.03 7.36 16.02 7.36 Z M 5.65 10.62 C 5.7 10.62 5.75 10.62 5.8 10.62 L 6.43 10.62 C 6.64 10.64 6.85 10.67 7.06 10.71 L 7.07 11.01 C 7.07 11.21 7.05 11.4 7 11.59 C 6.96 11.75 6.88 11.9 6.77 12.01 C 6.61 12.21 6.39 12.36 6.14 12.44 C 5.91 12.52 5.67 12.56 5.43 12.56 C 5.18 12.6 4.93 12.53 4.73 12.37 C 4.55 12.18 4.46 11.92 4.49 11.66 C 4.47 11.36 4.59 11.08 4.81 10.89 C 5.06 10.72 5.35 10.62 5.65 10.62 Z M 21.04 14.72 C 20.34 14.73 19.51 14.89 18.89 15.33 C 18.69 15.46 18.72 15.63 18.94 15.63 C 19.64 15.54 21.21 15.35 21.5 15.71 C 21.78 16.06 21.19 17.54 20.94 18.21 C 20.86 18.41 21.04 18.49 21.21 18.34 C 22.39 17.36 22.72 15.3 22.46 15 C 22.32 14.85 21.74 14.71 21.04 14.72 Z M 2.65 15.1 C 2.5 15.12 2.42 15.3 2.58 15.44 C 5.29 17.89 8.82 19.23 12.48 19.21 C 15.37 19.22 18.2 18.36 20.59 16.74 C 20.95 16.47 20.64 16.07 20.26 16.23 C 17.87 17.24 15.3 17.76 12.71 17.77 C 9.23 17.78 5.82 16.87 2.81 15.14 C 2.75 15.11 2.7 15.1 2.65 15.1 Z M 0 0 L 25 0 L 25 25 L 0 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Gevanni Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Gevanni Account
                </text>
            </switch>
        </g>
        <path d="M 20 40 L 180 40 L 180 180 L 20 180 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 30.59 46.65 C 30.53 46.65 30.48 46.65 30.42 46.65 L 30.42 46.65 C 29.11 46.68 28.03 47.24 27.14 48.25 C 27.13 48.25 27.13 48.25 27.13 48.25 C 26.2 49.36 25.87 50.52 25.96 51.73 C 24.81 52.06 24.12 52.92 23.76 53.74 C 23.75 53.75 23.75 53.76 23.74 53.78 C 23.33 55.05 23.68 56.36 24.24 57.16 C 24.25 57.17 24.25 57.17 24.26 57.18 C 24.94 58.05 25.97 58.53 27.02 58.53 L 38.17 58.53 C 39.19 58.53 40.07 58.16 40.8 57.37 C 41.25 56.94 41.49 56.29 41.58 55.59 C 41.67 54.9 41.61 54.16 41.32 53.55 C 41.31 53.54 41.31 53.53 41.31 53.52 C 40.8 52.62 39.95 51.81 38.76 51.64 C 38.74 50.79 38.28 49.99 37.68 49.56 C 37.67 49.55 37.66 49.55 37.65 49.54 C 37.01 49.18 36.4 49.14 35.91 49.3 C 35.6 49.4 35.36 49.56 35.14 49.74 C 34.51 48.36 33.43 47.18 31.81 46.79 C 31.81 46.79 31.81 46.79 31.81 46.79 C 31.38 46.7 30.97 46.65 30.59 46.65 Z M 30.43 47.38 C 30.8 47.38 31.2 47.43 31.64 47.53 C 33.16 47.89 34.15 49.07 34.66 50.48 C 34.71 50.6 34.81 50.69 34.94 50.72 C 35.07 50.74 35.2 50.7 35.29 50.61 C 35.54 50.34 35.83 50.11 36.14 50.01 C 36.44 49.91 36.78 49.92 37.26 50.18 C 37.67 50.49 38.11 51.31 38.03 51.9 C 38.01 52.01 38.05 52.12 38.12 52.2 C 38.19 52.28 38.29 52.33 38.39 52.33 C 39.46 52.34 40.16 53.02 40.64 53.88 C 40.85 54.3 40.91 54.92 40.84 55.5 C 40.76 56.07 40.53 56.59 40.28 56.83 C 40.27 56.84 40.27 56.85 40.26 56.85 C 39.65 57.53 39.03 57.78 38.17 57.78 L 27.02 57.78 C 26.2 57.78 25.39 57.41 24.85 56.73 C 24.44 56.13 24.14 55.02 24.46 54.02 C 24.79 53.27 25.36 52.55 26.41 52.36 C 26.6 52.32 26.74 52.14 26.71 51.94 C 26.56 50.79 26.8 49.81 27.7 48.74 C 28.49 47.85 29.33 47.39 30.43 47.38 Z M 32.2 50.7 C 31.77 50.7 31.4 50.93 31.13 51.21 C 30.85 51.5 30.64 51.85 30.64 52.25 L 30.64 52.71 L 30.14 52.71 C 30.04 52.71 29.94 52.75 29.87 52.82 C 29.8 52.89 29.76 52.98 29.76 53.08 L 29.76 55.7 C 29.76 55.8 29.8 55.89 29.87 55.96 C 29.94 56.03 30.04 56.07 30.14 56.07 L 34.16 56.07 C 34.26 56.07 34.35 56.03 34.42 55.96 C 34.49 55.89 34.53 55.8 34.53 55.7 L 34.53 53.08 C 34.53 52.98 34.49 52.89 34.42 52.82 C 34.35 52.75 34.26 52.71 34.16 52.71 L 33.68 52.71 L 33.68 52.25 C 33.68 51.84 33.47 51.47 33.21 51.2 C 32.94 50.92 32.61 50.7 32.2 50.7 Z M 32.2 51.45 C 32.29 51.45 32.5 51.54 32.67 51.72 C 32.83 51.89 32.93 52.11 32.93 52.25 L 32.93 52.71 L 31.39 52.71 L 31.39 52.25 C 31.39 52.15 31.49 51.91 31.66 51.74 C 31.83 51.56 32.06 51.45 32.2 51.45 Z M 30.51 53.46 L 33.78 53.46 L 33.78 55.32 L 30.51 55.32 Z M 20 65 L 20 40 L 45 40 L 45 65 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 47px; margin-left: 52px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="59" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 241 0 L 601 0 L 601 200 L 241 200 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 247.09 7.18 C 247.01 7.18 246.93 7.19 246.85 7.19 C 246.5 7.19 246.15 7.23 245.81 7.32 C 245.53 7.39 245.25 7.49 244.98 7.62 C 244.9 7.65 244.84 7.7 244.79 7.76 C 244.75 7.83 244.74 7.91 244.74 7.99 L 244.74 8.32 C 244.74 8.46 244.78 8.53 244.88 8.53 L 244.99 8.53 L 245.22 8.44 C 245.45 8.35 245.69 8.27 245.94 8.21 C 246.17 8.16 246.41 8.13 246.65 8.13 C 247.04 8.09 247.43 8.2 247.74 8.44 C 247.97 8.74 248.09 9.12 248.05 9.5 L 248.05 9.99 C 247.78 9.93 247.54 9.88 247.29 9.84 C 247.05 9.81 246.81 9.79 246.57 9.79 C 245.98 9.76 245.4 9.94 244.94 10.31 C 244.54 10.65 244.32 11.15 244.34 11.68 C 244.31 12.15 244.49 12.62 244.82 12.96 C 245.18 13.29 245.66 13.46 246.15 13.44 C 246.91 13.45 247.63 13.11 248.11 12.51 C 248.18 12.66 248.24 12.79 248.31 12.91 C 248.38 13.02 248.46 13.12 248.55 13.21 C 248.6 13.27 248.67 13.31 248.75 13.31 C 248.81 13.31 248.87 13.29 248.92 13.25 L 249.34 12.97 C 249.41 12.93 249.46 12.86 249.47 12.77 C 249.47 12.72 249.45 12.67 249.42 12.62 C 249.34 12.47 249.26 12.31 249.21 12.14 C 249.15 11.95 249.12 11.75 249.13 11.55 L 249.14 9.37 C 249.2 8.77 249 8.18 248.59 7.74 C 248.17 7.39 247.64 7.19 247.09 7.18 Z M 260.89 7.19 C 260.78 7.19 260.68 7.19 260.57 7.2 C 260.29 7.2 260 7.24 259.73 7.31 C 259.47 7.38 259.23 7.5 259.01 7.66 C 258.82 7.81 258.66 7.99 258.54 8.21 C 258.42 8.43 258.35 8.67 258.36 8.92 C 258.36 9.27 258.48 9.61 258.69 9.89 C 258.97 10.22 259.34 10.46 259.76 10.56 L 260.72 10.87 C 260.97 10.93 261.2 11.05 261.39 11.22 C 261.51 11.35 261.58 11.51 261.57 11.69 C 261.58 11.94 261.45 12.18 261.23 12.31 C 260.93 12.48 260.6 12.56 260.26 12.54 C 259.99 12.54 259.72 12.51 259.46 12.45 C 259.22 12.4 258.98 12.32 258.75 12.22 L 258.58 12.15 C 258.54 12.14 258.5 12.14 258.46 12.15 C 258.36 12.15 258.31 12.22 258.31 12.36 L 258.31 12.69 C 258.31 12.76 258.32 12.82 258.35 12.89 C 258.4 12.97 258.47 13.03 258.56 13.07 C 258.8 13.19 259.06 13.28 259.32 13.34 C 259.66 13.41 260 13.45 260.35 13.45 L 260.33 13.46 C 260.66 13.45 260.98 13.4 261.29 13.3 C 261.55 13.22 261.8 13.09 262.01 12.92 C 262.21 12.77 262.38 12.57 262.49 12.34 C 262.61 12.1 262.67 11.83 262.66 11.56 C 262.67 11.23 262.56 10.9 262.36 10.63 C 262.09 10.32 261.73 10.09 261.33 9.99 L 260.39 9.69 C 260.13 9.61 259.88 9.49 259.67 9.32 C 259.54 9.2 259.47 9.03 259.47 8.85 C 259.46 8.61 259.58 8.38 259.79 8.25 C 260.06 8.11 260.36 8.05 260.67 8.06 C 261.11 8.06 261.55 8.14 261.96 8.32 C 262.04 8.37 262.12 8.4 262.21 8.41 C 262.31 8.41 262.36 8.34 262.36 8.19 L 262.36 7.88 C 262.37 7.8 262.35 7.72 262.31 7.66 C 262.25 7.59 262.18 7.54 262.11 7.49 L 261.83 7.38 L 261.45 7.27 L 261.01 7.2 C 260.97 7.2 260.93 7.19 260.89 7.19 Z M 257.02 7.36 C 256.94 7.35 256.86 7.38 256.79 7.42 C 256.72 7.5 256.68 7.59 256.66 7.69 L 255.51 12.14 L 254.47 7.71 C 254.45 7.61 254.41 7.52 254.34 7.44 C 254.26 7.39 254.17 7.37 254.07 7.38 L 253.54 7.38 C 253.44 7.37 253.35 7.39 253.27 7.44 C 253.2 7.51 253.15 7.61 253.14 7.71 L 252.09 12.14 L 250.97 7.7 C 250.95 7.6 250.91 7.51 250.84 7.44 C 250.76 7.39 250.67 7.36 250.58 7.37 L 249.92 7.37 C 249.81 7.37 249.76 7.43 249.76 7.54 C 249.77 7.63 249.79 7.72 249.82 7.81 L 251.38 12.95 C 251.4 13.05 251.45 13.14 251.52 13.21 C 251.6 13.26 251.69 13.29 251.78 13.28 L 252.36 13.26 C 252.46 13.27 252.55 13.25 252.63 13.19 C 252.7 13.12 252.74 13.03 252.76 12.93 L 253.79 8.64 L 254.82 12.93 C 254.83 13.03 254.88 13.12 254.95 13.19 C 255.03 13.25 255.12 13.27 255.21 13.26 L 255.78 13.26 C 255.88 13.27 255.97 13.25 256.04 13.2 C 256.11 13.13 256.16 13.03 256.18 12.94 L 257.79 7.79 C 257.84 7.72 257.84 7.63 257.84 7.63 C 257.84 7.59 257.84 7.56 257.84 7.52 C 257.84 7.48 257.82 7.43 257.79 7.4 C 257.76 7.37 257.72 7.35 257.67 7.36 L 257.05 7.36 C 257.04 7.36 257.03 7.36 257.02 7.36 Z M 246.65 10.62 C 246.7 10.62 246.75 10.62 246.8 10.62 L 247.43 10.62 C 247.64 10.64 247.85 10.67 248.06 10.71 L 248.06 11.01 C 248.07 11.21 248.05 11.4 248 11.59 C 247.96 11.75 247.88 11.9 247.77 12.01 C 247.61 12.21 247.39 12.36 247.14 12.44 C 246.91 12.52 246.67 12.56 246.43 12.56 C 246.18 12.6 245.93 12.53 245.73 12.37 C 245.55 12.18 245.46 11.92 245.49 11.66 C 245.47 11.36 245.59 11.08 245.81 10.89 C 246.06 10.72 246.35 10.62 246.65 10.62 Z M 262.04 14.72 C 261.34 14.73 260.51 14.89 259.88 15.33 C 259.69 15.46 259.72 15.63 259.94 15.63 C 260.64 15.54 262.21 15.35 262.5 15.71 C 262.78 16.06 262.19 17.54 261.94 18.21 C 261.86 18.41 262.04 18.49 262.21 18.34 C 263.39 17.36 263.72 15.3 263.46 15 C 263.32 14.85 262.74 14.71 262.04 14.72 Z M 243.65 15.1 C 243.5 15.12 243.42 15.3 243.58 15.44 C 246.29 17.89 249.82 19.23 253.48 19.21 C 256.37 19.22 259.2 18.36 261.59 16.74 C 261.95 16.47 261.63 16.07 261.26 16.23 C 258.87 17.24 256.3 17.76 253.71 17.77 C 250.23 17.78 246.82 16.87 243.81 15.14 C 243.75 15.11 243.69 15.1 243.65 15.1 Z M 241 0 L 266 0 L 266 25 L 241 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 7px; margin-left: 273px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Service Provider Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="273" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Service Provider Account
                </text>
            </switch>
        </g>
        <path d="M 261 30 L 581 30 L 581 180 L 261 180 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 271.59 36.65 C 271.53 36.65 271.48 36.65 271.42 36.65 L 271.42 36.65 C 270.11 36.68 269.03 37.24 268.14 38.25 C 268.13 38.25 268.13 38.25 268.13 38.25 C 267.2 39.36 266.87 40.52 266.96 41.73 C 265.81 42.06 265.12 42.92 264.76 43.74 C 264.75 43.75 264.75 43.76 264.74 43.78 C 264.33 45.05 264.68 46.36 265.24 47.16 C 265.25 47.17 265.25 47.17 265.26 47.18 C 265.94 48.05 266.97 48.53 268.02 48.53 L 279.17 48.53 C 280.19 48.53 281.07 48.16 281.8 47.37 C 282.25 46.94 282.49 46.29 282.58 45.59 C 282.67 44.9 282.61 44.16 282.32 43.55 C 282.31 43.54 282.31 43.53 282.31 43.52 C 281.8 42.62 280.95 41.81 279.76 41.64 C 279.74 40.79 279.28 39.99 278.68 39.56 C 278.67 39.55 278.66 39.55 278.65 39.54 C 278.01 39.18 277.4 39.14 276.91 39.3 C 276.6 39.4 276.36 39.56 276.14 39.74 C 275.51 38.36 274.43 37.18 272.81 36.79 C 272.81 36.79 272.81 36.79 272.81 36.79 C 272.38 36.7 271.97 36.65 271.59 36.65 Z M 271.43 37.38 C 271.8 37.38 272.2 37.43 272.64 37.53 C 274.16 37.89 275.15 39.07 275.66 40.48 C 275.71 40.6 275.81 40.69 275.94 40.72 C 276.07 40.74 276.2 40.7 276.29 40.61 C 276.54 40.34 276.83 40.11 277.14 40.01 C 277.44 39.91 277.78 39.92 278.26 40.18 C 278.67 40.49 279.11 41.31 279.03 41.9 C 279.01 42.01 279.05 42.12 279.12 42.2 C 279.19 42.28 279.29 42.33 279.39 42.33 C 280.46 42.34 281.16 43.02 281.64 43.88 C 281.85 44.3 281.91 44.92 281.84 45.5 C 281.76 46.07 281.53 46.59 281.28 46.83 C 281.27 46.84 281.27 46.85 281.26 46.85 C 280.65 47.53 280.03 47.78 279.17 47.78 L 268.02 47.78 C 267.2 47.78 266.39 47.41 265.85 46.73 C 265.44 46.13 265.14 45.02 265.46 44.02 C 265.79 43.27 266.36 42.55 267.41 42.36 C 267.6 42.32 267.74 42.14 267.71 41.94 C 267.56 40.79 267.8 39.81 268.7 38.74 C 269.49 37.85 270.33 37.39 271.43 37.38 Z M 273.2 40.7 C 272.77 40.7 272.4 40.93 272.13 41.21 C 271.85 41.5 271.64 41.85 271.64 42.25 L 271.64 42.71 L 271.14 42.71 C 271.04 42.71 270.94 42.75 270.87 42.82 C 270.8 42.89 270.76 42.98 270.76 43.08 L 270.76 45.7 C 270.76 45.8 270.8 45.89 270.87 45.96 C 270.94 46.03 271.04 46.07 271.14 46.07 L 275.16 46.07 C 275.26 46.07 275.35 46.03 275.42 45.96 C 275.49 45.89 275.53 45.8 275.53 45.7 L 275.53 43.08 C 275.53 42.98 275.49 42.89 275.42 42.82 C 275.35 42.75 275.26 42.71 275.16 42.71 L 274.68 42.71 L 274.68 42.25 C 274.68 41.84 274.47 41.47 274.21 41.2 C 273.94 40.92 273.61 40.7 273.2 40.7 Z M 273.2 41.45 C 273.29 41.45 273.5 41.54 273.67 41.72 C 273.83 41.89 273.93 42.11 273.93 42.25 L 273.93 42.71 L 272.39 42.71 L 272.39 42.25 C 272.39 42.15 272.49 41.91 272.66 41.74 C 272.83 41.56 273.06 41.45 273.2 41.45 Z M 271.51 43.46 L 274.78 43.46 L 274.78 45.32 L 271.51 45.32 Z M 261 55 L 261 30 L 286 30 L 286 55 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 37px; margin-left: 293px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="293" y="49" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="321" y="80" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 352.14 96.7 L 347.36 96.7 C 346.86 96.7 346.45 97.11 346.45 97.61 L 346.45 102.39 C 346.45 102.89 346.86 103.29 347.36 103.29 L 352.14 103.29 C 352.64 103.29 353.05 102.89 353.05 102.39 L 353.05 97.61 C 353.05 97.11 352.64 96.7 352.14 96.7 Z M 348.27 101.48 L 348.27 98.52 L 351.23 98.52 L 351.23 101.48 Z M 345.22 108.32 C 345.35 108.8 345.06 109.3 344.58 109.43 L 340.92 110.42 L 340.45 108.67 L 341.87 108.28 L 335.24 104.78 L 336.09 103.17 L 342.92 106.78 L 342.47 105.14 L 344.23 104.66 Z M 333.76 95.14 L 325.86 95.14 C 325.36 95.14 324.95 95.55 324.95 96.05 L 324.95 103.95 C 324.95 104.45 325.36 104.86 325.86 104.86 L 333.76 104.86 C 334.26 104.86 334.67 104.45 334.67 103.95 L 334.67 96.05 C 334.67 95.55 334.26 95.14 333.76 95.14 Z M 326.77 103.04 L 326.77 96.96 L 332.85 96.96 L 332.85 103.04 Z M 352.14 87.95 L 347.36 87.95 C 346.86 87.95 346.45 88.36 346.45 88.86 L 346.45 93.64 C 346.45 94.14 346.86 94.55 347.36 94.55 L 352.14 94.55 C 352.64 94.55 353.05 94.14 353.05 93.64 L 353.05 88.86 C 353.05 88.36 352.64 87.95 352.14 87.95 Z M 348.27 92.73 L 348.27 89.77 L 351.23 89.77 L 351.23 92.73 Z M 335.24 95.22 L 341.87 91.72 L 340.45 91.33 L 340.92 89.58 L 344.58 90.57 C 345.06 90.7 345.35 91.2 345.22 91.68 L 344.23 95.34 L 342.47 94.86 L 342.92 93.22 L 336.09 96.83 Z M 352.14 105.45 L 347.36 105.45 C 346.86 105.45 346.45 105.86 346.45 106.36 L 346.45 111.14 C 346.45 111.64 346.86 112.05 347.36 112.05 L 352.14 112.05 C 352.64 112.05 353.05 111.64 353.05 111.14 L 353.05 106.36 C 353.05 105.86 352.64 105.45 352.14 105.45 Z M 348.27 110.23 L 348.27 107.27 L 351.23 107.27 L 351.23 110.23 Z M 345.62 99.36 C 345.98 99.71 345.98 100.29 345.62 100.64 L 342.94 103.32 L 341.66 102.03 L 342.78 100.91 L 336.23 100.91 L 336.23 99.09 L 342.78 99.09 L 341.66 97.96 L 342.94 96.68 Z M 341 80 C 329.97 80 321 88.97 321 100 C 321 111.03 329.97 120 341 120 C 352.03 120 361 111.03 361 100 C 361 88.97 352.03 80 341 80 Z M 341 118.18 C 330.97 118.18 322.82 110.03 322.82 100 C 322.82 89.97 330.97 81.82 341 81.82 C 351.03 81.82 359.18 89.97 359.18 100 C 359.18 110.03 351.03 118.18 341 118.18 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 341px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NLB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="341" y="139" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NLB
                </text>
            </switch>
        </g>
        <image x="79.5" y="79.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAIpQ/4xQ/4tQ/41O/4xP/4xP/4xP/4xP/4tO/4tQ/49Q/4xP/4xP/4pQ/4xP/4dQ/6Q/yTwAAAARdFJOUwAwUHCPv8+vn4BAEO//YN8gvcN7NwAAAAlwSFlzAAAXEQAAFxEByibzPwAABHhJREFUWEftWNuiqyoMrOKlirX+/9eemRAkQexqn/eZJ0UYJhcC+PgfXR+GcQLG+bmsUVt/R+yf28tje+768RfEda6JFPOqXb5EDJOOnOYh7H3f72EZ37ntF3FrGrUNlYdgtXz4nq2b2X0bu6arle556PtHBPppGzp9vaJLbH9Li9Jx/DxrN7LToG93OOjzrde3ewSSTfrSRkenjxc/xWsayKTTh+Tt6Kqh7rAOaB4uVtMZ71uyA6q2yqVxyZk21s5e0HhnZsSoikokFVTiSDbqcwWmlaU6k3/sY5DI1eIGtDz12YGhMWHOkrYlZZpmlRfH6RtBPzB01udH3LOk3Xj31FnE0S1X/8OIHJVYSXrEvAiKOG048HwqyOjRmNVKIr7GoNQHqN+nZSouZ9yO52q5RaTDyY/4FEnrpKGcs2UrxZ1mgrpKDEjZTnpwaXSOJdcs4r2oUvjj5Orwwbsf7CWGynVKgrVBOVNRtVyMpRMGb20l1IlrT8O3Oek9yyDYHBeF2RwGt4lG4hJNZwAAFbdVXA+0moSNGGdsTlxkqiL06DHphQuhRFsG3t76SJxcfvkRMOjCFdGzFCXMZovkj1wcvuijWGzDWnFZQ5tc1qw6Ep5rseOaXDQyhwgZ4TLEcXFBlYFNLoY8i0d3tz4dFytkGdnmQpegj8hCtz91+4647HuqN46szQWC7HzEwX2qYMnaXKivMESATwzjcQfZ6dLgNheckp2EecnF6T9AsqbNZRz+HZf0bnMhwfJ+9BVXOhh8o8t9qsHtXDfOWy7j+5weLRiqe9/npEJIy9oEuhDQNYQ0gaW64TK1wYRUgDUFV8JHXBiOip+wjMEl3sswtYEVWh8F6R1epDD4whwy0rRVXeF6zLWB69zWibTuYbms+GCoyIK3qn8yXJF6FGCeTrZkGWHqFxSjDWMdF6SX445ZT4IUWMzgWgE4GSYiNV2NcnW1dhi40xCz0RErBMFRKAt2Eu48pd7TQdZIckd/LhBAL42pXA8T7c6DmdwBLzkQwupBFJCcVoCA2/SkePsZRnIqrtCinlS0DRNbuTV17WfGEIpoe54zgl9OaDz0WbUw3HuC5lhyhIbCqBdnr3XtFzCkoxBk2aM4ZRXtAgizHmMKMevlxJ8xcTaONXFiAfeyznCfwOyyHNP9iNjk9EVyOykW1VbJEqtsNjEh0unuCOP0nuZ0jmPpL4e+RO32MAH9bLOZVvqVBawYam9etLB1j6EjrOXid3fllIOqXemRxlwsJNjT1lche80LO8e4pouRpZIRrooWIFMcWSejiTOa7nRHqquzEmSDdvPomTXDnjhThO9vkELms2VdqBbYxnzkT6CvPl5GhUxS0iIe3VEPkt8O1xuwRRQV1mlNpBvT5QZcg0v4NTlzLlgpf/tzRnSUoOkNoQVhek12xd1Cl+Dlfi3Q+/f2p30ZcuAC5uBDh3xNmXa5RHxCr5kAvueyhD3wl1rO2J+YiM7/Ajjx9mn2Lfqcpxl1vv6Ifl+e8zzO87D07Z9Y/xIej/8ALBBQGQ1j/7oAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="45" y="128" width="112" height="15" stroke-width="0"/>
            <text x="99.5" y="137.5">
                PrivateLink Endpoint
            </text>
        </g>
        <image x="480.5" y="79.5" width="40" height="40" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="467" y="128" width="70" height="30" stroke-width="0"/>
            <text x="500.5" y="137.5">
                TiDB Cluster
            </text>
            <text x="500.5" y="151.5">
                Serverless
            </text>
        </g>
        <path d="M 130 100 L 303.63 100" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 308.88 100 L 301.88 103.5 L 303.63 100 L 301.88 96.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 371 100 L 464.63 100" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 469.88 100 L 462.88 103.5 L 464.63 100 L 462.88 96.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>