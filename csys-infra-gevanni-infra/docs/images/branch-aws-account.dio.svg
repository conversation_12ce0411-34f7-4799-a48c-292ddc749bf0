<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="6055px" height="5155px" viewBox="-0.5 -0.5 6055 5155" content="&lt;mxfile scale=&quot;5&quot; border=&quot;0&quot;&gt;&lt;diagram id=&quot;MPQZ45l6xmbpFw6dPRn0&quot; name=&quot;250110&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="350" width="6050" height="4800" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <rect x="0" y="350" width="2450" height="4800" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" stroke-dasharray="40 40" pointer-events="all"/>
        <rect x="400" y="1450" width="1650" height="300" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 320px; margin-left: 81px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                csys-infra-gevanni-sampleapp
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="245" y="324" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    csys-infra-gevanni-sampleapp
                </text>
            </switch>
        </g>
        <rect x="2850" y="1200" width="1300" height="1700" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <rect x="3050" y="1900" width="900" height="800" fill="none" stroke="#99ff99" stroke-width="15" stroke-dasharray="45 45" pointer-events="all"/>
        <rect x="3050" y="1400" width="900" height="400" fill="none" stroke="#66b2ff" stroke-width="15" stroke-dasharray="45 45" pointer-events="all"/>
        <image x="2849.5" y="1199.5" width="150" height="150" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <path d="M 2050 2525 L 3008.82 2522.51" fill="none" stroke="#99ff99" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 3038.82 2522.43 L 2998.87 2542.53 L 3008.82 2522.51 L 2998.77 2502.53 Z" fill="#99ff99" stroke="#99ff99" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 505px; margin-left: 490px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                App Deploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="508" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle" font-weight="bold">
                    App Deploy
                </text>
            </switch>
        </g>
        <rect x="400" y="2350" width="1650" height="350" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 505px; margin-left: 81px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                csys-infra-gevanni-workflow-sample
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="245" y="509" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    csys-infra-gevanni-workflow-sample
                </text>
            </switch>
        </g>
        <path d="M 2050 4225 L 2808.82 4225" fill="none" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 2838.82 4225 L 2798.82 4245 L 2808.82 4225 L 2798.82 4205 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 845px; margin-left: 490px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                CDK Deploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="848" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle" font-weight="bold">
                    CDK Deploy
                </text>
            </switch>
        </g>
        <rect x="400" y="4050" width="1650" height="350" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 845px; margin-left: 81px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                csys-infra-gevanni-cf-sample
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="245" y="849" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    csys-infra-gevanni-cf-sample
                </text>
            </switch>
        </g>
        <path d="M 2050 1600 L 3008.82 1600" fill="none" stroke="#99ccff" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 3038.82 1600 L 2998.82 1620 L 3008.82 1600 L 2998.82 1580 Z" fill="#99ccff" stroke="#99ccff" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 320px; margin-left: 490px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                App Deploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="323" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle" font-weight="bold">
                    App Deploy
                </text>
            </switch>
        </g>
        <path d="M 2050 700 L 4950 700 Q 5000 700 5000 750 L 5000 1158.82" fill="none" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 5000 1188.82 L 4980 1148.82 L 5000 1158.82 L 5020 1148.82 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="400" y="550" width="1650" height="300" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 328px; height: 1px; padding-top: 140px; margin-left: 81px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                csys-infra-gevanni-infra
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="245" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    csys-infra-gevanni-infra
                </text>
            </switch>
        </g>
        <rect x="3150" y="1475" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 320px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                dev
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="324" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    dev
                </text>
            </switch>
        </g>
        <rect x="3150" y="2000" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 425px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                stg
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="429" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    stg
                </text>
            </switch>
        </g>
        <rect x="3150" y="2350" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 495px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                prod
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="499" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    prod
                </text>
            </switch>
        </g>
        <rect x="3200" y="1200" width="600" height="200" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 641px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Main Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    Main Account
                </text>
            </switch>
        </g>
        <path d="M 3500 3500 L 3500 2941.18" fill="none" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 3500 2911.18 L 3520 2951.18 L 3500 2941.18 L 3480 2951.18 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 640px; margin-left: 700px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Cross Account Access
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="643" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle" font-weight="bold">
                    Cross Account Access
                </text>
            </switch>
        </g>
        <rect x="2850" y="3500" width="1300" height="1450" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <rect x="3150" y="3800" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 785px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                dev
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="789" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    dev
                </text>
            </switch>
        </g>
        <rect x="3150" y="4150" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 855px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                stg
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    stg
                </text>
            </switch>
        </g>
        <rect x="3200" y="3500" width="600" height="200" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 720px; margin-left: 641px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                App account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="724" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    App account
                </text>
            </switch>
        </g>
        <rect x="4350" y="1200" width="1300" height="1700" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <rect x="4650" y="1475" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 320px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                dev
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="324" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    dev
                </text>
            </switch>
        </g>
        <rect x="4650" y="1910" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 407px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                stg
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="411" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    stg
                </text>
            </switch>
        </g>
        <rect x="4650" y="2350" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 495px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                prod
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="499" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    prod
                </text>
            </switch>
        </g>
        <rect x="4700" y="1200" width="600" height="200" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 941px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                Route 53
                                <br/>
                                Account
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    Route 53...
                </text>
            </switch>
        </g>
        <image x="4349.5" y="1199.5" width="150" height="150" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <image x="2849.5" y="3499.5" width="150" height="150" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <image x="449.5" y="624.5" width="150" height="150" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <image x="449.5" y="2449.5" width="150" height="150" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <image x="449.5" y="4149.5" width="150" height="150" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <rect x="3900" y="0" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 25px; margin-left: 781px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                AWS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="850" y="29" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    AWS
                </text>
            </switch>
        </g>
        <rect x="875" y="0" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 25px; margin-left: 176px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                GitHub
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="245" y="29" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    GitHub
                </text>
            </switch>
        </g>
        <image x="449.5" y="1524.5" width="150" height="150" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iOTYiIHdpZHRoPSI5OCIgdmlld0JveD0iMCAwIDk4IDk2Ij48cGF0aCBmaWxsPSIjMjQyOTJmIiBkPSJNNDguODU0IDBDMjEuODM5IDAgMCAyMiAwIDQ5LjIxN2MwIDIxLjc1NiAxMy45OTMgNDAuMTcyIDMzLjQwNSA0Ni42OSAyLjQyNy40OSAzLjMxNi0xLjA1OSAzLjMxNi0yLjM2MiAwLTEuMTQxLS4wOC01LjA1Mi0uMDgtOS4xMjctMTMuNTkgMi45MzQtMTYuNDItNS44NjctMTYuNDItNS44NjctMi4xODQtNS43MDQtNS40Mi03LjE3LTUuNDItNy4xNy00LjQ0OC0zLjAxNS4zMjQtMy4wMTUuMzI0LTMuMDE1IDQuOTM0LjMyNiA3LjUyMyA1LjA1MiA3LjUyMyA1LjA1MiA0LjM2NyA3LjQ5NiAxMS40MDQgNS4zNzggMTQuMjM1IDQuMDc0LjQwNC0zLjE3OCAxLjY5OS01LjM3OCAzLjA3NC02LjYtMTAuODM5LTEuMTQxLTIyLjI0My01LjM3OC0yMi4yNDMtMjQuMjgzIDAtNS4zNzggMS45NC05Ljc3OCA1LjAxNC0xMy4yLS40ODUtMS4yMjItMi4xODQtNi4yNzUuNDg2LTEzLjAzOCAwIDAgNC4xMjUtMS4zMDQgMTMuNDI2IDUuMDUyYTQ2Ljk3IDQ2Ljk3IDAgMCAxIDEyLjIxNC0xLjYzYzQuMTI1IDAgOC4zMy41NzEgMTIuMjEzIDEuNjMgOS4zMDItNi4zNTYgMTMuNDI3LTUuMDUyIDEzLjQyNy01LjA1MiAyLjY3IDYuNzYzLjk3IDExLjgxNi40ODUgMTMuMDM4IDMuMTU1IDMuNDIyIDUuMDE1IDcuODIyIDUuMDE1IDEzLjIgMCAxOC45MDUtMTEuNDA0IDIzLjA2LTIyLjMyNCAyNC4yODMgMS43OCAxLjU0OCAzLjMxNiA0LjQ4MSAzLjMxNiA5LjEyNiAwIDYuNi0uMDggMTEuODk3LS4wOCAxMy41MjYgMCAxLjMwNC44OSAyLjg1MyAzLjMxNiAyLjM2NCAxOS40MTItNi41MiAzMy40MDUtMjQuOTM1IDMzLjQwNS00Ni42OTFDOTcuNzA3IDIyIDc1Ljc4OCAwIDQ4Ljg1NCAweiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz48L3N2Zz4=" preserveAspectRatio="none"/>
        <path d="M 2050 700 L 3450 700 Q 3500 700 3500 750 L 3500 1158.82" fill="none" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 3500 1188.82 L 3480 1148.82 L 3500 1158.82 L 3520 1148.82 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="10" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 560px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                CDK Deploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle" font-weight="bold">
                    CDK Deploy
                </text>
            </switch>
        </g>
        <rect x="4350" y="3500" width="1300" height="1450" fill="none" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <rect x="4650" y="3800" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 785px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                dev
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="789" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    dev
                </text>
            </switch>
        </g>
        <rect x="4650" y="4150" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 855px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                stg
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="859" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    stg
                </text>
            </switch>
        </g>
        <rect x="4650" y="3500" width="700" height="200" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 720px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                <div>
                                    Marketplace
                                </div>
                                <div>
                                    Purchase Account
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="724" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    Marketplace...
                </text>
            </switch>
        </g>
        <image x="4349.5" y="3499.5" width="150" height="150" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD8AAAA/CAMAAABggeDtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURSQvPjI8SltjbpKXn9bY2/Hy8////+Tl55+kqrq+woSKkz9JVmlwe62xtsjLz01WYnZ9hxOS1AAAAAAJcEhZcwAAFxEAABcRAcom8z8AAAHZSURBVFhH7ZXbcusgDEW5WVxt+P+v7d7C6UOD3Z4+dc54TSYmCMFGkhXz8PDw8Eew9hysuTc7HzaRmDD0uXCm5tzOZzVWzdlzYkWFFR+RYMwuG6cK1vPpZbMmwxxh1h0X2MN3a+wuMkwTcZiCi1DygW0wVTCuhYY7IhZbLNZh5D741Uyakr7nkIMnQ3iXkAT3xdEV/tLPFVdYN4armf6Dwoekxn0SvyBC8rhLwNDoAfhjtYOIZqk6UoRxDJ7MhKxAsA/fht/orz4RsoP0TvmkIbbX8Q8SVJ3qZ8pwfWovg88TaLwKowgrhyfTHxfA9Rm7nFX+CVRexGDq5gI9IaKYIBUB4DWQDE6qrjl4A5dLowTWINciX+qHqKn8Q/YyChZdFbANjM5WcCJLDFFTP+yjHp5W2NN1CodPTHCf0XZuSnZuetjh9+S/q6E/jm2t/f4KTkO8zMCYxXMPXi/XjrPOvpDX0+8M9JcVSHCambvBuurPt+mNjlLL671PnI8yDm2JS1D77LDLDmfRnlF+o2t1XlD1Dccee3GfKm1vZWfvZnEbf1X/k8r35sUWY9xeXUmiZyFfl/+Lxrb9lS2juf+Y5g/tdsoWMv8Z/pnqUKyu11+4Pjw8/O8Y8wHjAA9cnAnpaQAAAABJRU5ErkJggg==" preserveAspectRatio="none"/>
        <rect x="4650" y="4500" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 925px; margin-left: 931px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                prod
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="929" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    prod
                </text>
            </switch>
        </g>
        <rect x="3150" y="4500" width="700" height="250" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="5" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 925px; margin-left: 631px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">
                                prod
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="700" y="929" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle" font-weight="bold">
                    prod
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>