<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1488px" height="501px" viewBox="-0.5 -0.5 1488 501" content="&lt;mxfile&gt;&lt;diagram id=&quot;FOUvjBAzowrTEmdGM5G6&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="100%" y1="0%" x2="0%" y2="0%" id="drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0">
            <stop offset="0%" stop-color="#dae8fc" stop-opacity="1" style="stop-color: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stop-opacity: 1;"/>
            <stop offset="100%" stop-color="#D5E8D4" stop-opacity="1" style="stop-color: light-dark(rgb(213, 232, 212), rgb(31, 47, 30)); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <g>
            <rect x="0.5" y="0" width="283.5" height="500" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 282px; height: 1px; padding-top: 7px; margin-left: 3px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <font style="font-size: 15px;">
                                        デプロイ前
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="3" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        デプロイ前
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="604" y="0" width="283.5" height="500" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 282px; height: 1px; padding-top: 7px; margin-left: 606px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <font style="font-size: 15px;">
                                        タスク切替後
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="606" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        タスク切替後
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1204" y="0" width="283.5" height="500" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 282px; height: 1px; padding-top: 7px; margin-left: 1206px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <font style="font-size: 15px;">
                                        タスク切替後
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1206" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        タスク切替後
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="14" y="418" width="110" height="80" fill="url(#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0)" stroke="none" pointer-events="all" style="fill: url(&quot;#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0&quot;);"/>
        </g>
        <g>
            <rect x="754" y="418" width="110" height="80" fill="url(#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0)" stroke="none" pointer-events="all" style="fill: url(&quot;#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0&quot;);"/>
        </g>
        <g>
            <rect x="1217.5" y="418" width="110" height="80" fill="url(#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0)" stroke="none" pointer-events="all" style="fill: url(&quot;#drawio-svg-Wq1lp93FAg3Lz59g8klS-gradient-light-dark_d5e8d4_1f2f1e_-1-light-dark_dae8fc_1d293b_-1-e-0&quot;);"/>
        </g>
        <g>
            <rect x="304" y="0" width="283.5" height="500" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 282px; height: 1px; padding-top: 7px; margin-left: 306px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <font style="font-size: 15px;">
                                        新タスクデプロイ（テスト）
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="306" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        新タスクデプロイ（テスト）
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="904" y="0" width="283.5" height="500" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 282px; height: 1px; padding-top: 7px; margin-left: 906px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <font style="font-size: 15px;">
                                        新タスクデプロイ（テスト）
                                    </font>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="906" y="19" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        新タスクデプロイ（テスト）
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1054" y="418" width="110" height="80" fill="#dae8fc" stroke="none" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59));"/>
        </g>
        <g>
            <rect x="917.5" y="418" width="110" height="80" fill="#d5e8d4" stroke="none" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30));"/>
        </g>
        <g>
            <rect x="454" y="418" width="110" height="80" fill="#d5e8d4" stroke="none" pointer-events="all" style="fill: light-dark(rgb(213, 232, 212), rgb(31, 47, 30));"/>
        </g>
        <g>
            <rect x="317.5" y="418" width="110" height="80" fill="#dae8fc" stroke="none" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59));"/>
        </g>
        <g>
            <path d="M 94.22 118 L 124.38 164.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 127.23 169.06 L 120.49 165.08 L 124.38 164.65 L 126.37 161.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="30" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 37.14 114.45 C 38.02 96.88 51.97 82.86 69 82.86 C 74.76 82.86 80.4 84.48 85.32 87.55 C 94.45 93.24 100.29 103.43 100.86 114.45 Z M 51.61 61.06 C 51.61 51.4 59.41 43.55 69 43.55 C 78.59 43.55 86.39 51.4 86.39 61.06 C 86.39 70.71 78.59 78.57 69 78.57 C 59.41 78.57 51.61 70.71 51.61 61.06 Z M 87.19 84.54 C 84.12 82.62 80.79 81.23 77.33 80.37 C 84.74 77.12 89.93 69.69 89.93 61.06 C 89.93 49.45 80.54 40 69 40 C 57.46 40 48.07 49.45 48.07 61.06 C 48.07 69.7 53.28 77.14 60.7 80.38 C 45.15 84.28 33.55 98.85 33.55 116.23 C 33.55 117.21 34.34 118 35.32 118 L 102.68 118 C 103.66 118 104.45 117.21 104.45 116.23 C 104.45 103.32 97.84 91.18 87.19 84.54 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 69px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    一般ユーザ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="69" y="137" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        一般ユーザ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 184.33 118 L 160.05 164.36" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 157.61 169.01 L 157.76 161.19 L 160.05 164.36 L 163.96 164.43 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="165.75" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 193.2 40 C 184.65 40 177.69 46.66 177.69 54.84 C 177.69 59.77 180.23 64.16 184.12 66.85 C 172.53 70.71 165.75 81.82 165.75 92.89 L 165.75 101.93 L 166.83 101.93 L 192.7 101.93 L 192.7 105.78 C 192.7 108.43 194.97 110.61 197.75 110.61 L 213.44 110.61 L 213.44 113.85 L 206.55 113.85 C 205.34 113.83 204.35 114.76 204.35 115.91 C 204.35 117.07 205.34 118 206.55 117.98 L 229.91 117.98 C 231.11 118 232.1 117.07 232.1 115.91 C 232.1 114.76 231.11 113.83 229.91 113.85 L 223.01 113.85 L 223.01 110.61 L 238.7 110.61 C 241.48 110.61 243.75 108.43 243.75 105.78 L 243.75 83.36 C 243.75 80.71 241.48 78.53 238.7 78.53 L 216.67 78.53 C 215.9 77.29 215.02 76.07 214.01 74.89 C 211.13 71.52 207.21 68.55 202.25 66.88 C 206.16 64.18 208.71 59.79 208.71 54.84 C 208.71 46.66 201.75 40 193.2 40 Z M 193.2 42.48 C 200.35 42.48 206.11 48 206.11 54.84 C 206.11 61.67 200.35 67.19 193.2 67.19 C 186.05 67.19 180.29 61.67 180.29 54.84 C 180.29 48 186.05 42.48 193.2 42.48 Z M 186.64 68.28 C 188.63 69.17 190.86 69.67 193.2 69.67 C 195.53 69.67 197.75 69.18 199.73 68.29 C 205.17 69.66 209.31 72.67 212.34 76.2 C 212.98 76.96 213.57 77.74 214.11 78.53 L 197.75 78.53 C 194.97 78.53 192.7 80.71 192.7 83.36 L 192.7 99.87 L 167.91 99.87 L 167.91 92.89 C 167.91 82.04 174.79 71.21 186.64 68.28 Z M 197.75 81.02 L 238.7 81.02 C 240.08 81.02 241.16 82.04 241.16 83.36 L 241.16 105.78 C 241.16 107.1 240.08 108.12 238.7 108.12 L 197.75 108.12 C 196.37 108.12 195.29 107.1 195.29 105.78 L 195.29 83.36 C 195.29 82.04 196.37 81.02 197.75 81.02 Z M 198.84 83.63 L 198.84 105.42 L 237.61 105.42 L 237.61 83.63 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 205px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    管理者
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="205" y="137" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        管理者
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="50.5" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 65.18 466.5 L 82.36 466.5 L 82.36 464.32 L 65.18 464.32 Z M 65.18 453.14 L 82.36 453.14 L 82.36 450.95 L 65.18 450.95 Z M 65.18 439.77 L 82.36 439.77 L 82.36 437.59 L 65.18 437.59 Z M 57.68 467.18 L 57.68 463.64 L 61.23 463.64 L 61.23 467.18 Z M 56.59 469.36 L 62.32 469.36 C 62.92 469.36 63.41 468.88 63.41 468.27 L 63.41 462.55 C 63.41 461.94 62.92 461.45 62.32 461.45 L 56.59 461.45 C 55.99 461.45 55.5 461.94 55.5 462.55 L 55.5 468.27 C 55.5 468.88 55.99 469.36 56.59 469.36 Z M 57.68 453.82 L 57.68 450.27 L 61.23 450.27 L 61.23 453.82 Z M 56.59 456 L 62.32 456 C 62.92 456 63.41 455.51 63.41 454.91 L 63.41 449.18 C 63.41 448.58 62.92 448.09 62.32 448.09 L 56.59 448.09 C 55.99 448.09 55.5 448.58 55.5 449.18 L 55.5 454.91 C 55.5 455.51 55.99 456 56.59 456 Z M 57.68 440.45 L 57.68 436.91 L 61.23 436.91 L 61.23 440.45 Z M 56.59 442.64 L 62.32 442.64 C 62.92 442.64 63.41 442.15 63.41 441.55 L 63.41 435.82 C 63.41 435.22 62.92 434.73 62.32 434.73 L 56.59 434.73 C 55.99 434.73 55.5 435.22 55.5 435.82 L 55.5 441.55 C 55.5 442.15 55.99 442.64 56.59 442.64 Z M 52.91 474.82 L 52.91 431.18 L 85.09 431.18 L 85.09 474.82 Z M 86.18 429 L 51.82 429 C 51.21 429 50.73 429.49 50.73 430.09 L 50.73 475.91 C 50.73 476.51 51.21 477 51.82 477 L 86.18 477 C 86.79 477 87.27 476.51 87.27 475.91 L 87.27 430.09 C 87.27 429.49 86.79 429 86.18 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 69px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="69" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 119 217 L 73.78 256.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 69.84 260.26 L 72.78 253.01 L 73.78 256.79 L 77.41 258.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 167.01 220 L 200.44 256.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 203.99 260.18 L 196.68 257.4 L 200.44 256.31 L 201.83 252.66 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="119" y="170" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 144 170 C 130.21 170 119 181.22 119 195 C 119 208.78 130.21 220 144 220 C 157.78 220 169 208.78 169 195 C 169 181.22 157.78 170 144 170 Z M 144 217.73 C 131.47 217.73 121.27 207.53 121.27 195 C 121.27 182.47 131.47 172.27 144 172.27 C 156.53 172.27 166.73 182.47 166.73 195 C 166.73 207.53 156.53 217.73 144 217.73 Z M 158.81 201.82 L 157.07 201.82 L 157.07 197.98 C 157.07 197.35 156.56 196.85 155.93 196.85 L 153.09 196.85 L 153.09 193.01 C 153.09 192.38 152.58 191.88 151.95 191.88 L 145.14 191.88 L 145.14 189.18 L 151.95 189.18 C 152.58 189.18 153.09 188.67 153.09 188.04 L 153.09 179.09 C 153.09 178.46 152.58 177.95 151.95 177.95 L 136.05 177.95 C 135.42 177.95 134.91 178.46 134.91 179.09 L 134.91 188.04 C 134.91 188.67 135.42 189.18 136.05 189.18 L 142.86 189.18 L 142.86 191.88 L 136.05 191.88 C 135.42 191.88 134.91 192.38 134.91 193.01 L 134.91 196.85 L 132.07 196.85 C 131.44 196.85 130.93 197.35 130.93 197.98 L 130.93 201.82 L 129.19 201.82 C 128.56 201.82 128.05 202.33 128.05 202.95 L 128.05 207.93 C 128.05 208.55 128.56 209.06 129.19 209.06 L 134.06 209.06 C 134.68 209.06 135.19 208.55 135.19 207.93 L 135.19 202.95 C 135.19 202.33 134.68 201.82 134.06 201.82 L 133.21 201.82 L 133.21 199.12 L 137.89 199.12 L 137.89 201.82 L 137.04 201.82 C 136.41 201.82 135.9 202.33 135.9 202.95 L 135.9 207.93 C 135.9 208.55 136.41 209.06 137.04 209.06 L 142.01 209.06 C 142.64 209.06 143.15 208.55 143.15 207.93 L 143.15 202.95 C 143.15 202.33 142.64 201.82 142.01 201.82 L 140.17 201.82 L 140.17 197.98 C 140.17 197.35 139.66 196.85 139.03 196.85 L 137.18 196.85 L 137.18 194.15 L 150.82 194.15 L 150.82 196.85 L 148.97 196.85 C 148.34 196.85 147.84 197.35 147.84 197.98 L 147.84 201.82 L 145.99 201.82 C 145.36 201.82 144.85 202.33 144.85 202.95 L 144.85 207.93 C 144.85 208.55 145.36 209.06 145.99 209.06 L 150.96 209.06 C 151.59 209.06 152.1 208.55 152.1 207.93 L 152.1 202.95 C 152.1 202.33 151.59 201.82 150.96 201.82 L 150.11 201.82 L 150.11 199.12 L 154.8 199.12 L 154.8 201.82 L 153.87 201.82 C 153.25 201.82 152.74 202.33 152.74 202.95 L 152.74 207.93 C 152.74 208.55 153.25 209.06 153.87 209.06 L 158.81 209.06 C 159.44 209.06 159.95 208.55 159.95 207.93 L 159.95 202.95 C 159.95 202.33 159.44 201.82 158.81 201.82 Z M 137.18 186.9 L 137.18 180.23 L 150.82 180.23 L 150.82 186.9 Z M 130.32 206.79 L 130.32 204.09 L 132.92 204.09 L 132.92 206.79 Z M 138.18 206.79 L 138.18 204.09 L 140.88 204.09 L 140.88 206.79 Z M 147.13 206.79 L 147.13 204.09 L 149.82 204.09 L 149.82 206.79 Z M 155.01 206.79 L 155.01 204.09 L 157.68 204.09 L 157.68 206.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 144px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="144" y="239" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 69 301 L 69 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 69 338.88 L 65.5 331.88 L 69 333.63 L 72.5 331.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="14" y="261" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 281px; margin-left: 15px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Blue用
                                    <br/>
                                    リスナー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="69" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Blue用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 204.75 301 L 204.8 320.5 L 69 320.5 L 69 333.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 69 338.88 L 65.5 331.88 L 69 333.63 L 72.5 331.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="149" y="261" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 281px; margin-left: 150px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Green用
                                    <div>
                                        リスナー
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="205" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Green用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 397.72 118 L 427.88 164.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 430.73 169.06 L 423.99 165.08 L 427.88 164.65 L 429.87 161.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="333.5" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 340.64 114.45 C 341.52 96.88 355.47 82.86 372.5 82.86 C 378.26 82.86 383.9 84.48 388.82 87.55 C 397.95 93.24 403.79 103.43 404.36 114.45 Z M 355.11 61.06 C 355.11 51.4 362.91 43.55 372.5 43.55 C 382.09 43.55 389.89 51.4 389.89 61.06 C 389.89 70.71 382.09 78.57 372.5 78.57 C 362.91 78.57 355.11 70.71 355.11 61.06 Z M 390.69 84.54 C 387.62 82.62 384.29 81.23 380.83 80.37 C 388.24 77.12 393.43 69.69 393.43 61.06 C 393.43 49.45 384.04 40 372.5 40 C 360.96 40 351.57 49.45 351.57 61.06 C 351.57 69.7 356.78 77.14 364.2 80.38 C 348.65 84.28 337.05 98.85 337.05 116.23 C 337.05 117.21 337.84 118 338.82 118 L 406.18 118 C 407.16 118 407.95 117.21 407.95 116.23 C 407.95 103.32 401.34 91.18 390.69 84.54 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 373px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    一般ユーザ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="373" y="137" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        一般ユーザ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 487.83 118 L 463.55 164.36" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 461.11 169.01 L 461.26 161.19 L 463.55 164.36 L 467.46 164.43 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="469.25" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 496.7 40 C 488.15 40 481.19 46.66 481.19 54.84 C 481.19 59.77 483.73 64.16 487.62 66.85 C 476.03 70.71 469.25 81.82 469.25 92.89 L 469.25 101.93 L 470.33 101.93 L 496.2 101.93 L 496.2 105.78 C 496.2 108.43 498.47 110.61 501.25 110.61 L 516.94 110.61 L 516.94 113.85 L 510.05 113.85 C 508.84 113.83 507.85 114.76 507.85 115.91 C 507.85 117.07 508.84 118 510.05 117.98 L 533.41 117.98 C 534.61 118 535.6 117.07 535.6 115.91 C 535.6 114.76 534.61 113.83 533.41 113.85 L 526.51 113.85 L 526.51 110.61 L 542.2 110.61 C 544.98 110.61 547.25 108.43 547.25 105.78 L 547.25 83.36 C 547.25 80.71 544.98 78.53 542.2 78.53 L 520.17 78.53 C 519.4 77.29 518.52 76.07 517.51 74.89 C 514.63 71.52 510.71 68.55 505.75 66.88 C 509.66 64.18 512.21 59.79 512.21 54.84 C 512.21 46.66 505.25 40 496.7 40 Z M 496.7 42.48 C 503.85 42.48 509.61 48 509.61 54.84 C 509.61 61.67 503.85 67.19 496.7 67.19 C 489.55 67.19 483.79 61.67 483.79 54.84 C 483.79 48 489.55 42.48 496.7 42.48 Z M 490.14 68.28 C 492.13 69.17 494.36 69.67 496.7 69.67 C 499.03 69.67 501.25 69.18 503.23 68.29 C 508.67 69.66 512.81 72.67 515.84 76.2 C 516.48 76.96 517.07 77.74 517.61 78.53 L 501.25 78.53 C 498.47 78.53 496.2 80.71 496.2 83.36 L 496.2 99.87 L 471.41 99.87 L 471.41 92.89 C 471.41 82.04 478.29 71.21 490.14 68.28 Z M 501.25 81.02 L 542.2 81.02 C 543.58 81.02 544.66 82.04 544.66 83.36 L 544.66 105.78 C 544.66 107.1 543.58 108.12 542.2 108.12 L 501.25 108.12 C 499.87 108.12 498.79 107.1 498.79 105.78 L 498.79 83.36 C 498.79 82.04 499.87 81.02 501.25 81.02 Z M 502.34 83.63 L 502.34 105.42 L 541.11 105.42 L 541.11 83.63 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 508px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    管理者
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="508" y="137" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        管理者
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="354" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 368.68 466.5 L 385.86 466.5 L 385.86 464.32 L 368.68 464.32 Z M 368.68 453.14 L 385.86 453.14 L 385.86 450.95 L 368.68 450.95 Z M 368.68 439.77 L 385.86 439.77 L 385.86 437.59 L 368.68 437.59 Z M 361.18 467.18 L 361.18 463.64 L 364.73 463.64 L 364.73 467.18 Z M 360.09 469.36 L 365.82 469.36 C 366.42 469.36 366.91 468.88 366.91 468.27 L 366.91 462.55 C 366.91 461.94 366.42 461.45 365.82 461.45 L 360.09 461.45 C 359.49 461.45 359 461.94 359 462.55 L 359 468.27 C 359 468.88 359.49 469.36 360.09 469.36 Z M 361.18 453.82 L 361.18 450.27 L 364.73 450.27 L 364.73 453.82 Z M 360.09 456 L 365.82 456 C 366.42 456 366.91 455.51 366.91 454.91 L 366.91 449.18 C 366.91 448.58 366.42 448.09 365.82 448.09 L 360.09 448.09 C 359.49 448.09 359 448.58 359 449.18 L 359 454.91 C 359 455.51 359.49 456 360.09 456 Z M 361.18 440.45 L 361.18 436.91 L 364.73 436.91 L 364.73 440.45 Z M 360.09 442.64 L 365.82 442.64 C 366.42 442.64 366.91 442.15 366.91 441.55 L 366.91 435.82 C 366.91 435.22 366.42 434.73 365.82 434.73 L 360.09 434.73 C 359.49 434.73 359 435.22 359 435.82 L 359 441.55 C 359 442.15 359.49 442.64 360.09 442.64 Z M 356.41 474.82 L 356.41 431.18 L 388.59 431.18 L 388.59 474.82 Z M 389.68 429 L 355.32 429 C 354.71 429 354.23 429.49 354.23 430.09 L 354.23 475.91 C 354.23 476.51 354.71 477 355.32 477 L 389.68 477 C 390.29 477 390.77 476.51 390.77 475.91 L 390.77 430.09 C 390.77 429.49 390.29 429 389.68 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 373px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    旧タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="373" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        旧タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 422.5 217 L 377.28 256.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 373.34 260.26 L 376.28 253.01 L 377.28 256.79 L 380.91 258.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 470.51 220 L 503.94 256.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 507.49 260.18 L 500.18 257.4 L 503.94 256.31 L 505.33 252.66 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="422.5" y="170" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 447.5 170 C 433.71 170 422.5 181.22 422.5 195 C 422.5 208.78 433.71 220 447.5 220 C 461.28 220 472.5 208.78 472.5 195 C 472.5 181.22 461.28 170 447.5 170 Z M 447.5 217.73 C 434.97 217.73 424.77 207.53 424.77 195 C 424.77 182.47 434.97 172.27 447.5 172.27 C 460.03 172.27 470.23 182.47 470.23 195 C 470.23 207.53 460.03 217.73 447.5 217.73 Z M 462.31 201.82 L 460.57 201.82 L 460.57 197.98 C 460.57 197.35 460.06 196.85 459.43 196.85 L 456.59 196.85 L 456.59 193.01 C 456.59 192.38 456.08 191.88 455.45 191.88 L 448.64 191.88 L 448.64 189.18 L 455.45 189.18 C 456.08 189.18 456.59 188.67 456.59 188.04 L 456.59 179.09 C 456.59 178.46 456.08 177.95 455.45 177.95 L 439.55 177.95 C 438.92 177.95 438.41 178.46 438.41 179.09 L 438.41 188.04 C 438.41 188.67 438.92 189.18 439.55 189.18 L 446.36 189.18 L 446.36 191.88 L 439.55 191.88 C 438.92 191.88 438.41 192.38 438.41 193.01 L 438.41 196.85 L 435.57 196.85 C 434.94 196.85 434.43 197.35 434.43 197.98 L 434.43 201.82 L 432.69 201.82 C 432.06 201.82 431.55 202.33 431.55 202.95 L 431.55 207.93 C 431.55 208.55 432.06 209.06 432.69 209.06 L 437.56 209.06 C 438.18 209.06 438.69 208.55 438.69 207.93 L 438.69 202.95 C 438.69 202.33 438.18 201.82 437.56 201.82 L 436.71 201.82 L 436.71 199.12 L 441.39 199.12 L 441.39 201.82 L 440.54 201.82 C 439.91 201.82 439.4 202.33 439.4 202.95 L 439.4 207.93 C 439.4 208.55 439.91 209.06 440.54 209.06 L 445.51 209.06 C 446.14 209.06 446.65 208.55 446.65 207.93 L 446.65 202.95 C 446.65 202.33 446.14 201.82 445.51 201.82 L 443.67 201.82 L 443.67 197.98 C 443.67 197.35 443.16 196.85 442.53 196.85 L 440.68 196.85 L 440.68 194.15 L 454.32 194.15 L 454.32 196.85 L 452.47 196.85 C 451.84 196.85 451.34 197.35 451.34 197.98 L 451.34 201.82 L 449.49 201.82 C 448.86 201.82 448.35 202.33 448.35 202.95 L 448.35 207.93 C 448.35 208.55 448.86 209.06 449.49 209.06 L 454.46 209.06 C 455.09 209.06 455.6 208.55 455.6 207.93 L 455.6 202.95 C 455.6 202.33 455.09 201.82 454.46 201.82 L 453.61 201.82 L 453.61 199.12 L 458.3 199.12 L 458.3 201.82 L 457.37 201.82 C 456.75 201.82 456.24 202.33 456.24 202.95 L 456.24 207.93 C 456.24 208.55 456.75 209.06 457.37 209.06 L 462.31 209.06 C 462.94 209.06 463.45 208.55 463.45 207.93 L 463.45 202.95 C 463.45 202.33 462.94 201.82 462.31 201.82 Z M 440.68 186.9 L 440.68 180.23 L 454.32 180.23 L 454.32 186.9 Z M 433.82 206.79 L 433.82 204.09 L 436.42 204.09 L 436.42 206.79 Z M 441.68 206.79 L 441.68 204.09 L 444.38 204.09 L 444.38 206.79 Z M 450.63 206.79 L 450.63 204.09 L 453.32 204.09 L 453.32 206.79 Z M 458.51 206.79 L 458.51 204.09 L 461.18 204.09 L 461.18 206.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 448px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="448" y="239" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 372.5 380 L 372.5 422.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 372.5 427.88 L 369 420.88 L 372.5 422.63 L 376 420.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 372.5 301 L 372.5 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 372.5 338.88 L 369 331.88 L 372.5 333.63 L 376 331.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="317.5" y="261" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 281px; margin-left: 319px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Blue用
                                    <br/>
                                    リスナー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="373" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Blue用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 508.25 380 L 508.25 422.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 508.25 427.88 L 504.75 420.88 L 508.25 422.63 L 511.75 420.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <path d="M 508.25 301 L 508.25 333.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 508.25 338.88 L 504.75 331.88 L 508.25 333.63 L 511.75 331.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="452.5" y="261" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 281px; margin-left: 454px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Green用
                                    <div>
                                        リスナー
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="508" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Green用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="489.75" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 504.43 466.5 L 521.61 466.5 L 521.61 464.32 L 504.43 464.32 Z M 504.43 453.14 L 521.61 453.14 L 521.61 450.95 L 504.43 450.95 Z M 504.43 439.77 L 521.61 439.77 L 521.61 437.59 L 504.43 437.59 Z M 496.93 467.18 L 496.93 463.64 L 500.48 463.64 L 500.48 467.18 Z M 495.84 469.36 L 501.57 469.36 C 502.17 469.36 502.66 468.88 502.66 468.27 L 502.66 462.55 C 502.66 461.94 502.17 461.45 501.57 461.45 L 495.84 461.45 C 495.24 461.45 494.75 461.94 494.75 462.55 L 494.75 468.27 C 494.75 468.88 495.24 469.36 495.84 469.36 Z M 496.93 453.82 L 496.93 450.27 L 500.48 450.27 L 500.48 453.82 Z M 495.84 456 L 501.57 456 C 502.17 456 502.66 455.51 502.66 454.91 L 502.66 449.18 C 502.66 448.58 502.17 448.09 501.57 448.09 L 495.84 448.09 C 495.24 448.09 494.75 448.58 494.75 449.18 L 494.75 454.91 C 494.75 455.51 495.24 456 495.84 456 Z M 496.93 440.45 L 496.93 436.91 L 500.48 436.91 L 500.48 440.45 Z M 495.84 442.64 L 501.57 442.64 C 502.17 442.64 502.66 442.15 502.66 441.55 L 502.66 435.82 C 502.66 435.22 502.17 434.73 501.57 434.73 L 495.84 434.73 C 495.24 434.73 494.75 435.22 494.75 435.82 L 494.75 441.55 C 494.75 442.15 495.24 442.64 495.84 442.64 Z M 492.16 474.82 L 492.16 431.18 L 524.34 431.18 L 524.34 474.82 Z M 525.43 429 L 491.07 429 C 490.46 429 489.98 429.49 489.98 430.09 L 489.98 475.91 C 489.98 476.51 490.46 477 491.07 477 L 525.43 477 C 526.04 477 526.52 476.51 526.52 475.91 L 526.52 430.09 C 526.52 429.49 526.04 429 525.43 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 508px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    新タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="508" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        新タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 697.72 118 L 727.88 164.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 730.73 169.06 L 723.99 165.08 L 727.88 164.65 L 729.87 161.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="633.5" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 640.64 114.45 C 641.52 96.88 655.47 82.86 672.5 82.86 C 678.26 82.86 683.9 84.48 688.82 87.55 C 697.95 93.24 703.79 103.43 704.36 114.45 Z M 655.11 61.06 C 655.11 51.4 662.91 43.55 672.5 43.55 C 682.09 43.55 689.89 51.4 689.89 61.06 C 689.89 70.71 682.09 78.57 672.5 78.57 C 662.91 78.57 655.11 70.71 655.11 61.06 Z M 690.69 84.54 C 687.62 82.62 684.29 81.23 680.83 80.37 C 688.24 77.12 693.43 69.69 693.43 61.06 C 693.43 49.45 684.04 40 672.5 40 C 660.96 40 651.57 49.45 651.57 61.06 C 651.57 69.7 656.78 77.14 664.2 80.38 C 648.65 84.28 637.05 98.85 637.05 116.23 C 637.05 117.21 637.84 118 638.82 118 L 706.18 118 C 707.16 118 707.95 117.21 707.95 116.23 C 707.95 103.32 701.34 91.18 690.69 84.54 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 673px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    一般ユーザ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="673" y="137" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        一般ユーザ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 787.83 118 L 763.55 164.36" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 761.11 169.01 L 761.26 161.19 L 763.55 164.36 L 767.46 164.43 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="769.25" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 796.7 40 C 788.15 40 781.19 46.66 781.19 54.84 C 781.19 59.77 783.73 64.16 787.62 66.85 C 776.03 70.71 769.25 81.82 769.25 92.89 L 769.25 101.93 L 770.33 101.93 L 796.2 101.93 L 796.2 105.78 C 796.2 108.43 798.47 110.61 801.25 110.61 L 816.94 110.61 L 816.94 113.85 L 810.05 113.85 C 808.84 113.83 807.85 114.76 807.85 115.91 C 807.85 117.07 808.84 118 810.05 117.98 L 833.41 117.98 C 834.61 118 835.6 117.07 835.6 115.91 C 835.6 114.76 834.61 113.83 833.41 113.85 L 826.51 113.85 L 826.51 110.61 L 842.2 110.61 C 844.98 110.61 847.25 108.43 847.25 105.78 L 847.25 83.36 C 847.25 80.71 844.98 78.53 842.2 78.53 L 820.17 78.53 C 819.4 77.29 818.52 76.07 817.51 74.89 C 814.63 71.52 810.71 68.55 805.75 66.88 C 809.66 64.18 812.21 59.79 812.21 54.84 C 812.21 46.66 805.25 40 796.7 40 Z M 796.7 42.48 C 803.85 42.48 809.61 48 809.61 54.84 C 809.61 61.67 803.85 67.19 796.7 67.19 C 789.55 67.19 783.79 61.67 783.79 54.84 C 783.79 48 789.55 42.48 796.7 42.48 Z M 790.14 68.28 C 792.13 69.17 794.36 69.67 796.7 69.67 C 799.03 69.67 801.25 69.18 803.23 68.29 C 808.67 69.66 812.81 72.67 815.84 76.2 C 816.48 76.96 817.07 77.74 817.61 78.53 L 801.25 78.53 C 798.47 78.53 796.2 80.71 796.2 83.36 L 796.2 99.87 L 771.41 99.87 L 771.41 92.89 C 771.41 82.04 778.29 71.21 790.14 68.28 Z M 801.25 81.02 L 842.2 81.02 C 843.58 81.02 844.66 82.04 844.66 83.36 L 844.66 105.78 C 844.66 107.1 843.58 108.12 842.2 108.12 L 801.25 108.12 C 799.87 108.12 798.79 107.1 798.79 105.78 L 798.79 83.36 C 798.79 82.04 799.87 81.02 801.25 81.02 Z M 802.34 83.63 L 802.34 105.42 L 841.11 105.42 L 841.11 83.63 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 808px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    管理者
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="808" y="137" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        管理者
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 722.5 217 L 677.28 256.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 673.34 260.26 L 676.28 253.01 L 677.28 256.79 L 680.91 258.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 770.51 220 L 803.94 256.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 807.49 260.18 L 800.18 257.4 L 803.94 256.31 L 805.33 252.66 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="722.5" y="170" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 747.5 170 C 733.71 170 722.5 181.22 722.5 195 C 722.5 208.78 733.71 220 747.5 220 C 761.28 220 772.5 208.78 772.5 195 C 772.5 181.22 761.28 170 747.5 170 Z M 747.5 217.73 C 734.97 217.73 724.77 207.53 724.77 195 C 724.77 182.47 734.97 172.27 747.5 172.27 C 760.03 172.27 770.23 182.47 770.23 195 C 770.23 207.53 760.03 217.73 747.5 217.73 Z M 762.31 201.82 L 760.57 201.82 L 760.57 197.98 C 760.57 197.35 760.06 196.85 759.43 196.85 L 756.59 196.85 L 756.59 193.01 C 756.59 192.38 756.08 191.88 755.45 191.88 L 748.64 191.88 L 748.64 189.18 L 755.45 189.18 C 756.08 189.18 756.59 188.67 756.59 188.04 L 756.59 179.09 C 756.59 178.46 756.08 177.95 755.45 177.95 L 739.55 177.95 C 738.92 177.95 738.41 178.46 738.41 179.09 L 738.41 188.04 C 738.41 188.67 738.92 189.18 739.55 189.18 L 746.36 189.18 L 746.36 191.88 L 739.55 191.88 C 738.92 191.88 738.41 192.38 738.41 193.01 L 738.41 196.85 L 735.57 196.85 C 734.94 196.85 734.43 197.35 734.43 197.98 L 734.43 201.82 L 732.69 201.82 C 732.06 201.82 731.55 202.33 731.55 202.95 L 731.55 207.93 C 731.55 208.55 732.06 209.06 732.69 209.06 L 737.56 209.06 C 738.18 209.06 738.69 208.55 738.69 207.93 L 738.69 202.95 C 738.69 202.33 738.18 201.82 737.56 201.82 L 736.71 201.82 L 736.71 199.12 L 741.39 199.12 L 741.39 201.82 L 740.54 201.82 C 739.91 201.82 739.4 202.33 739.4 202.95 L 739.4 207.93 C 739.4 208.55 739.91 209.06 740.54 209.06 L 745.51 209.06 C 746.14 209.06 746.65 208.55 746.65 207.93 L 746.65 202.95 C 746.65 202.33 746.14 201.82 745.51 201.82 L 743.67 201.82 L 743.67 197.98 C 743.67 197.35 743.16 196.85 742.53 196.85 L 740.68 196.85 L 740.68 194.15 L 754.32 194.15 L 754.32 196.85 L 752.47 196.85 C 751.84 196.85 751.34 197.35 751.34 197.98 L 751.34 201.82 L 749.49 201.82 C 748.86 201.82 748.35 202.33 748.35 202.95 L 748.35 207.93 C 748.35 208.55 748.86 209.06 749.49 209.06 L 754.46 209.06 C 755.09 209.06 755.6 208.55 755.6 207.93 L 755.6 202.95 C 755.6 202.33 755.09 201.82 754.46 201.82 L 753.61 201.82 L 753.61 199.12 L 758.3 199.12 L 758.3 201.82 L 757.37 201.82 C 756.75 201.82 756.24 202.33 756.24 202.95 L 756.24 207.93 C 756.24 208.55 756.75 209.06 757.37 209.06 L 762.31 209.06 C 762.94 209.06 763.45 208.55 763.45 207.93 L 763.45 202.95 C 763.45 202.33 762.94 201.82 762.31 201.82 Z M 740.68 186.9 L 740.68 180.23 L 754.32 180.23 L 754.32 186.9 Z M 733.82 206.79 L 733.82 204.09 L 736.42 204.09 L 736.42 206.79 Z M 741.68 206.79 L 741.68 204.09 L 744.38 204.09 L 744.38 206.79 Z M 750.63 206.79 L 750.63 204.09 L 753.32 204.09 L 753.32 206.79 Z M 758.51 206.79 L 758.51 204.09 L 761.18 204.09 L 761.18 206.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 748px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="748" y="239" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 672.5 301 L 672.5 320.5 L 808.3 320.5 L 808.27 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 808.25 338.88 L 804.77 331.87 L 808.27 333.63 L 811.77 331.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="617.5" y="261" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 281px; margin-left: 619px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Blue用
                                    <br/>
                                    リスナー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="673" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Blue用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 803.25 380.5 L 813.25 380.5 L 813.25 409.5 L 823.75 409.5 L 808.25 428.5 L 792.75 409.5 L 803.25 409.5 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/>
        </g>
        <g>
            <path d="M 808.25 301 L 808.25 333.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 808.25 338.88 L 804.75 331.88 L 808.25 333.63 L 811.75 331.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="752.5" y="261" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 281px; margin-left: 754px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Green用
                                    <div>
                                        リスナー
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="808" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Green用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="789.75" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 804.43 466.5 L 821.61 466.5 L 821.61 464.32 L 804.43 464.32 Z M 804.43 453.14 L 821.61 453.14 L 821.61 450.95 L 804.43 450.95 Z M 804.43 439.77 L 821.61 439.77 L 821.61 437.59 L 804.43 437.59 Z M 796.93 467.18 L 796.93 463.64 L 800.48 463.64 L 800.48 467.18 Z M 795.84 469.36 L 801.57 469.36 C 802.17 469.36 802.66 468.88 802.66 468.27 L 802.66 462.55 C 802.66 461.94 802.17 461.45 801.57 461.45 L 795.84 461.45 C 795.24 461.45 794.75 461.94 794.75 462.55 L 794.75 468.27 C 794.75 468.88 795.24 469.36 795.84 469.36 Z M 796.93 453.82 L 796.93 450.27 L 800.48 450.27 L 800.48 453.82 Z M 795.84 456 L 801.57 456 C 802.17 456 802.66 455.51 802.66 454.91 L 802.66 449.18 C 802.66 448.58 802.17 448.09 801.57 448.09 L 795.84 448.09 C 795.24 448.09 794.75 448.58 794.75 449.18 L 794.75 454.91 C 794.75 455.51 795.24 456 795.84 456 Z M 796.93 440.45 L 796.93 436.91 L 800.48 436.91 L 800.48 440.45 Z M 795.84 442.64 L 801.57 442.64 C 802.17 442.64 802.66 442.15 802.66 441.55 L 802.66 435.82 C 802.66 435.22 802.17 434.73 801.57 434.73 L 795.84 434.73 C 795.24 434.73 794.75 435.22 794.75 435.82 L 794.75 441.55 C 794.75 442.15 795.24 442.64 795.84 442.64 Z M 792.16 474.82 L 792.16 431.18 L 824.34 431.18 L 824.34 474.82 Z M 825.43 429 L 791.07 429 C 790.46 429 789.98 429.49 789.98 430.09 L 789.98 475.91 C 789.98 476.51 790.46 477 791.07 477 L 825.43 477 C 826.04 477 826.52 476.51 826.52 475.91 L 826.52 430.09 C 826.52 429.49 826.04 429 825.43 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 808px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="808" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 64 380.5 L 74 380.5 L 74 409.5 L 84.5 409.5 L 69 428.5 L 53.5 409.5 L 64 409.5 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/>
        </g>
        <g>
            <rect x="14" y="340" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 360px; margin-left: 15px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➀
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="69" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➀
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="149" y="340" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 360px; margin-left: 150px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➁
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="205" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➁
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="317.5" y="340" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 360px; margin-left: 319px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➀
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="373" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➀
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="452.5" y="340" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 360px; margin-left: 454px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➁
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="508" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➁
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="617.5" y="340" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 360px; margin-left: 619px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➀
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="673" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➀
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="752.5" y="340" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 360px; margin-left: 754px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➁
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="808" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➁
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 997.72 118 L 1027.88 164.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1030.73 169.06 L 1023.99 165.08 L 1027.88 164.65 L 1029.87 161.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="933.5" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 940.64 114.45 C 941.52 96.88 955.47 82.86 972.5 82.86 C 978.26 82.86 983.9 84.48 988.82 87.55 C 997.95 93.24 1003.79 103.43 1004.36 114.45 Z M 955.11 61.06 C 955.11 51.4 962.91 43.55 972.5 43.55 C 982.09 43.55 989.89 51.4 989.89 61.06 C 989.89 70.71 982.09 78.57 972.5 78.57 C 962.91 78.57 955.11 70.71 955.11 61.06 Z M 990.69 84.54 C 987.62 82.62 984.29 81.23 980.83 80.37 C 988.24 77.12 993.43 69.69 993.43 61.06 C 993.43 49.45 984.04 40 972.5 40 C 960.96 40 951.57 49.45 951.57 61.06 C 951.57 69.7 956.78 77.14 964.2 80.38 C 948.65 84.28 937.05 98.85 937.05 116.23 C 937.05 117.21 937.84 118 938.82 118 L 1006.18 118 C 1007.16 118 1007.95 117.21 1007.95 116.23 C 1007.95 103.32 1001.34 91.18 990.69 84.54 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 973px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    一般ユーザ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="973" y="137" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        一般ユーザ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1087.83 118 L 1063.55 164.36" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 1061.11 169.01 L 1061.26 161.19 L 1063.55 164.36 L 1067.46 164.43 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1069.25" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1096.7 40 C 1088.15 40 1081.19 46.66 1081.19 54.84 C 1081.19 59.77 1083.73 64.16 1087.62 66.85 C 1076.03 70.71 1069.25 81.82 1069.25 92.89 L 1069.25 101.93 L 1070.33 101.93 L 1096.2 101.93 L 1096.2 105.78 C 1096.2 108.43 1098.47 110.61 1101.25 110.61 L 1116.94 110.61 L 1116.94 113.85 L 1110.05 113.85 C 1108.84 113.83 1107.85 114.76 1107.85 115.91 C 1107.85 117.07 1108.84 118 1110.05 117.98 L 1133.41 117.98 C 1134.61 118 1135.6 117.07 1135.6 115.91 C 1135.6 114.76 1134.61 113.83 1133.41 113.85 L 1126.51 113.85 L 1126.51 110.61 L 1142.2 110.61 C 1144.98 110.61 1147.25 108.43 1147.25 105.78 L 1147.25 83.36 C 1147.25 80.71 1144.98 78.53 1142.2 78.53 L 1120.17 78.53 C 1119.4 77.29 1118.52 76.07 1117.51 74.89 C 1114.63 71.52 1110.71 68.55 1105.75 66.88 C 1109.66 64.18 1112.21 59.79 1112.21 54.84 C 1112.21 46.66 1105.25 40 1096.7 40 Z M 1096.7 42.48 C 1103.85 42.48 1109.61 48 1109.61 54.84 C 1109.61 61.67 1103.85 67.19 1096.7 67.19 C 1089.55 67.19 1083.79 61.67 1083.79 54.84 C 1083.79 48 1089.55 42.48 1096.7 42.48 Z M 1090.14 68.28 C 1092.13 69.17 1094.36 69.67 1096.7 69.67 C 1099.03 69.67 1101.25 69.18 1103.23 68.29 C 1108.67 69.66 1112.81 72.67 1115.84 76.2 C 1116.48 76.96 1117.07 77.74 1117.61 78.53 L 1101.25 78.53 C 1098.47 78.53 1096.2 80.71 1096.2 83.36 L 1096.2 99.87 L 1071.41 99.87 L 1071.41 92.89 C 1071.41 82.04 1078.29 71.21 1090.14 68.28 Z M 1101.25 81.02 L 1142.2 81.02 C 1143.58 81.02 1144.66 82.04 1144.66 83.36 L 1144.66 105.78 C 1144.66 107.1 1143.58 108.12 1142.2 108.12 L 1101.25 108.12 C 1099.87 108.12 1098.79 107.1 1098.79 105.78 L 1098.79 83.36 C 1098.79 82.04 1099.87 81.02 1101.25 81.02 Z M 1102.34 83.63 L 1102.34 105.42 L 1141.11 105.42 L 1141.11 83.63 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 1108px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    管理者
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1108" y="137" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        管理者
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="954" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 968.68 466.5 L 985.86 466.5 L 985.86 464.32 L 968.68 464.32 Z M 968.68 453.14 L 985.86 453.14 L 985.86 450.95 L 968.68 450.95 Z M 968.68 439.77 L 985.86 439.77 L 985.86 437.59 L 968.68 437.59 Z M 961.18 467.18 L 961.18 463.64 L 964.73 463.64 L 964.73 467.18 Z M 960.09 469.36 L 965.82 469.36 C 966.42 469.36 966.91 468.88 966.91 468.27 L 966.91 462.55 C 966.91 461.94 966.42 461.45 965.82 461.45 L 960.09 461.45 C 959.49 461.45 959 461.94 959 462.55 L 959 468.27 C 959 468.88 959.49 469.36 960.09 469.36 Z M 961.18 453.82 L 961.18 450.27 L 964.73 450.27 L 964.73 453.82 Z M 960.09 456 L 965.82 456 C 966.42 456 966.91 455.51 966.91 454.91 L 966.91 449.18 C 966.91 448.58 966.42 448.09 965.82 448.09 L 960.09 448.09 C 959.49 448.09 959 448.58 959 449.18 L 959 454.91 C 959 455.51 959.49 456 960.09 456 Z M 961.18 440.45 L 961.18 436.91 L 964.73 436.91 L 964.73 440.45 Z M 960.09 442.64 L 965.82 442.64 C 966.42 442.64 966.91 442.15 966.91 441.55 L 966.91 435.82 C 966.91 435.22 966.42 434.73 965.82 434.73 L 960.09 434.73 C 959.49 434.73 959 435.22 959 435.82 L 959 441.55 C 959 442.15 959.49 442.64 960.09 442.64 Z M 956.41 474.82 L 956.41 431.18 L 988.59 431.18 L 988.59 474.82 Z M 989.68 429 L 955.32 429 C 954.71 429 954.23 429.49 954.23 430.09 L 954.23 475.91 C 954.23 476.51 954.71 477 955.32 477 L 989.68 477 C 990.29 477 990.77 476.51 990.77 475.91 L 990.77 430.09 C 990.77 429.49 990.29 429 989.68 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 973px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    新タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="973" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        新タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1022.5 217 L 977.28 256.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 973.34 260.26 L 976.28 253.01 L 977.28 256.79 L 980.91 258.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1070.51 220 L 1103.94 256.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 1107.49 260.18 L 1100.18 257.4 L 1103.94 256.31 L 1105.33 252.66 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1022.5" y="170" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1047.5 170 C 1033.71 170 1022.5 181.22 1022.5 195 C 1022.5 208.78 1033.71 220 1047.5 220 C 1061.28 220 1072.5 208.78 1072.5 195 C 1072.5 181.22 1061.28 170 1047.5 170 Z M 1047.5 217.73 C 1034.97 217.73 1024.77 207.53 1024.77 195 C 1024.77 182.47 1034.97 172.27 1047.5 172.27 C 1060.03 172.27 1070.23 182.47 1070.23 195 C 1070.23 207.53 1060.03 217.73 1047.5 217.73 Z M 1062.31 201.82 L 1060.57 201.82 L 1060.57 197.98 C 1060.57 197.35 1060.06 196.85 1059.43 196.85 L 1056.59 196.85 L 1056.59 193.01 C 1056.59 192.38 1056.08 191.88 1055.45 191.88 L 1048.64 191.88 L 1048.64 189.18 L 1055.45 189.18 C 1056.08 189.18 1056.59 188.67 1056.59 188.04 L 1056.59 179.09 C 1056.59 178.46 1056.08 177.95 1055.45 177.95 L 1039.55 177.95 C 1038.92 177.95 1038.41 178.46 1038.41 179.09 L 1038.41 188.04 C 1038.41 188.67 1038.92 189.18 1039.55 189.18 L 1046.36 189.18 L 1046.36 191.88 L 1039.55 191.88 C 1038.92 191.88 1038.41 192.38 1038.41 193.01 L 1038.41 196.85 L 1035.57 196.85 C 1034.94 196.85 1034.43 197.35 1034.43 197.98 L 1034.43 201.82 L 1032.69 201.82 C 1032.06 201.82 1031.55 202.33 1031.55 202.95 L 1031.55 207.93 C 1031.55 208.55 1032.06 209.06 1032.69 209.06 L 1037.56 209.06 C 1038.18 209.06 1038.69 208.55 1038.69 207.93 L 1038.69 202.95 C 1038.69 202.33 1038.18 201.82 1037.56 201.82 L 1036.71 201.82 L 1036.71 199.12 L 1041.39 199.12 L 1041.39 201.82 L 1040.54 201.82 C 1039.91 201.82 1039.4 202.33 1039.4 202.95 L 1039.4 207.93 C 1039.4 208.55 1039.91 209.06 1040.54 209.06 L 1045.51 209.06 C 1046.14 209.06 1046.65 208.55 1046.65 207.93 L 1046.65 202.95 C 1046.65 202.33 1046.14 201.82 1045.51 201.82 L 1043.67 201.82 L 1043.67 197.98 C 1043.67 197.35 1043.16 196.85 1042.53 196.85 L 1040.68 196.85 L 1040.68 194.15 L 1054.32 194.15 L 1054.32 196.85 L 1052.47 196.85 C 1051.84 196.85 1051.34 197.35 1051.34 197.98 L 1051.34 201.82 L 1049.49 201.82 C 1048.86 201.82 1048.35 202.33 1048.35 202.95 L 1048.35 207.93 C 1048.35 208.55 1048.86 209.06 1049.49 209.06 L 1054.46 209.06 C 1055.09 209.06 1055.6 208.55 1055.6 207.93 L 1055.6 202.95 C 1055.6 202.33 1055.09 201.82 1054.46 201.82 L 1053.61 201.82 L 1053.61 199.12 L 1058.3 199.12 L 1058.3 201.82 L 1057.37 201.82 C 1056.75 201.82 1056.24 202.33 1056.24 202.95 L 1056.24 207.93 C 1056.24 208.55 1056.75 209.06 1057.37 209.06 L 1062.31 209.06 C 1062.94 209.06 1063.45 208.55 1063.45 207.93 L 1063.45 202.95 C 1063.45 202.33 1062.94 201.82 1062.31 201.82 Z M 1040.68 186.9 L 1040.68 180.23 L 1054.32 180.23 L 1054.32 186.9 Z M 1033.82 206.79 L 1033.82 204.09 L 1036.42 204.09 L 1036.42 206.79 Z M 1041.68 206.79 L 1041.68 204.09 L 1044.38 204.09 L 1044.38 206.79 Z M 1050.63 206.79 L 1050.63 204.09 L 1053.32 204.09 L 1053.32 206.79 Z M 1058.51 206.79 L 1058.51 204.09 L 1061.18 204.09 L 1061.18 206.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 1048px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1048" y="239" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 972.5 380 L 972.5 422.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 972.5 427.88 L 969 420.88 L 972.5 422.63 L 976 420.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <path d="M 972.5 301 L 1102.13 338.24" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1107.18 339.69 L 1099.48 341.12 L 1102.13 338.24 L 1101.41 334.39 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="917.5" y="261" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 281px; margin-left: 919px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Blue用
                                    <br/>
                                    リスナー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="973" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Blue用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1108.25 380 L 1108.25 422.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/>
            <path d="M 1108.25 427.88 L 1104.75 420.88 L 1108.25 422.63 L 1111.75 420.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(237, 237, 237)); stroke: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));"/>
        </g>
        <g>
            <path d="M 1108.25 301 L 978.62 338.24" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 973.57 339.69 L 979.34 334.39 L 978.62 338.24 L 981.27 341.12 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1052.5" y="261" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 281px; margin-left: 1054px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Green用
                                    <div>
                                        リスナー
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1108" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Green用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1089.75" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1104.43 466.5 L 1121.61 466.5 L 1121.61 464.32 L 1104.43 464.32 Z M 1104.43 453.14 L 1121.61 453.14 L 1121.61 450.95 L 1104.43 450.95 Z M 1104.43 439.77 L 1121.61 439.77 L 1121.61 437.59 L 1104.43 437.59 Z M 1096.93 467.18 L 1096.93 463.64 L 1100.48 463.64 L 1100.48 467.18 Z M 1095.84 469.36 L 1101.57 469.36 C 1102.17 469.36 1102.66 468.88 1102.66 468.27 L 1102.66 462.55 C 1102.66 461.94 1102.17 461.45 1101.57 461.45 L 1095.84 461.45 C 1095.24 461.45 1094.75 461.94 1094.75 462.55 L 1094.75 468.27 C 1094.75 468.88 1095.24 469.36 1095.84 469.36 Z M 1096.93 453.82 L 1096.93 450.27 L 1100.48 450.27 L 1100.48 453.82 Z M 1095.84 456 L 1101.57 456 C 1102.17 456 1102.66 455.51 1102.66 454.91 L 1102.66 449.18 C 1102.66 448.58 1102.17 448.09 1101.57 448.09 L 1095.84 448.09 C 1095.24 448.09 1094.75 448.58 1094.75 449.18 L 1094.75 454.91 C 1094.75 455.51 1095.24 456 1095.84 456 Z M 1096.93 440.45 L 1096.93 436.91 L 1100.48 436.91 L 1100.48 440.45 Z M 1095.84 442.64 L 1101.57 442.64 C 1102.17 442.64 1102.66 442.15 1102.66 441.55 L 1102.66 435.82 C 1102.66 435.22 1102.17 434.73 1101.57 434.73 L 1095.84 434.73 C 1095.24 434.73 1094.75 435.22 1094.75 435.82 L 1094.75 441.55 C 1094.75 442.15 1095.24 442.64 1095.84 442.64 Z M 1092.16 474.82 L 1092.16 431.18 L 1124.34 431.18 L 1124.34 474.82 Z M 1125.43 429 L 1091.07 429 C 1090.46 429 1089.98 429.49 1089.98 430.09 L 1089.98 475.91 C 1089.98 476.51 1090.46 477 1091.07 477 L 1125.43 477 C 1126.04 477 1126.52 476.51 1126.52 475.91 L 1126.52 430.09 C 1126.52 429.49 1126.04 429 1125.43 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 1108px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    旧タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1108" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        旧タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1297.72 118 L 1327.88 164.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1330.73 169.06 L 1323.99 165.08 L 1327.88 164.65 L 1329.87 161.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1233.5" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1240.64 114.45 C 1241.52 96.88 1255.47 82.86 1272.5 82.86 C 1278.26 82.86 1283.9 84.48 1288.82 87.55 C 1297.95 93.24 1303.79 103.43 1304.36 114.45 Z M 1255.11 61.06 C 1255.11 51.4 1262.91 43.55 1272.5 43.55 C 1282.09 43.55 1289.89 51.4 1289.89 61.06 C 1289.89 70.71 1282.09 78.57 1272.5 78.57 C 1262.91 78.57 1255.11 70.71 1255.11 61.06 Z M 1290.69 84.54 C 1287.62 82.62 1284.29 81.23 1280.83 80.37 C 1288.24 77.12 1293.43 69.69 1293.43 61.06 C 1293.43 49.45 1284.04 40 1272.5 40 C 1260.96 40 1251.57 49.45 1251.57 61.06 C 1251.57 69.7 1256.78 77.14 1264.2 80.38 C 1248.65 84.28 1237.05 98.85 1237.05 116.23 C 1237.05 117.21 1237.84 118 1238.82 118 L 1306.18 118 C 1307.16 118 1307.95 117.21 1307.95 116.23 C 1307.95 103.32 1301.34 91.18 1290.69 84.54 Z" fill="#232f3d" stroke="none" pointer-events="all" style="fill: light-dark(rgb(35, 47, 61), rgb(190, 200, 212));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 1273px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    一般ユーザ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1273" y="137" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        一般ユーザ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1387.83 118 L 1363.55 164.36" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 1361.11 169.01 L 1361.26 161.19 L 1363.55 164.36 L 1367.46 164.43 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1369.25" y="40" width="78" height="78" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1396.7 40 C 1388.15 40 1381.19 46.66 1381.19 54.84 C 1381.19 59.77 1383.73 64.16 1387.62 66.85 C 1376.03 70.71 1369.25 81.82 1369.25 92.89 L 1369.25 101.93 L 1370.33 101.93 L 1396.2 101.93 L 1396.2 105.78 C 1396.2 108.43 1398.47 110.61 1401.25 110.61 L 1416.94 110.61 L 1416.94 113.85 L 1410.05 113.85 C 1408.84 113.83 1407.85 114.76 1407.85 115.91 C 1407.85 117.07 1408.84 118 1410.05 117.98 L 1433.41 117.98 C 1434.61 118 1435.6 117.07 1435.6 115.91 C 1435.6 114.76 1434.61 113.83 1433.41 113.85 L 1426.51 113.85 L 1426.51 110.61 L 1442.2 110.61 C 1444.98 110.61 1447.25 108.43 1447.25 105.78 L 1447.25 83.36 C 1447.25 80.71 1444.98 78.53 1442.2 78.53 L 1420.17 78.53 C 1419.4 77.29 1418.52 76.07 1417.51 74.89 C 1414.63 71.52 1410.71 68.55 1405.75 66.88 C 1409.66 64.18 1412.21 59.79 1412.21 54.84 C 1412.21 46.66 1405.25 40 1396.7 40 Z M 1396.7 42.48 C 1403.85 42.48 1409.61 48 1409.61 54.84 C 1409.61 61.67 1403.85 67.19 1396.7 67.19 C 1389.55 67.19 1383.79 61.67 1383.79 54.84 C 1383.79 48 1389.55 42.48 1396.7 42.48 Z M 1390.14 68.28 C 1392.13 69.17 1394.36 69.67 1396.7 69.67 C 1399.03 69.67 1401.25 69.18 1403.23 68.29 C 1408.67 69.66 1412.81 72.67 1415.84 76.2 C 1416.48 76.96 1417.07 77.74 1417.61 78.53 L 1401.25 78.53 C 1398.47 78.53 1396.2 80.71 1396.2 83.36 L 1396.2 99.87 L 1371.41 99.87 L 1371.41 92.89 C 1371.41 82.04 1378.29 71.21 1390.14 68.28 Z M 1401.25 81.02 L 1442.2 81.02 C 1443.58 81.02 1444.66 82.04 1444.66 83.36 L 1444.66 105.78 C 1444.66 107.1 1443.58 108.12 1442.2 108.12 L 1401.25 108.12 C 1399.87 108.12 1398.79 107.1 1398.79 105.78 L 1398.79 83.36 C 1398.79 82.04 1399.87 81.02 1401.25 81.02 Z M 1402.34 83.63 L 1402.34 105.42 L 1441.11 105.42 L 1441.11 83.63 Z" fill="#005073" stroke="none" pointer-events="all" style="fill: light-dark(rgb(0, 80, 115), rgb(124, 193, 223));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 125px; margin-left: 1408px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    管理者
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1408" y="137" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        管理者
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1322.5 217 L 1277.28 256.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1273.34 260.26 L 1276.28 253.01 L 1277.28 256.79 L 1280.91 258.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1370.51 220 L 1403.94 256.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 1407.49 260.18 L 1400.18 257.4 L 1403.94 256.31 L 1405.33 252.66 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1322.5" y="170" width="50" height="50" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1347.5 170 C 1333.71 170 1322.5 181.22 1322.5 195 C 1322.5 208.78 1333.71 220 1347.5 220 C 1361.28 220 1372.5 208.78 1372.5 195 C 1372.5 181.22 1361.28 170 1347.5 170 Z M 1347.5 217.73 C 1334.97 217.73 1324.77 207.53 1324.77 195 C 1324.77 182.47 1334.97 172.27 1347.5 172.27 C 1360.03 172.27 1370.23 182.47 1370.23 195 C 1370.23 207.53 1360.03 217.73 1347.5 217.73 Z M 1362.31 201.82 L 1360.57 201.82 L 1360.57 197.98 C 1360.57 197.35 1360.06 196.85 1359.43 196.85 L 1356.59 196.85 L 1356.59 193.01 C 1356.59 192.38 1356.08 191.88 1355.45 191.88 L 1348.64 191.88 L 1348.64 189.18 L 1355.45 189.18 C 1356.08 189.18 1356.59 188.67 1356.59 188.04 L 1356.59 179.09 C 1356.59 178.46 1356.08 177.95 1355.45 177.95 L 1339.55 177.95 C 1338.92 177.95 1338.41 178.46 1338.41 179.09 L 1338.41 188.04 C 1338.41 188.67 1338.92 189.18 1339.55 189.18 L 1346.36 189.18 L 1346.36 191.88 L 1339.55 191.88 C 1338.92 191.88 1338.41 192.38 1338.41 193.01 L 1338.41 196.85 L 1335.57 196.85 C 1334.94 196.85 1334.43 197.35 1334.43 197.98 L 1334.43 201.82 L 1332.69 201.82 C 1332.06 201.82 1331.55 202.33 1331.55 202.95 L 1331.55 207.93 C 1331.55 208.55 1332.06 209.06 1332.69 209.06 L 1337.56 209.06 C 1338.18 209.06 1338.69 208.55 1338.69 207.93 L 1338.69 202.95 C 1338.69 202.33 1338.18 201.82 1337.56 201.82 L 1336.71 201.82 L 1336.71 199.12 L 1341.39 199.12 L 1341.39 201.82 L 1340.54 201.82 C 1339.91 201.82 1339.4 202.33 1339.4 202.95 L 1339.4 207.93 C 1339.4 208.55 1339.91 209.06 1340.54 209.06 L 1345.51 209.06 C 1346.14 209.06 1346.65 208.55 1346.65 207.93 L 1346.65 202.95 C 1346.65 202.33 1346.14 201.82 1345.51 201.82 L 1343.67 201.82 L 1343.67 197.98 C 1343.67 197.35 1343.16 196.85 1342.53 196.85 L 1340.68 196.85 L 1340.68 194.15 L 1354.32 194.15 L 1354.32 196.85 L 1352.47 196.85 C 1351.84 196.85 1351.34 197.35 1351.34 197.98 L 1351.34 201.82 L 1349.49 201.82 C 1348.86 201.82 1348.35 202.33 1348.35 202.95 L 1348.35 207.93 C 1348.35 208.55 1348.86 209.06 1349.49 209.06 L 1354.46 209.06 C 1355.09 209.06 1355.6 208.55 1355.6 207.93 L 1355.6 202.95 C 1355.6 202.33 1355.09 201.82 1354.46 201.82 L 1353.61 201.82 L 1353.61 199.12 L 1358.3 199.12 L 1358.3 201.82 L 1357.37 201.82 C 1356.75 201.82 1356.24 202.33 1356.24 202.95 L 1356.24 207.93 C 1356.24 208.55 1356.75 209.06 1357.37 209.06 L 1362.31 209.06 C 1362.94 209.06 1363.45 208.55 1363.45 207.93 L 1363.45 202.95 C 1363.45 202.33 1362.94 201.82 1362.31 201.82 Z M 1340.68 186.9 L 1340.68 180.23 L 1354.32 180.23 L 1354.32 186.9 Z M 1333.82 206.79 L 1333.82 204.09 L 1336.42 204.09 L 1336.42 206.79 Z M 1341.68 206.79 L 1341.68 204.09 L 1344.38 204.09 L 1344.38 206.79 Z M 1350.63 206.79 L 1350.63 204.09 L 1353.32 204.09 L 1353.32 206.79 Z M 1358.51 206.79 L 1358.51 204.09 L 1361.18 204.09 L 1361.18 206.79 Z" fill="#8c4fff" stroke="none" pointer-events="all" style="fill: light-dark(rgb(140, 79, 255), rgb(177, 125, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 227px; margin-left: 1348px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    ALB
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1348" y="239" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ALB
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1272.5 301 L 1272.5 333.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1272.5 338.88 L 1269 331.88 L 1272.5 333.63 L 1276 331.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1217.5" y="261" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 281px; margin-left: 1219px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Blue用
                                    <br/>
                                    リスナー
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1273" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Blue用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1408.25 301 L 1408.3 320.5 L 1272.5 320.5 L 1272.5 333.63" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
            <path d="M 1272.5 338.88 L 1269 331.88 L 1272.5 333.63 L 1276 331.88 Z" fill="#ff0000" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 144, 144)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 144, 144));"/>
        </g>
        <g>
            <rect x="1352.5" y="261" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 281px; margin-left: 1354px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Green用
                                    <div>
                                        リスナー
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1408" y="285" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Green用...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1254" y="429" width="37" height="48" fill="none" stroke="none" pointer-events="all"/>
            <path d="M 1268.68 466.5 L 1285.86 466.5 L 1285.86 464.32 L 1268.68 464.32 Z M 1268.68 453.14 L 1285.86 453.14 L 1285.86 450.95 L 1268.68 450.95 Z M 1268.68 439.77 L 1285.86 439.77 L 1285.86 437.59 L 1268.68 437.59 Z M 1261.18 467.18 L 1261.18 463.64 L 1264.73 463.64 L 1264.73 467.18 Z M 1260.09 469.36 L 1265.82 469.36 C 1266.42 469.36 1266.91 468.88 1266.91 468.27 L 1266.91 462.55 C 1266.91 461.94 1266.42 461.45 1265.82 461.45 L 1260.09 461.45 C 1259.49 461.45 1259 461.94 1259 462.55 L 1259 468.27 C 1259 468.88 1259.49 469.36 1260.09 469.36 Z M 1261.18 453.82 L 1261.18 450.27 L 1264.73 450.27 L 1264.73 453.82 Z M 1260.09 456 L 1265.82 456 C 1266.42 456 1266.91 455.51 1266.91 454.91 L 1266.91 449.18 C 1266.91 448.58 1266.42 448.09 1265.82 448.09 L 1260.09 448.09 C 1259.49 448.09 1259 448.58 1259 449.18 L 1259 454.91 C 1259 455.51 1259.49 456 1260.09 456 Z M 1261.18 440.45 L 1261.18 436.91 L 1264.73 436.91 L 1264.73 440.45 Z M 1260.09 442.64 L 1265.82 442.64 C 1266.42 442.64 1266.91 442.15 1266.91 441.55 L 1266.91 435.82 C 1266.91 435.22 1266.42 434.73 1265.82 434.73 L 1260.09 434.73 C 1259.49 434.73 1259 435.22 1259 435.82 L 1259 441.55 C 1259 442.15 1259.49 442.64 1260.09 442.64 Z M 1256.41 474.82 L 1256.41 431.18 L 1288.59 431.18 L 1288.59 474.82 Z M 1289.68 429 L 1255.32 429 C 1254.71 429 1254.23 429.49 1254.23 430.09 L 1254.23 475.91 C 1254.23 476.51 1254.71 477 1255.32 477 L 1289.68 477 C 1290.29 477 1290.77 476.51 1290.77 475.91 L 1290.77 430.09 C 1290.77 429.49 1290.29 429 1289.68 429 Z" fill="#ed7100" stroke="none" pointer-events="all" style="fill: light-dark(rgb(237, 113, 0), rgb(216, 109, 12));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 484px; margin-left: 1273px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #232F3E; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#232F3E, #bdc7d4); line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                    タスク
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1273" y="496" fill="#232F3E" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        タスク
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="917.5" y="340" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 360px; margin-left: 919px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➀
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="973" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➀
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1052.5" y="340" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 360px; margin-left: 1054px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➁
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1108" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➁
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1267.5 380.5 L 1277.5 380.5 L 1277.5 409.5 L 1288 409.5 L 1272.5 428.5 L 1257 409.5 L 1267.5 409.5 Z" fill="none" stroke="#000000" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1217.5" y="340" width="110" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 360px; margin-left: 1219px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➀
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1273" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➀
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1352.5" y="340" width="111.5" height="40" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 360px; margin-left: 1354px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    ターゲットグループ
                                    <div>
                                        ➁
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1408" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        ターゲットグループ
➁
                    </text>
                </switch>
            </g>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>