<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="881px" height="311px" viewBox="-0.5 -0.5 881 311" content="&lt;mxfile&gt;&lt;diagram id=&quot;nABLbFnIvHvZXe0GfxQu&quot; name=&quot;ページ1&quot;&gt;3Vlbc6IwFP41PG4HgiI+Vmq7L50668xu+5iBCJkGwobgZX/9JhAEDFarqN3tg02+nBPCd65Rw/bi9RODafRMA0QMYAZrw34wABiOXfEpgU0JWJbtlEjIcKCwGpjjP0iBpkJzHKCsJcgpJRynbdCnSYJ83sIgY3TVFltQ0n5qCkOkAXMfEh39hQMelagLRjX+HeEwUk8euna5EMNKVr1IFsGArhqQPTVsj1HKy1G89hCR3FW0lHqPe1a352Io4ccoyDWpsYQkV++mDsY<PERSON>csymicBkgqmYU9WEeZonkJfrq6EeQUW8ZiImSWGC5pwjxLKCl3bLP4Erh9MnXWJGEfrBqQO+oRojDjbCJF12/iV0wzUfNWwgKuwqME+cBQIldXD7dY1M2KgyOkmahZOXx9/viyCWR4B+LJ0SBx8q+zdH3uYkIq9hCZCaBLALCrUrX5YHJo7NDo6jVusRaPdA41DnbApMMaWcW96DEGOxOKEwcSPNCJRIEJQTRU3DebQGvPXxvhNEn43VLOHteK/mGzUZC+bGc2Zrx6rLMohC5GSGpSQPNCHjDNEIMfLds7oYk+pzigWB6lF6GKRiWfu0rt9wlGM6wEeQ5yc56Y9uOFgxwvdDi8EHV7YRyzbGiWly+EkFPCcM+GH4UajSLwZb/OQcUbf0U7AdsQwJDhMxNQXfCGBTyRPWJSTe7UQ4yCQj+kkvm2a6yTSLdbkvo8E0JExVQKwnpEIsQ/DnjIe0ZAmkExr9NwsIHhkm9fmpKElp7VaMav02sYXtc7zVK070mDNLDPQs4zzL2WZgWbWhcjmOUNy01vnGuDeMNlY444ewSFc9UpFk1q5kPM7l43fpG6cGpATFv9VtAxUtJQ7iUOUmymhT9bOviJg2+3tdIHbyDgqFhw9FqxR38FwXt+nu3tlF3BqFmsHxd4ic1RPfYBN92uRqfeEvXNY1QXzDlTF4K2xdKgw1JGw1TopLE41GLhIKbhnDG4aAqksDFlHpahuDsN2Gq3S6uMeecv8UF4MyhOcWnMczW8yHpqW5jfXLjdDcMNyU+37lcvNCU3ahUqN21FqbpYd3U5/PvMbhQv4sw2u2T51fGNwhD/7/n5/dh1jDPa48g+0xGilUf7fXP1G48N3v/GFrn6WfvdLGQ2+QMYeObe8IOhxX9By+8DfpeWqgd/dK+o1v6pQ9kuKmEjINBHrXoT8d42/LIKpHIroIzTnhzkUO2JxcBm3ldKshlrhHqAFzIuMktIMy2OAoqwNQE9JeKf5Gh9pitHnTSGm9Q8QZV9W/4pjT/8C&lt;/diagram&gt;&lt;diagram id=&quot;BOTwUq6DcCUt5vj739mR&quot; name=&quot;ページ3&quot;&gt;7VlNc5swEP01OjaDhPk6YuwmhyYzbTptkpsKMjAVyJXl2O6vrwDJgMGOk9gh7TQXa1cfoH3v7UoEmEG2vuR4nlyziFCAjGgNzAlACFoWkj+FZ6M80LQrT8zTSPlqx236myinobzLNCKL1kDBGBXpvO0MWZ6TULR8mHO2ag+bMdp+6hzHpOO4DTHter+nkUgqr4uc2n9F0jhRT7Zcs+rIsB6rNrJIcMRWDZc5BWbAGRNVK1sHhBbB02Gp5n3c07t9L05yccyE8Q//282nlCTu2v6a+xw+bOIPapVHTJdqv+plxUYHgLNlHpFiEQOY41WSCnI7x2HRu5KYS18iMiotKJszlouAUcbLuaZR/km/egzhgqz3vj/cRkXSibCMCL6RQ/SEkQqkYpKjzFWNimMpX9JAxNQAYMWEeLt0HS3ZUAF7TvDsTrBIJMmjTMZFwmKWYzqtvc1gyY3zzZ0KbGncF8aFpc3Jutk52ShrITj7SfrCTNapuNOry3ZjPWnVyxWGXq2Nb7WjYhuHYZK7ZksekgPhUUIQmMdEHAqj0Y87JxSL9LH9IqcH0fkP4ilARIOC6B0EMWf5LmoqxsYFspphPhjkGmo1S6MNX4z1AEi5QwJldupNhtP8dTXnFLXFadcWZFqd4gJRT3Gxz1Zb3Bcz2mkyGh7LaKfFaOO5jA6C0zPaPZLR5pCMdjuMnhEslpwUy743XkPYPTS9La+9ngOnTYU6OpbneM0p+9eyOBuP63zZcNlx+TtFwIPAN8HUBPLk7U1VYxyUDQTGrl5fvm71CDX1mfI6eZnfOSqfQ0Aa7aer92jQ6m10SLEQsQE7EL21fCy0Ix93aPnAvgvbX6GfFxxqh9UOOlY71qDaQb3aeeU1/gza2V7Dh9NO9/B5jHbCcL92XBt4aJ9AKiX5gdaWFplvtUUGC51VanP9Dm4SAdEGpy0LJbVZSumOC9M0zqUZSsSI9I8LPNMQU191ZGkUldfZPjb03ShfRQjXePL7jdf3+eZsdBh16DDnLHoHZcex313ZsfpjNXya2Y3Vm6aZUXB1dZON7Gvj8vEzfLgfY+tL3ydVlQBsnQA8nQB0SpA5YGoD1wW+HOMAzyqSxD+bCY74knuiTCDN+gN72df4N4U5/QM=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="50" width="880" height="260" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="490" y="70" width="160" height="230" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <path d="M 150 120 L 263.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 268.88 120 L 261.88 123.5 L 263.63 120 L 261.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 210px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ①Create Branch
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="210" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ①Create Branch
                </text>
            </switch>
        </g>
        <rect x="30" y="90" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 31px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                main
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    main
                </text>
            </switch>
        </g>
        <rect x="0" y="50" width="140" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 65px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Branching Strategy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="70" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Branching Strategy
                </text>
            </switch>
        </g>
        <path d="M 390 120 L 503.63 120" fill="none" stroke="#cc0000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 508.88 120 L 501.88 123.5 L 503.63 120 L 501.88 116.5 Z" fill="#cc0000" stroke="#cc0000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 450px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ②Merge
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="450" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ②Merge
                </text>
            </switch>
        </g>
        <rect x="270" y="90" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 271px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                feature/
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="330" y="124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    feature/
                </text>
            </switch>
        </g>
        <path d="M 630 120 L 743.63 120" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 748.88 120 L 741.88 123.5 L 743.63 120 L 741.88 116.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 690px;">
                        <div data-drawio-colors="color: #CC0000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(204, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <font color="#000000">
                                    ⑤Merge
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="690" y="123" fill="#CC0000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ⑤Merge
                </text>
            </switch>
        </g>
        <path d="M 570 150 L 570 223.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 570 228.88 L 566.5 221.88 L 570 223.63 L 573.5 221.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 190px; margin-left: 570px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ③Merge
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="570" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ③Merge
                </text>
            </switch>
        </g>
        <path d="M 540 150 L 540 190 L 90 190 L 90 156.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 90 151.12 L 93.5 158.12 L 90 156.37 L 86.5 158.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 190px; margin-left: 315px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Merge
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="315" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Merge
                </text>
            </switch>
        </g>
        <rect x="510" y="90" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 511px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                stg01
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="570" y="124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    stg01
                </text>
            </switch>
        </g>
        <path d="M 630 260 L 743.63 260" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 748.88 260 L 741.88 263.5 L 743.63 260 L 741.88 256.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 690px;">
                        <div data-drawio-colors="color: #CC0000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(204, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <font color="#000000">
                                    ⑤Merge
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="690" y="263" fill="#CC0000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ⑤Merge
                </text>
            </switch>
        </g>
        <rect x="510" y="230" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 511px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                stg02
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="570" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    stg02
                </text>
            </switch>
        </g>
        <rect x="780" y="50" width="90" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 65px; margin-left: 781px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font color="#cc0000">
                                    →
                                </font>
                                Review
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="825" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    →Review
                </text>
            </switch>
        </g>
        <rect x="750" y="90" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 120px; margin-left: 751px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                prod01
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="810" y="124" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    prod01
                </text>
            </switch>
        </g>
        <rect x="750" y="230" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 260px; margin-left: 751px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                prod02
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="810" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    prod02
                </text>
            </switch>
        </g>
        <path d="M 540 0 L 660 0 L 660 40 L 620 40 L 590.4 70 L 600 40 L 540 40 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 20px; margin-left: 541px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ④Operation Check
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="24" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ④Operation Check
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>