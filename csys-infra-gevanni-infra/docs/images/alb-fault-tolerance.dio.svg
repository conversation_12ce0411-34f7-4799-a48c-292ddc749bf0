<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1291px" height="741px" viewBox="-0.5 -0.5 1291 741" content="&lt;mxfile&gt;&lt;diagram id=&quot;_K1tkZvUuKXP9ynMXcLM&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <rect x="0" y="0" width="1290" height="740" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 97 201 L 617 201 L 617 621 L 97 621 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 107.59 207.65 C 107.53 207.65 107.48 207.65 107.42 207.65 L 107.42 207.65 C 106.11 207.68 105.03 208.24 104.14 209.25 C 104.13 209.25 104.13 209.25 104.13 209.25 C 103.2 210.36 102.87 211.52 102.96 212.73 C 101.81 213.06 101.12 213.92 100.76 214.74 C 100.75 214.75 100.75 214.76 100.74 214.78 C 100.33 216.05 100.68 217.36 101.24 218.16 C 101.25 218.17 101.25 218.17 101.26 218.18 C 101.94 219.05 102.97 219.53 104.02 219.53 L 115.17 219.53 C 116.19 219.53 117.07 219.16 117.8 218.37 C 118.25 217.94 118.49 217.29 118.58 216.59 C 118.67 215.9 118.61 215.16 118.32 214.55 C 118.31 214.54 118.31 214.53 118.31 214.52 C 117.8 213.62 116.95 212.81 115.76 212.64 C 115.74 211.79 115.28 210.99 114.68 210.56 C 114.67 210.55 114.66 210.55 114.65 210.54 C 114.01 210.18 113.4 210.14 112.91 210.3 C 112.6 210.4 112.36 210.56 112.14 210.74 C 111.51 209.36 110.43 208.18 108.81 207.79 C 108.81 207.79 108.81 207.79 108.81 207.79 C 108.38 207.7 107.97 207.65 107.59 207.65 Z M 107.43 208.38 C 107.8 208.38 108.2 208.43 108.64 208.53 C 110.16 208.89 111.15 210.07 111.66 211.48 C 111.71 211.6 111.81 211.69 111.94 211.72 C 112.07 211.74 112.2 211.7 112.29 211.61 C 112.54 211.34 112.83 211.11 113.14 211.01 C 113.44 210.91 113.78 210.92 114.26 211.18 C 114.67 211.49 115.11 212.31 115.03 212.9 C 115.01 213.01 115.05 213.12 115.12 213.2 C 115.19 213.28 115.29 213.33 115.39 213.33 C 116.46 213.34 117.16 214.02 117.64 214.88 C 117.85 215.3 117.91 215.92 117.84 216.5 C 117.76 217.07 117.53 217.59 117.28 217.83 C 117.27 217.84 117.27 217.85 117.26 217.85 C 116.65 218.53 116.03 218.78 115.17 218.78 L 104.02 218.78 C 103.2 218.78 102.39 218.41 101.85 217.73 C 101.44 217.13 101.14 216.02 101.46 215.02 C 101.79 214.27 102.36 213.55 103.41 213.36 C 103.6 213.32 103.74 213.14 103.71 212.94 C 103.56 211.79 103.8 210.81 104.7 209.74 C 105.49 208.85 106.33 208.39 107.43 208.38 Z M 109.2 211.7 C 108.77 211.7 108.4 211.93 108.13 212.21 C 107.85 212.5 107.64 212.85 107.64 213.25 L 107.64 213.71 L 107.14 213.71 C 107.04 213.71 106.94 213.75 106.87 213.82 C 106.8 213.89 106.76 213.98 106.76 214.08 L 106.76 216.7 C 106.76 216.8 106.8 216.89 106.87 216.96 C 106.94 217.03 107.04 217.07 107.14 217.07 L 111.16 217.07 C 111.26 217.07 111.35 217.03 111.42 216.96 C 111.49 216.89 111.53 216.8 111.53 216.7 L 111.53 214.08 C 111.53 213.98 111.49 213.89 111.42 213.82 C 111.35 213.75 111.26 213.71 111.16 213.71 L 110.68 213.71 L 110.68 213.25 C 110.68 212.84 110.47 212.47 110.21 212.2 C 109.94 211.92 109.61 211.7 109.2 211.7 Z M 109.2 212.45 C 109.29 212.45 109.5 212.54 109.67 212.72 C 109.83 212.89 109.93 213.11 109.93 213.25 L 109.93 213.71 L 108.39 213.71 L 108.39 213.25 C 108.39 213.15 108.49 212.91 108.66 212.74 C 108.83 212.56 109.06 212.45 109.2 212.45 Z M 107.51 214.46 L 110.78 214.46 L 110.78 216.32 L 107.51 216.32 Z M 97 226 L 97 201 L 122 201 L 122 226 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 488px; height: 1px; padding-top: 208px; margin-left: 129px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="129" y="220" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="136" y="241" width="442" height="170" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="356.5" y="258.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 146 271 L 346 271 L 346 401 L 146 401 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 146 271 L 171 271 L 171 296 L 146 296 Z M 158.52 274.21 C 157.4 274.21 156.31 274.63 155.49 275.39 C 154.67 276.11 154.2 277.15 154.2 278.24 L 154.2 280.78 L 151.89 280.78 C 151.8 280.78 151.7 280.82 151.64 280.89 C 151.57 280.95 151.54 281.04 151.54 281.13 L 151.54 292.43 C 151.54 292.63 151.7 292.79 151.89 292.79 L 165.11 292.79 C 165.3 292.79 165.46 292.63 165.46 292.43 L 165.46 281.15 C 165.47 281.06 165.43 280.97 165.36 280.9 C 165.3 280.83 165.21 280.79 165.11 280.79 L 162.81 280.79 L 162.81 278.29 C 162.8 277.21 162.35 276.18 161.56 275.44 C 160.74 274.65 159.65 274.22 158.52 274.21 Z M 158.51 274.93 C 159.46 274.92 160.37 275.28 161.06 275.93 C 161.72 276.54 162.1 277.4 162.1 278.29 L 162.1 280.79 L 154.88 280.79 L 154.89 278.26 C 154.9 277.36 155.28 276.51 155.95 275.91 C 156.65 275.27 157.57 274.92 158.51 274.93 Z M 152.24 281.5 L 164.76 281.5 L 164.75 292.07 L 152.24 292.07 Z M 158.51 283.74 C 157.48 283.73 156.61 284.51 156.51 285.53 C 156.42 286.56 157.13 287.48 158.14 287.66 L 158.14 290.44 L 158.86 290.44 L 158.86 287.66 C 159.79 287.49 160.47 286.67 160.48 285.72 C 160.48 284.63 159.6 283.75 158.51 283.74 Z M 158.39 284.45 C 158.43 284.45 158.47 284.45 158.51 284.46 C 158.84 284.46 159.16 284.59 159.4 284.83 C 159.64 285.07 159.77 285.39 159.76 285.72 C 159.77 286.06 159.64 286.38 159.4 286.61 C 159.16 286.85 158.84 286.98 158.51 286.98 C 158.04 287.02 157.6 286.8 157.34 286.42 C 157.08 286.03 157.06 285.53 157.28 285.12 C 157.5 284.71 157.93 284.46 158.39 284.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 278px; margin-left: 178px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="178" y="290" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="219" y="320" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 246 320 C 231.11 320 219 332.11 219 347 C 219 361.89 231.11 374 246 374 C 260.89 374 273 361.89 273 347 C 273 332.11 260.89 320 246 320 Z M 246 371.54 C 232.47 371.54 221.45 360.53 221.45 347 C 221.45 333.47 232.47 322.46 246 322.46 C 259.53 322.46 270.55 333.47 270.55 347 C 270.55 360.53 259.53 371.54 246 371.54 Z M 262 354.36 L 260.11 354.36 L 260.11 350.22 C 260.11 349.54 259.56 348.99 258.89 348.99 L 255.82 348.99 L 255.82 344.85 C 255.82 344.17 255.27 343.63 254.59 343.63 L 247.23 343.63 L 247.23 340.71 L 254.59 340.71 C 255.27 340.71 255.82 340.16 255.82 339.48 L 255.82 329.82 C 255.82 329.14 255.27 328.59 254.59 328.59 L 237.41 328.59 C 236.73 328.59 236.18 329.14 236.18 329.82 L 236.18 339.48 C 236.18 340.16 236.73 340.71 237.41 340.71 L 244.77 340.71 L 244.77 343.63 L 237.41 343.63 C 236.73 343.63 236.18 344.17 236.18 344.85 L 236.18 348.99 L 233.11 348.99 C 232.44 348.99 231.89 349.54 231.89 350.22 L 231.89 354.36 L 230 354.36 C 229.32 354.36 228.78 354.91 228.78 355.59 L 228.78 360.96 C 228.78 361.64 229.32 362.19 230 362.19 L 235.26 362.19 C 235.94 362.19 236.49 361.64 236.49 360.96 L 236.49 355.59 C 236.49 354.91 235.94 354.36 235.26 354.36 L 234.34 354.36 L 234.34 351.45 L 239.4 351.45 L 239.4 354.36 L 238.48 354.36 C 237.81 354.36 237.26 354.91 237.26 355.59 L 237.26 360.96 C 237.26 361.64 237.81 362.19 238.48 362.19 L 243.85 362.19 C 244.53 362.19 245.08 361.64 245.08 360.96 L 245.08 355.59 C 245.08 354.91 244.53 354.36 243.85 354.36 L 241.86 354.36 L 241.86 350.22 C 241.86 349.54 241.31 348.99 240.63 348.99 L 238.64 348.99 L 238.64 346.08 L 253.36 346.08 L 253.36 348.99 L 251.37 348.99 C 250.69 348.99 250.14 349.54 250.14 350.22 L 250.14 354.36 L 248.15 354.36 C 247.47 354.36 246.92 354.91 246.92 355.59 L 246.92 360.96 C 246.92 361.64 247.47 362.19 248.15 362.19 L 253.52 362.19 C 254.19 362.19 254.74 361.64 254.74 360.96 L 254.74 355.59 C 254.74 354.91 254.19 354.36 253.52 354.36 L 252.6 354.36 L 252.6 351.45 L 257.66 351.45 L 257.66 354.36 L 256.66 354.36 C 255.99 354.36 255.44 354.91 255.44 355.59 L 255.44 360.96 C 255.44 361.64 255.99 362.19 256.66 362.19 L 262 362.19 C 262.67 362.19 263.22 361.64 263.22 360.96 L 263.22 355.59 C 263.22 354.91 262.67 354.36 262 354.36 Z M 238.64 338.26 L 238.64 331.05 L 253.36 331.05 L 253.36 338.26 Z M 231.23 359.73 L 231.23 356.82 L 234.03 356.82 L 234.03 359.73 Z M 239.71 359.73 L 239.71 356.82 L 242.63 356.82 L 242.63 359.73 Z M 249.38 359.73 L 249.38 356.82 L 252.29 356.82 L 252.29 359.73 Z M 257.89 359.73 L 257.89 356.82 L 260.77 356.82 L 260.77 359.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 381px; margin-left: 246px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="246" y="393" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 226 343 L 242 343 L 242 327 L 250 327 L 250 343 L 266 343 L 266 351 L 250 351 L 250 367 L 242 367 L 242 351 L 226 351 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,246,347)" pointer-events="all"/>
        <path d="M 368 271 L 568 271 L 568 401 L 368 401 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 368 271 L 393 271 L 393 296 L 368 296 Z M 380.52 274.21 C 379.4 274.21 378.31 274.63 377.49 275.39 C 376.67 276.11 376.2 277.15 376.2 278.24 L 376.2 280.78 L 373.89 280.78 C 373.8 280.78 373.7 280.82 373.64 280.89 C 373.57 280.95 373.54 281.04 373.54 281.13 L 373.54 292.43 C 373.54 292.63 373.7 292.79 373.89 292.79 L 387.11 292.79 C 387.3 292.79 387.46 292.63 387.46 292.43 L 387.46 281.15 C 387.47 281.06 387.43 280.97 387.36 280.9 C 387.3 280.83 387.21 280.79 387.11 280.79 L 384.81 280.79 L 384.81 278.29 C 384.8 277.21 384.35 276.18 383.56 275.44 C 382.74 274.65 381.65 274.22 380.52 274.21 Z M 380.51 274.93 C 381.46 274.92 382.37 275.28 383.06 275.93 C 383.72 276.54 384.1 277.4 384.1 278.29 L 384.1 280.79 L 376.88 280.79 L 376.89 278.26 C 376.9 277.36 377.28 276.51 377.95 275.91 C 378.65 275.27 379.57 274.92 380.51 274.93 Z M 374.24 281.5 L 386.76 281.5 L 386.75 292.07 L 374.24 292.07 Z M 380.51 283.74 C 379.48 283.73 378.61 284.51 378.51 285.53 C 378.42 286.56 379.13 287.48 380.14 287.66 L 380.14 290.44 L 380.86 290.44 L 380.86 287.66 C 381.79 287.49 382.47 286.67 382.48 285.72 C 382.48 284.63 381.6 283.75 380.51 283.74 Z M 380.39 284.45 C 380.43 284.45 380.47 284.45 380.51 284.46 C 380.84 284.46 381.16 284.59 381.4 284.83 C 381.64 285.07 381.77 285.39 381.76 285.72 C 381.77 286.06 381.64 286.38 381.4 286.61 C 381.16 286.85 380.84 286.98 380.51 286.98 C 380.04 287.02 379.6 286.8 379.34 286.42 C 379.08 286.03 379.06 285.53 379.28 285.12 C 379.5 284.71 379.93 284.46 380.39 284.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 278px; margin-left: 400px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="290" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="444" y="331" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 490.91 331.09 L 445.09 331.09 C 444.49 331.09 444 331.58 444 332.18 L 444 360.82 C 444 361.42 444.49 361.91 445.09 361.91 L 490.91 361.91 C 491.51 361.91 492 361.42 492 360.82 L 492 332.18 C 492 331.58 491.51 331.09 490.91 331.09 Z M 446.18 359.73 L 446.18 333.27 L 489.82 333.27 L 489.82 359.73 Z M 449.73 357 L 451.91 357 L 451.91 336 L 449.73 336 Z M 455.45 357 L 457.64 357 L 457.64 336 L 455.45 336 Z M 461.18 357 L 463.36 357 L 463.36 336 L 461.18 336 Z M 466.91 357 L 469.09 357 L 469.09 336 L 466.91 336 Z M 472.63 357 L 474.82 357 L 474.82 336 L 472.63 336 Z M 478.36 357 L 480.55 357 L 480.55 336 L 478.36 336 Z M 484.09 357 L 486.27 357 L 486.27 336 L 484.09 336 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 369px; margin-left: 468px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="468" y="381" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <rect x="136" y="421" width="442" height="170" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="356.5" y="438.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 146 451 L 346 451 L 346 581 L 146 581 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 146 451 L 171 451 L 171 476 L 146 476 Z M 158.52 454.21 C 157.4 454.21 156.31 454.63 155.49 455.39 C 154.67 456.11 154.2 457.15 154.2 458.24 L 154.2 460.78 L 151.89 460.78 C 151.8 460.78 151.7 460.82 151.64 460.89 C 151.57 460.95 151.54 461.04 151.54 461.13 L 151.54 472.43 C 151.54 472.63 151.7 472.79 151.89 472.79 L 165.11 472.79 C 165.3 472.79 165.46 472.63 165.46 472.43 L 165.46 461.15 C 165.47 461.06 165.43 460.97 165.36 460.9 C 165.3 460.83 165.21 460.79 165.11 460.79 L 162.81 460.79 L 162.81 458.29 C 162.8 457.21 162.35 456.18 161.56 455.44 C 160.74 454.65 159.65 454.22 158.52 454.21 Z M 158.51 454.93 C 159.46 454.92 160.37 455.28 161.06 455.93 C 161.72 456.54 162.1 457.4 162.1 458.29 L 162.1 460.79 L 154.88 460.79 L 154.89 458.26 C 154.9 457.36 155.28 456.51 155.95 455.91 C 156.65 455.27 157.57 454.92 158.51 454.93 Z M 152.24 461.5 L 164.76 461.5 L 164.75 472.07 L 152.24 472.07 Z M 158.51 463.74 C 157.48 463.73 156.61 464.51 156.51 465.53 C 156.42 466.56 157.13 467.48 158.14 467.66 L 158.14 470.44 L 158.86 470.44 L 158.86 467.66 C 159.79 467.49 160.47 466.67 160.48 465.72 C 160.48 464.63 159.6 463.75 158.51 463.74 Z M 158.39 464.45 C 158.43 464.45 158.47 464.45 158.51 464.46 C 158.84 464.46 159.16 464.59 159.4 464.83 C 159.64 465.07 159.77 465.39 159.76 465.72 C 159.77 466.06 159.64 466.38 159.4 466.61 C 159.16 466.85 158.84 466.98 158.51 466.98 C 158.04 467.02 157.6 466.8 157.34 466.42 C 157.08 466.03 157.06 465.53 157.28 465.12 C 157.5 464.71 157.93 464.46 158.39 464.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 458px; margin-left: 178px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="178" y="470" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="219" y="501" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 246 501 C 231.11 501 219 513.11 219 528 C 219 542.89 231.11 555 246 555 C 260.89 555 273 542.89 273 528 C 273 513.11 260.89 501 246 501 Z M 246 552.54 C 232.47 552.54 221.45 541.53 221.45 528 C 221.45 514.47 232.47 503.46 246 503.46 C 259.53 503.46 270.55 514.47 270.55 528 C 270.55 541.53 259.53 552.54 246 552.54 Z M 262 535.36 L 260.11 535.36 L 260.11 531.22 C 260.11 530.54 259.56 529.99 258.89 529.99 L 255.82 529.99 L 255.82 525.85 C 255.82 525.17 255.27 524.63 254.59 524.63 L 247.23 524.63 L 247.23 521.71 L 254.59 521.71 C 255.27 521.71 255.82 521.16 255.82 520.48 L 255.82 510.82 C 255.82 510.14 255.27 509.59 254.59 509.59 L 237.41 509.59 C 236.73 509.59 236.18 510.14 236.18 510.82 L 236.18 520.48 C 236.18 521.16 236.73 521.71 237.41 521.71 L 244.77 521.71 L 244.77 524.63 L 237.41 524.63 C 236.73 524.63 236.18 525.17 236.18 525.85 L 236.18 529.99 L 233.11 529.99 C 232.44 529.99 231.89 530.54 231.89 531.22 L 231.89 535.36 L 230 535.36 C 229.32 535.36 228.78 535.91 228.78 536.59 L 228.78 541.96 C 228.78 542.64 229.32 543.19 230 543.19 L 235.26 543.19 C 235.94 543.19 236.49 542.64 236.49 541.96 L 236.49 536.59 C 236.49 535.91 235.94 535.36 235.26 535.36 L 234.34 535.36 L 234.34 532.45 L 239.4 532.45 L 239.4 535.36 L 238.48 535.36 C 237.81 535.36 237.26 535.91 237.26 536.59 L 237.26 541.96 C 237.26 542.64 237.81 543.19 238.48 543.19 L 243.85 543.19 C 244.53 543.19 245.08 542.64 245.08 541.96 L 245.08 536.59 C 245.08 535.91 244.53 535.36 243.85 535.36 L 241.86 535.36 L 241.86 531.22 C 241.86 530.54 241.31 529.99 240.63 529.99 L 238.64 529.99 L 238.64 527.08 L 253.36 527.08 L 253.36 529.99 L 251.37 529.99 C 250.69 529.99 250.14 530.54 250.14 531.22 L 250.14 535.36 L 248.15 535.36 C 247.47 535.36 246.92 535.91 246.92 536.59 L 246.92 541.96 C 246.92 542.64 247.47 543.19 248.15 543.19 L 253.52 543.19 C 254.19 543.19 254.74 542.64 254.74 541.96 L 254.74 536.59 C 254.74 535.91 254.19 535.36 253.52 535.36 L 252.6 535.36 L 252.6 532.45 L 257.66 532.45 L 257.66 535.36 L 256.66 535.36 C 255.99 535.36 255.44 535.91 255.44 536.59 L 255.44 541.96 C 255.44 542.64 255.99 543.19 256.66 543.19 L 262 543.19 C 262.67 543.19 263.22 542.64 263.22 541.96 L 263.22 536.59 C 263.22 535.91 262.67 535.36 262 535.36 Z M 238.64 519.26 L 238.64 512.05 L 253.36 512.05 L 253.36 519.26 Z M 231.23 540.73 L 231.23 537.82 L 234.03 537.82 L 234.03 540.73 Z M 239.71 540.73 L 239.71 537.82 L 242.63 537.82 L 242.63 540.73 Z M 249.38 540.73 L 249.38 537.82 L 252.29 537.82 L 252.29 540.73 Z M 257.89 540.73 L 257.89 537.82 L 260.77 537.82 L 260.77 540.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 562px; margin-left: 246px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="246" y="574" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 368 451 L 568 451 L 568 581 L 368 581 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 368 451 L 393 451 L 393 476 L 368 476 Z M 380.52 454.21 C 379.4 454.21 378.31 454.63 377.49 455.39 C 376.67 456.11 376.2 457.15 376.2 458.24 L 376.2 460.78 L 373.89 460.78 C 373.8 460.78 373.7 460.82 373.64 460.89 C 373.57 460.95 373.54 461.04 373.54 461.13 L 373.54 472.43 C 373.54 472.63 373.7 472.79 373.89 472.79 L 387.11 472.79 C 387.3 472.79 387.46 472.63 387.46 472.43 L 387.46 461.15 C 387.47 461.06 387.43 460.97 387.36 460.9 C 387.3 460.83 387.21 460.79 387.11 460.79 L 384.81 460.79 L 384.81 458.29 C 384.8 457.21 384.35 456.18 383.56 455.44 C 382.74 454.65 381.65 454.22 380.52 454.21 Z M 380.51 454.93 C 381.46 454.92 382.37 455.28 383.06 455.93 C 383.72 456.54 384.1 457.4 384.1 458.29 L 384.1 460.79 L 376.88 460.79 L 376.89 458.26 C 376.9 457.36 377.28 456.51 377.95 455.91 C 378.65 455.27 379.57 454.92 380.51 454.93 Z M 374.24 461.5 L 386.76 461.5 L 386.75 472.07 L 374.24 472.07 Z M 380.51 463.74 C 379.48 463.73 378.61 464.51 378.51 465.53 C 378.42 466.56 379.13 467.48 380.14 467.66 L 380.14 470.44 L 380.86 470.44 L 380.86 467.66 C 381.79 467.49 382.47 466.67 382.48 465.72 C 382.48 464.63 381.6 463.75 380.51 463.74 Z M 380.39 464.45 C 380.43 464.45 380.47 464.45 380.51 464.46 C 380.84 464.46 381.16 464.59 381.4 464.83 C 381.64 465.07 381.77 465.39 381.76 465.72 C 381.77 466.06 381.64 466.38 381.4 466.61 C 381.16 466.85 380.84 466.98 380.51 466.98 C 380.04 467.02 379.6 466.8 379.34 466.42 C 379.08 466.03 379.06 465.53 379.28 465.12 C 379.5 464.71 379.93 464.46 380.39 464.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 458px; margin-left: 400px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="470" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="444" y="511" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 490.91 511.09 L 445.09 511.09 C 444.49 511.09 444 511.58 444 512.18 L 444 540.82 C 444 541.42 444.49 541.91 445.09 541.91 L 490.91 541.91 C 491.51 541.91 492 541.42 492 540.82 L 492 512.18 C 492 511.58 491.51 511.09 490.91 511.09 Z M 446.18 539.73 L 446.18 513.27 L 489.82 513.27 L 489.82 539.73 Z M 449.73 537 L 451.91 537 L 451.91 516 L 449.73 516 Z M 455.45 537 L 457.64 537 L 457.64 516 L 455.45 516 Z M 461.18 537 L 463.36 537 L 463.36 516 L 461.18 516 Z M 466.91 537 L 469.09 537 L 469.09 516 L 466.91 516 Z M 472.63 537 L 474.82 537 L 474.82 516 L 472.63 516 Z M 478.36 537 L 480.55 537 L 480.55 516 L 478.36 516 Z M 484.09 537 L 486.27 537 L 486.27 516 L 484.09 516 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 549px; margin-left: 468px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="468" y="561" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <path d="M 273 346.94 L 435.76 346.57" fill="none" stroke="#e51400" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 441.76 346.56 L 433.77 350.58 L 435.76 346.57 L 433.76 342.58 Z" fill="#e51400" stroke="#e51400" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 272 527.94 L 436.63 527.57" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 441.88 527.56 L 434.89 531.07 L 436.63 527.57 L 434.87 524.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="13" y="397" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 40 397 C 25.11 397 13 409.11 13 424 C 13 438.89 25.11 451 40 451 C 54.89 451 67 438.89 67 424 C 67 409.11 54.89 397 40 397 Z M 40 448.54 C 26.47 448.54 15.45 437.53 15.45 424 C 15.45 410.47 26.47 399.46 40 399.46 C 53.53 399.46 64.55 410.47 64.55 424 C 64.55 437.53 53.53 448.54 40 448.54 Z M 56 431.36 L 54.11 431.36 L 54.11 427.22 C 54.11 426.54 53.56 425.99 52.89 425.99 L 49.82 425.99 L 49.82 421.85 C 49.82 421.17 49.27 420.63 48.59 420.63 L 41.23 420.63 L 41.23 417.71 L 48.59 417.71 C 49.27 417.71 49.82 417.16 49.82 416.48 L 49.82 406.82 C 49.82 406.14 49.27 405.59 48.59 405.59 L 31.41 405.59 C 30.73 405.59 30.18 406.14 30.18 406.82 L 30.18 416.48 C 30.18 417.16 30.73 417.71 31.41 417.71 L 38.77 417.71 L 38.77 420.63 L 31.41 420.63 C 30.73 420.63 30.18 421.17 30.18 421.85 L 30.18 425.99 L 27.11 425.99 C 26.44 425.99 25.89 426.54 25.89 427.22 L 25.89 431.36 L 24 431.36 C 23.32 431.36 22.78 431.91 22.78 432.59 L 22.78 437.96 C 22.78 438.64 23.32 439.19 24 439.19 L 29.26 439.19 C 29.94 439.19 30.49 438.64 30.49 437.96 L 30.49 432.59 C 30.49 431.91 29.94 431.36 29.26 431.36 L 28.34 431.36 L 28.34 428.45 L 33.4 428.45 L 33.4 431.36 L 32.48 431.36 C 31.81 431.36 31.26 431.91 31.26 432.59 L 31.26 437.96 C 31.26 438.64 31.81 439.19 32.48 439.19 L 37.85 439.19 C 38.53 439.19 39.08 438.64 39.08 437.96 L 39.08 432.59 C 39.08 431.91 38.53 431.36 37.85 431.36 L 35.86 431.36 L 35.86 427.22 C 35.86 426.54 35.31 425.99 34.63 425.99 L 32.64 425.99 L 32.64 423.08 L 47.36 423.08 L 47.36 425.99 L 45.37 425.99 C 44.69 425.99 44.14 426.54 44.14 427.22 L 44.14 431.36 L 42.15 431.36 C 41.47 431.36 40.92 431.91 40.92 432.59 L 40.92 437.96 C 40.92 438.64 41.47 439.19 42.15 439.19 L 47.52 439.19 C 48.19 439.19 48.74 438.64 48.74 437.96 L 48.74 432.59 C 48.74 431.91 48.19 431.36 47.52 431.36 L 46.6 431.36 L 46.6 428.45 L 51.66 428.45 L 51.66 431.36 L 50.66 431.36 C 49.99 431.36 49.44 431.91 49.44 432.59 L 49.44 437.96 C 49.44 438.64 49.99 439.19 50.66 439.19 L 56 439.19 C 56.67 439.19 57.22 438.64 57.22 437.96 L 57.22 432.59 C 57.22 431.91 56.67 431.36 56 431.36 Z M 32.64 415.26 L 32.64 408.05 L 47.36 408.05 L 47.36 415.26 Z M 25.23 436.73 L 25.23 433.82 L 28.03 433.82 L 28.03 436.73 Z M 33.71 436.73 L 33.71 433.82 L 36.63 433.82 L 36.63 436.73 Z M 43.38 436.73 L 43.38 433.82 L 46.29 433.82 L 46.29 436.73 Z M 51.89 436.73 L 51.89 433.82 L 54.77 433.82 L 54.77 436.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 458px; margin-left: 40px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="470" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 67 413.91 L 211.29 359.98" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 216.91 357.88 L 210.81 364.42 L 211.29 359.98 L 208.01 356.93 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 67 440.33 L 211.55 527.73" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 216.05 530.45 L 208.24 529.82 L 211.55 527.73 L 211.87 523.83 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 736 92 L 1256 92 L 1256 698 L 736 698 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 746.59 98.65 C 746.53 98.65 746.48 98.65 746.42 98.65 L 746.42 98.65 C 745.11 98.68 744.03 99.24 743.14 100.25 C 743.13 100.25 743.13 100.25 743.13 100.25 C 742.2 101.36 741.87 102.52 741.96 103.73 C 740.81 104.06 740.12 104.92 739.76 105.74 C 739.75 105.75 739.75 105.76 739.74 105.78 C 739.33 107.05 739.68 108.36 740.24 109.16 C 740.25 109.17 740.25 109.17 740.26 109.18 C 740.94 110.05 741.97 110.53 743.02 110.53 L 754.17 110.53 C 755.19 110.53 756.07 110.16 756.8 109.37 C 757.25 108.94 757.49 108.29 757.58 107.59 C 757.67 106.9 757.61 106.16 757.32 105.55 C 757.31 105.54 757.31 105.53 757.31 105.52 C 756.8 104.62 755.95 103.81 754.76 103.64 C 754.74 102.79 754.28 101.99 753.68 101.56 C 753.67 101.55 753.66 101.55 753.65 101.54 C 753.01 101.18 752.4 101.14 751.91 101.3 C 751.6 101.4 751.36 101.56 751.14 101.74 C 750.51 100.36 749.43 99.18 747.81 98.79 C 747.81 98.79 747.81 98.79 747.81 98.79 C 747.38 98.7 746.97 98.65 746.59 98.65 Z M 746.43 99.38 C 746.8 99.38 747.2 99.43 747.64 99.53 C 749.16 99.89 750.15 101.07 750.66 102.48 C 750.71 102.6 750.81 102.69 750.94 102.72 C 751.07 102.74 751.2 102.7 751.29 102.61 C 751.54 102.34 751.83 102.11 752.14 102.01 C 752.44 101.91 752.78 101.92 753.26 102.18 C 753.67 102.49 754.11 103.31 754.03 103.9 C 754.01 104.01 754.05 104.12 754.12 104.2 C 754.19 104.28 754.29 104.33 754.39 104.33 C 755.46 104.34 756.16 105.02 756.64 105.88 C 756.85 106.3 756.91 106.92 756.84 107.5 C 756.76 108.07 756.53 108.59 756.28 108.83 C 756.27 108.84 756.27 108.85 756.26 108.85 C 755.65 109.53 755.03 109.78 754.17 109.78 L 743.02 109.78 C 742.2 109.78 741.39 109.41 740.85 108.73 C 740.44 108.13 740.14 107.02 740.46 106.02 C 740.79 105.27 741.36 104.55 742.41 104.36 C 742.6 104.32 742.74 104.14 742.71 103.94 C 742.56 102.79 742.8 101.81 743.7 100.74 C 744.49 99.85 745.33 99.39 746.43 99.38 Z M 748.2 102.7 C 747.77 102.7 747.4 102.93 747.13 103.21 C 746.85 103.5 746.64 103.85 746.64 104.25 L 746.64 104.71 L 746.14 104.71 C 746.04 104.71 745.94 104.75 745.87 104.82 C 745.8 104.89 745.76 104.98 745.76 105.08 L 745.76 107.7 C 745.76 107.8 745.8 107.89 745.87 107.96 C 745.94 108.03 746.04 108.07 746.14 108.07 L 750.16 108.07 C 750.26 108.07 750.35 108.03 750.42 107.96 C 750.49 107.89 750.53 107.8 750.53 107.7 L 750.53 105.08 C 750.53 104.98 750.49 104.89 750.42 104.82 C 750.35 104.75 750.26 104.71 750.16 104.71 L 749.68 104.71 L 749.68 104.25 C 749.68 103.84 749.47 103.47 749.21 103.2 C 748.94 102.92 748.61 102.7 748.2 102.7 Z M 748.2 103.45 C 748.29 103.45 748.5 103.54 748.67 103.72 C 748.83 103.89 748.93 104.11 748.93 104.25 L 748.93 104.71 L 747.39 104.71 L 747.39 104.25 C 747.39 104.15 747.49 103.91 747.66 103.74 C 747.83 103.56 748.06 103.45 748.2 103.45 Z M 746.51 105.46 L 749.78 105.46 L 749.78 107.32 L 746.51 107.32 Z M 736 117 L 736 92 L 761 92 L 761 117 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 488px; height: 1px; padding-top: 99px; margin-left: 768px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <br/>
                                <span style="color: rgb(35, 47, 62); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    Container
                                </span>
                                <div>
                                    <br/>
                                </div>
                                <div>
                                    <br/>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="768" y="111" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    Container...
                </text>
            </switch>
        </g>
        <rect x="775" y="132" width="442" height="170" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="995.5" y="149.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 785 162 L 985 162 L 985 292 L 785 292 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 785 162 L 810 162 L 810 187 L 785 187 Z M 797.52 165.21 C 796.4 165.21 795.31 165.63 794.49 166.39 C 793.67 167.11 793.2 168.15 793.2 169.24 L 793.2 171.78 L 790.89 171.78 C 790.8 171.78 790.7 171.82 790.64 171.89 C 790.57 171.95 790.54 172.04 790.54 172.13 L 790.54 183.43 C 790.54 183.63 790.7 183.79 790.89 183.79 L 804.11 183.79 C 804.3 183.79 804.46 183.63 804.46 183.43 L 804.46 172.15 C 804.47 172.06 804.43 171.97 804.36 171.9 C 804.3 171.83 804.21 171.79 804.11 171.79 L 801.81 171.79 L 801.81 169.29 C 801.8 168.21 801.35 167.18 800.56 166.44 C 799.74 165.65 798.65 165.22 797.52 165.21 Z M 797.51 165.93 C 798.46 165.92 799.37 166.28 800.06 166.93 C 800.72 167.54 801.1 168.4 801.1 169.29 L 801.1 171.79 L 793.88 171.79 L 793.89 169.26 C 793.9 168.36 794.28 167.51 794.95 166.91 C 795.65 166.27 796.57 165.92 797.51 165.93 Z M 791.24 172.5 L 803.76 172.5 L 803.75 183.07 L 791.24 183.07 Z M 797.51 174.74 C 796.48 174.73 795.61 175.51 795.51 176.53 C 795.42 177.56 796.13 178.48 797.14 178.66 L 797.14 181.44 L 797.86 181.44 L 797.86 178.66 C 798.79 178.49 799.47 177.67 799.48 176.72 C 799.48 175.63 798.6 174.75 797.51 174.74 Z M 797.39 175.45 C 797.43 175.45 797.47 175.45 797.51 175.46 C 797.84 175.46 798.16 175.59 798.4 175.83 C 798.64 176.07 798.77 176.39 798.76 176.72 C 798.77 177.06 798.64 177.38 798.4 177.61 C 798.16 177.85 797.84 177.98 797.51 177.98 C 797.04 178.02 796.6 177.8 796.34 177.42 C 796.08 177.03 796.06 176.53 796.28 176.12 C 796.5 175.71 796.93 175.46 797.39 175.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 169px; margin-left: 817px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="817" y="181" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <path d="M 1007 162 L 1207 162 L 1207 292 L 1007 292 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1007 162 L 1032 162 L 1032 187 L 1007 187 Z M 1019.52 165.21 C 1018.4 165.21 1017.31 165.63 1016.49 166.39 C 1015.67 167.11 1015.2 168.15 1015.2 169.24 L 1015.2 171.78 L 1012.89 171.78 C 1012.8 171.78 1012.7 171.82 1012.64 171.89 C 1012.57 171.95 1012.54 172.04 1012.54 172.13 L 1012.54 183.43 C 1012.54 183.63 1012.7 183.79 1012.89 183.79 L 1026.11 183.79 C 1026.3 183.79 1026.46 183.63 1026.46 183.43 L 1026.46 172.15 C 1026.47 172.06 1026.43 171.97 1026.36 171.9 C 1026.3 171.83 1026.21 171.79 1026.11 171.79 L 1023.81 171.79 L 1023.81 169.29 C 1023.8 168.21 1023.35 167.18 1022.56 166.44 C 1021.74 165.65 1020.65 165.22 1019.52 165.21 Z M 1019.51 165.93 C 1020.46 165.92 1021.37 166.28 1022.06 166.93 C 1022.72 167.54 1023.1 168.4 1023.1 169.29 L 1023.1 171.79 L 1015.88 171.79 L 1015.89 169.26 C 1015.9 168.36 1016.28 167.51 1016.95 166.91 C 1017.65 166.27 1018.57 165.92 1019.51 165.93 Z M 1013.24 172.5 L 1025.76 172.5 L 1025.75 183.07 L 1013.24 183.07 Z M 1019.51 174.74 C 1018.48 174.73 1017.61 175.51 1017.51 176.53 C 1017.42 177.56 1018.13 178.48 1019.14 178.66 L 1019.14 181.44 L 1019.86 181.44 L 1019.86 178.66 C 1020.79 178.49 1021.47 177.67 1021.48 176.72 C 1021.48 175.63 1020.6 174.75 1019.51 174.74 Z M 1019.39 175.45 C 1019.43 175.45 1019.47 175.45 1019.51 175.46 C 1019.84 175.46 1020.16 175.59 1020.4 175.83 C 1020.64 176.07 1020.77 176.39 1020.76 176.72 C 1020.77 177.06 1020.64 177.38 1020.4 177.61 C 1020.16 177.85 1019.84 177.98 1019.51 177.98 C 1019.04 178.02 1018.6 177.8 1018.34 177.42 C 1018.08 177.03 1018.06 176.53 1018.28 176.12 C 1018.5 175.71 1018.93 175.46 1019.39 175.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 169px; margin-left: 1039px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1039" y="181" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="775" y="312" width="442" height="170" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="995.5" y="329.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 785 342 L 985 342 L 985 472 L 785 472 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 785 342 L 810 342 L 810 367 L 785 367 Z M 797.52 345.21 C 796.4 345.21 795.31 345.63 794.49 346.39 C 793.67 347.11 793.2 348.15 793.2 349.24 L 793.2 351.78 L 790.89 351.78 C 790.8 351.78 790.7 351.82 790.64 351.89 C 790.57 351.95 790.54 352.04 790.54 352.13 L 790.54 363.43 C 790.54 363.63 790.7 363.79 790.89 363.79 L 804.11 363.79 C 804.3 363.79 804.46 363.63 804.46 363.43 L 804.46 352.15 C 804.47 352.06 804.43 351.97 804.36 351.9 C 804.3 351.83 804.21 351.79 804.11 351.79 L 801.81 351.79 L 801.81 349.29 C 801.8 348.21 801.35 347.18 800.56 346.44 C 799.74 345.65 798.65 345.22 797.52 345.21 Z M 797.51 345.93 C 798.46 345.92 799.37 346.28 800.06 346.93 C 800.72 347.54 801.1 348.4 801.1 349.29 L 801.1 351.79 L 793.88 351.79 L 793.89 349.26 C 793.9 348.36 794.28 347.51 794.95 346.91 C 795.65 346.27 796.57 345.92 797.51 345.93 Z M 791.24 352.5 L 803.76 352.5 L 803.75 363.07 L 791.24 363.07 Z M 797.51 354.74 C 796.48 354.73 795.61 355.51 795.51 356.53 C 795.42 357.56 796.13 358.48 797.14 358.66 L 797.14 361.44 L 797.86 361.44 L 797.86 358.66 C 798.79 358.49 799.47 357.67 799.48 356.72 C 799.48 355.63 798.6 354.75 797.51 354.74 Z M 797.39 355.45 C 797.43 355.45 797.47 355.45 797.51 355.46 C 797.84 355.46 798.16 355.59 798.4 355.83 C 798.64 356.07 798.77 356.39 798.76 356.72 C 798.77 357.06 798.64 357.38 798.4 357.61 C 798.16 357.85 797.84 357.98 797.51 357.98 C 797.04 358.02 796.6 357.8 796.34 357.42 C 796.08 357.03 796.06 356.53 796.28 356.12 C 796.5 355.71 796.93 355.46 797.39 355.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 349px; margin-left: 817px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="817" y="361" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <path d="M 1007 342 L 1207 342 L 1207 472 L 1007 472 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1007 342 L 1032 342 L 1032 367 L 1007 367 Z M 1019.52 345.21 C 1018.4 345.21 1017.31 345.63 1016.49 346.39 C 1015.67 347.11 1015.2 348.15 1015.2 349.24 L 1015.2 351.78 L 1012.89 351.78 C 1012.8 351.78 1012.7 351.82 1012.64 351.89 C 1012.57 351.95 1012.54 352.04 1012.54 352.13 L 1012.54 363.43 C 1012.54 363.63 1012.7 363.79 1012.89 363.79 L 1026.11 363.79 C 1026.3 363.79 1026.46 363.63 1026.46 363.43 L 1026.46 352.15 C 1026.47 352.06 1026.43 351.97 1026.36 351.9 C 1026.3 351.83 1026.21 351.79 1026.11 351.79 L 1023.81 351.79 L 1023.81 349.29 C 1023.8 348.21 1023.35 347.18 1022.56 346.44 C 1021.74 345.65 1020.65 345.22 1019.52 345.21 Z M 1019.51 345.93 C 1020.46 345.92 1021.37 346.28 1022.06 346.93 C 1022.72 347.54 1023.1 348.4 1023.1 349.29 L 1023.1 351.79 L 1015.88 351.79 L 1015.89 349.26 C 1015.9 348.36 1016.28 347.51 1016.95 346.91 C 1017.65 346.27 1018.57 345.92 1019.51 345.93 Z M 1013.24 352.5 L 1025.76 352.5 L 1025.75 363.07 L 1013.24 363.07 Z M 1019.51 354.74 C 1018.48 354.73 1017.61 355.51 1017.51 356.53 C 1017.42 357.56 1018.13 358.48 1019.14 358.66 L 1019.14 361.44 L 1019.86 361.44 L 1019.86 358.66 C 1020.79 358.49 1021.47 357.67 1021.48 356.72 C 1021.48 355.63 1020.6 354.75 1019.51 354.74 Z M 1019.39 355.45 C 1019.43 355.45 1019.47 355.45 1019.51 355.46 C 1019.84 355.46 1020.16 355.59 1020.4 355.83 C 1020.64 356.07 1020.77 356.39 1020.76 356.72 C 1020.77 357.06 1020.64 357.38 1020.4 357.61 C 1020.16 357.85 1019.84 357.98 1019.51 357.98 C 1019.04 358.02 1018.6 357.8 1018.34 357.42 C 1018.08 357.03 1018.06 356.53 1018.28 356.12 C 1018.5 355.71 1018.93 355.46 1019.39 355.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 349px; margin-left: 1039px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1039" y="361" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="775" y="498" width="442" height="170" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="995.5" y="515.5">
                Availability Zone 1d
            </text>
        </g>
        <path d="M 785 528 L 985 528 L 985 658 L 785 658 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 785 528 L 810 528 L 810 553 L 785 553 Z M 797.52 531.21 C 796.4 531.21 795.31 531.63 794.49 532.39 C 793.67 533.11 793.2 534.15 793.2 535.24 L 793.2 537.78 L 790.89 537.78 C 790.8 537.78 790.7 537.82 790.64 537.89 C 790.57 537.95 790.54 538.04 790.54 538.13 L 790.54 549.43 C 790.54 549.63 790.7 549.79 790.89 549.79 L 804.11 549.79 C 804.3 549.79 804.46 549.63 804.46 549.43 L 804.46 538.15 C 804.47 538.06 804.43 537.97 804.36 537.9 C 804.3 537.83 804.21 537.79 804.11 537.79 L 801.81 537.79 L 801.81 535.29 C 801.8 534.21 801.35 533.18 800.56 532.44 C 799.74 531.65 798.65 531.22 797.52 531.21 Z M 797.51 531.93 C 798.46 531.92 799.37 532.28 800.06 532.93 C 800.72 533.54 801.1 534.4 801.1 535.29 L 801.1 537.79 L 793.88 537.79 L 793.89 535.26 C 793.9 534.36 794.28 533.51 794.95 532.91 C 795.65 532.27 796.57 531.92 797.51 531.93 Z M 791.24 538.5 L 803.76 538.5 L 803.75 549.07 L 791.24 549.07 Z M 797.51 540.74 C 796.48 540.73 795.61 541.51 795.51 542.53 C 795.42 543.56 796.13 544.48 797.14 544.66 L 797.14 547.44 L 797.86 547.44 L 797.86 544.66 C 798.79 544.49 799.47 543.67 799.48 542.72 C 799.48 541.63 798.6 540.75 797.51 540.74 Z M 797.39 541.45 C 797.43 541.45 797.47 541.45 797.51 541.46 C 797.84 541.46 798.16 541.59 798.4 541.83 C 798.64 542.07 798.77 542.39 798.76 542.72 C 798.77 543.06 798.64 543.38 798.4 543.61 C 798.16 543.85 797.84 543.98 797.51 543.98 C 797.04 544.02 796.6 543.8 796.34 543.42 C 796.08 543.03 796.06 542.53 796.28 542.12 C 796.5 541.71 796.93 541.46 797.39 541.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 535px; margin-left: 817px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="817" y="547" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <path d="M 1007 528 L 1207 528 L 1207 658 L 1007 658 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 1007 528 L 1032 528 L 1032 553 L 1007 553 Z M 1019.52 531.21 C 1018.4 531.21 1017.31 531.63 1016.49 532.39 C 1015.67 533.11 1015.2 534.15 1015.2 535.24 L 1015.2 537.78 L 1012.89 537.78 C 1012.8 537.78 1012.7 537.82 1012.64 537.89 C 1012.57 537.95 1012.54 538.04 1012.54 538.13 L 1012.54 549.43 C 1012.54 549.63 1012.7 549.79 1012.89 549.79 L 1026.11 549.79 C 1026.3 549.79 1026.46 549.63 1026.46 549.43 L 1026.46 538.15 C 1026.47 538.06 1026.43 537.97 1026.36 537.9 C 1026.3 537.83 1026.21 537.79 1026.11 537.79 L 1023.81 537.79 L 1023.81 535.29 C 1023.8 534.21 1023.35 533.18 1022.56 532.44 C 1021.74 531.65 1020.65 531.22 1019.52 531.21 Z M 1019.51 531.93 C 1020.46 531.92 1021.37 532.28 1022.06 532.93 C 1022.72 533.54 1023.1 534.4 1023.1 535.29 L 1023.1 537.79 L 1015.88 537.79 L 1015.89 535.26 C 1015.9 534.36 1016.28 533.51 1016.95 532.91 C 1017.65 532.27 1018.57 531.92 1019.51 531.93 Z M 1013.24 538.5 L 1025.76 538.5 L 1025.75 549.07 L 1013.24 549.07 Z M 1019.51 540.74 C 1018.48 540.73 1017.61 541.51 1017.51 542.53 C 1017.42 543.56 1018.13 544.48 1019.14 544.66 L 1019.14 547.44 L 1019.86 547.44 L 1019.86 544.66 C 1020.79 544.49 1021.47 543.67 1021.48 542.72 C 1021.48 541.63 1020.6 540.75 1019.51 540.74 Z M 1019.39 541.45 C 1019.43 541.45 1019.47 541.45 1019.51 541.46 C 1019.84 541.46 1020.16 541.59 1020.4 541.83 C 1020.64 542.07 1020.77 542.39 1020.76 542.72 C 1020.77 543.06 1020.64 543.38 1020.4 543.61 C 1020.16 543.85 1019.84 543.98 1019.51 543.98 C 1019.04 544.02 1018.6 543.8 1018.34 543.42 C 1018.08 543.03 1018.06 542.53 1018.28 542.12 C 1018.5 541.71 1018.93 541.46 1019.39 541.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 535px; margin-left: 1039px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1039" y="547" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="1083" y="578" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1129.91 578.09 L 1084.09 578.09 C 1083.49 578.09 1083 578.58 1083 579.18 L 1083 607.82 C 1083 608.42 1083.49 608.91 1084.09 608.91 L 1129.91 608.91 C 1130.51 608.91 1131 608.42 1131 607.82 L 1131 579.18 C 1131 578.58 1130.51 578.09 1129.91 578.09 Z M 1085.18 606.73 L 1085.18 580.27 L 1128.82 580.27 L 1128.82 606.73 Z M 1088.73 604 L 1090.91 604 L 1090.91 583 L 1088.73 583 Z M 1094.45 604 L 1096.64 604 L 1096.64 583 L 1094.45 583 Z M 1100.18 604 L 1102.36 604 L 1102.36 583 L 1100.18 583 Z M 1105.91 604 L 1108.09 604 L 1108.09 583 L 1105.91 583 Z M 1111.63 604 L 1113.82 604 L 1113.82 583 L 1111.63 583 Z M 1117.36 604 L 1119.55 604 L 1119.55 583 L 1117.36 583 Z M 1123.09 604 L 1125.27 604 L 1125.27 583 L 1123.09 583 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 616px; margin-left: 1107px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1107" y="628" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <path d="M 911 594.94 L 1075.63 594.57" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1080.88 594.56 L 1073.89 598.07 L 1075.63 594.57 L 1073.87 591.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="858" y="567" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 885 567 C 870.11 567 858 579.11 858 594 C 858 608.89 870.11 621 885 621 C 899.89 621 912 608.89 912 594 C 912 579.11 899.89 567 885 567 Z M 885 618.54 C 871.47 618.54 860.45 607.53 860.45 594 C 860.45 580.47 871.47 569.46 885 569.46 C 898.53 569.46 909.55 580.47 909.55 594 C 909.55 607.53 898.53 618.54 885 618.54 Z M 901 601.36 L 899.11 601.36 L 899.11 597.22 C 899.11 596.54 898.56 595.99 897.89 595.99 L 894.82 595.99 L 894.82 591.85 C 894.82 591.17 894.27 590.63 893.59 590.63 L 886.23 590.63 L 886.23 587.71 L 893.59 587.71 C 894.27 587.71 894.82 587.16 894.82 586.48 L 894.82 576.82 C 894.82 576.14 894.27 575.59 893.59 575.59 L 876.41 575.59 C 875.73 575.59 875.18 576.14 875.18 576.82 L 875.18 586.48 C 875.18 587.16 875.73 587.71 876.41 587.71 L 883.77 587.71 L 883.77 590.63 L 876.41 590.63 C 875.73 590.63 875.18 591.17 875.18 591.85 L 875.18 595.99 L 872.11 595.99 C 871.44 595.99 870.89 596.54 870.89 597.22 L 870.89 601.36 L 869 601.36 C 868.32 601.36 867.78 601.91 867.78 602.59 L 867.78 607.96 C 867.78 608.64 868.32 609.19 869 609.19 L 874.26 609.19 C 874.94 609.19 875.49 608.64 875.49 607.96 L 875.49 602.59 C 875.49 601.91 874.94 601.36 874.26 601.36 L 873.34 601.36 L 873.34 598.45 L 878.4 598.45 L 878.4 601.36 L 877.48 601.36 C 876.81 601.36 876.26 601.91 876.26 602.59 L 876.26 607.96 C 876.26 608.64 876.81 609.19 877.48 609.19 L 882.85 609.19 C 883.53 609.19 884.08 608.64 884.08 607.96 L 884.08 602.59 C 884.08 601.91 883.53 601.36 882.85 601.36 L 880.86 601.36 L 880.86 597.22 C 880.86 596.54 880.31 595.99 879.63 595.99 L 877.64 595.99 L 877.64 593.08 L 892.36 593.08 L 892.36 595.99 L 890.37 595.99 C 889.69 595.99 889.14 596.54 889.14 597.22 L 889.14 601.36 L 887.15 601.36 C 886.47 601.36 885.92 601.91 885.92 602.59 L 885.92 607.96 C 885.92 608.64 886.47 609.19 887.15 609.19 L 892.52 609.19 C 893.19 609.19 893.74 608.64 893.74 607.96 L 893.74 602.59 C 893.74 601.91 893.19 601.36 892.52 601.36 L 891.6 601.36 L 891.6 598.45 L 896.66 598.45 L 896.66 601.36 L 895.66 601.36 C 894.99 601.36 894.44 601.91 894.44 602.59 L 894.44 607.96 C 894.44 608.64 894.99 609.19 895.66 609.19 L 901 609.19 C 901.67 609.19 902.22 608.64 902.22 607.96 L 902.22 602.59 C 902.22 601.91 901.67 601.36 901 601.36 Z M 877.64 585.26 L 877.64 578.05 L 892.36 578.05 L 892.36 585.26 Z M 870.23 606.73 L 870.23 603.82 L 873.03 603.82 L 873.03 606.73 Z M 878.71 606.73 L 878.71 603.82 L 881.63 603.82 L 881.63 606.73 Z M 888.38 606.73 L 888.38 603.82 L 891.29 603.82 L 891.29 606.73 Z M 896.89 606.73 L 896.89 603.82 L 899.77 603.82 L 899.77 606.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 628px; margin-left: 885px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="885" y="640" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="858" y="211" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 885 211 C 870.11 211 858 223.11 858 238 C 858 252.89 870.11 265 885 265 C 899.89 265 912 252.89 912 238 C 912 223.11 899.89 211 885 211 Z M 885 262.54 C 871.47 262.54 860.45 251.53 860.45 238 C 860.45 224.47 871.47 213.46 885 213.46 C 898.53 213.46 909.55 224.47 909.55 238 C 909.55 251.53 898.53 262.54 885 262.54 Z M 901 245.36 L 899.11 245.36 L 899.11 241.22 C 899.11 240.54 898.56 239.99 897.89 239.99 L 894.82 239.99 L 894.82 235.85 C 894.82 235.17 894.27 234.63 893.59 234.63 L 886.23 234.63 L 886.23 231.71 L 893.59 231.71 C 894.27 231.71 894.82 231.16 894.82 230.48 L 894.82 220.82 C 894.82 220.14 894.27 219.59 893.59 219.59 L 876.41 219.59 C 875.73 219.59 875.18 220.14 875.18 220.82 L 875.18 230.48 C 875.18 231.16 875.73 231.71 876.41 231.71 L 883.77 231.71 L 883.77 234.63 L 876.41 234.63 C 875.73 234.63 875.18 235.17 875.18 235.85 L 875.18 239.99 L 872.11 239.99 C 871.44 239.99 870.89 240.54 870.89 241.22 L 870.89 245.36 L 869 245.36 C 868.32 245.36 867.78 245.91 867.78 246.59 L 867.78 251.96 C 867.78 252.64 868.32 253.19 869 253.19 L 874.26 253.19 C 874.94 253.19 875.49 252.64 875.49 251.96 L 875.49 246.59 C 875.49 245.91 874.94 245.36 874.26 245.36 L 873.34 245.36 L 873.34 242.45 L 878.4 242.45 L 878.4 245.36 L 877.48 245.36 C 876.81 245.36 876.26 245.91 876.26 246.59 L 876.26 251.96 C 876.26 252.64 876.81 253.19 877.48 253.19 L 882.85 253.19 C 883.53 253.19 884.08 252.64 884.08 251.96 L 884.08 246.59 C 884.08 245.91 883.53 245.36 882.85 245.36 L 880.86 245.36 L 880.86 241.22 C 880.86 240.54 880.31 239.99 879.63 239.99 L 877.64 239.99 L 877.64 237.08 L 892.36 237.08 L 892.36 239.99 L 890.37 239.99 C 889.69 239.99 889.14 240.54 889.14 241.22 L 889.14 245.36 L 887.15 245.36 C 886.47 245.36 885.92 245.91 885.92 246.59 L 885.92 251.96 C 885.92 252.64 886.47 253.19 887.15 253.19 L 892.52 253.19 C 893.19 253.19 893.74 252.64 893.74 251.96 L 893.74 246.59 C 893.74 245.91 893.19 245.36 892.52 245.36 L 891.6 245.36 L 891.6 242.45 L 896.66 242.45 L 896.66 245.36 L 895.66 245.36 C 894.99 245.36 894.44 245.91 894.44 246.59 L 894.44 251.96 C 894.44 252.64 894.99 253.19 895.66 253.19 L 901 253.19 C 901.67 253.19 902.22 252.64 902.22 251.96 L 902.22 246.59 C 902.22 245.91 901.67 245.36 901 245.36 Z M 877.64 229.26 L 877.64 222.05 L 892.36 222.05 L 892.36 229.26 Z M 870.23 250.73 L 870.23 247.82 L 873.03 247.82 L 873.03 250.73 Z M 878.71 250.73 L 878.71 247.82 L 881.63 247.82 L 881.63 250.73 Z M 888.38 250.73 L 888.38 247.82 L 891.29 247.82 L 891.29 250.73 Z M 896.89 250.73 L 896.89 247.82 L 899.77 247.82 L 899.77 250.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 272px; margin-left: 885px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="885" y="284" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="1083" y="222" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1129.91 222.09 L 1084.09 222.09 C 1083.49 222.09 1083 222.58 1083 223.18 L 1083 251.82 C 1083 252.42 1083.49 252.91 1084.09 252.91 L 1129.91 252.91 C 1130.51 252.91 1131 252.42 1131 251.82 L 1131 223.18 C 1131 222.58 1130.51 222.09 1129.91 222.09 Z M 1085.18 250.73 L 1085.18 224.27 L 1128.82 224.27 L 1128.82 250.73 Z M 1088.73 248 L 1090.91 248 L 1090.91 227 L 1088.73 227 Z M 1094.45 248 L 1096.64 248 L 1096.64 227 L 1094.45 227 Z M 1100.18 248 L 1102.36 248 L 1102.36 227 L 1100.18 227 Z M 1105.91 248 L 1108.09 248 L 1108.09 227 L 1105.91 227 Z M 1111.63 248 L 1113.82 248 L 1113.82 227 L 1111.63 227 Z M 1117.36 248 L 1119.55 248 L 1119.55 227 L 1117.36 227 Z M 1123.09 248 L 1125.27 248 L 1125.27 227 L 1123.09 227 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 1107px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1107" y="272" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <rect x="858" y="392" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 885 392 C 870.11 392 858 404.11 858 419 C 858 433.89 870.11 446 885 446 C 899.89 446 912 433.89 912 419 C 912 404.11 899.89 392 885 392 Z M 885 443.54 C 871.47 443.54 860.45 432.53 860.45 419 C 860.45 405.47 871.47 394.46 885 394.46 C 898.53 394.46 909.55 405.47 909.55 419 C 909.55 432.53 898.53 443.54 885 443.54 Z M 901 426.36 L 899.11 426.36 L 899.11 422.22 C 899.11 421.54 898.56 420.99 897.89 420.99 L 894.82 420.99 L 894.82 416.85 C 894.82 416.17 894.27 415.63 893.59 415.63 L 886.23 415.63 L 886.23 412.71 L 893.59 412.71 C 894.27 412.71 894.82 412.16 894.82 411.48 L 894.82 401.82 C 894.82 401.14 894.27 400.59 893.59 400.59 L 876.41 400.59 C 875.73 400.59 875.18 401.14 875.18 401.82 L 875.18 411.48 C 875.18 412.16 875.73 412.71 876.41 412.71 L 883.77 412.71 L 883.77 415.63 L 876.41 415.63 C 875.73 415.63 875.18 416.17 875.18 416.85 L 875.18 420.99 L 872.11 420.99 C 871.44 420.99 870.89 421.54 870.89 422.22 L 870.89 426.36 L 869 426.36 C 868.32 426.36 867.78 426.91 867.78 427.59 L 867.78 432.96 C 867.78 433.64 868.32 434.19 869 434.19 L 874.26 434.19 C 874.94 434.19 875.49 433.64 875.49 432.96 L 875.49 427.59 C 875.49 426.91 874.94 426.36 874.26 426.36 L 873.34 426.36 L 873.34 423.45 L 878.4 423.45 L 878.4 426.36 L 877.48 426.36 C 876.81 426.36 876.26 426.91 876.26 427.59 L 876.26 432.96 C 876.26 433.64 876.81 434.19 877.48 434.19 L 882.85 434.19 C 883.53 434.19 884.08 433.64 884.08 432.96 L 884.08 427.59 C 884.08 426.91 883.53 426.36 882.85 426.36 L 880.86 426.36 L 880.86 422.22 C 880.86 421.54 880.31 420.99 879.63 420.99 L 877.64 420.99 L 877.64 418.08 L 892.36 418.08 L 892.36 420.99 L 890.37 420.99 C 889.69 420.99 889.14 421.54 889.14 422.22 L 889.14 426.36 L 887.15 426.36 C 886.47 426.36 885.92 426.91 885.92 427.59 L 885.92 432.96 C 885.92 433.64 886.47 434.19 887.15 434.19 L 892.52 434.19 C 893.19 434.19 893.74 433.64 893.74 432.96 L 893.74 427.59 C 893.74 426.91 893.19 426.36 892.52 426.36 L 891.6 426.36 L 891.6 423.45 L 896.66 423.45 L 896.66 426.36 L 895.66 426.36 C 894.99 426.36 894.44 426.91 894.44 427.59 L 894.44 432.96 C 894.44 433.64 894.99 434.19 895.66 434.19 L 901 434.19 C 901.67 434.19 902.22 433.64 902.22 432.96 L 902.22 427.59 C 902.22 426.91 901.67 426.36 901 426.36 Z M 877.64 410.26 L 877.64 403.05 L 892.36 403.05 L 892.36 410.26 Z M 870.23 431.73 L 870.23 428.82 L 873.03 428.82 L 873.03 431.73 Z M 878.71 431.73 L 878.71 428.82 L 881.63 428.82 L 881.63 431.73 Z M 888.38 431.73 L 888.38 428.82 L 891.29 428.82 L 891.29 431.73 Z M 896.89 431.73 L 896.89 428.82 L 899.77 428.82 L 899.77 431.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 453px; margin-left: 885px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="885" y="465" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="1083" y="402" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1129.91 402.09 L 1084.09 402.09 C 1083.49 402.09 1083 402.58 1083 403.18 L 1083 431.82 C 1083 432.42 1083.49 432.91 1084.09 432.91 L 1129.91 432.91 C 1130.51 432.91 1131 432.42 1131 431.82 L 1131 403.18 C 1131 402.58 1130.51 402.09 1129.91 402.09 Z M 1085.18 430.73 L 1085.18 404.27 L 1128.82 404.27 L 1128.82 430.73 Z M 1088.73 428 L 1090.91 428 L 1090.91 407 L 1088.73 407 Z M 1094.45 428 L 1096.64 428 L 1096.64 407 L 1094.45 407 Z M 1100.18 428 L 1102.36 428 L 1102.36 407 L 1100.18 407 Z M 1105.91 428 L 1108.09 428 L 1108.09 407 L 1105.91 407 Z M 1111.63 428 L 1113.82 428 L 1113.82 407 L 1111.63 407 Z M 1117.36 428 L 1119.55 428 L 1119.55 407 L 1117.36 407 Z M 1123.09 428 L 1125.27 428 L 1125.27 407 L 1123.09 407 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 440px; margin-left: 1107px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1107" y="452" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <path d="M 912 237.94 L 1074.76 237.57" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/>
        <path d="M 1080.76 237.56 L 1072.77 241.58 L 1074.76 237.57 L 1072.76 233.58 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 911 418.94 L 1075.63 418.57" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1080.88 418.56 L 1073.89 422.07 L 1075.63 418.57 L 1073.87 415.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="643" y="389" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 670 389 C 655.11 389 643 401.11 643 416 C 643 430.89 655.11 443 670 443 C 684.89 443 697 430.89 697 416 C 697 401.11 684.89 389 670 389 Z M 670 440.54 C 656.47 440.54 645.45 429.53 645.45 416 C 645.45 402.47 656.47 391.46 670 391.46 C 683.53 391.46 694.55 402.47 694.55 416 C 694.55 429.53 683.53 440.54 670 440.54 Z M 686 423.36 L 684.11 423.36 L 684.11 419.22 C 684.11 418.54 683.56 417.99 682.89 417.99 L 679.82 417.99 L 679.82 413.85 C 679.82 413.17 679.27 412.63 678.59 412.63 L 671.23 412.63 L 671.23 409.71 L 678.59 409.71 C 679.27 409.71 679.82 409.16 679.82 408.48 L 679.82 398.82 C 679.82 398.14 679.27 397.59 678.59 397.59 L 661.41 397.59 C 660.73 397.59 660.18 398.14 660.18 398.82 L 660.18 408.48 C 660.18 409.16 660.73 409.71 661.41 409.71 L 668.77 409.71 L 668.77 412.63 L 661.41 412.63 C 660.73 412.63 660.18 413.17 660.18 413.85 L 660.18 417.99 L 657.11 417.99 C 656.44 417.99 655.89 418.54 655.89 419.22 L 655.89 423.36 L 654 423.36 C 653.32 423.36 652.78 423.91 652.78 424.59 L 652.78 429.96 C 652.78 430.64 653.32 431.19 654 431.19 L 659.26 431.19 C 659.94 431.19 660.49 430.64 660.49 429.96 L 660.49 424.59 C 660.49 423.91 659.94 423.36 659.26 423.36 L 658.34 423.36 L 658.34 420.45 L 663.4 420.45 L 663.4 423.36 L 662.48 423.36 C 661.81 423.36 661.26 423.91 661.26 424.59 L 661.26 429.96 C 661.26 430.64 661.81 431.19 662.48 431.19 L 667.85 431.19 C 668.53 431.19 669.08 430.64 669.08 429.96 L 669.08 424.59 C 669.08 423.91 668.53 423.36 667.85 423.36 L 665.86 423.36 L 665.86 419.22 C 665.86 418.54 665.31 417.99 664.63 417.99 L 662.64 417.99 L 662.64 415.08 L 677.36 415.08 L 677.36 417.99 L 675.37 417.99 C 674.69 417.99 674.14 418.54 674.14 419.22 L 674.14 423.36 L 672.15 423.36 C 671.47 423.36 670.92 423.91 670.92 424.59 L 670.92 429.96 C 670.92 430.64 671.47 431.19 672.15 431.19 L 677.52 431.19 C 678.19 431.19 678.74 430.64 678.74 429.96 L 678.74 424.59 C 678.74 423.91 678.19 423.36 677.52 423.36 L 676.6 423.36 L 676.6 420.45 L 681.66 420.45 L 681.66 423.36 L 680.66 423.36 C 679.99 423.36 679.44 423.91 679.44 424.59 L 679.44 429.96 C 679.44 430.64 679.99 431.19 680.66 431.19 L 686 431.19 C 686.67 431.19 687.22 430.64 687.22 429.96 L 687.22 424.59 C 687.22 423.91 686.67 423.36 686 423.36 Z M 662.64 407.26 L 662.64 400.05 L 677.36 400.05 L 677.36 407.26 Z M 655.23 428.73 L 655.23 425.82 L 658.03 425.82 L 658.03 428.73 Z M 663.71 428.73 L 663.71 425.82 L 666.63 425.82 L 666.63 428.73 Z M 673.38 428.73 L 673.38 425.82 L 676.29 425.82 L 676.29 428.73 Z M 681.89 428.73 L 681.89 425.82 L 684.77 425.82 L 684.77 428.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 450px; margin-left: 670px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="670" y="462" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 697 393.65 L 851.66 265.61" fill="none" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/>
        <path d="M 856.28 261.78 L 852.67 269.96 L 851.66 265.61 L 847.56 263.8 Z" fill="#ff3333" stroke="#ff3333" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 700 422 L 849.63 422.02" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 854.88 422.02 L 847.88 425.52 L 849.63 422.02 L 847.88 418.52 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 697 438.35 L 853.09 567.59" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 857.14 570.93 L 849.51 569.17 L 853.09 567.59 L 853.98 563.77 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 865 233.5 L 881 233.5 L 881 217.5 L 889 217.5 L 889 233.5 L 905 233.5 L 905 241.5 L 889 241.5 L 889 257.5 L 881 257.5 L 881 241.5 L 865 241.5 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,885,237.5)" pointer-events="all"/>
        <path d="M 250 117 L 460 117 L 460 193 L 358.2 193 L 355 213 L 338.2 193 L 250 193 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 155px; margin-left: 251px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 16px;">
                                    2AZでは切り離しが不可
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="355" y="158" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    2AZでは切り離しが不可
                </text>
            </switch>
        </g>
        <path d="M 891 11 L 1101 11 L 1101 87 L 999.2 87 L 996 107 L 979.2 87 L 891 87 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 49px; margin-left: 892px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 10px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 16px;">
                                    3AZでは切り離しが可能
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="996" y="52" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="10px" text-anchor="middle">
                    3AZでは切り離しが可能
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>