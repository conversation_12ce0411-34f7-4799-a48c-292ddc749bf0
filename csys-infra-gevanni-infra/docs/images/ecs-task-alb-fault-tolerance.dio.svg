<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="702px" height="832px" viewBox="-0.5 -0.5 702 832" content="&lt;mxfile&gt;&lt;diagram id=&quot;_K1tkZvUuKXP9ynMXcLM&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="275" y="39" width="370" height="771" fill="none" stroke="#ff8000" stroke-width="2" pointer-events="all"/>
        <g fill="#FF8000" font-family="Helvetica" text-anchor="middle" font-size="14px">
            <text x="459.5" y="58.5">
                ECS Cluster
            </text>
        </g>
        <path d="M 354.5 162 L 554.5 162 L 554.5 322 L 354.5 322 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 354.5 162 L 379.5 162 L 379.5 187 L 354.5 187 Z M 367.02 165.21 C 365.9 165.21 364.81 165.63 363.98 166.39 C 363.17 167.11 362.7 168.15 362.7 169.24 L 362.7 171.78 L 360.39 171.78 C 360.3 171.78 360.2 171.82 360.14 171.89 C 360.07 171.95 360.04 172.04 360.04 172.13 L 360.04 183.43 C 360.04 183.63 360.2 183.79 360.39 183.79 L 373.61 183.79 C 373.8 183.79 373.96 183.63 373.96 183.43 L 373.96 172.15 C 373.97 172.06 373.93 171.97 373.86 171.9 C 373.8 171.83 373.71 171.79 373.61 171.79 L 371.31 171.79 L 371.31 169.29 C 371.3 168.21 370.85 167.18 370.06 166.44 C 369.24 165.65 368.15 165.22 367.02 165.21 Z M 367.01 165.93 C 367.96 165.92 368.87 166.28 369.56 166.93 C 370.22 167.54 370.6 168.4 370.6 169.29 L 370.6 171.79 L 363.38 171.79 L 363.39 169.26 C 363.4 168.36 363.78 167.51 364.45 166.91 C 365.15 166.27 366.07 165.92 367.01 165.93 Z M 360.74 172.5 L 373.26 172.5 L 373.25 183.07 L 360.74 183.07 Z M 367.01 174.74 C 365.98 174.73 365.11 175.51 365.01 176.53 C 364.92 177.56 365.63 178.48 366.64 178.66 L 366.64 181.44 L 367.36 181.44 L 367.36 178.66 C 368.29 178.49 368.97 177.67 368.98 176.72 C 368.98 175.63 368.1 174.75 367.01 174.74 Z M 366.89 175.45 C 366.93 175.45 366.97 175.45 367.01 175.46 C 367.34 175.46 367.66 175.59 367.9 175.83 C 368.14 176.07 368.27 176.39 368.26 176.72 C 368.27 177.06 368.14 177.38 367.9 177.61 C 367.66 177.85 367.34 177.98 367.01 177.98 C 366.54 178.02 366.1 177.8 365.84 177.42 C 365.58 177.03 365.56 176.53 365.78 176.12 C 366 175.71 366.43 175.46 366.89 175.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 169px; margin-left: 387px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="387" y="181" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <path d="M 0 0 L 701 0 L 701 830 L 0 830 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 10.59 6.65 C 10.53 6.65 10.48 6.65 10.42 6.65 L 10.42 6.65 C 9.11 6.68 8.03 7.24 7.14 8.25 C 7.13 8.25 7.13 8.25 7.13 8.25 C 6.2 9.36 5.87 10.52 5.96 11.73 C 4.81 12.06 4.12 12.92 3.76 13.74 C 3.75 13.75 3.75 13.76 3.74 13.78 C 3.33 15.05 3.68 16.36 4.24 17.16 C 4.25 17.17 4.25 17.17 4.26 17.18 C 4.94 18.05 5.97 18.53 7.02 18.53 L 18.17 18.53 C 19.19 18.53 20.07 18.16 20.8 17.37 C 21.25 16.94 21.49 16.29 21.58 15.59 C 21.67 14.9 21.61 14.16 21.32 13.55 C 21.31 13.54 21.31 13.53 21.31 13.52 C 20.8 12.62 19.95 11.81 18.76 11.64 C 18.74 10.79 18.28 9.99 17.68 9.56 C 17.67 9.55 17.66 9.55 17.65 9.54 C 17.01 9.18 16.4 9.14 15.91 9.3 C 15.6 9.4 15.36 9.56 15.14 9.74 C 14.51 8.36 13.43 7.18 11.81 6.79 C 11.81 6.79 11.81 6.79 11.81 6.79 C 11.38 6.7 10.97 6.65 10.59 6.65 Z M 10.43 7.38 C 10.8 7.38 11.2 7.43 11.64 7.53 C 13.16 7.89 14.15 9.07 14.66 10.48 C 14.71 10.6 14.81 10.69 14.94 10.72 C 15.07 10.74 15.2 10.7 15.29 10.61 C 15.54 10.34 15.83 10.11 16.14 10.01 C 16.44 9.91 16.78 9.92 17.26 10.18 C 17.67 10.49 18.11 11.31 18.03 11.9 C 18.01 12.01 18.05 12.12 18.12 12.2 C 18.19 12.28 18.29 12.33 18.39 12.33 C 19.46 12.34 20.16 13.02 20.64 13.88 C 20.85 14.3 20.91 14.92 20.84 15.5 C 20.76 16.07 20.53 16.59 20.28 16.83 C 20.27 16.84 20.27 16.85 20.26 16.85 C 19.65 17.53 19.03 17.78 18.17 17.78 L 7.02 17.78 C 6.2 17.78 5.39 17.41 4.85 16.73 C 4.44 16.13 4.14 15.02 4.46 14.02 C 4.79 13.27 5.36 12.55 6.41 12.36 C 6.6 12.32 6.74 12.14 6.71 11.94 C 6.56 10.79 6.8 9.81 7.7 8.74 C 8.49 7.85 9.33 7.39 10.43 7.38 Z M 12.2 10.7 C 11.77 10.7 11.4 10.93 11.13 11.21 C 10.85 11.5 10.64 11.85 10.64 12.25 L 10.64 12.71 L 10.14 12.71 C 10.04 12.71 9.94 12.75 9.87 12.82 C 9.8 12.89 9.76 12.98 9.76 13.08 L 9.76 15.7 C 9.76 15.8 9.8 15.89 9.87 15.96 C 9.94 16.03 10.04 16.07 10.14 16.07 L 14.16 16.07 C 14.26 16.07 14.35 16.03 14.42 15.96 C 14.49 15.89 14.53 15.8 14.53 15.7 L 14.53 13.08 C 14.53 12.98 14.49 12.89 14.42 12.82 C 14.35 12.75 14.26 12.71 14.16 12.71 L 13.68 12.71 L 13.68 12.25 C 13.68 11.84 13.47 11.47 13.21 11.2 C 12.94 10.92 12.61 10.7 12.2 10.7 Z M 12.2 11.45 C 12.29 11.45 12.5 11.54 12.67 11.72 C 12.83 11.89 12.93 12.11 12.93 12.25 L 12.93 12.71 L 11.39 12.71 L 11.39 12.25 C 11.39 12.15 11.49 11.91 11.66 11.74 C 11.83 11.56 12.06 11.45 12.2 11.45 Z M 10.51 13.46 L 13.78 13.46 L 13.78 15.32 L 10.51 15.32 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 669px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 275.5 39 L 311.5 39 L 311.5 75 L 275.5 75 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 305.36 60.92 L 301.33 58.49 L 301.33 52.72 C 301.33 52.54 301.23 52.37 301.07 52.28 L 295.27 48.9 L 295.27 44.01 L 305.36 49.98 Z M 306.13 49.26 L 295.02 42.69 C 294.86 42.6 294.67 42.6 294.51 42.69 C 294.35 42.78 294.26 42.95 294.26 43.13 L 294.26 49.19 C 294.26 49.37 294.35 49.53 294.51 49.62 L 300.32 53.01 L 300.32 58.78 C 300.32 58.96 300.41 59.12 300.56 59.21 L 305.61 62.24 C 305.69 62.29 305.78 62.31 305.87 62.31 C 305.95 62.31 306.04 62.29 306.12 62.25 C 306.28 62.16 306.37 61.99 306.37 61.81 L 306.37 49.69 C 306.37 49.51 306.28 49.35 306.13 49.26 Z M 293.47 70.32 L 281.64 64.03 L 281.64 49.98 L 291.73 44.01 L 291.73 48.91 L 286.41 52.29 C 286.27 52.39 286.18 52.55 286.18 52.72 L 286.18 61.3 C 286.18 61.49 286.28 61.66 286.45 61.75 L 293.24 65.28 C 293.39 65.36 293.56 65.36 293.71 65.29 L 300.3 61.88 L 304.35 64.31 Z M 305.62 63.9 L 300.58 60.87 C 300.43 60.78 300.24 60.77 300.08 60.85 L 293.48 64.27 L 287.19 61 L 287.19 53 L 292.51 49.61 C 292.65 49.52 292.74 49.36 292.74 49.19 L 292.74 43.13 C 292.74 42.95 292.65 42.78 292.49 42.69 C 292.33 42.6 292.14 42.6 291.98 42.69 L 280.87 49.26 C 280.72 49.35 280.63 49.51 280.63 49.69 L 280.63 64.33 C 280.63 64.52 280.73 64.69 280.89 64.78 L 293.24 71.34 C 293.31 71.38 293.39 71.4 293.48 71.4 C 293.56 71.4 293.64 71.38 293.72 71.34 L 305.61 64.77 C 305.77 64.69 305.87 64.52 305.87 64.34 C 305.87 64.16 305.78 63.99 305.62 63.9 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="311.5" y="79" width="299" height="701" fill="none" stroke="#ff8000" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <rect x="318.5" y="81" width="29.25" height="36" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 334.09 110.63 L 344.51 110.63 L 344.51 108.99 L 334.09 108.99 Z M 334.09 102.52 L 344.51 102.52 L 344.51 100.88 L 334.09 100.88 Z M 334.09 94.41 L 344.51 94.41 L 344.51 92.78 L 334.09 92.78 Z M 329.7 110.73 L 329.7 108.89 L 331.53 108.89 L 331.53 110.73 Z M 328.88 112.37 L 332.35 112.37 C 332.8 112.37 333.17 112 333.17 111.55 L 333.17 108.07 C 333.17 107.62 332.8 107.26 332.35 107.26 L 328.88 107.26 C 328.43 107.26 328.06 107.62 328.06 108.07 L 328.06 111.55 C 328.06 112 328.43 112.37 328.88 112.37 Z M 329.7 102.62 L 329.7 100.78 L 331.53 100.78 L 331.53 102.62 Z M 328.88 104.26 L 332.35 104.26 C 332.8 104.26 333.17 103.89 333.17 103.44 L 333.17 99.97 C 333.17 99.51 332.8 99.15 332.35 99.15 L 328.88 99.15 C 328.43 99.15 328.06 99.51 328.06 99.97 L 328.06 103.44 C 328.06 103.89 328.43 104.26 328.88 104.26 Z M 329.7 94.51 L 329.7 92.68 L 331.53 92.68 L 331.53 94.51 Z M 328.88 96.15 L 332.35 96.15 C 332.8 96.15 333.17 95.78 333.17 95.33 L 333.17 91.86 C 333.17 91.4 332.8 91.04 332.35 91.04 L 328.88 91.04 C 328.43 91.04 328.06 91.4 328.06 91.86 L 328.06 95.33 C 328.06 95.78 328.43 96.15 328.88 96.15 Z M 326.8 115.36 L 326.8 89.2 L 346.01 89.2 L 346.01 115.36 Z M 325.16 88.38 L 325.16 111.89 L 323.32 111.89 L 323.32 85.73 L 342.54 85.73 L 342.54 87.56 L 325.98 87.56 C 325.53 87.56 325.16 87.93 325.16 88.38 Z M 321.69 84.91 L 321.69 108.8 L 320.24 108.8 L 320.24 82.64 L 339.45 82.64 L 339.45 84.09 L 322.51 84.09 C 322.05 84.09 321.69 84.46 321.69 84.91 Z M 346.83 87.56 L 344.18 87.56 L 344.18 84.91 C 344.18 84.46 343.81 84.09 343.36 84.09 L 341.09 84.09 L 341.09 81.82 C 341.09 81.37 340.72 81 340.27 81 L 319.42 81 C 318.97 81 318.6 81.37 318.6 81.82 L 318.6 109.62 C 318.6 110.07 318.97 110.44 319.42 110.44 L 321.69 110.44 L 321.69 112.71 C 321.69 113.16 322.05 113.53 322.51 113.53 L 325.16 113.53 L 325.16 116.18 C 325.16 116.63 325.53 117 325.98 117 L 346.83 117 C 347.28 117 347.65 116.63 347.65 116.18 L 347.65 88.38 C 347.65 87.93 347.28 87.56 346.83 87.56 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 333px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="333" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Servi...
                </text>
            </switch>
        </g>
        <path d="M 19 386 L 219 386 L 219 516 L 19 516 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 19 386 L 44 386 L 44 411 L 19 411 Z M 31.52 389.21 C 30.4 389.21 29.31 389.63 28.48 390.39 C 27.67 391.11 27.2 392.15 27.2 393.24 L 27.2 395.78 L 24.89 395.78 C 24.8 395.78 24.7 395.82 24.64 395.89 C 24.57 395.95 24.54 396.04 24.54 396.13 L 24.54 407.43 C 24.54 407.63 24.7 407.79 24.89 407.79 L 38.11 407.79 C 38.3 407.79 38.46 407.63 38.46 407.43 L 38.46 396.15 C 38.47 396.06 38.43 395.97 38.36 395.9 C 38.3 395.83 38.21 395.79 38.11 395.79 L 35.81 395.79 L 35.81 393.29 C 35.8 392.21 35.35 391.18 34.56 390.44 C 33.74 389.65 32.65 389.22 31.52 389.21 Z M 31.51 389.93 C 32.46 389.92 33.37 390.28 34.06 390.93 C 34.72 391.54 35.1 392.4 35.1 393.29 L 35.1 395.79 L 27.88 395.79 L 27.89 393.26 C 27.9 392.36 28.28 391.51 28.95 390.91 C 29.65 390.27 30.57 389.92 31.51 389.93 Z M 25.24 396.5 L 37.76 396.5 L 37.75 407.07 L 25.24 407.07 Z M 31.51 398.74 C 30.48 398.73 29.61 399.51 29.51 400.53 C 29.42 401.56 30.13 402.48 31.14 402.66 L 31.14 405.44 L 31.86 405.44 L 31.86 402.66 C 32.79 402.49 33.47 401.67 33.48 400.72 C 33.48 399.63 32.6 398.75 31.51 398.74 Z M 31.39 399.45 C 31.43 399.45 31.47 399.45 31.51 399.46 C 31.84 399.46 32.16 399.59 32.4 399.83 C 32.64 400.07 32.77 400.39 32.76 400.72 C 32.77 401.06 32.64 401.38 32.4 401.61 C 32.16 401.85 31.84 401.98 31.51 401.98 C 31.04 402.02 30.6 401.8 30.34 401.42 C 30.08 401.03 30.06 400.53 30.28 400.12 C 30.5 399.71 30.93 399.46 31.39 399.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 393px; margin-left: 51px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="51" y="405" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <path d="M 151.99 450 L 438.3 227.55" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/>
        <path d="M 443.04 223.87 L 439.17 231.94 L 438.3 227.55 L 434.26 225.62 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="92" y="427" width="54" height="54" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 119 427 C 104.11 427 92 439.11 92 454 C 92 468.89 104.11 481 119 481 C 133.89 481 146 468.89 146 454 C 146 439.11 133.89 427 119 427 Z M 119 478.54 C 105.47 478.54 94.45 467.53 94.45 454 C 94.45 440.47 105.47 429.46 119 429.46 C 132.53 429.46 143.55 440.47 143.55 454 C 143.55 467.53 132.53 478.54 119 478.54 Z M 135 461.36 L 133.11 461.36 L 133.11 457.22 C 133.11 456.54 132.56 455.99 131.89 455.99 L 128.82 455.99 L 128.82 451.85 C 128.82 451.17 128.27 450.63 127.59 450.63 L 120.23 450.63 L 120.23 447.71 L 127.59 447.71 C 128.27 447.71 128.82 447.16 128.82 446.48 L 128.82 436.82 C 128.82 436.14 128.27 435.59 127.59 435.59 L 110.41 435.59 C 109.73 435.59 109.18 436.14 109.18 436.82 L 109.18 446.48 C 109.18 447.16 109.73 447.71 110.41 447.71 L 117.77 447.71 L 117.77 450.63 L 110.41 450.63 C 109.73 450.63 109.18 451.17 109.18 451.85 L 109.18 455.99 L 106.11 455.99 C 105.44 455.99 104.89 456.54 104.89 457.22 L 104.89 461.36 L 103 461.36 C 102.32 461.36 101.78 461.91 101.78 462.59 L 101.78 467.96 C 101.78 468.64 102.32 469.19 103 469.19 L 108.26 469.19 C 108.94 469.19 109.49 468.64 109.49 467.96 L 109.49 462.59 C 109.49 461.91 108.94 461.36 108.26 461.36 L 107.34 461.36 L 107.34 458.45 L 112.4 458.45 L 112.4 461.36 L 111.48 461.36 C 110.81 461.36 110.26 461.91 110.26 462.59 L 110.26 467.96 C 110.26 468.64 110.81 469.19 111.48 469.19 L 116.85 469.19 C 117.53 469.19 118.08 468.64 118.08 467.96 L 118.08 462.59 C 118.08 461.91 117.53 461.36 116.85 461.36 L 114.86 461.36 L 114.86 457.22 C 114.86 456.54 114.31 455.99 113.63 455.99 L 111.64 455.99 L 111.64 453.08 L 126.36 453.08 L 126.36 455.99 L 124.37 455.99 C 123.69 455.99 123.14 456.54 123.14 457.22 L 123.14 461.36 L 121.15 461.36 C 120.47 461.36 119.92 461.91 119.92 462.59 L 119.92 467.96 C 119.92 468.64 120.47 469.19 121.15 469.19 L 126.52 469.19 C 127.19 469.19 127.74 468.64 127.74 467.96 L 127.74 462.59 C 127.74 461.91 127.19 461.36 126.52 461.36 L 125.6 461.36 L 125.6 458.45 L 130.66 458.45 L 130.66 461.36 L 129.66 461.36 C 128.99 461.36 128.44 461.91 128.44 462.59 L 128.44 467.96 C 128.44 468.64 128.99 469.19 129.66 469.19 L 135 469.19 C 135.67 469.19 136.22 468.64 136.22 467.96 L 136.22 462.59 C 136.22 461.91 135.67 461.36 135 461.36 Z M 111.64 445.26 L 111.64 438.05 L 126.36 438.05 L 126.36 445.26 Z M 104.23 466.73 L 104.23 463.82 L 107.03 463.82 L 107.03 466.73 Z M 112.71 466.73 L 112.71 463.82 L 115.63 463.82 L 115.63 466.73 Z M 122.38 466.73 L 122.38 463.82 L 125.29 463.82 L 125.29 466.73 Z M 130.89 466.73 L 130.89 463.82 L 133.77 463.82 L 133.77 466.73 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 488px; margin-left: 119px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="119" y="500" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <rect x="345" y="140" width="220" height="200" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="454.5" y="157.5">
                Availability Zone 1a
            </text>
        </g>
        <rect x="407.5" y="191" width="94" height="57" fill="none" stroke="#ff8000" pointer-events="none"/>
        <path d="M 416.41 212.88 L 426.44 212.88 L 426.44 211.6 L 416.41 211.6 Z M 416.41 205.08 L 426.44 205.08 L 426.44 203.81 L 416.41 203.81 Z M 416.41 197.28 L 426.44 197.28 L 426.44 196.01 L 416.41 196.01 Z M 412.04 213.27 L 412.04 211.2 L 414.11 211.2 L 414.11 213.27 Z M 411.4 214.55 L 414.74 214.55 C 415.09 214.55 415.38 214.26 415.38 213.91 L 415.38 210.57 C 415.38 210.22 415.09 209.93 414.74 209.93 L 411.4 209.93 C 411.05 209.93 410.77 210.22 410.77 210.57 L 410.77 213.91 C 410.77 214.26 411.05 214.55 411.4 214.55 Z M 412.04 205.48 L 412.04 203.41 L 414.11 203.41 L 414.11 205.48 Z M 411.4 206.75 L 414.74 206.75 C 415.09 206.75 415.38 206.47 415.38 206.11 L 415.38 202.77 C 415.38 202.42 415.09 202.14 414.74 202.14 L 411.4 202.14 C 411.05 202.14 410.77 202.42 410.77 202.77 L 410.77 206.11 C 410.77 206.47 411.05 206.75 411.4 206.75 Z M 412.04 197.68 L 412.04 195.61 L 414.11 195.61 L 414.11 197.68 Z M 411.4 198.95 L 414.74 198.95 C 415.09 198.95 415.38 198.67 415.38 198.32 L 415.38 194.98 C 415.38 194.63 415.09 194.34 414.74 194.34 L 411.4 194.34 C 411.05 194.34 410.77 194.63 410.77 194.98 L 410.77 198.32 C 410.77 198.67 411.05 198.95 411.4 198.95 Z M 409.25 217.73 L 409.25 192.27 L 428.03 192.27 L 428.03 217.73 Z M 428.66 191 L 408.62 191 C 408.27 191 407.98 191.29 407.98 191.64 L 407.98 218.36 C 407.98 218.72 408.27 219 408.62 219 L 428.66 219 C 429.01 219 429.3 218.72 429.3 218.36 L 429.3 191.64 C 429.3 191.29 429.01 191 428.66 191 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 226px; margin-left: 419px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="text-align: justify;">
                                    <span style="font-size: 8px; background-color: initial;">
                                        Task
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="238" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Task
                </text>
            </switch>
        </g>
        <path d="M 405.54 201.44 L 415.91 201.44 L 415.91 191.03 L 421.09 191.03 L 421.09 201.44 L 431.46 201.44 L 431.46 206.62 L 421.09 206.62 L 421.09 217.03 L 415.91 217.03 L 415.91 206.62 L 405.54 206.62 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,418.5,204.03)" pointer-events="none"/>
        <path d="M 464.85 208.04 L 443.36 208.04 C 443.08 208.04 442.85 208.27 442.85 208.55 L 442.85 221.99 C 442.85 222.27 443.08 222.5 443.36 222.5 L 464.85 222.5 C 465.14 222.5 465.36 222.27 465.36 221.99 L 465.36 208.55 C 465.36 208.27 465.14 208.04 464.85 208.04 Z M 443.87 221.47 L 443.87 209.07 L 464.34 209.07 L 464.34 221.47 Z M 445.53 220.2 L 446.56 220.2 L 446.56 210.34 L 445.53 210.34 Z M 448.22 220.2 L 449.24 220.2 L 449.24 210.34 L 448.22 210.34 Z M 450.91 220.2 L 451.93 220.2 L 451.93 210.34 L 450.91 210.34 Z M 453.59 220.2 L 454.62 220.2 L 454.62 210.34 L 453.59 210.34 Z M 456.28 220.2 L 457.3 220.2 L 457.3 210.34 L 456.28 210.34 Z M 458.97 220.2 L 459.99 220.2 L 459.99 210.34 L 458.97 210.34 Z M 461.65 220.2 L 462.68 220.2 L 462.68 210.34 L 461.65 210.34 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 230px; margin-left: 454px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <font style="font-size: 7px;">
                                    Container
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="454" y="242" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cont...
                </text>
            </switch>
        </g>
        <rect x="407.5" y="256" width="94" height="57" fill="none" stroke="#ff8000" pointer-events="none"/>
        <path d="M 416.41 277.88 L 426.44 277.88 L 426.44 276.6 L 416.41 276.6 Z M 416.41 270.08 L 426.44 270.08 L 426.44 268.81 L 416.41 268.81 Z M 416.41 262.28 L 426.44 262.28 L 426.44 261.01 L 416.41 261.01 Z M 412.04 278.27 L 412.04 276.2 L 414.11 276.2 L 414.11 278.27 Z M 411.4 279.55 L 414.74 279.55 C 415.09 279.55 415.38 279.26 415.38 278.91 L 415.38 275.57 C 415.38 275.22 415.09 274.93 414.74 274.93 L 411.4 274.93 C 411.05 274.93 410.77 275.22 410.77 275.57 L 410.77 278.91 C 410.77 279.26 411.05 279.55 411.4 279.55 Z M 412.04 270.48 L 412.04 268.41 L 414.11 268.41 L 414.11 270.48 Z M 411.4 271.75 L 414.74 271.75 C 415.09 271.75 415.38 271.47 415.38 271.11 L 415.38 267.77 C 415.38 267.42 415.09 267.14 414.74 267.14 L 411.4 267.14 C 411.05 267.14 410.77 267.42 410.77 267.77 L 410.77 271.11 C 410.77 271.47 411.05 271.75 411.4 271.75 Z M 412.04 262.68 L 412.04 260.61 L 414.11 260.61 L 414.11 262.68 Z M 411.4 263.95 L 414.74 263.95 C 415.09 263.95 415.38 263.67 415.38 263.32 L 415.38 259.98 C 415.38 259.63 415.09 259.34 414.74 259.34 L 411.4 259.34 C 411.05 259.34 410.77 259.63 410.77 259.98 L 410.77 263.32 C 410.77 263.67 411.05 263.95 411.4 263.95 Z M 409.25 282.73 L 409.25 257.27 L 428.03 257.27 L 428.03 282.73 Z M 428.66 256 L 408.62 256 C 408.27 256 407.98 256.29 407.98 256.64 L 407.98 283.36 C 407.98 283.72 408.27 284 408.62 284 L 428.66 284 C 429.01 284 429.3 283.72 429.3 283.36 L 429.3 256.64 C 429.3 256.29 429.01 256 428.66 256 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 291px; margin-left: 419px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="text-align: justify; font-size: 3px;">
                                    <span style="background-color: initial;">
                                        <br/>
                                    </span>
                                </div>
                                <div style="text-align: justify;">
                                    <span style="background-color: initial; font-size: 8px;">
                                        Task
                                    </span>
                                    <br/>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="303" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Tas...
                </text>
            </switch>
        </g>
        <path d="M 463.85 272.04 L 442.36 272.04 C 442.08 272.04 441.85 272.27 441.85 272.55 L 441.85 285.99 C 441.85 286.27 442.08 286.5 442.36 286.5 L 463.85 286.5 C 464.14 286.5 464.36 286.27 464.36 285.99 L 464.36 272.55 C 464.36 272.27 464.14 272.04 463.85 272.04 Z M 442.87 285.47 L 442.87 273.07 L 463.34 273.07 L 463.34 285.47 Z M 444.53 284.2 L 445.56 284.2 L 445.56 274.34 L 444.53 274.34 Z M 447.22 284.2 L 448.24 284.2 L 448.24 274.34 L 447.22 274.34 Z M 449.91 284.2 L 450.93 284.2 L 450.93 274.34 L 449.91 274.34 Z M 452.59 284.2 L 453.62 284.2 L 453.62 274.34 L 452.59 274.34 Z M 455.28 284.2 L 456.3 284.2 L 456.3 274.34 L 455.28 274.34 Z M 457.97 284.2 L 458.99 284.2 L 458.99 274.34 L 457.97 274.34 Z M 460.65 284.2 L 461.68 284.2 L 461.68 274.34 L 460.65 274.34 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 294px; margin-left: 453px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <font style="font-size: 7px;">
                                    Container
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="453" y="306" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cont...
                </text>
            </switch>
        </g>
        <path d="M 354.5 374 L 554.5 374 L 554.5 534 L 354.5 534 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 354.5 374 L 379.5 374 L 379.5 399 L 354.5 399 Z M 367.02 377.21 C 365.9 377.21 364.81 377.63 363.98 378.39 C 363.17 379.11 362.7 380.15 362.7 381.24 L 362.7 383.78 L 360.39 383.78 C 360.3 383.78 360.2 383.82 360.14 383.89 C 360.07 383.95 360.04 384.04 360.04 384.13 L 360.04 395.43 C 360.04 395.63 360.2 395.79 360.39 395.79 L 373.61 395.79 C 373.8 395.79 373.96 395.63 373.96 395.43 L 373.96 384.15 C 373.97 384.06 373.93 383.97 373.86 383.9 C 373.8 383.83 373.71 383.79 373.61 383.79 L 371.31 383.79 L 371.31 381.29 C 371.3 380.21 370.85 379.18 370.06 378.44 C 369.24 377.65 368.15 377.22 367.02 377.21 Z M 367.01 377.93 C 367.96 377.92 368.87 378.28 369.56 378.93 C 370.22 379.54 370.6 380.4 370.6 381.29 L 370.6 383.79 L 363.38 383.79 L 363.39 381.26 C 363.4 380.36 363.78 379.51 364.45 378.91 C 365.15 378.27 366.07 377.92 367.01 377.93 Z M 360.74 384.5 L 373.26 384.5 L 373.25 395.07 L 360.74 395.07 Z M 367.01 386.74 C 365.98 386.73 365.11 387.51 365.01 388.53 C 364.92 389.56 365.63 390.48 366.64 390.66 L 366.64 393.44 L 367.36 393.44 L 367.36 390.66 C 368.29 390.49 368.97 389.67 368.98 388.72 C 368.98 387.63 368.1 386.75 367.01 386.74 Z M 366.89 387.45 C 366.93 387.45 366.97 387.45 367.01 387.46 C 367.34 387.46 367.66 387.59 367.9 387.83 C 368.14 388.07 368.27 388.39 368.26 388.72 C 368.27 389.06 368.14 389.38 367.9 389.61 C 367.66 389.85 367.34 389.98 367.01 389.98 C 366.54 390.02 366.1 389.8 365.84 389.42 C 365.58 389.03 365.56 388.53 365.78 388.12 C 366 387.71 366.43 387.46 366.89 387.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 381px; margin-left: 387px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="387" y="393" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="407.5" y="425.5" width="94" height="57" fill="none" stroke="#ff8000" pointer-events="none"/>
        <path d="M 416.41 447.38 L 426.44 447.38 L 426.44 446.1 L 416.41 446.1 Z M 416.41 439.58 L 426.44 439.58 L 426.44 438.31 L 416.41 438.31 Z M 416.41 431.78 L 426.44 431.78 L 426.44 430.51 L 416.41 430.51 Z M 412.04 447.77 L 412.04 445.7 L 414.11 445.7 L 414.11 447.77 Z M 411.4 449.05 L 414.74 449.05 C 415.09 449.05 415.38 448.76 415.38 448.41 L 415.38 445.07 C 415.38 444.72 415.09 444.43 414.74 444.43 L 411.4 444.43 C 411.05 444.43 410.77 444.72 410.77 445.07 L 410.77 448.41 C 410.77 448.76 411.05 449.05 411.4 449.05 Z M 412.04 439.98 L 412.04 437.91 L 414.11 437.91 L 414.11 439.98 Z M 411.4 441.25 L 414.74 441.25 C 415.09 441.25 415.38 440.97 415.38 440.61 L 415.38 437.27 C 415.38 436.92 415.09 436.64 414.74 436.64 L 411.4 436.64 C 411.05 436.64 410.77 436.92 410.77 437.27 L 410.77 440.61 C 410.77 440.97 411.05 441.25 411.4 441.25 Z M 412.04 432.18 L 412.04 430.11 L 414.11 430.11 L 414.11 432.18 Z M 411.4 433.45 L 414.74 433.45 C 415.09 433.45 415.38 433.17 415.38 432.82 L 415.38 429.48 C 415.38 429.13 415.09 428.84 414.74 428.84 L 411.4 428.84 C 411.05 428.84 410.77 429.13 410.77 429.48 L 410.77 432.82 C 410.77 433.17 411.05 433.45 411.4 433.45 Z M 409.25 452.23 L 409.25 426.77 L 428.03 426.77 L 428.03 452.23 Z M 428.66 425.5 L 408.62 425.5 C 408.27 425.5 407.98 425.79 407.98 426.14 L 407.98 452.86 C 407.98 453.22 408.27 453.5 408.62 453.5 L 428.66 453.5 C 429.01 453.5 429.3 453.22 429.3 452.86 L 429.3 426.14 C 429.3 425.79 429.01 425.5 428.66 425.5 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 461px; margin-left: 419px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="text-align: justify;">
                                    <span style="font-size: 8px; background-color: initial;">
                                        Task
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="473" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Task
                </text>
            </switch>
        </g>
        <path d="M 463.85 448.54 L 442.36 448.54 C 442.08 448.54 441.85 448.77 441.85 449.05 L 441.85 462.49 C 441.85 462.77 442.08 463 442.36 463 L 463.85 463 C 464.14 463 464.36 462.77 464.36 462.49 L 464.36 449.05 C 464.36 448.77 464.14 448.54 463.85 448.54 Z M 442.87 461.97 L 442.87 449.57 L 463.34 449.57 L 463.34 461.97 Z M 444.53 460.7 L 445.56 460.7 L 445.56 450.84 L 444.53 450.84 Z M 447.22 460.7 L 448.24 460.7 L 448.24 450.84 L 447.22 450.84 Z M 449.91 460.7 L 450.93 460.7 L 450.93 450.84 L 449.91 450.84 Z M 452.59 460.7 L 453.62 460.7 L 453.62 450.84 L 452.59 450.84 Z M 455.28 460.7 L 456.3 460.7 L 456.3 450.84 L 455.28 450.84 Z M 457.97 460.7 L 458.99 460.7 L 458.99 450.84 L 457.97 450.84 Z M 460.65 460.7 L 461.68 460.7 L 461.68 450.84 L 460.65 450.84 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 453px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <font style="font-size: 7px;">
                                    Container
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="453" y="482" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cont...
                </text>
            </switch>
        </g>
        <path d="M 146 454.14 L 433.61 455.67" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 439.61 455.7 L 431.59 459.66 L 433.61 455.67 L 431.63 451.66 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 354.5 586 L 554.5 586 L 554.5 746 L 354.5 746 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 354.5 586 L 379.5 586 L 379.5 611 L 354.5 611 Z M 367.02 589.21 C 365.9 589.21 364.81 589.63 363.98 590.39 C 363.17 591.11 362.7 592.15 362.7 593.24 L 362.7 595.78 L 360.39 595.78 C 360.3 595.78 360.2 595.82 360.14 595.89 C 360.07 595.95 360.04 596.04 360.04 596.13 L 360.04 607.43 C 360.04 607.63 360.2 607.79 360.39 607.79 L 373.61 607.79 C 373.8 607.79 373.96 607.63 373.96 607.43 L 373.96 596.15 C 373.97 596.06 373.93 595.97 373.86 595.9 C 373.8 595.83 373.71 595.79 373.61 595.79 L 371.31 595.79 L 371.31 593.29 C 371.3 592.21 370.85 591.18 370.06 590.44 C 369.24 589.65 368.15 589.22 367.02 589.21 Z M 367.01 589.93 C 367.96 589.92 368.87 590.28 369.56 590.93 C 370.22 591.54 370.6 592.4 370.6 593.29 L 370.6 595.79 L 363.38 595.79 L 363.39 593.26 C 363.4 592.36 363.78 591.51 364.45 590.91 C 365.15 590.27 366.07 589.92 367.01 589.93 Z M 360.74 596.5 L 373.26 596.5 L 373.25 607.07 L 360.74 607.07 Z M 367.01 598.74 C 365.98 598.73 365.11 599.51 365.01 600.53 C 364.92 601.56 365.63 602.48 366.64 602.66 L 366.64 605.44 L 367.36 605.44 L 367.36 602.66 C 368.29 602.49 368.97 601.67 368.98 600.72 C 368.98 599.63 368.1 598.75 367.01 598.74 Z M 366.89 599.45 C 366.93 599.45 366.97 599.45 367.01 599.46 C 367.34 599.46 367.66 599.59 367.9 599.83 C 368.14 600.07 368.27 600.39 368.26 600.72 C 368.27 601.06 368.14 601.38 367.9 601.61 C 367.66 601.85 367.34 601.98 367.01 601.98 C 366.54 602.02 366.1 601.8 365.84 601.42 C 365.58 601.03 365.56 600.53 365.78 600.12 C 366 599.71 366.43 599.46 366.89 599.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 593px; margin-left: 387px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="387" y="605" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="407.5" y="637.5" width="94" height="57" fill="none" stroke="#ff8000" pointer-events="none"/>
        <path d="M 416.41 659.37 L 426.44 659.37 L 426.44 658.1 L 416.41 658.1 Z M 416.41 651.58 L 426.44 651.58 L 426.44 650.31 L 416.41 650.31 Z M 416.41 643.78 L 426.44 643.78 L 426.44 642.51 L 416.41 642.51 Z M 412.04 659.77 L 412.04 657.7 L 414.11 657.7 L 414.11 659.77 Z M 411.4 661.05 L 414.74 661.05 C 415.09 661.05 415.38 660.76 415.38 660.41 L 415.38 657.07 C 415.38 656.72 415.09 656.43 414.74 656.43 L 411.4 656.43 C 411.05 656.43 410.77 656.72 410.77 657.07 L 410.77 660.41 C 410.77 660.76 411.05 661.05 411.4 661.05 Z M 412.04 651.98 L 412.04 649.91 L 414.11 649.91 L 414.11 651.98 Z M 411.4 653.25 L 414.74 653.25 C 415.09 653.25 415.38 652.97 415.38 652.61 L 415.38 649.27 C 415.38 648.92 415.09 648.64 414.74 648.64 L 411.4 648.64 C 411.05 648.64 410.77 648.92 410.77 649.27 L 410.77 652.61 C 410.77 652.97 411.05 653.25 411.4 653.25 Z M 412.04 644.18 L 412.04 642.11 L 414.11 642.11 L 414.11 644.18 Z M 411.4 645.45 L 414.74 645.45 C 415.09 645.45 415.38 645.17 415.38 644.82 L 415.38 641.48 C 415.38 641.13 415.09 640.84 414.74 640.84 L 411.4 640.84 C 411.05 640.84 410.77 641.13 410.77 641.48 L 410.77 644.82 C 410.77 645.17 411.05 645.45 411.4 645.45 Z M 409.25 664.23 L 409.25 638.77 L 428.03 638.77 L 428.03 664.23 Z M 428.66 637.5 L 408.62 637.5 C 408.27 637.5 407.98 637.79 407.98 638.14 L 407.98 664.86 C 407.98 665.22 408.27 665.5 408.62 665.5 L 428.66 665.5 C 429.01 665.5 429.3 665.22 429.3 664.86 L 429.3 638.14 C 429.3 637.79 429.01 637.5 428.66 637.5 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 672px; margin-left: 419px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="text-align: justify;">
                                    <span style="font-size: 8px; background-color: initial;">
                                        Task
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="419" y="684" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Task
                </text>
            </switch>
        </g>
        <path d="M 463.85 658.54 L 442.36 658.54 C 442.08 658.54 441.85 658.77 441.85 659.05 L 441.85 672.49 C 441.85 672.77 442.08 673 442.36 673 L 463.85 673 C 464.14 673 464.36 672.77 464.36 672.49 L 464.36 659.05 C 464.36 658.77 464.14 658.54 463.85 658.54 Z M 442.87 671.97 L 442.87 659.57 L 463.34 659.57 L 463.34 671.97 Z M 444.53 670.7 L 445.56 670.7 L 445.56 660.84 L 444.53 660.84 Z M 447.22 670.7 L 448.24 670.7 L 448.24 660.84 L 447.22 660.84 Z M 449.91 670.7 L 450.93 670.7 L 450.93 660.84 L 449.91 660.84 Z M 452.59 670.7 L 453.62 670.7 L 453.62 660.84 L 452.59 660.84 Z M 455.28 670.7 L 456.3 670.7 L 456.3 660.84 L 455.28 660.84 Z M 457.97 670.7 L 458.99 670.7 L 458.99 660.84 L 457.97 660.84 Z M 460.65 670.7 L 461.68 670.7 L 461.68 660.84 L 460.65 660.84 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 680px; margin-left: 453px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <font style="font-size: 7px;">
                                    Container
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="453" y="692" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cont...
                </text>
            </switch>
        </g>
        <rect x="345" y="352" width="220" height="198" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="454.5" y="369.5">
                Availability Zone 1c
            </text>
        </g>
        <rect x="345" y="563" width="220" height="195" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="454.5" y="580.5">
                Availability Zone 1d
            </text>
        </g>
        <path d="M 144.97 454 L 434.68 289.72" fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 439.9 286.76 L 434.92 294.18 L 434.68 289.72 L 430.97 287.22 Z" fill="#000000" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 567.25 185.75 L 622.75 185.75 L 622.75 335.75 L 615 335.75 L 595 365.75 L 595 335.75 L 567.25 335.75 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,595,275.75)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 276px; margin-left: 536px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                サービスで設定したタスク数を維持するよう自動的に復旧が行われる
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="610" y="279" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    サービスで設定したタスク数を維持するよう自動的に復旧が行われる
                </text>
            </switch>
        </g>
        <path d="M 567.25 364.75 L 622.75 364.75 L 622.75 514.75 L 615 514.75 L 595 544.75 L 595 514.75 L 567.25 514.75 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" transform="rotate(90,595,454.75)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 455px; margin-left: 536px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                AZ単位で障害が発生した際は別のAZに対して再配置が行われる
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="610" y="458" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    AZ単位で障害が発生した際は別のAZに対して再配置が行われる
                </text>
            </switch>
        </g>
        <path d="M 92.5 563 L 146 563 L 146 616.5 L 92.5 616.5 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 129.19 592.38 C 129.44 592.38 129.67 592.26 129.81 592.05 C 130.3 591.35 134.54 585.13 134.54 582.33 C 134.54 579.24 132.24 576.92 129.19 576.92 C 126.14 576.92 123.84 579.24 123.84 582.33 C 123.84 585.13 128.08 591.35 128.56 592.05 C 128.7 592.26 128.94 592.38 129.19 592.38 Z M 129.19 578.46 C 131.37 578.46 133.01 580.13 133.01 582.33 C 133.01 584.02 130.64 588 129.19 590.23 C 127.74 588.01 125.37 584.02 125.37 582.33 C 125.37 580.13 127.01 578.46 129.19 578.46 Z M 132.24 582.33 C 132.24 580.71 130.79 579.24 129.19 579.24 C 127.59 579.24 126.13 580.71 126.13 582.33 C 126.13 583.95 127.59 585.42 129.19 585.42 C 130.79 585.42 132.24 583.95 132.24 582.33 Z M 127.66 582.33 C 127.66 581.56 128.43 580.78 129.19 580.78 C 129.94 580.78 130.72 581.56 130.72 582.33 C 130.72 583.1 129.94 583.88 129.19 583.88 C 128.43 583.88 127.66 583.1 127.66 582.33 Z M 134.79 588.19 L 134.29 589.65 C 135.57 590.1 137.46 591.09 138.46 593.16 L 126.13 593.16 C 125.71 593.16 125.37 593.5 125.37 593.93 L 125.37 603.2 L 123.07 603.2 L 123.07 600.11 C 123.07 599.69 122.73 599.34 122.31 599.34 L 116.96 599.34 C 116.54 599.34 116.2 599.69 116.2 600.11 L 116.2 603.19 L 113.9 603.19 L 113.9 594.7 C 113.9 594.4 113.73 594.12 113.45 593.99 C 113.17 593.87 112.85 593.92 112.62 594.13 L 103.74 602.37 C 103.22 602.12 102.74 601.81 102.3 601.46 L 113.64 591.42 C 113.81 591.27 113.9 591.06 113.9 590.84 L 113.9 588.52 C 113.9 588.09 113.56 587.74 113.14 587.74 L 104.72 587.74 C 104.72 587.68 104.72 587.63 104.72 587.57 C 104.73 587.4 104.74 587.23 104.74 587.08 C 104.74 586.28 104.88 585.46 105.12 584.65 L 113.14 584.65 C 113.56 584.65 113.9 584.3 113.9 583.88 L 113.9 577.29 C 114.67 577.24 115.44 577.3 116.2 577.44 L 116.2 590.06 C 116.2 590.49 116.54 590.84 116.96 590.84 L 122.31 590.84 C 122.73 590.84 123.07 590.49 123.07 590.06 L 123.07 585.42 L 121.55 585.42 L 121.55 589.29 L 117.73 589.29 L 117.73 577.88 C 119.17 578.42 120.55 579.31 121.77 580.56 L 122.85 579.46 C 119.25 575.81 114.49 574.75 110.11 576.62 C 106.24 578.28 103.21 582.87 103.21 587.08 C 103.21 587.21 103.2 587.36 103.2 587.51 C 103.19 587.65 103.19 587.8 103.19 587.94 C 100.96 588.67 97.86 590.62 97.86 595.78 L 97.85 595.94 C 97.85 596.05 97.85 596.14 97.86 596.3 C 98.12 600.48 101.68 604.1 106.14 604.72 C 106.17 604.72 106.21 604.72 106.24 604.72 L 116.88 604.74 C 116.91 604.74 116.93 604.75 116.96 604.75 L 122.31 604.75 C 122.33 604.75 122.35 604.74 122.37 604.74 L 133 604.75 L 133 604.75 C 133.02 604.75 133.05 604.75 133.07 604.75 C 133.15 604.74 140.65 603.88 140.65 596.25 C 140.65 590.83 136.55 588.81 134.79 588.19 Z M 117.73 600.89 L 121.55 600.89 L 121.55 603.19 L 117.73 603.19 Z M 110.71 578.04 C 111.26 577.81 111.82 577.63 112.38 577.51 L 112.38 583.1 L 105.72 583.1 C 106.79 580.89 108.63 578.94 110.71 578.04 Z M 99.38 595.99 L 99.39 595.78 C 99.39 592.31 100.96 590.13 104.07 589.29 L 112.38 589.29 L 112.38 590.48 L 101.18 600.4 C 100.14 599.21 99.48 597.75 99.38 596.2 C 99.38 596.11 99.38 596.06 99.38 595.99 Z M 105.35 602.98 L 112.38 596.46 L 112.38 603.19 L 106.3 603.18 C 105.97 603.13 105.66 603.06 105.35 602.98 Z M 132.95 603.21 L 126.9 603.2 L 126.9 594.7 L 138.97 594.7 C 139.07 595.18 139.12 595.69 139.12 596.25 C 139.12 602.29 133.5 603.14 132.95 603.21 Z M 122.31 591.61 L 116.9 591.61 C 116.48 591.61 116.14 591.96 116.14 592.38 L 116.14 597.79 C 116.14 598.22 116.48 598.57 116.9 598.57 L 122.31 598.57 C 122.73 598.57 123.07 598.22 123.07 597.79 L 123.07 592.38 C 123.07 591.96 122.73 591.61 122.31 591.61 Z M 117.67 597.02 L 117.67 593.16 L 121.55 593.16 L 121.55 597.02 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 624px; margin-left: 119px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Cloud Map
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="119" y="636" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cloud Map
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 538px; margin-left: 119px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                または
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="119" y="542" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    または
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>