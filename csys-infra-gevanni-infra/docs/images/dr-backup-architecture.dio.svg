<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="781px" height="461px" viewBox="-0.5 -0.5 781 461" content="&lt;mxfile&gt;&lt;diagram id=&quot;BM4EBFp2VpfTbR8uryxK&quot; name=&quot;250115&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="60" y="80" width="160" height="320" fill="none" stroke="#277116" stroke-dasharray="8 8" pointer-events="all"/>
        <path d="M 60 80 L 90 80 L 90 110 L 60 110 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 80.25 95.16 C 80.44 96.46 80.11 97.75 79.32 98.81 C 78.54 99.86 77.39 100.55 76.09 100.74 C 75.84 100.77 75.6 100.79 75.36 100.79 C 74.31 100.79 73.29 100.45 72.43 99.81 C 71.19 98.88 70.46 97.46 70.45 95.9 L 71.31 95.89 C 71.32 97.18 71.92 98.36 72.95 99.13 C 73.82 99.78 74.89 100.05 75.96 99.89 C 77.04 99.73 77.99 99.17 78.63 98.3 C 79.28 97.42 79.55 96.35 79.4 95.28 C 79.24 94.2 78.68 93.26 77.81 92.61 C 76.28 91.47 74.04 91.63 72.57 92.86 L 73.29 92.85 L 73.29 93.71 L 71.57 93.71 L 71.57 93.71 C 71.46 93.71 71.35 93.67 71.27 93.59 C 71.19 93.51 71.14 93.4 71.14 93.29 L 71.14 91.58 L 72 91.58 L 72 92.22 C 72.71 91.62 73.6 91.2 74.56 91.05 C 75.91 90.83 77.28 91.15 78.32 91.92 C 79.37 92.7 80.06 93.85 80.25 95.16 M 81 102.71 C 80.8 102.71 80.61 102.74 80.42 102.8 L 79.2 101.17 L 78.51 101.69 L 79.64 103.19 C 79.28 103.48 79 103.9 78.9 104.43 L 71.11 104.43 C 71.02 103.89 70.77 103.46 70.39 103.16 L 71.49 101.68 L 70.8 101.17 L 69.6 102.79 C 69.41 102.75 69.21 102.72 69 102.72 C 68.82 102.72 68.64 102.74 68.48 102.77 L 65.98 94.44 C 66.36 94.31 66.67 94.08 66.89 93.77 L 68.84 94.54 L 69.16 93.74 L 67.22 92.99 C 67.26 92.81 67.29 92.63 67.29 92.43 C 67.29 91.96 67.17 91.55 66.97 91.22 L 73.44 86.7 C 73.72 86.98 74.11 87.17 74.57 87.25 L 74.57 89.43 L 75.43 89.43 L 75.43 87.25 C 75.9 87.18 76.28 86.99 76.57 86.7 L 83.01 91.27 C 82.82 91.6 82.71 91.98 82.71 92.43 C 82.71 92.61 82.74 92.79 82.78 92.97 L 80.84 93.74 L 81.16 94.54 L 83.15 93.75 C 83.41 94.1 83.78 94.37 84.26 94.49 L 81.13 102.72 C 81.09 102.72 81.05 102.71 81 102.71 M 81 106.14 C 80.16 106.14 79.71 105.7 79.71 104.86 C 79.71 104.05 80.37 103.57 81 103.57 C 81.83 103.57 82.29 104.03 82.29 104.86 C 82.29 105.7 81.84 106.14 81 106.14 M 69 106.14 C 68.18 106.14 67.71 105.67 67.71 104.86 C 67.71 104.03 68.17 103.57 69 103.57 C 70.06 103.57 70.29 104.27 70.29 104.86 C 70.29 105.69 69.83 106.14 69 106.14 M 63.86 92.43 C 63.86 91.6 64.31 91.14 65.14 91.14 C 65.97 91.14 66.43 91.6 66.43 92.43 C 66.43 93.27 65.98 93.71 65.14 93.71 C 64.29 93.71 63.86 93.28 63.86 92.43 M 75 83.86 C 75.84 83.86 76.29 84.3 76.29 85.14 C 76.29 85.98 75.84 86.43 75 86.43 C 74.17 86.43 73.71 85.97 73.71 85.14 C 73.71 84.31 74.17 83.86 75 83.86 M 84.86 91.14 C 85.69 91.14 86.14 91.6 86.14 92.43 C 86.14 93.26 85.69 93.71 84.86 93.71 C 84.02 93.71 83.57 93.05 83.57 92.43 C 83.57 91.61 84.04 91.14 84.86 91.14 M 87 92.43 C 87 91.13 86.16 90.29 84.86 90.29 C 84.37 90.29 83.94 90.41 83.6 90.64 L 77.01 85.97 C 77.09 85.72 77.14 85.45 77.14 85.14 C 77.14 83.82 76.32 83 75 83 C 73.7 83 72.86 83.84 72.86 85.14 C 72.86 85.44 72.91 85.71 72.99 85.96 L 66.36 90.6 C 66.03 90.4 65.62 90.29 65.14 90.29 C 63.84 90.29 63 91.13 63 92.43 C 63 93.75 63.82 94.56 65.13 94.57 L 67.68 103.1 C 67.16 103.47 66.86 104.08 66.86 104.86 C 66.86 106.16 67.7 107 69 107 C 70.15 107 70.94 106.34 71.11 105.29 L 78.89 105.29 C 79.06 106.34 79.85 107 81 107 C 82.3 107 83.14 106.16 83.14 104.86 C 83.14 103.92 82.7 103.23 81.98 102.91 L 85.16 94.55 C 86.29 94.42 87 93.62 87 92.43" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 95px; margin-left: 92px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                AWS Backup
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="92" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS B...
                </text>
            </switch>
        </g>
        <path d="M 155.38 180 L 293.63 180" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 298.88 180 L 291.88 183.5 L 293.63 180 L 291.88 176.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 227px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                バックアップ
                                <br/>
                                スケジュール設定
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="227" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    バックアップ
スケジュール設定
                </text>
            </switch>
        </g>
        <rect x="124.62" y="160" width="30.77" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 153.58 198.1 L 153.58 169.14 L 147.24 169.14 C 146.74 169.14 146.34 168.73 146.34 168.24 L 146.34 161.9 L 126.42 161.9 L 126.42 198.1 Z M 148.14 167.33 L 152.3 167.33 L 148.14 163.18 Z M 155.38 168.24 L 155.38 199.01 C 155.38 199.5 154.98 199.91 154.48 199.91 L 125.52 199.91 C 125.02 199.91 124.62 199.5 124.62 199.01 L 124.62 161 C 124.62 160.5 125.02 160.09 125.52 160.09 L 147.24 160.09 L 147.24 160.1 C 147.48 160.1 147.71 160.18 147.88 160.36 L 155.12 167.6 C 155.29 167.77 155.38 168 155.38 168.24 Z M 149.57 191.24 L 148.07 192.75 L 146.56 191.24 L 145.28 192.52 L 146.79 194.03 L 145.28 195.54 L 146.56 196.82 L 148.07 195.31 L 149.57 196.82 L 150.85 195.54 L 149.35 194.03 L 150.85 192.52 Z M 132.31 169.14 C 133.06 169.14 133.66 168.53 133.66 167.78 C 133.66 167.03 133.06 166.42 132.31 166.42 C 131.56 166.42 130.95 167.03 130.95 167.78 C 130.95 168.53 131.56 169.14 132.31 169.14 Z M 132.31 170.95 C 130.56 170.95 129.14 169.53 129.14 167.78 C 129.14 166.04 130.56 164.62 132.31 164.62 C 134.05 164.62 135.47 166.04 135.47 167.78 C 135.47 169.53 134.05 170.95 132.31 170.95 Z M 141.38 191.65 C 140.9 191.72 140.42 191.76 139.94 191.76 C 137.82 191.76 135.78 191.08 134.05 189.8 C 131.55 187.93 130.1 185.07 130.08 181.94 L 131.89 181.93 C 131.91 184.48 133.09 186.82 135.14 188.35 C 136.86 189.63 138.99 190.17 141.12 189.86 C 143.25 189.55 145.14 188.43 146.43 186.7 C 149.08 183.13 148.35 178.07 144.78 175.41 C 141.45 172.93 136.82 173.41 134.06 176.38 L 135.47 176.38 L 135.47 178.19 L 131.86 178.19 C 131.35 178.19 130.95 177.78 130.95 177.29 L 130.95 173.66 L 132.76 173.66 L 132.76 175.12 C 136.15 171.51 141.8 170.93 145.86 173.96 C 150.23 177.21 151.13 183.41 147.88 187.78 C 146.3 189.9 143.99 191.27 141.38 191.65 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 207px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Plan
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="219" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backu...
                </text>
            </switch>
        </g>
        <path d="M 160 340 L 633.63 340" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 638.88 340 L 631.88 343.5 L 633.63 340 L 631.88 336.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 340px; margin-left: 400px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                レプリケーション
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="343" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    レプリケーション
                </text>
            </switch>
        </g>
        <rect x="120" y="320" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 127.54 339.05 L 125.73 339.06 C 125.76 343.62 127.87 347.79 131.52 350.51 C 134.09 352.43 137.11 353.36 140.1 353.36 C 144.49 353.36 148.83 351.36 151.65 347.57 C 156.4 341.21 155.08 332.18 148.72 327.44 C 142.56 322.85 133.91 323.95 129.06 329.8 L 129.09 327.29 L 127.27 327.26 L 127.21 331.81 C 127.21 332.05 127.3 332.28 127.47 332.45 C 127.64 332.63 127.87 332.72 128.11 332.73 L 132.71 332.79 L 132.74 330.97 L 130.47 330.94 C 134.71 325.85 142.25 324.89 147.63 328.89 C 153.19 333.04 154.34 340.93 150.2 346.49 C 146.05 352.05 138.16 353.2 132.6 349.05 C 129.42 346.68 127.57 343.03 127.54 339.05 Z M 140 335.45 C 137.99 335.45 136.36 337.09 136.36 339.09 C 136.36 341.1 137.99 342.73 140 342.73 C 142.01 342.73 143.64 341.1 143.64 339.09 C 143.64 337.09 142.01 335.45 140 335.45 Z M 133.59 344.35 L 135.61 342.32 C 134.95 341.41 134.55 340.3 134.55 339.09 C 134.55 337.84 134.98 336.68 135.69 335.76 L 133.59 333.81 L 134.83 332.48 L 137.03 334.52 C 137.88 333.97 138.9 333.64 140 333.64 C 141.22 333.64 142.34 334.04 143.25 334.72 L 145.44 332.48 L 146.74 333.75 L 144.51 336.03 C 145.11 336.91 145.45 337.96 145.45 339.09 C 145.45 340.23 145.1 341.29 144.5 342.16 L 146.74 344.32 L 145.48 345.63 L 143.24 343.47 C 142.33 344.14 141.21 344.55 140 344.55 C 138.86 344.55 137.79 344.19 136.92 343.59 L 134.87 345.63 Z M 150 358.18 L 153.64 358.18 L 153.64 357.27 L 150 357.27 Z M 126.36 358.18 L 130 358.18 L 130 357.27 L 126.36 357.27 Z M 160 320.91 L 160 356.36 C 160 356.87 159.59 357.27 159.09 357.27 L 155.45 357.27 L 155.45 359.09 C 155.45 359.59 155.05 360 154.55 360 L 149.09 360 C 148.59 360 148.18 359.59 148.18 359.09 L 148.18 357.27 L 131.82 357.27 L 131.82 359.09 C 131.82 359.59 131.41 360 130.91 360 L 125.45 360 C 124.95 360 124.55 359.59 124.55 359.09 L 124.55 357.27 L 120.91 357.27 C 120.41 357.27 120 356.87 120 356.36 L 120 348.18 L 121.82 348.18 L 121.82 355.45 L 158.18 355.45 L 158.18 321.82 L 121.82 321.82 L 121.82 328.18 L 120 328.18 L 120 320.91 C 120 320.41 120.41 320 120.91 320 L 159.09 320 C 159.59 320 160 320.41 160 320.91 Z M 120 342.73 L 121.82 342.73 L 121.82 333.64 L 120 333.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 367px; margin-left: 140px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="379" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 20 40 L 520 40 L 520 440 L 20 440 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 28.06 43.47 C 27.47 43.47 27 43.95 27 44.53 C 27 44.99 27.3 45.39 27.71 45.53 L 27.71 60.82 L 25.95 60.82 L 25.95 61.57 L 30.16 61.57 L 30.16 60.82 L 28.46 60.82 L 28.46 52.75 L 39.84 52.75 L 37.19 49.59 L 39.83 46.36 L 28.46 46.36 L 28.46 45.51 C 28.85 45.35 29.12 44.97 29.12 44.53 C 29.12 43.95 28.64 43.47 28.06 43.47 Z M 28.06 44.22 C 28.23 44.22 28.37 44.35 28.37 44.53 C 28.37 44.71 28.23 44.84 28.06 44.84 C 27.88 44.84 27.75 44.71 27.75 44.53 C 27.75 44.35 27.88 44.22 28.06 44.22 Z M 28.46 47.11 L 38.25 47.11 L 36.22 49.6 L 38.23 52 L 28.46 52 Z M 20 65 L 20 40 L 45 40 L 45 65 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 52px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Primary Region
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="59" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Primary Region
                </text>
            </switch>
        </g>
        <path d="M 560 40 L 760 40 L 760 440 L 560 440 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 568.06 43.47 C 567.47 43.47 567 43.95 567 44.53 C 567 44.99 567.3 45.39 567.71 45.53 L 567.71 60.82 L 565.95 60.82 L 565.95 61.57 L 570.16 61.57 L 570.16 60.82 L 568.46 60.82 L 568.46 52.75 L 579.84 52.75 L 577.19 49.59 L 579.83 46.36 L 568.46 46.36 L 568.46 45.51 C 568.85 45.35 569.12 44.97 569.12 44.53 C 569.12 43.95 568.64 43.47 568.06 43.47 Z M 568.06 44.22 C 568.23 44.22 568.37 44.35 568.37 44.53 C 568.37 44.71 568.23 44.84 568.06 44.84 C 567.88 44.84 567.75 44.71 567.75 44.53 C 567.75 44.35 567.88 44.22 568.06 44.22 Z M 568.46 47.11 L 578.25 47.11 L 576.22 49.6 L 578.23 52 L 568.46 52 Z M 560 65 L 560 40 L 585 40 L 585 65 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 47px; margin-left: 592px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Seccondary Region
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="592" y="59" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Seccondary Region
                </text>
            </switch>
        </g>
        <path d="M 300 237.6 L 165.36 323.77" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 160.94 326.6 L 164.95 319.88 L 165.36 323.77 L 168.72 325.77 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 282px; margin-left: 230px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                バックアップ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="230" y="286" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    バックアップ
                </text>
            </switch>
        </g>
        <path d="M 330 160 L 370 160 L 370 200 L 330 200 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 352.81 167.89 L 351.15 167.89 L 351.15 166.77 L 352.81 166.77 L 352.81 165.1 L 353.92 165.1 L 353.92 166.77 L 355.58 166.77 L 355.58 167.89 L 353.92 167.89 L 353.92 169.56 L 352.81 169.56 Z M 359.45 174.01 L 357.79 174.01 L 357.79 172.9 L 359.45 172.9 L 359.45 171.23 L 360.55 171.23 L 360.55 172.9 L 362.21 172.9 L 362.21 174.01 L 360.55 174.01 L 360.55 175.68 L 359.45 175.68 Z M 356.57 191.6 C 355.51 188.92 353.07 186.47 350.4 185.4 C 353.07 184.34 355.51 181.89 356.57 179.2 C 357.62 181.89 360.06 184.34 362.73 185.4 C 360.06 186.47 357.62 188.92 356.57 191.6 Z M 365.45 184.85 C 361.56 184.85 357.12 180.38 357.12 176.47 C 357.12 176.16 356.87 175.91 356.57 175.91 C 356.26 175.91 356.01 176.16 356.01 176.47 C 356.01 180.38 351.58 184.85 347.68 184.85 C 347.38 184.85 347.13 185.1 347.13 185.4 C 347.13 185.71 347.38 185.96 347.68 185.96 C 351.58 185.96 356.01 190.42 356.01 194.34 C 356.01 194.65 356.26 194.9 356.57 194.9 C 356.87 194.9 357.12 194.65 357.12 194.34 C 357.12 190.42 361.56 185.96 365.45 185.96 C 365.75 185.96 366 185.71 366 185.4 C 366 185.1 365.75 184.85 365.45 184.85 Z M 335.11 173.88 C 336.72 175.06 339.85 175.68 342.85 175.68 C 345.85 175.68 348.99 175.06 350.6 173.88 L 350.6 179.21 C 349.8 180.28 346.87 181.33 342.96 181.33 C 338.47 181.33 335.11 179.91 335.11 178.65 Z M 342.85 169.56 C 347.65 169.56 350.6 171.02 350.6 172.06 C 350.6 173.11 347.65 174.57 342.85 174.57 C 338.05 174.57 335.11 173.11 335.11 172.06 C 335.11 171.02 338.05 169.56 342.85 169.56 Z M 350.6 190.45 C 350.6 191.73 347.28 193.17 342.85 193.17 C 338.42 193.17 335.11 191.73 335.11 190.45 L 335.11 186.89 C 336.74 188.14 339.93 188.8 342.99 188.8 C 345.12 188.8 347.18 188.49 348.79 187.94 L 348.44 186.89 C 346.94 187.4 345.01 187.68 342.99 187.68 C 338.48 187.68 335.11 186.27 335.11 185.01 L 335.11 180.54 C 336.73 181.78 339.91 182.44 342.96 182.44 C 346.23 182.44 349.03 181.76 350.6 180.69 L 350.6 182.36 L 351.7 182.36 L 351.7 172.06 C 351.7 169.71 347.14 168.44 342.85 168.44 C 338.73 168.44 334.38 169.61 334.03 171.78 L 334 171.78 L 334 190.45 C 334 192.94 338.56 194.28 342.85 194.28 C 347.14 194.28 351.7 192.94 351.7 190.45 L 351.7 188.48 L 350.6 188.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 207px; margin-left: 350px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Aurora
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="219" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora
                </text>
            </switch>
        </g>
        <image x="409.5" y="159.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURXqhFoKnJZOzQpu5UO7z4v////f58N7nxbTKfKzEboutM6S+X+bt09XitsXWmc3cp73Qi5boi3sAAAAJcEhZcwAAFxEAABcRAcom8z8AAASQSURBVGhD7ZmLrqM6DEUbaENLS+H/v3b2dsyjPOOQI11dsaSRaM/AAiexHXq7uLi4uLi4uPif44pyitOvrTg9X9Fvj3D3xw+x580p9XzF69cHzM7KZY+8TjjrPpBqL/R8IFe02Cv9lIOnXNFiz6h/eblgtP1eZdQXkHv8i7ff8ulFXmLoDfZs+hpe/74Z7Zn07oPLvG5mexa9a3CRJw7M9gx6kX95ZLef1397eYr9rJ5Zpg1FKsV+Tk/5Rytkkn1F78qu+jB73Ztu94JMcb083n77KcYzffmleMB/a/3DAmaZ+/BXtAxJfQL1bz0uft1CmFVLcJ4v9PgEFbOF8BS3r7qyqOv63fHG8Ln/8y+uQorLgF7EtXRVz2kEnxjPzcePHOgoJHN45q0fpHafSgoxiLxamWM1M/lHP/wVkrbWpy1H5G+f/g2Dpq0l1Hd6/Bewyx4yxxKOSs45NqPbX7ys4iE/umIz/SRTY6HvhrbGw79udccZ+GjWE0AyePT7fqLE0PsxEX6yBgCjfjCrnHqVHDm2BxPeHz2NJEL/xH9789Dvh8oCAt/o4SZ8+EqVLxwfnhANFtTxcm4GecgO2YYewx5RrupJsDH3Wz08C5eT8UkQ+8j9+iG062EsnAWZQp9gZ+gXxTiJkuXNuoCk7nWnH7+U5GkOI+8YbDedMbiQPf1W67iJ+0rPdarySkPzqF5JiasOTV9630H550Rn+qI/VT/Zh6XC+09LuqzqG61cPJz8SdHDiWtVfXgDqp8P4J5GDy04PPpKl8L9WSBuQJn2EpodjPpamx62T0LcRRHChJHHhFlZ5gWs8vYTIYgrY9iRJ4R+UVeLN0CnESLCMvZaYZndcJ/2RgsPOb1S2ESSEEc80jqLiCBXmwees2U64zVzg5A8Z33khLkeM8Vc72Z2ltlPRRr99imf5nBFzIKPCXTWjlZtZys1AdNllglgty85PMXkMphkcesbcf40Qj9nF9M3ht/hsth7wggwa9nLPKbZ+ObshJ17ETkwwYEfI2axN13H/aRmuLRcx9kyPrzFjjnGDX+4dVZK+7CHpDqsXaOd4ZYl4vD5YP+7ARNMP/GMdsRNwi0ZMuXRAftZrTQ2O+c5ndIRm1tSRX7XuEuqsNlRnb1zZcPMl96biV6Kms0u1RnlEJzqzXgJlhWTnUWhlH10lTjmPUUr1dlkD02A802uFygmO/6d2MOsYLFzpdoz+x4WO/Jc5tfGFjvIG3irPW/gjfaUoraHzX5ylU8oxWqyS3krcwTg7UVrstNbhvPOwV/BPLpLk52BR6pP0RdtO9ZEpnn5XQ32h7bs+/BupbDwNgb9t23jsu7kd1jHrHWX02iPRW9+ql92+RuMdmlM9E0/y1YkHChhorfb5Y3VsIEpdI96zBjiUW+2sx+cvPVOYtBb7QVOOP/SqNcb7fJ6JrUfnKB6mx25ItNb5qA32SXH2Le9q4jeYid5frsn1AOLfVi0GQh6k11ei5H0vrTVK3D1GCM/kB4EZquRuOtwDzYlkz3610l9C6ykJ5xaryBk7vQuLi4uLi4uLv5j3G7/AGTDLvVz1z2rAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="418" y="208" width="25" height="15" stroke-width="0"/>
            <text x="429.5" y="217.5">
                EFS
            </text>
        </g>
        <rect x="300" y="120" width="180" height="120" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="8 8" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 117px; margin-left: 301px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                バックアップ対象
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="390" y="117" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    バックアップ対象
                </text>
            </switch>
        </g>
        <path d="M 600 240 L 630 240 L 630 270 L 600 270 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 620.25 255.16 C 620.44 256.46 620.11 257.75 619.32 258.81 C 618.54 259.86 617.39 260.55 616.09 260.74 C 615.84 260.77 615.6 260.79 615.36 260.79 C 614.31 260.79 613.29 260.45 612.43 259.81 C 611.19 258.88 610.46 257.46 610.45 255.9 L 611.31 255.89 C 611.32 257.18 611.92 258.36 612.95 259.13 C 613.82 259.78 614.89 260.05 615.96 259.89 C 617.04 259.73 617.99 259.17 618.63 258.3 C 619.28 257.42 619.55 256.35 619.4 255.28 C 619.24 254.2 618.68 253.26 617.8 252.61 C 616.28 251.47 614.04 251.63 612.57 252.86 L 613.29 252.85 L 613.29 253.71 L 611.57 253.71 L 611.57 253.71 C 611.46 253.71 611.35 253.67 611.27 253.59 C 611.19 253.51 611.14 253.4 611.14 253.29 L 611.14 251.58 L 612 251.58 L 612 252.22 C 612.71 251.62 613.6 251.2 614.56 251.05 C 615.91 250.83 617.28 251.15 618.32 251.92 C 619.37 252.71 620.06 253.85 620.25 255.16 M 621 262.71 C 620.8 262.71 620.61 262.74 620.42 262.8 L 619.2 261.17 L 618.51 261.69 L 619.64 263.19 C 619.27 263.48 619 263.9 618.9 264.43 L 611.11 264.43 C 611.02 263.89 610.77 263.46 610.39 263.16 L 611.49 261.68 L 610.8 261.17 L 609.6 262.79 C 609.41 262.75 609.21 262.72 609 262.72 C 608.82 262.72 608.64 262.74 608.48 262.77 L 605.98 254.44 C 606.36 254.31 606.67 254.08 606.89 253.77 L 608.84 254.54 L 609.16 253.74 L 607.22 252.99 C 607.26 252.81 607.29 252.63 607.29 252.43 C 607.29 251.96 607.17 251.55 606.97 251.22 L 613.44 246.7 C 613.72 246.98 614.11 247.17 614.57 247.25 L 614.57 249.43 L 615.43 249.43 L 615.43 247.25 C 615.9 247.18 616.28 246.99 616.57 246.7 L 623.01 251.27 C 622.82 251.6 622.71 251.98 622.71 252.43 C 622.71 252.61 622.74 252.79 622.78 252.97 L 620.84 253.74 L 621.16 254.54 L 623.15 253.75 C 623.41 254.1 623.78 254.37 624.26 254.49 L 621.13 262.73 C 621.09 262.72 621.04 262.71 621 262.71 M 621 266.14 C 620.16 266.14 619.71 265.7 619.71 264.86 C 619.71 264.05 620.37 263.57 621 263.57 C 621.83 263.57 622.29 264.03 622.29 264.86 C 622.29 265.7 621.84 266.14 621 266.14 M 609 266.14 C 608.18 266.14 607.71 265.67 607.71 264.86 C 607.71 264.03 608.17 263.57 609 263.57 C 610.06 263.57 610.29 264.27 610.29 264.86 C 610.29 265.69 609.83 266.14 609 266.14 M 603.86 252.43 C 603.86 251.6 604.31 251.14 605.14 251.14 C 605.97 251.14 606.43 251.6 606.43 252.43 C 606.43 253.27 605.98 253.71 605.14 253.71 C 604.29 253.71 603.86 253.28 603.86 252.43 M 615 243.86 C 615.84 243.86 616.29 244.3 616.29 245.14 C 616.29 245.98 615.84 246.43 615 246.43 C 614.17 246.43 613.71 245.97 613.71 245.14 C 613.71 244.31 614.17 243.86 615 243.86 M 624.86 251.14 C 625.69 251.14 626.14 251.6 626.14 252.43 C 626.14 253.26 625.69 253.71 624.86 253.71 C 624.02 253.71 623.57 253.05 623.57 252.43 C 623.57 251.61 624.04 251.14 624.86 251.14 M 627 252.43 C 627 251.13 626.16 250.29 624.86 250.29 C 624.37 250.29 623.94 250.41 623.6 250.64 L 617.01 245.97 C 617.09 245.72 617.14 245.45 617.14 245.14 C 617.14 243.82 616.32 243 615 243 C 613.7 243 612.86 243.84 612.86 245.14 C 612.86 245.44 612.91 245.71 612.99 245.96 L 606.36 250.6 C 606.03 250.4 605.62 250.29 605.14 250.29 C 603.84 250.29 603 251.13 603 252.43 C 603 253.75 603.82 254.56 605.13 254.57 L 607.68 263.1 C 607.16 263.47 606.86 264.08 606.86 264.86 C 606.86 266.16 607.7 267 609 267 C 610.15 267 610.94 266.34 611.11 265.29 L 618.89 265.29 C 619.06 266.34 619.85 267 621 267 C 622.3 267 623.14 266.16 623.14 264.86 C 623.14 263.92 622.7 263.23 621.98 262.91 L 625.16 254.55 C 626.29 254.42 627 253.62 627 252.43" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 255px; margin-left: 632px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                AWS Backup
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="632" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS B...
                </text>
            </switch>
        </g>
        <rect x="600" y="240" width="120" height="160" fill="none" stroke="#277116" stroke-dasharray="8 8" pointer-events="all"/>
        <rect x="640" y="320" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 647.54 339.05 L 645.73 339.06 C 645.76 343.62 647.87 347.79 651.52 350.51 C 654.09 352.43 657.11 353.36 660.1 353.36 C 664.49 353.36 668.83 351.36 671.65 347.57 C 676.4 341.21 675.08 332.18 668.72 327.44 C 662.56 322.85 653.91 323.95 649.06 329.8 L 649.09 327.29 L 647.27 327.26 L 647.21 331.81 C 647.21 332.05 647.3 332.28 647.47 332.45 C 647.64 332.63 647.87 332.72 648.11 332.73 L 652.71 332.79 L 652.74 330.97 L 650.47 330.94 C 654.71 325.85 662.25 324.89 667.63 328.89 C 673.19 333.04 674.34 340.93 670.2 346.49 C 666.05 352.05 658.16 353.2 652.6 349.05 C 649.42 346.68 647.57 343.03 647.54 339.05 Z M 660 335.45 C 657.99 335.45 656.36 337.09 656.36 339.09 C 656.36 341.1 657.99 342.73 660 342.73 C 662.01 342.73 663.64 341.1 663.64 339.09 C 663.64 337.09 662.01 335.45 660 335.45 Z M 653.59 344.35 L 655.61 342.32 C 654.95 341.41 654.55 340.3 654.55 339.09 C 654.55 337.84 654.98 336.68 655.69 335.76 L 653.59 333.81 L 654.83 332.48 L 657.03 334.52 C 657.88 333.97 658.9 333.64 660 333.64 C 661.22 333.64 662.34 334.04 663.25 334.72 L 665.44 332.48 L 666.74 333.75 L 664.51 336.03 C 665.11 336.91 665.45 337.96 665.45 339.09 C 665.45 340.23 665.1 341.29 664.5 342.16 L 666.74 344.32 L 665.48 345.63 L 663.24 343.47 C 662.33 344.14 661.21 344.55 660 344.55 C 658.86 344.55 657.79 344.19 656.92 343.59 L 654.87 345.63 Z M 670 358.18 L 673.64 358.18 L 673.64 357.27 L 670 357.27 Z M 646.36 358.18 L 650 358.18 L 650 357.27 L 646.36 357.27 Z M 680 320.91 L 680 356.36 C 680 356.87 679.59 357.27 679.09 357.27 L 675.45 357.27 L 675.45 359.09 C 675.45 359.59 675.05 360 674.55 360 L 669.09 360 C 668.59 360 668.18 359.59 668.18 359.09 L 668.18 357.27 L 651.82 357.27 L 651.82 359.09 C 651.82 359.59 651.41 360 650.91 360 L 645.45 360 C 644.95 360 644.55 359.59 644.55 359.09 L 644.55 357.27 L 640.91 357.27 C 640.41 357.27 640 356.87 640 356.36 L 640 348.18 L 641.82 348.18 L 641.82 355.45 L 678.18 355.45 L 678.18 321.82 L 641.82 321.82 L 641.82 328.18 L 640 328.18 L 640 320.91 C 640 320.41 640.41 320 640.91 320 L 679.09 320 C 679.59 320 680 320.41 680 320.91 Z M 640 342.73 L 641.82 342.73 L 641.82 333.64 L 640 333.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 367px; margin-left: 660px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="379" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 0 0 L 780 0 L 780 460 L 0 460 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 6.09 7.18 C 6.01 7.18 5.93 7.19 5.85 7.19 C 5.5 7.19 5.15 7.23 4.81 7.32 C 4.53 7.39 4.25 7.49 3.98 7.62 C 3.9 7.65 3.84 7.7 3.79 7.76 C 3.75 7.83 3.74 7.91 3.74 7.99 L 3.74 8.32 C 3.74 8.46 3.79 8.53 3.89 8.53 L 3.99 8.53 L 4.22 8.44 C 4.45 8.35 4.69 8.27 4.94 8.21 C 5.17 8.16 5.41 8.13 5.65 8.13 C 6.04 8.09 6.43 8.2 6.73 8.44 C 6.97 8.74 7.09 9.12 7.05 9.5 L 7.05 9.99 C 6.79 9.93 6.54 9.88 6.29 9.84 C 6.05 9.81 5.81 9.79 5.57 9.79 C 4.98 9.76 4.4 9.94 3.94 10.31 C 3.54 10.65 3.32 11.15 3.34 11.68 C 3.31 12.15 3.49 12.62 3.82 12.96 C 4.18 13.29 4.66 13.46 5.15 13.44 C 5.91 13.45 6.63 13.11 7.11 12.51 C 7.18 12.66 7.24 12.79 7.31 12.91 C 7.38 13.02 7.46 13.12 7.55 13.21 C 7.6 13.27 7.67 13.31 7.75 13.31 C 7.81 13.31 7.87 13.29 7.92 13.25 L 8.34 12.97 C 8.41 12.93 8.46 12.86 8.47 12.77 C 8.47 12.72 8.45 12.67 8.42 12.62 C 8.34 12.47 8.26 12.31 8.21 12.14 C 8.15 11.95 8.12 11.75 8.13 11.55 L 8.14 9.37 C 8.2 8.77 8 8.18 7.59 7.74 C 7.17 7.39 6.64 7.19 6.09 7.18 Z M 19.89 7.19 C 19.78 7.19 19.68 7.19 19.57 7.2 C 19.29 7.2 19 7.24 18.73 7.31 C 18.47 7.38 18.23 7.5 18.02 7.66 C 17.82 7.81 17.66 7.99 17.54 8.21 C 17.42 8.43 17.35 8.67 17.36 8.92 C 17.36 9.27 17.48 9.61 17.69 9.89 C 17.97 10.22 18.34 10.46 18.76 10.56 L 19.72 10.87 C 19.97 10.93 20.2 11.05 20.39 11.22 C 20.51 11.35 20.58 11.51 20.57 11.69 C 20.58 11.94 20.45 12.18 20.23 12.31 C 19.93 12.48 19.6 12.56 19.26 12.54 C 18.99 12.54 18.72 12.51 18.46 12.45 C 18.22 12.4 17.98 12.32 17.75 12.22 L 17.59 12.15 C 17.54 12.14 17.5 12.14 17.46 12.15 C 17.36 12.15 17.31 12.22 17.31 12.36 L 17.31 12.69 C 17.31 12.76 17.32 12.82 17.35 12.89 C 17.4 12.97 17.47 13.03 17.56 13.07 C 17.8 13.19 18.06 13.28 18.32 13.34 C 18.66 13.41 19 13.45 19.35 13.45 L 19.33 13.46 C 19.66 13.45 19.98 13.4 20.29 13.3 C 20.55 13.22 20.8 13.09 21.01 12.92 C 21.21 12.77 21.38 12.57 21.49 12.34 C 21.61 12.1 21.67 11.83 21.66 11.56 C 21.67 11.23 21.56 10.9 21.36 10.63 C 21.09 10.32 20.73 10.09 20.33 9.99 L 19.39 9.69 C 19.13 9.61 18.88 9.49 18.67 9.32 C 18.54 9.2 18.47 9.03 18.47 8.85 C 18.46 8.61 18.58 8.38 18.79 8.25 C 19.06 8.11 19.36 8.05 19.67 8.06 C 20.11 8.06 20.55 8.14 20.96 8.32 C 21.04 8.37 21.12 8.4 21.21 8.41 C 21.31 8.41 21.36 8.34 21.36 8.19 L 21.36 7.88 C 21.37 7.8 21.35 7.72 21.31 7.66 C 21.25 7.59 21.18 7.54 21.11 7.49 L 20.83 7.38 L 20.45 7.27 L 20.01 7.2 C 19.97 7.2 19.93 7.19 19.89 7.19 Z M 16.02 7.36 C 15.94 7.35 15.86 7.38 15.79 7.42 C 15.72 7.5 15.68 7.59 15.66 7.69 L 14.51 12.14 L 13.47 7.71 C 13.45 7.61 13.41 7.52 13.34 7.44 C 13.26 7.39 13.17 7.37 13.07 7.38 L 12.54 7.38 C 12.44 7.37 12.35 7.39 12.27 7.44 C 12.2 7.51 12.15 7.61 12.14 7.71 L 11.09 12.14 L 9.97 7.7 C 9.95 7.6 9.91 7.51 9.84 7.44 C 9.76 7.39 9.67 7.36 9.58 7.37 L 8.92 7.37 C 8.81 7.37 8.76 7.43 8.76 7.54 C 8.77 7.63 8.79 7.72 8.82 7.81 L 10.38 12.95 C 10.4 13.05 10.45 13.14 10.52 13.21 C 10.6 13.26 10.69 13.29 10.78 13.28 L 11.36 13.26 C 11.46 13.27 11.55 13.25 11.63 13.19 C 11.7 13.12 11.74 13.03 11.76 12.93 L 12.79 8.64 L 13.82 12.93 C 13.83 13.03 13.88 13.12 13.95 13.19 C 14.03 13.25 14.12 13.27 14.21 13.26 L 14.79 13.26 C 14.88 13.27 14.97 13.25 15.04 13.2 C 15.11 13.13 15.16 13.03 15.18 12.94 L 16.79 7.79 C 16.84 7.72 16.84 7.63 16.84 7.63 C 16.84 7.59 16.84 7.56 16.84 7.52 C 16.84 7.48 16.82 7.43 16.79 7.4 C 16.76 7.37 16.72 7.35 16.67 7.36 L 16.05 7.36 C 16.04 7.36 16.03 7.36 16.02 7.36 Z M 5.65 10.62 C 5.7 10.62 5.75 10.62 5.8 10.62 L 6.43 10.62 C 6.64 10.64 6.85 10.67 7.06 10.71 L 7.07 11.01 C 7.07 11.21 7.05 11.4 7 11.59 C 6.96 11.75 6.88 11.9 6.77 12.01 C 6.61 12.21 6.39 12.36 6.14 12.44 C 5.91 12.52 5.67 12.56 5.43 12.56 C 5.18 12.6 4.93 12.53 4.73 12.37 C 4.55 12.18 4.46 11.92 4.49 11.66 C 4.47 11.36 4.59 11.08 4.81 10.89 C 5.06 10.72 5.35 10.62 5.65 10.62 Z M 21.04 14.72 C 20.34 14.73 19.51 14.89 18.89 15.33 C 18.69 15.46 18.72 15.63 18.94 15.63 C 19.64 15.54 21.21 15.35 21.5 15.71 C 21.78 16.06 21.19 17.54 20.94 18.21 C 20.86 18.41 21.04 18.49 21.21 18.34 C 22.39 17.36 22.72 15.3 22.46 15 C 22.32 14.85 21.74 14.71 21.04 14.72 Z M 2.65 15.1 C 2.5 15.12 2.42 15.3 2.58 15.44 C 5.29 17.89 8.82 19.23 12.48 19.21 C 15.37 19.22 18.2 18.36 20.59 16.74 C 20.95 16.47 20.64 16.07 20.26 16.23 C 17.87 17.24 15.3 17.76 12.71 17.77 C 9.23 17.78 5.82 16.87 2.81 15.14 C 2.75 15.11 2.7 15.1 2.65 15.1 Z M 0 0 L 25 0 L 25 25 L 0 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 748px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS Cloud
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS Cloud
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>