<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="761px" height="444px" viewBox="-0.5 -0.5 761 444" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="40" y="251" width="680" height="140" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <path d="M 0 0 L 760 0 L 760 443 L 0 443 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 10.59 6.65 C 10.53 6.65 10.48 6.65 10.42 6.65 L 10.42 6.65 C 9.11 6.68 8.03 7.24 7.14 8.25 C 7.13 8.25 7.13 8.25 7.13 8.25 C 6.2 9.36 5.87 10.52 5.96 11.73 C 4.81 12.06 4.12 12.92 3.76 13.74 C 3.75 13.75 3.75 13.76 3.74 13.78 C 3.33 15.05 3.68 16.36 4.24 17.16 C 4.25 17.17 4.25 17.17 4.26 17.18 C 4.94 18.05 5.97 18.53 7.02 18.53 L 18.17 18.53 C 19.19 18.53 20.07 18.16 20.8 17.37 C 21.25 16.94 21.49 16.29 21.58 15.59 C 21.67 14.9 21.61 14.16 21.32 13.55 C 21.31 13.54 21.31 13.53 21.31 13.52 C 20.8 12.62 19.95 11.81 18.76 11.64 C 18.74 10.79 18.28 9.99 17.68 9.56 C 17.67 9.55 17.66 9.55 17.65 9.54 C 17.01 9.18 16.4 9.14 15.91 9.3 C 15.6 9.4 15.36 9.56 15.14 9.74 C 14.51 8.36 13.43 7.18 11.81 6.79 C 11.81 6.79 11.81 6.79 11.81 6.79 C 11.38 6.7 10.97 6.65 10.59 6.65 Z M 10.43 7.38 C 10.8 7.38 11.2 7.43 11.64 7.53 C 13.16 7.89 14.15 9.07 14.66 10.48 C 14.71 10.6 14.81 10.69 14.94 10.72 C 15.07 10.74 15.2 10.7 15.29 10.61 C 15.54 10.34 15.83 10.11 16.14 10.01 C 16.44 9.91 16.78 9.92 17.26 10.18 C 17.67 10.49 18.11 11.31 18.03 11.9 C 18.01 12.01 18.05 12.12 18.12 12.2 C 18.19 12.28 18.29 12.33 18.39 12.33 C 19.46 12.34 20.16 13.02 20.64 13.88 C 20.85 14.3 20.91 14.92 20.84 15.5 C 20.76 16.07 20.53 16.59 20.28 16.83 C 20.27 16.84 20.27 16.85 20.26 16.85 C 19.65 17.53 19.03 17.78 18.17 17.78 L 7.02 17.78 C 6.2 17.78 5.39 17.41 4.85 16.73 C 4.44 16.13 4.14 15.02 4.46 14.02 C 4.79 13.27 5.36 12.55 6.41 12.36 C 6.6 12.32 6.74 12.14 6.71 11.94 C 6.56 10.79 6.8 9.81 7.7 8.74 C 8.49 7.85 9.33 7.39 10.43 7.38 Z M 12.2 10.7 C 11.77 10.7 11.4 10.93 11.13 11.21 C 10.85 11.5 10.64 11.85 10.64 12.25 L 10.64 12.71 L 10.14 12.71 C 10.04 12.71 9.94 12.75 9.87 12.82 C 9.8 12.89 9.76 12.98 9.76 13.08 L 9.76 15.7 C 9.76 15.8 9.8 15.89 9.87 15.96 C 9.94 16.03 10.04 16.07 10.14 16.07 L 14.16 16.07 C 14.26 16.07 14.35 16.03 14.42 15.96 C 14.49 15.89 14.53 15.8 14.53 15.7 L 14.53 13.08 C 14.53 12.98 14.49 12.89 14.42 12.82 C 14.35 12.75 14.26 12.71 14.16 12.71 L 13.68 12.71 L 13.68 12.25 C 13.68 11.84 13.47 11.47 13.21 11.2 C 12.94 10.92 12.61 10.7 12.2 10.7 Z M 12.2 11.45 C 12.29 11.45 12.5 11.54 12.67 11.72 C 12.83 11.89 12.93 12.11 12.93 12.25 L 12.93 12.71 L 11.39 12.71 L 11.39 12.25 C 11.39 12.15 11.49 11.91 11.66 11.74 C 11.83 11.56 12.06 11.45 12.2 11.45 Z M 10.51 13.46 L 13.78 13.46 L 13.78 15.32 L 10.51 15.32 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 728px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #AAB7B8; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 45 82 L 245 82 L 245 212 L 45 212 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 45 82 L 70 82 L 70 107 L 45 107 Z M 57.52 85.21 C 56.4 85.21 55.31 85.63 54.48 86.39 C 53.67 87.11 53.2 88.15 53.2 89.24 L 53.2 91.78 L 50.89 91.78 C 50.8 91.78 50.7 91.82 50.64 91.89 C 50.57 91.95 50.54 92.04 50.54 92.14 L 50.54 103.43 C 50.54 103.63 50.7 103.79 50.89 103.79 L 64.11 103.79 C 64.3 103.79 64.46 103.63 64.46 103.43 L 64.46 92.15 C 64.47 92.06 64.43 91.97 64.36 91.9 C 64.3 91.83 64.21 91.79 64.11 91.79 L 61.81 91.79 L 61.81 89.29 C 61.8 88.21 61.35 87.18 60.56 86.44 C 59.74 85.65 58.65 85.22 57.52 85.21 Z M 57.51 85.93 C 58.46 85.92 59.37 86.28 60.06 86.93 C 60.72 87.54 61.1 88.4 61.1 89.29 L 61.1 91.79 L 53.88 91.79 L 53.89 89.26 C 53.9 88.36 54.28 87.51 54.95 86.91 C 55.65 86.27 56.57 85.92 57.51 85.93 Z M 51.24 92.5 L 63.76 92.5 L 63.75 103.07 L 51.24 103.07 Z M 57.51 94.74 C 56.48 94.73 55.61 95.51 55.51 96.53 C 55.42 97.56 56.13 98.48 57.14 98.66 L 57.14 101.44 L 57.86 101.44 L 57.86 98.66 C 58.79 98.49 59.47 97.67 59.48 96.72 C 59.48 95.63 58.6 94.75 57.51 94.74 Z M 57.39 95.45 C 57.43 95.45 57.47 95.45 57.51 95.46 C 57.84 95.46 58.16 95.59 58.4 95.83 C 58.64 96.07 58.77 96.39 58.76 96.72 C 58.77 97.06 58.64 97.38 58.4 97.61 C 58.16 97.85 57.84 97.98 57.51 97.98 C 57.04 98.02 56.6 97.8 56.34 97.42 C 56.08 97.03 56.06 96.53 56.28 96.12 C 56.5 95.71 56.93 95.46 57.39 95.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 89px; margin-left: 77px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="77" y="101" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 279 82 L 479 82 L 479 212 L 279 212 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 279 82 L 304 82 L 304 107 L 279 107 Z M 291.52 85.21 C 290.4 85.21 289.31 85.63 288.49 86.39 C 287.67 87.11 287.2 88.15 287.2 89.24 L 287.2 91.78 L 284.89 91.78 C 284.8 91.78 284.7 91.82 284.64 91.89 C 284.57 91.95 284.54 92.04 284.54 92.14 L 284.54 103.43 C 284.54 103.63 284.7 103.79 284.89 103.79 L 298.11 103.79 C 298.3 103.79 298.46 103.63 298.46 103.43 L 298.46 92.15 C 298.47 92.06 298.43 91.97 298.36 91.9 C 298.3 91.83 298.21 91.79 298.11 91.79 L 295.81 91.79 L 295.81 89.29 C 295.8 88.21 295.35 87.18 294.56 86.44 C 293.74 85.65 292.65 85.22 291.52 85.21 Z M 291.51 85.93 C 292.46 85.92 293.37 86.28 294.06 86.93 C 294.72 87.54 295.1 88.4 295.1 89.29 L 295.1 91.79 L 287.88 91.79 L 287.89 89.26 C 287.9 88.36 288.28 87.51 288.95 86.91 C 289.65 86.27 290.57 85.92 291.51 85.93 Z M 285.24 92.5 L 297.76 92.5 L 297.75 103.07 L 285.24 103.07 Z M 291.51 94.74 C 290.48 94.73 289.61 95.51 289.51 96.53 C 289.42 97.56 290.13 98.48 291.14 98.66 L 291.14 101.44 L 291.86 101.44 L 291.86 98.66 C 292.79 98.49 293.47 97.67 293.48 96.72 C 293.48 95.63 292.6 94.75 291.51 94.74 Z M 291.39 95.45 C 291.43 95.45 291.47 95.45 291.51 95.46 C 291.84 95.46 292.16 95.59 292.4 95.83 C 292.64 96.07 292.77 96.39 292.76 96.72 C 292.77 97.06 292.64 97.38 292.4 97.61 C 292.16 97.85 291.84 97.98 291.51 97.98 C 291.04 98.02 290.6 97.8 290.34 97.42 C 290.08 97.03 290.06 96.53 290.28 96.12 C 290.5 95.71 290.93 95.46 291.39 95.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 89px; margin-left: 311px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="311" y="101" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 353.25 121.5 L 404.75 121.5 L 404.75 173 L 353.25 173 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 382.62 131.65 L 380.48 131.65 L 380.48 130.22 L 382.62 130.22 L 382.62 128.07 L 384.04 128.07 L 384.04 130.22 L 386.18 130.22 L 386.18 131.65 L 384.04 131.65 L 384.04 133.8 L 382.62 133.8 Z M 391.16 139.54 L 389.03 139.54 L 389.03 138.11 L 391.16 138.11 L 391.16 135.95 L 392.59 135.95 L 392.59 138.11 L 394.72 138.11 L 394.72 139.54 L 392.59 139.54 L 392.59 141.69 L 391.16 141.69 Z M 387.45 162.19 C 386.09 158.73 382.95 155.58 379.52 154.21 C 382.95 152.84 386.09 149.68 387.45 146.22 C 388.81 149.68 391.95 152.84 395.39 154.21 C 391.95 155.58 388.81 158.73 387.45 162.19 Z M 398.89 153.49 C 393.88 153.49 388.17 147.74 388.17 142.7 C 388.17 142.3 387.85 141.98 387.45 141.98 C 387.06 141.98 386.74 142.3 386.74 142.7 C 386.74 147.74 381.03 153.49 376.02 153.49 C 375.63 153.49 375.31 153.81 375.31 154.21 C 375.31 154.6 375.63 154.92 376.02 154.92 C 381.03 154.92 386.74 160.67 386.74 165.71 C 386.74 166.11 387.06 166.43 387.45 166.43 C 387.85 166.43 388.17 166.11 388.17 165.71 C 388.17 160.67 393.88 154.92 398.89 154.92 C 399.28 154.92 399.6 154.6 399.6 154.21 C 399.6 153.81 399.28 153.49 398.89 153.49 Z M 359.82 139.37 C 361.9 140.89 365.93 141.69 369.8 141.69 C 373.66 141.69 377.69 140.89 379.77 139.37 L 379.77 146.23 C 378.74 147.61 374.97 148.96 369.94 148.96 C 364.15 148.96 359.82 147.14 359.82 145.51 Z M 369.8 133.8 C 375.97 133.8 379.77 135.68 379.77 137.03 C 379.77 138.38 375.97 140.26 369.8 140.26 C 363.62 140.26 359.82 138.38 359.82 137.03 C 359.82 135.68 363.62 133.8 369.8 133.8 Z M 379.77 160.71 C 379.77 162.36 375.5 164.21 369.79 164.21 C 364.09 164.21 359.82 162.36 359.82 160.71 L 359.82 156.13 C 361.92 157.73 366.03 158.58 369.98 158.58 C 372.72 158.58 375.37 158.19 377.45 157.48 L 376.99 156.12 C 375.06 156.78 372.57 157.14 369.98 157.14 C 364.17 157.14 359.82 155.32 359.82 153.69 L 359.82 147.95 C 361.92 149.55 366.01 150.39 369.94 150.39 C 374.15 150.39 377.76 149.52 379.77 148.14 L 379.77 150.29 L 381.19 150.29 L 381.19 137.03 C 381.19 134 375.32 132.37 369.8 132.37 C 364.5 132.37 358.89 133.88 358.44 136.67 L 358.4 136.67 L 358.4 160.71 C 358.4 163.91 364.27 165.64 369.79 165.64 C 375.32 165.64 381.19 163.91 381.19 160.71 L 381.19 158.17 L 379.77 158.17 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 379px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Replica
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="379" y="192" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Replica
                </text>
            </switch>
        </g>
        <path d="M 514 82 L 714 82 L 714 212 L 514 212 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 514 82 L 539 82 L 539 107 L 514 107 Z M 526.52 85.21 C 525.4 85.21 524.31 85.63 523.49 86.39 C 522.67 87.11 522.2 88.15 522.2 89.24 L 522.2 91.78 L 519.89 91.78 C 519.8 91.78 519.7 91.82 519.64 91.89 C 519.57 91.95 519.54 92.04 519.54 92.14 L 519.54 103.43 C 519.54 103.63 519.7 103.79 519.89 103.79 L 533.11 103.79 C 533.3 103.79 533.46 103.63 533.46 103.43 L 533.46 92.15 C 533.47 92.06 533.43 91.97 533.36 91.9 C 533.3 91.83 533.21 91.79 533.11 91.79 L 530.81 91.79 L 530.81 89.29 C 530.8 88.21 530.35 87.18 529.56 86.44 C 528.74 85.65 527.65 85.22 526.52 85.21 Z M 526.51 85.93 C 527.46 85.92 528.37 86.28 529.06 86.93 C 529.72 87.54 530.1 88.4 530.1 89.29 L 530.1 91.79 L 522.88 91.79 L 522.89 89.26 C 522.9 88.36 523.28 87.51 523.95 86.91 C 524.65 86.27 525.57 85.92 526.51 85.93 Z M 520.24 92.5 L 532.76 92.5 L 532.75 103.07 L 520.24 103.07 Z M 526.51 94.74 C 525.48 94.73 524.61 95.51 524.51 96.53 C 524.42 97.56 525.13 98.48 526.14 98.66 L 526.14 101.44 L 526.86 101.44 L 526.86 98.66 C 527.79 98.49 528.47 97.67 528.48 96.72 C 528.48 95.63 527.6 94.75 526.51 94.74 Z M 526.39 95.45 C 526.43 95.45 526.47 95.45 526.51 95.46 C 526.84 95.46 527.16 95.59 527.4 95.83 C 527.64 96.07 527.77 96.39 527.76 96.72 C 527.77 97.06 527.64 97.38 527.4 97.61 C 527.16 97.85 526.84 97.98 526.51 97.98 C 526.04 98.02 525.6 97.8 525.34 97.42 C 525.08 97.03 525.06 96.53 525.28 96.12 C 525.5 95.71 525.93 95.46 526.39 95.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 89px; margin-left: 546px;">
                        <div data-drawio-colors="color: #147EBA; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                <span style="background-color: rgb(230, 242, 248);">
                                    Protected subnet
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="546" y="101" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <path d="M 86 343 L 86 303 C 86 297.48 94.95 293 106 293 C 117.05 293 126 297.48 126 303 L 126 343 C 126 348.52 117.05 353 106 353 C 94.95 353 86 348.52 86 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 86 303 C 86 308.52 94.95 313 106 313 C 117.05 313 126 308.52 126 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 164 343 L 164 303 C 164 297.48 172.95 293 184 293 C 195.05 293 204 297.48 204 303 L 204 343 C 204 348.52 195.05 353 184 353 C 172.95 353 164 348.52 164 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 164 303 C 164 308.52 172.95 313 184 313 C 195.05 313 204 308.52 204 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 322 343 L 322 303 C 322 297.48 330.95 293 342 293 C 353.05 293 362 297.48 362 303 L 362 343 C 362 348.52 353.05 353 342 353 C 330.95 353 322 348.52 322 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 322 303 C 322 308.52 330.95 313 342 313 C 353.05 313 362 308.52 362 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 400 343 L 400 303 C 400 297.48 408.95 293 420 293 C 431.05 293 440 297.48 440 303 L 440 343 C 440 348.52 431.05 353 420 353 C 408.95 353 400 348.52 400 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 400 303 C 400 308.52 408.95 313 420 313 C 431.05 313 440 308.52 440 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 556 343 L 556 303 C 556 297.48 564.95 293 576 293 C 587.05 293 596 297.48 596 303 L 596 343 C 596 348.52 587.05 353 576 353 C 564.95 353 556 348.52 556 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 556 303 C 556 308.52 564.95 313 576 313 C 587.05 313 596 308.52 596 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 634 343 L 634 303 C 634 297.48 642.95 293 654 293 C 665.05 293 674 297.48 674 303 L 674 343 C 674 348.52 665.05 353 654 353 C 642.95 353 634 348.52 634 343 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 634 303 C 634 308.52 642.95 313 654 313 C 665.05 313 674 308.52 674 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 117.5 121.5 L 169 121.5 L 169 173 L 117.5 173 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 146.87 131.65 L 144.73 131.65 L 144.73 130.22 L 146.87 130.22 L 146.87 128.07 L 148.29 128.07 L 148.29 130.22 L 150.43 130.22 L 150.43 131.65 L 148.29 131.65 L 148.29 133.8 L 146.87 133.8 Z M 155.41 139.54 L 153.28 139.54 L 153.28 138.11 L 155.41 138.11 L 155.41 135.95 L 156.84 135.95 L 156.84 138.11 L 158.97 138.11 L 158.97 139.54 L 156.84 139.54 L 156.84 141.69 L 155.41 141.69 Z M 151.7 162.19 C 150.34 158.73 147.2 155.58 143.77 154.21 C 147.2 152.84 150.34 149.68 151.7 146.22 C 153.06 149.68 156.2 152.84 159.64 154.21 C 156.2 155.58 153.06 158.73 151.7 162.19 Z M 163.14 153.49 C 158.13 153.49 152.42 147.74 152.42 142.7 C 152.42 142.3 152.1 141.98 151.7 141.98 C 151.31 141.98 150.99 142.3 150.99 142.7 C 150.99 147.74 145.28 153.49 140.27 153.49 C 139.88 153.49 139.56 153.81 139.56 154.21 C 139.56 154.6 139.88 154.92 140.27 154.92 C 145.28 154.92 150.99 160.67 150.99 165.71 C 150.99 166.11 151.31 166.43 151.7 166.43 C 152.1 166.43 152.42 166.11 152.42 165.71 C 152.42 160.67 158.13 154.92 163.14 154.92 C 163.53 154.92 163.85 154.6 163.85 154.21 C 163.85 153.81 163.53 153.49 163.14 153.49 Z M 124.07 139.37 C 126.15 140.89 130.18 141.69 134.05 141.69 C 137.91 141.69 141.94 140.89 144.02 139.37 L 144.02 146.23 C 142.99 147.61 139.22 148.96 134.19 148.96 C 128.4 148.96 124.07 147.14 124.07 145.51 Z M 134.05 133.8 C 140.22 133.8 144.02 135.68 144.02 137.03 C 144.02 138.38 140.22 140.26 134.05 140.26 C 127.87 140.26 124.07 138.38 124.07 137.03 C 124.07 135.68 127.87 133.8 134.05 133.8 Z M 144.02 160.71 C 144.02 162.36 139.75 164.21 134.04 164.21 C 128.34 164.21 124.07 162.36 124.07 160.71 L 124.07 156.13 C 126.17 157.73 130.28 158.58 134.23 158.58 C 136.97 158.58 139.62 158.19 141.7 157.48 L 141.24 156.12 C 139.31 156.78 136.82 157.14 134.23 157.14 C 128.42 157.14 124.07 155.32 124.07 153.69 L 124.07 147.95 C 126.17 149.55 130.26 150.39 134.19 150.39 C 138.4 150.39 142.01 149.52 144.02 148.14 L 144.02 150.29 L 145.44 150.29 L 145.44 137.03 C 145.44 134 139.57 132.37 134.05 132.37 C 128.75 132.37 123.14 133.88 122.69 136.67 L 122.65 136.67 L 122.65 160.71 C 122.65 163.91 128.52 165.64 134.04 165.64 C 139.57 165.64 145.44 163.91 145.44 160.71 L 145.44 158.17 L 144.02 158.17 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 143px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Primary
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="143" y="192" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Primary
                </text>
            </switch>
        </g>
        <rect x="35" y="43" width="220" height="370" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="93" y="51" width="106" height="15" stroke-width="0"/>
            <text x="144.5" y="60.5">
                Availability Zone 1a
            </text>
        </g>
        <rect x="505" y="43" width="220" height="370" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="563" y="51" width="106" height="15" stroke-width="0"/>
            <text x="614.5" y="60.5">
                Availability Zone 1d
            </text>
        </g>
        <rect x="269" y="43" width="220" height="370" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="none"/>
        <g fill="#147EBA" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="327" y="51" width="105" height="15" stroke-width="0"/>
            <text x="378.5" y="60.5">
                Availability Zone 1c
            </text>
        </g>
        <path d="M 555 326 L 571 326 L 571 310 L 579 310 L 579 326 L 595 326 L 595 334 L 579 334 L 579 350 L 571 350 L 571 334 L 555 334 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,575,330)" pointer-events="none"/>
        <path d="M 634 326 L 650 326 L 650 310 L 658 310 L 658 326 L 674 326 L 674 334 L 658 334 L 658 350 L 650 350 L 650 334 L 634 334 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,654,330)" pointer-events="none"/>
        <path d="M 400 326 L 416 326 L 416 310 L 424 310 L 424 326 L 440 326 L 440 334 L 424 334 L 424 350 L 416 350 L 416 334 L 400 334 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,420,330)" pointer-events="none"/>
        <rect x="532" y="355" width="170" height="50" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 380px; margin-left: 533px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                ２つ障害があっても、
                                <br/>
                                読み込み書き込みが可能
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="617" y="384" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ２つ障害があっても、
読み込み書き込みが可能
                </text>
            </switch>
        </g>
        <rect x="405" y="233" width="297" height="50" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 295px; height: 1px; padding-top: 258px; margin-left: 406px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: normal; overflow-wrap: normal;">
                                3つ障害があっても、
                                <br/>
                                読み込みが可能
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="554" y="262" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    3つ障害があっても、...
                </text>
            </switch>
        </g>
        <path d="M 145 193 L 145 246.79" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 145 252.04 L 141.5 245.04 L 145 246.79 L 148.5 245.04 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 378.5 252.16 L 378.5 198.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 378.5 193.12 L 382 200.12 L 378.5 198.37 L 375 200.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 228px; margin-left: 122px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                write
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="122" y="232" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    write
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 228px; margin-left: 351px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                read
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="351" y="232" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    read
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 370px; margin-left: 380px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <font style="font-size: 17px;">
                                    Cluster Volume
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="374" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cluster Volume
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>