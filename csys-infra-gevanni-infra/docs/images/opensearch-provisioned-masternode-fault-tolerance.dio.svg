<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="521px" height="371px" viewBox="-0.5 -0.5 521 371" content="&lt;mxfile&gt;&lt;diagram id=&quot;7ISzqH4pcPf2vZZOh1lO&quot; name=&quot;Page-1&quot;&gt;7Vpbc6M2FP41PK4HcTM8gkO2ndlOO81DZ/qSkUG2NYuRK+TY2V9fCSQbIZE4sTfJZuw4CTq6cr5zleT4s/X+K4Wb1R+kRJXjueXe8W8cz0tcwP8KwmNHiEHQEZYUlx0JHAl3+AeSRFdSt7hEjdaQEVIxvNGJBalrVDCNBiklO73ZglT6rBu4RAbhroCVSf0Hl2wl38KbHum/IbxcqZlBlHQ1c1h8X1KyreV8jucv2k9XvYZqLPmizQqWZNcj+bnjzyghrHta72eoEqxVbOv63Y7UHtZNUc1O6eB3HR5gtZWv/ucG1XcI0mLl5L4T828iH1LQPnhOGkhKJrs37FFxrn13JEZ3HT/brTBDdxtYiNodFxVOW7F1xUuAPy5wVc1IRSgv16TmjbKGUfIdKSJnXxS57mzGax4QZZhDlFZ4WfM6RsRoUJYqtGCiO58L18tvbekmFGswWSK5JAZE+x5JsugrImvE6CNvImsDidajXtwdRSNUgK56YuGrhlCK4/Iw8hES/iBRsSMUGgilDxBXcI4rzMQa/xWM81wADSxOYS8IpnmW8poSNqsWNzDK6wWp2Z0c3pVl21AX4HikczwBBsdBaOG4N03O57hncNzgbPMdsWIl2bAhuGbtfGHGv3xZs+435E1ngjIRKBpEG21qEoHZjP8DthmGRBttahKB2UyU1Kp1oo02Dc0VD3sDS28w6M2/fka2rMI1l1Fl2i2ixn9uBXjZksISI60uCcKbW69Xd4MpHwiTulUDKgRIszy8T5j6bhba9ENa755+uD39+AbnqPqLNFgOPyeMkfWzxqrgq0J0YAuFcnWOEHgWZYPNpmPHAu/FOjLuOjaicr1fCh88gbsmmFDUkC0t0O+FWE/Gi92T3gpVsOHLa1ozf98g+oCFhX4bUxlY9PYShjIw1bbzUkku/VaWqIdb5cBmTh46seskvCp2Ut6YzxNVwpXMKX9aspYtkjIkCIzaKESJS/TflnQNuOD4/NMnyb6558SRk5y2OjkRf/duLn1+Tu6t6SkbdZZSSd8x0JmbgKv/1NAZ2fjDqUtrpRHNH1BnrMGYCuG6YbAukHcZheCxoaYRwDd9WRBbVCI+XyWiU2OH4jPFDp7/jsHD9FSWl5+J5X7yjiyPX2f4r/byQ9pLLwYDe2kJIX6SvUwuFkJcZesjylYQDGXr7Xyx2toyhSue9kRJF64416va3ZihcFnzTyP3HOadRs6p55tGLjfM44wcTs8mjYRzmJUaqaue3RoJ5jALNVLVJ/PHETXqCbFtq+o5weapFYN8LirHGMp116aq4KbB80Mvno1uaYMf0N+o6QZ3x+RfbCVu2uXb0ri29h4V3r3Sk3uxom5yM3a4iaMI+PZtt1dsrvkWs3WYQjM5F9DcqR5feBancHAUfc09EM9SXXOXVCmqUl1u9g1FBZZMUrqTQzflTg5bqonS+DQ1FP24tzomsD15NuB3289Tu65qq3vE2rovBS3UMLNBZt01vQRiZhh+CmLeKGLpzIAuVdCZqffPA+rysMRvCQuww3L1gVcfePWBz2ZGwbs6wZH91Veb1F/TCb4YtXe0tubR4Xlhywd2gmfC8qaxieV88eoEr07w6gRP0dvAfVcnaJ6ofFqT6r0Umnc0qSNHAJ8x3TsTlrcMQNS4T96kkdauoKRpTuWeitEEhRIG5Rb2lyA0t8ZRCALXYqEOFzvGAanEFnl2uNGnmpRoAbet3PTNvuhQ9m6aNNz9rC4U+Mf+REcR+N7ETwwk1fa2tnftT5LofCwDS9Si34I4gqquPIiKL51n43G8C4LNvmXI8UqE/eqEBMC8OmHcwzjpdGY8gMoTJ0nFeU0eOmkurjPmkTjHiT37DYuRyxgDkeaoMl1qjcOToZdd47IU3cWFHfwDHkMEPaQTUQ3cMiKjBdOcWE6HFEmX/tvb9nbKZcTT010ysJiY6DImhhePd2Pbut79Yz//Hw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="30" width="520" height="340" fill="none" stroke="#6600cc" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 37px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OpenSearchドメイン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="49" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    OpenSearchドメイン
                </text>
            </switch>
        </g>
        <rect x="20" y="81" width="150" height="279" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="94.5" y="98.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 0 30 L 40 30 L 40 70 L 0 70 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 10.47 43.53 L 4.59 43.53 C 4.26 43.53 4 43.8 4 44.12 L 4 65.29 C 4 65.61 4.26 65.88 4.59 65.88 L 10.47 65.88 C 10.79 65.88 11.06 65.61 11.06 65.29 L 11.06 44.12 C 11.06 43.8 10.79 43.53 10.47 43.53 Z M 5.18 64.7 L 5.18 44.71 L 9.88 44.71 L 9.88 64.7 Z M 13.41 47.65 L 12.23 47.65 L 12.23 40 C 12.23 39.68 12.5 39.42 12.82 39.42 L 18.7 39.42 C 19.03 39.42 19.29 39.68 19.29 40 L 19.29 42.36 L 18.11 42.36 L 18.11 40.59 L 13.41 40.59 Z M 18.11 61.17 L 19.29 61.17 L 19.29 65.29 C 19.29 65.61 19.03 65.88 18.7 65.88 L 12.82 65.88 C 12.5 65.88 12.23 65.61 12.23 65.29 L 12.23 56.47 L 13.41 56.47 L 13.41 64.7 L 18.11 64.7 Z M 21.64 41.77 L 20.46 41.77 L 20.46 34.71 C 20.46 34.39 20.73 34.12 21.05 34.12 L 26.93 34.12 C 27.26 34.12 27.52 34.39 27.52 34.71 L 27.52 42.94 L 26.34 42.94 L 26.34 35.3 L 21.64 35.3 Z M 26.34 62.35 L 27.52 62.35 L 27.52 65.29 C 27.52 65.61 27.26 65.88 26.93 65.88 L 21.05 65.88 C 20.73 65.88 20.46 65.61 20.46 65.29 L 20.46 61.76 L 21.64 61.76 L 21.64 64.7 L 26.34 64.7 Z M 35.75 38.24 L 35.75 58.23 L 34.58 58.23 L 34.58 38.83 L 29.87 38.83 L 29.87 45.3 L 28.7 45.3 L 28.7 38.24 C 28.7 37.92 28.96 37.65 29.28 37.65 L 35.17 37.65 C 35.49 37.65 35.75 37.92 35.75 38.24 Z M 29.91 55.61 C 30.45 54.5 30.76 53.26 30.76 51.95 C 30.76 47.33 27 43.57 22.38 43.57 C 17.76 43.57 14 47.33 14 51.95 C 14 56.57 17.76 60.33 22.38 60.33 C 24.09 60.33 25.67 59.82 27 58.94 L 32.06 63.5 C 32.48 63.88 33.01 64.07 33.54 64.07 C 34.14 64.07 34.75 63.82 35.19 63.34 C 36 62.43 35.93 61.03 35.02 60.21 Z M 15.18 51.95 C 15.18 47.98 18.41 44.75 22.38 44.75 C 26.35 44.75 29.58 47.98 29.58 51.95 C 29.58 55.92 26.35 59.16 22.38 59.16 C 18.41 59.16 15.18 55.92 15.18 51.95 Z M 34.31 62.55 C 33.93 62.98 33.27 63.01 32.84 62.63 L 27.94 58.21 C 28.46 57.74 28.92 57.23 29.31 56.65 L 34.23 61.08 C 34.66 61.47 34.69 62.13 34.31 62.55 Z M 22.38 46.07 C 19.14 46.07 16.5 48.71 16.5 51.95 C 16.5 55.2 19.14 57.84 22.38 57.84 C 25.63 57.84 28.27 55.2 28.27 51.95 C 28.27 48.71 25.63 46.07 22.38 46.07 Z M 22.38 56.66 C 19.78 56.66 17.67 54.55 17.67 51.95 C 17.67 49.36 19.78 47.24 22.38 47.24 C 24.98 47.24 27.09 49.36 27.09 51.95 C 27.09 54.55 24.98 56.66 22.38 56.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="76" y="121" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 124 133 L 124 130.82 L 118.55 130.82 L 118.55 127.55 C 118.55 126.94 118.06 126.45 117.45 126.45 L 114.18 126.45 L 114.18 121 L 112 121 L 112 126.45 L 107.64 126.45 L 107.64 121 L 105.45 121 L 105.45 126.45 L 101.09 126.45 L 101.09 121 L 98.91 121 L 98.91 126.45 L 94.55 126.45 L 94.55 121 L 92.36 121 L 92.36 126.45 L 88 126.45 L 88 121 L 85.82 121 L 85.82 126.45 L 82.55 126.45 C 81.94 126.45 81.45 126.94 81.45 127.55 L 81.45 130.82 L 76 130.82 L 76 133 L 81.45 133 L 81.45 137.36 L 76 137.36 L 76 139.55 L 81.45 139.55 L 81.45 143.91 L 76 143.91 L 76 146.09 L 81.45 146.09 L 81.45 150.45 L 76 150.45 L 76 152.64 L 81.45 152.64 L 81.45 157 L 76 157 L 76 159.18 L 81.45 159.18 L 81.45 162.45 C 81.45 163.06 81.94 163.55 82.55 163.55 L 85.82 163.55 L 85.82 169 L 88 169 L 88 163.55 L 92.36 163.55 L 92.36 169 L 94.55 169 L 94.55 163.55 L 98.91 163.55 L 98.91 169 L 101.09 169 L 101.09 163.55 L 105.45 163.55 L 105.45 169 L 107.64 169 L 107.64 163.55 L 112 163.55 L 112 169 L 114.18 169 L 114.18 163.55 L 117.45 163.55 C 118.06 163.55 118.55 163.06 118.55 162.45 L 118.55 159.18 L 124 159.18 L 124 157 L 118.55 157 L 118.55 152.64 L 124 152.64 L 124 150.45 L 118.55 150.45 L 118.55 146.09 L 124 146.09 L 124 143.91 L 118.55 143.91 L 118.55 139.55 L 124 139.55 L 124 137.36 L 118.55 137.36 L 118.55 133 Z M 83.64 161.36 L 83.64 128.64 L 116.36 128.64 L 116.36 161.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 176px; margin-left: 100px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                                <br/>
                                <b>
                                    <font color="#ff3333">
                                        →マスター
                                    </font>
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="188" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
→マスター
                </text>
            </switch>
        </g>
        <rect x="190" y="81" width="150" height="279" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="264.5" y="98.5">
                Availability Zone 1c
            </text>
        </g>
        <rect x="350" y="81" width="150" height="279" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="424.5" y="98.5">
                Availability Zone 1d
            </text>
        </g>
        <rect x="241" y="120" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 289 132 L 289 129.82 L 283.55 129.82 L 283.55 126.55 C 283.55 125.94 283.06 125.45 282.45 125.45 L 279.18 125.45 L 279.18 120 L 277 120 L 277 125.45 L 272.64 125.45 L 272.64 120 L 270.45 120 L 270.45 125.45 L 266.09 125.45 L 266.09 120 L 263.91 120 L 263.91 125.45 L 259.55 125.45 L 259.55 120 L 257.36 120 L 257.36 125.45 L 253 125.45 L 253 120 L 250.82 120 L 250.82 125.45 L 247.55 125.45 C 246.94 125.45 246.45 125.94 246.45 126.55 L 246.45 129.82 L 241 129.82 L 241 132 L 246.45 132 L 246.45 136.36 L 241 136.36 L 241 138.55 L 246.45 138.55 L 246.45 142.91 L 241 142.91 L 241 145.09 L 246.45 145.09 L 246.45 149.45 L 241 149.45 L 241 151.64 L 246.45 151.64 L 246.45 156 L 241 156 L 241 158.18 L 246.45 158.18 L 246.45 161.45 C 246.45 162.06 246.94 162.55 247.55 162.55 L 250.82 162.55 L 250.82 168 L 253 168 L 253 162.55 L 257.36 162.55 L 257.36 168 L 259.55 168 L 259.55 162.55 L 263.91 162.55 L 263.91 168 L 266.09 168 L 266.09 162.55 L 270.45 162.55 L 270.45 168 L 272.64 168 L 272.64 162.55 L 277 162.55 L 277 168 L 279.18 168 L 279.18 162.55 L 282.45 162.55 C 283.06 162.55 283.55 162.06 283.55 161.45 L 283.55 158.18 L 289 158.18 L 289 156 L 283.55 156 L 283.55 151.64 L 289 151.64 L 289 149.45 L 283.55 149.45 L 283.55 145.09 L 289 145.09 L 289 142.91 L 283.55 142.91 L 283.55 138.55 L 289 138.55 L 289 136.36 L 283.55 136.36 L 283.55 132 Z M 248.64 160.36 L 248.64 127.64 L 281.36 127.64 L 281.36 160.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 175px; margin-left: 265px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="187" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター
                </text>
            </switch>
        </g>
        <rect x="401" y="121" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 449 133 L 449 130.82 L 443.55 130.82 L 443.55 127.55 C 443.55 126.94 443.06 126.45 442.45 126.45 L 439.18 126.45 L 439.18 121 L 437 121 L 437 126.45 L 432.64 126.45 L 432.64 121 L 430.45 121 L 430.45 126.45 L 426.09 126.45 L 426.09 121 L 423.91 121 L 423.91 126.45 L 419.55 126.45 L 419.55 121 L 417.36 121 L 417.36 126.45 L 413 126.45 L 413 121 L 410.82 121 L 410.82 126.45 L 407.55 126.45 C 406.94 126.45 406.45 126.94 406.45 127.55 L 406.45 130.82 L 401 130.82 L 401 133 L 406.45 133 L 406.45 137.36 L 401 137.36 L 401 139.55 L 406.45 139.55 L 406.45 143.91 L 401 143.91 L 401 146.09 L 406.45 146.09 L 406.45 150.45 L 401 150.45 L 401 152.64 L 406.45 152.64 L 406.45 157 L 401 157 L 401 159.18 L 406.45 159.18 L 406.45 162.45 C 406.45 163.06 406.94 163.55 407.55 163.55 L 410.82 163.55 L 410.82 169 L 413 169 L 413 163.55 L 417.36 163.55 L 417.36 169 L 419.55 169 L 419.55 163.55 L 423.91 163.55 L 423.91 169 L 426.09 169 L 426.09 163.55 L 430.45 163.55 L 430.45 169 L 432.64 169 L 432.64 163.55 L 437 163.55 L 437 169 L 439.18 169 L 439.18 163.55 L 442.45 163.55 C 443.06 163.55 443.55 163.06 443.55 162.45 L 443.55 159.18 L 449 159.18 L 449 157 L 443.55 157 L 443.55 152.64 L 449 152.64 L 449 150.45 L 443.55 150.45 L 443.55 146.09 L 449 146.09 L 449 143.91 L 443.55 143.91 L 443.55 139.55 L 449 139.55 L 449 137.36 L 443.55 137.36 L 443.55 133 Z M 408.64 161.36 L 408.64 128.64 L 441.36 128.64 L 441.36 161.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 176px; margin-left: 425px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="188" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
                </text>
            </switch>
        </g>
        <path d="M 30 220 L 160 220 L 160 350 L 30 350 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 30 220 L 55 220 L 55 245 L 30 245 Z M 51.07 228.37 L 51.07 227.66 L 49.39 227.66 L 49.39 225.61 L 47.25 225.61 L 47.25 223.93 L 46.54 223.93 L 46.54 225.61 L 45 225.61 L 45 223.93 L 44.29 223.93 L 44.29 225.61 L 42.77 225.61 L 42.77 223.93 L 42.06 223.93 L 42.06 225.61 L 40.53 225.61 L 40.53 223.93 L 39.81 223.93 L 39.81 225.61 L 38.29 225.61 L 38.29 223.93 L 37.57 223.93 L 37.57 225.61 L 35.61 225.61 L 35.61 227.66 L 33.93 227.66 L 33.93 228.37 L 35.61 228.37 L 35.61 229.9 L 33.93 229.9 L 33.93 230.61 L 35.61 230.61 L 35.61 232.14 L 33.93 232.14 L 33.93 232.86 L 35.61 232.86 L 35.61 234.39 L 33.93 234.39 L 33.93 235.1 L 35.61 235.1 L 35.61 236.63 L 33.93 236.63 L 33.93 237.34 L 35.61 237.34 L 35.61 239.39 L 37.57 239.39 L 37.57 241.07 L 38.29 241.07 L 38.29 239.39 L 39.81 239.39 L 39.81 241.07 L 40.53 241.07 L 40.53 239.39 L 42.06 239.39 L 42.06 241.07 L 42.77 241.07 L 42.77 239.39 L 44.29 239.39 L 44.29 241.07 L 45 241.07 L 45 239.39 L 46.53 239.39 L 46.53 241.07 L 47.24 241.07 L 47.24 239.39 L 49.39 239.39 L 49.39 237.34 L 51.07 237.34 L 51.07 236.63 L 49.39 236.63 L 49.39 235.1 L 51.07 235.1 L 51.07 234.39 L 49.39 234.39 L 49.39 232.86 L 51.07 232.86 L 51.07 232.14 L 49.39 232.14 L 49.39 230.61 L 51.07 230.61 L 51.07 229.9 L 49.39 229.9 L 49.39 228.37 Z M 48.68 238.68 L 36.32 238.68 L 36.32 226.32 L 48.68 226.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 227px; margin-left: 62px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="62" y="239" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="35" y="250" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 265px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="269" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="35" y="300" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <path d="M 200 220 L 330 220 L 330 350 L 200 350 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 200 220 L 225 220 L 225 245 L 200 245 Z M 221.07 228.37 L 221.07 227.66 L 219.39 227.66 L 219.39 225.61 L 217.25 225.61 L 217.25 223.93 L 216.54 223.93 L 216.54 225.61 L 215 225.61 L 215 223.93 L 214.29 223.93 L 214.29 225.61 L 212.77 225.61 L 212.77 223.93 L 212.06 223.93 L 212.06 225.61 L 210.53 225.61 L 210.53 223.93 L 209.81 223.93 L 209.81 225.61 L 208.29 225.61 L 208.29 223.93 L 207.57 223.93 L 207.57 225.61 L 205.61 225.61 L 205.61 227.66 L 203.93 227.66 L 203.93 228.37 L 205.61 228.37 L 205.61 229.9 L 203.93 229.9 L 203.93 230.61 L 205.61 230.61 L 205.61 232.14 L 203.93 232.14 L 203.93 232.86 L 205.61 232.86 L 205.61 234.39 L 203.93 234.39 L 203.93 235.1 L 205.61 235.1 L 205.61 236.63 L 203.93 236.63 L 203.93 237.34 L 205.61 237.34 L 205.61 239.39 L 207.57 239.39 L 207.57 241.07 L 208.29 241.07 L 208.29 239.39 L 209.81 239.39 L 209.81 241.07 L 210.53 241.07 L 210.53 239.39 L 212.06 239.39 L 212.06 241.07 L 212.77 241.07 L 212.77 239.39 L 214.29 239.39 L 214.29 241.07 L 215 241.07 L 215 239.39 L 216.53 239.39 L 216.53 241.07 L 217.24 241.07 L 217.24 239.39 L 219.39 239.39 L 219.39 237.34 L 221.07 237.34 L 221.07 236.63 L 219.39 236.63 L 219.39 235.1 L 221.07 235.1 L 221.07 234.39 L 219.39 234.39 L 219.39 232.86 L 221.07 232.86 L 221.07 232.14 L 219.39 232.14 L 219.39 230.61 L 221.07 230.61 L 221.07 229.9 L 219.39 229.9 L 219.39 228.37 Z M 218.68 238.68 L 206.32 238.68 L 206.32 226.32 L 218.68 226.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 227px; margin-left: 232px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="239" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="205" y="300" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="205" y="250" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 265px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="269" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <path d="M 360 220 L 490 220 L 490 350 L 360 350 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 360 220 L 385 220 L 385 245 L 360 245 Z M 381.07 228.37 L 381.07 227.66 L 379.39 227.66 L 379.39 225.61 L 377.25 225.61 L 377.25 223.93 L 376.54 223.93 L 376.54 225.61 L 375 225.61 L 375 223.93 L 374.29 223.93 L 374.29 225.61 L 372.77 225.61 L 372.77 223.93 L 372.06 223.93 L 372.06 225.61 L 370.53 225.61 L 370.53 223.93 L 369.81 223.93 L 369.81 225.61 L 368.29 225.61 L 368.29 223.93 L 367.57 223.93 L 367.57 225.61 L 365.61 225.61 L 365.61 227.66 L 363.93 227.66 L 363.93 228.37 L 365.61 228.37 L 365.61 229.9 L 363.93 229.9 L 363.93 230.61 L 365.61 230.61 L 365.61 232.14 L 363.93 232.14 L 363.93 232.86 L 365.61 232.86 L 365.61 234.39 L 363.93 234.39 L 363.93 235.1 L 365.61 235.1 L 365.61 236.63 L 363.93 236.63 L 363.93 237.34 L 365.61 237.34 L 365.61 239.39 L 367.57 239.39 L 367.57 241.07 L 368.29 241.07 L 368.29 239.39 L 369.81 239.39 L 369.81 241.07 L 370.53 241.07 L 370.53 239.39 L 372.06 239.39 L 372.06 241.07 L 372.77 241.07 L 372.77 239.39 L 374.29 239.39 L 374.29 241.07 L 375 241.07 L 375 239.39 L 376.53 239.39 L 376.53 241.07 L 377.24 241.07 L 377.24 239.39 L 379.39 239.39 L 379.39 237.34 L 381.07 237.34 L 381.07 236.63 L 379.39 236.63 L 379.39 235.1 L 381.07 235.1 L 381.07 234.39 L 379.39 234.39 L 379.39 232.86 L 381.07 232.86 L 381.07 232.14 L 379.39 232.14 L 379.39 230.61 L 381.07 230.61 L 381.07 229.9 L 379.39 229.9 L 379.39 228.37 Z M 378.68 238.68 L 366.32 238.68 L 366.32 226.32 L 378.68 226.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 227px; margin-left: 392px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="392" y="239" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="365" y="250" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 265px; margin-left: 366px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="269" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="365" y="300" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 366px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <path d="M 242.02 140.27 L 259.9 140.27 L 259.9 123.87 L 268.1 123.87 L 268.1 140.27 L 285.98 140.27 L 285.98 148.47 L 268.1 148.47 L 268.1 164.87 L 259.9 164.87 L 259.9 148.47 L 242.02 148.47 Z" fill="#e51400" stroke="#000000" stroke-miterlimit="10" transform="rotate(45,264,144.37)" pointer-events="all"/>
        <rect x="180" y="0" width="160" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 260px;">
                        <div data-drawio-colors="color: #FF3333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                <font color="#000000" style="font-size: 14px;">
                                    <b>
                                        マスターノード障害時
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="260" y="19" fill="#FF3333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスターノード障害時
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>