<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1110px" height="432px" viewBox="-0.5 -0.5 1110 432" content="&lt;mxfile&gt;&lt;diagram id=&quot;7ISzqH4pcPf2vZZOh1lO&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="40" width="520" height="390" fill="none" stroke="#6600cc" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 52px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OpenSearchドメイン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="52" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    OpenSearchドメイン
                </text>
            </switch>
        </g>
        <rect x="20" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="94.5" y="108.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 0 40 L 40 40 L 40 80 L 0 80 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 10.47 53.53 L 4.59 53.53 C 4.26 53.53 4 53.8 4 54.12 L 4 75.29 C 4 75.61 4.26 75.88 4.59 75.88 L 10.47 75.88 C 10.79 75.88 11.06 75.61 11.06 75.29 L 11.06 54.12 C 11.06 53.8 10.79 53.53 10.47 53.53 Z M 5.18 74.7 L 5.18 54.71 L 9.88 54.71 L 9.88 74.7 Z M 13.41 57.65 L 12.23 57.65 L 12.23 50 C 12.23 49.68 12.5 49.42 12.82 49.42 L 18.7 49.42 C 19.03 49.42 19.29 49.68 19.29 50 L 19.29 52.36 L 18.11 52.36 L 18.11 50.59 L 13.41 50.59 Z M 18.11 71.17 L 19.29 71.17 L 19.29 75.29 C 19.29 75.61 19.03 75.88 18.7 75.88 L 12.82 75.88 C 12.5 75.88 12.23 75.61 12.23 75.29 L 12.23 66.47 L 13.41 66.47 L 13.41 74.7 L 18.11 74.7 Z M 21.64 51.77 L 20.46 51.77 L 20.46 44.71 C 20.46 44.39 20.73 44.12 21.05 44.12 L 26.93 44.12 C 27.26 44.12 27.52 44.39 27.52 44.71 L 27.52 52.94 L 26.34 52.94 L 26.34 45.3 L 21.64 45.3 Z M 26.34 72.35 L 27.52 72.35 L 27.52 75.29 C 27.52 75.61 27.26 75.88 26.93 75.88 L 21.05 75.88 C 20.73 75.88 20.46 75.61 20.46 75.29 L 20.46 71.76 L 21.64 71.76 L 21.64 74.7 L 26.34 74.7 Z M 35.75 48.24 L 35.75 68.23 L 34.58 68.23 L 34.58 48.83 L 29.87 48.83 L 29.87 55.3 L 28.7 55.3 L 28.7 48.24 C 28.7 47.92 28.96 47.65 29.28 47.65 L 35.17 47.65 C 35.49 47.65 35.75 47.92 35.75 48.24 Z M 29.91 65.61 C 30.45 64.5 30.76 63.26 30.76 61.95 C 30.76 57.33 27 53.57 22.38 53.57 C 17.76 53.57 14 57.33 14 61.95 C 14 66.57 17.76 70.33 22.38 70.33 C 24.09 70.33 25.67 69.82 27 68.94 L 32.06 73.5 C 32.48 73.88 33.01 74.07 33.54 74.07 C 34.14 74.07 34.75 73.82 35.19 73.34 C 36 72.43 35.93 71.03 35.02 70.21 Z M 15.18 61.95 C 15.18 57.98 18.41 54.75 22.38 54.75 C 26.35 54.75 29.58 57.98 29.58 61.95 C 29.58 65.92 26.35 69.16 22.38 69.16 C 18.41 69.16 15.18 65.92 15.18 61.95 Z M 34.31 72.55 C 33.93 72.98 33.27 73.01 32.84 72.63 L 27.94 68.21 C 28.46 67.74 28.92 67.23 29.31 66.65 L 34.23 71.08 C 34.66 71.47 34.69 72.13 34.31 72.55 Z M 22.38 56.07 C 19.14 56.07 16.5 58.71 16.5 61.95 C 16.5 65.2 19.14 67.84 22.38 67.84 C 25.63 67.84 28.27 65.2 28.27 61.95 C 28.27 58.71 25.63 56.07 22.38 56.07 Z M 22.38 66.66 C 19.78 66.66 17.67 64.55 17.67 61.95 C 17.67 59.36 19.78 57.24 22.38 57.24 C 24.98 57.24 27.09 59.36 27.09 61.95 C 27.09 64.55 24.98 66.66 22.38 66.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="76" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 124 143 L 124 140.82 L 118.55 140.82 L 118.55 137.55 C 118.55 136.94 118.06 136.45 117.45 136.45 L 114.18 136.45 L 114.18 131 L 112 131 L 112 136.45 L 107.64 136.45 L 107.64 131 L 105.45 131 L 105.45 136.45 L 101.09 136.45 L 101.09 131 L 98.91 131 L 98.91 136.45 L 94.55 136.45 L 94.55 131 L 92.36 131 L 92.36 136.45 L 88 136.45 L 88 131 L 85.82 131 L 85.82 136.45 L 82.55 136.45 C 81.94 136.45 81.45 136.94 81.45 137.55 L 81.45 140.82 L 76 140.82 L 76 143 L 81.45 143 L 81.45 147.36 L 76 147.36 L 76 149.55 L 81.45 149.55 L 81.45 153.91 L 76 153.91 L 76 156.09 L 81.45 156.09 L 81.45 160.45 L 76 160.45 L 76 162.64 L 81.45 162.64 L 81.45 167 L 76 167 L 76 169.18 L 81.45 169.18 L 81.45 172.45 C 81.45 173.06 81.94 173.55 82.55 173.55 L 85.82 173.55 L 85.82 179 L 88 179 L 88 173.55 L 92.36 173.55 L 92.36 179 L 94.55 179 L 94.55 173.55 L 98.91 173.55 L 98.91 179 L 101.09 179 L 101.09 173.55 L 105.45 173.55 L 105.45 179 L 107.64 179 L 107.64 173.55 L 112 173.55 L 112 179 L 114.18 179 L 114.18 173.55 L 117.45 173.55 C 118.06 173.55 118.55 173.06 118.55 172.45 L 118.55 169.18 L 124 169.18 L 124 167 L 118.55 167 L 118.55 162.64 L 124 162.64 L 124 160.45 L 118.55 160.45 L 118.55 156.09 L 124 156.09 L 124 153.91 L 118.55 153.91 L 118.55 149.55 L 124 149.55 L 124 147.36 L 118.55 147.36 L 118.55 143 Z M 83.64 171.36 L 83.64 138.64 L 116.36 138.64 L 116.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 100px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
                </text>
            </switch>
        </g>
        <rect x="190" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="264.5" y="108.5">
                Availability Zone 1c
            </text>
        </g>
        <rect x="350" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="424.5" y="108.5">
                Availability Zone 1d
            </text>
        </g>
        <rect x="241" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 289 143 L 289 140.82 L 283.55 140.82 L 283.55 137.55 C 283.55 136.94 283.06 136.45 282.45 136.45 L 279.18 136.45 L 279.18 131 L 277 131 L 277 136.45 L 272.64 136.45 L 272.64 131 L 270.45 131 L 270.45 136.45 L 266.09 136.45 L 266.09 131 L 263.91 131 L 263.91 136.45 L 259.55 136.45 L 259.55 131 L 257.36 131 L 257.36 136.45 L 253 136.45 L 253 131 L 250.82 131 L 250.82 136.45 L 247.55 136.45 C 246.94 136.45 246.45 136.94 246.45 137.55 L 246.45 140.82 L 241 140.82 L 241 143 L 246.45 143 L 246.45 147.36 L 241 147.36 L 241 149.55 L 246.45 149.55 L 246.45 153.91 L 241 153.91 L 241 156.09 L 246.45 156.09 L 246.45 160.45 L 241 160.45 L 241 162.64 L 246.45 162.64 L 246.45 167 L 241 167 L 241 169.18 L 246.45 169.18 L 246.45 172.45 C 246.45 173.06 246.94 173.55 247.55 173.55 L 250.82 173.55 L 250.82 179 L 253 179 L 253 173.55 L 257.36 173.55 L 257.36 179 L 259.55 179 L 259.55 173.55 L 263.91 173.55 L 263.91 179 L 266.09 179 L 266.09 173.55 L 270.45 173.55 L 270.45 179 L 272.64 179 L 272.64 173.55 L 277 173.55 L 277 179 L 279.18 179 L 279.18 173.55 L 282.45 173.55 C 283.06 173.55 283.55 173.06 283.55 172.45 L 283.55 169.18 L 289 169.18 L 289 167 L 283.55 167 L 283.55 162.64 L 289 162.64 L 289 160.45 L 283.55 160.45 L 283.55 156.09 L 289 156.09 L 289 153.91 L 283.55 153.91 L 283.55 149.55 L 289 149.55 L 289 147.36 L 283.55 147.36 L 283.55 143 Z M 248.64 171.36 L 248.64 138.64 L 281.36 138.64 L 281.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 265px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター
                </text>
            </switch>
        </g>
        <rect x="401" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 449 143 L 449 140.82 L 443.55 140.82 L 443.55 137.55 C 443.55 136.94 443.06 136.45 442.45 136.45 L 439.18 136.45 L 439.18 131 L 437 131 L 437 136.45 L 432.64 136.45 L 432.64 131 L 430.45 131 L 430.45 136.45 L 426.09 136.45 L 426.09 131 L 423.91 131 L 423.91 136.45 L 419.55 136.45 L 419.55 131 L 417.36 131 L 417.36 136.45 L 413 136.45 L 413 131 L 410.82 131 L 410.82 136.45 L 407.55 136.45 C 406.94 136.45 406.45 136.94 406.45 137.55 L 406.45 140.82 L 401 140.82 L 401 143 L 406.45 143 L 406.45 147.36 L 401 147.36 L 401 149.55 L 406.45 149.55 L 406.45 153.91 L 401 153.91 L 401 156.09 L 406.45 156.09 L 406.45 160.45 L 401 160.45 L 401 162.64 L 406.45 162.64 L 406.45 167 L 401 167 L 401 169.18 L 406.45 169.18 L 406.45 172.45 C 406.45 173.06 406.94 173.55 407.55 173.55 L 410.82 173.55 L 410.82 179 L 413 179 L 413 173.55 L 417.36 173.55 L 417.36 179 L 419.55 179 L 419.55 173.55 L 423.91 173.55 L 423.91 179 L 426.09 179 L 426.09 173.55 L 430.45 173.55 L 430.45 179 L 432.64 179 L 432.64 173.55 L 437 173.55 L 437 179 L 439.18 179 L 439.18 173.55 L 442.45 173.55 C 443.06 173.55 443.55 173.06 443.55 172.45 L 443.55 169.18 L 449 169.18 L 449 167 L 443.55 167 L 443.55 162.64 L 449 162.64 L 449 160.45 L 443.55 160.45 L 443.55 156.09 L 449 156.09 L 449 153.91 L 443.55 153.91 L 443.55 149.55 L 449 149.55 L 449 147.36 L 443.55 147.36 L 443.55 143 Z M 408.64 171.36 L 408.64 138.64 L 441.36 138.64 L 441.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 425px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
                </text>
            </switch>
        </g>
        <path d="M 30 230 L 160 230 L 160 400 L 30 400 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 30 230 L 55 230 L 55 255 L 30 255 Z M 51.07 238.37 L 51.07 237.66 L 49.39 237.66 L 49.39 235.61 L 47.25 235.61 L 47.25 233.93 L 46.54 233.93 L 46.54 235.61 L 45 235.61 L 45 233.93 L 44.29 233.93 L 44.29 235.61 L 42.77 235.61 L 42.77 233.93 L 42.06 233.93 L 42.06 235.61 L 40.53 235.61 L 40.53 233.93 L 39.81 233.93 L 39.81 235.61 L 38.29 235.61 L 38.29 233.93 L 37.57 233.93 L 37.57 235.61 L 35.61 235.61 L 35.61 237.66 L 33.93 237.66 L 33.93 238.37 L 35.61 238.37 L 35.61 239.9 L 33.93 239.9 L 33.93 240.61 L 35.61 240.61 L 35.61 242.14 L 33.93 242.14 L 33.93 242.86 L 35.61 242.86 L 35.61 244.39 L 33.93 244.39 L 33.93 245.1 L 35.61 245.1 L 35.61 246.63 L 33.93 246.63 L 33.93 247.34 L 35.61 247.34 L 35.61 249.39 L 37.57 249.39 L 37.57 251.07 L 38.29 251.07 L 38.29 249.39 L 39.81 249.39 L 39.81 251.07 L 40.53 251.07 L 40.53 249.39 L 42.06 249.39 L 42.06 251.07 L 42.77 251.07 L 42.77 249.39 L 44.29 249.39 L 44.29 251.07 L 45 251.07 L 45 249.39 L 46.53 249.39 L 46.53 251.07 L 47.24 251.07 L 47.24 249.39 L 49.39 249.39 L 49.39 247.34 L 51.07 247.34 L 51.07 246.63 L 49.39 246.63 L 49.39 245.1 L 51.07 245.1 L 51.07 244.39 L 49.39 244.39 L 49.39 242.86 L 51.07 242.86 L 51.07 242.14 L 49.39 242.14 L 49.39 240.61 L 51.07 240.61 L 51.07 239.9 L 49.39 239.9 L 49.39 238.37 Z M 48.68 248.68 L 36.32 248.68 L 36.32 236.32 L 48.68 236.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 62px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="62" y="249" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="35" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <path d="M 200 230 L 330 230 L 330 400 L 200 400 Z" fill="#b3b3b3" stroke="none" pointer-events="none"/>
        <path d="M 200 230 L 225 230 L 225 255 L 200 255 Z M 221.07 238.37 L 221.07 237.66 L 219.39 237.66 L 219.39 235.61 L 217.25 235.61 L 217.25 233.93 L 216.54 233.93 L 216.54 235.61 L 215 235.61 L 215 233.93 L 214.29 233.93 L 214.29 235.61 L 212.77 235.61 L 212.77 233.93 L 212.06 233.93 L 212.06 235.61 L 210.53 235.61 L 210.53 233.93 L 209.81 233.93 L 209.81 235.61 L 208.29 235.61 L 208.29 233.93 L 207.57 233.93 L 207.57 235.61 L 205.61 235.61 L 205.61 237.66 L 203.93 237.66 L 203.93 238.37 L 205.61 238.37 L 205.61 239.9 L 203.93 239.9 L 203.93 240.61 L 205.61 240.61 L 205.61 242.14 L 203.93 242.14 L 203.93 242.86 L 205.61 242.86 L 205.61 244.39 L 203.93 244.39 L 203.93 245.1 L 205.61 245.1 L 205.61 246.63 L 203.93 246.63 L 203.93 247.34 L 205.61 247.34 L 205.61 249.39 L 207.57 249.39 L 207.57 251.07 L 208.29 251.07 L 208.29 249.39 L 209.81 249.39 L 209.81 251.07 L 210.53 251.07 L 210.53 249.39 L 212.06 249.39 L 212.06 251.07 L 212.77 251.07 L 212.77 249.39 L 214.29 249.39 L 214.29 251.07 L 215 251.07 L 215 249.39 L 216.53 249.39 L 216.53 251.07 L 217.24 251.07 L 217.24 249.39 L 219.39 249.39 L 219.39 247.34 L 221.07 247.34 L 221.07 246.63 L 219.39 246.63 L 219.39 245.1 L 221.07 245.1 L 221.07 244.39 L 219.39 244.39 L 219.39 242.86 L 221.07 242.86 L 221.07 242.14 L 219.39 242.14 L 219.39 240.61 L 221.07 240.61 L 221.07 239.9 L 219.39 239.9 L 219.39 238.37 Z M 218.68 248.68 L 206.32 248.68 L 206.32 236.32 L 218.68 236.32 Z" fill="#000000" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 232px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="249" fill="#333333" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="205" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="205" y="310" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 325px; margin-left: 206px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <path d="M 233.13 364.93 L 259.06 364.93 L 259.06 338.65 L 272.03 338.65 L 272.03 364.93 L 297.96 364.93 L 297.96 377.89 L 272.03 377.89 L 272.03 404.17 L 259.06 404.17 L 259.06 377.89 L 233.13 377.89 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,265.55,371.41)" pointer-events="all"/>
        <path d="M 360 230 L 490 230 L 490 400 L 360 400 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 360 230 L 385 230 L 385 255 L 360 255 Z M 381.07 238.37 L 381.07 237.66 L 379.39 237.66 L 379.39 235.61 L 377.25 235.61 L 377.25 233.93 L 376.54 233.93 L 376.54 235.61 L 375 235.61 L 375 233.93 L 374.29 233.93 L 374.29 235.61 L 372.77 235.61 L 372.77 233.93 L 372.06 233.93 L 372.06 235.61 L 370.53 235.61 L 370.53 233.93 L 369.81 233.93 L 369.81 235.61 L 368.29 235.61 L 368.29 233.93 L 367.57 233.93 L 367.57 235.61 L 365.61 235.61 L 365.61 237.66 L 363.93 237.66 L 363.93 238.37 L 365.61 238.37 L 365.61 239.9 L 363.93 239.9 L 363.93 240.61 L 365.61 240.61 L 365.61 242.14 L 363.93 242.14 L 363.93 242.86 L 365.61 242.86 L 365.61 244.39 L 363.93 244.39 L 363.93 245.1 L 365.61 245.1 L 365.61 246.63 L 363.93 246.63 L 363.93 247.34 L 365.61 247.34 L 365.61 249.39 L 367.57 249.39 L 367.57 251.07 L 368.29 251.07 L 368.29 249.39 L 369.81 249.39 L 369.81 251.07 L 370.53 251.07 L 370.53 249.39 L 372.06 249.39 L 372.06 251.07 L 372.77 251.07 L 372.77 249.39 L 374.29 249.39 L 374.29 251.07 L 375 251.07 L 375 249.39 L 376.53 249.39 L 376.53 251.07 L 377.24 251.07 L 377.24 249.39 L 379.39 249.39 L 379.39 247.34 L 381.07 247.34 L 381.07 246.63 L 379.39 246.63 L 379.39 245.1 L 381.07 245.1 L 381.07 244.39 L 379.39 244.39 L 379.39 242.86 L 381.07 242.86 L 381.07 242.14 L 379.39 242.14 L 379.39 240.61 L 381.07 240.61 L 381.07 239.9 L 379.39 239.9 L 379.39 238.37 Z M 378.68 248.68 L 366.32 248.68 L 366.32 236.32 L 378.68 236.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 392px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="392" y="249" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="365" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 366px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="365" y="308" width="120" height="32" rx="4.8" ry="4.8" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 324px; margin-left: 366px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    シャード2
                                    <br/>
                                    レプリカ
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="328" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="366" y="360" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#ff3333" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 375px; margin-left: 367px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="426" y="379" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="35" y="310" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 325px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="35" y="360" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#ff3333" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 375px; margin-left: 36px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="95" y="379" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <path d="M 265 290 L 100.89 357.58" fill="none" stroke="#ff3333" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 96.03 359.57 L 101.17 353.67 L 100.89 357.58 L 103.84 360.15 Z" fill="#ff3333" stroke="#ff3333" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 265 340 L 419.68 359.21" fill="none" stroke="#ff3333" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 424.89 359.86 L 417.51 362.47 L 419.68 359.21 L 418.38 355.53 Z" fill="#ff3333" stroke="#ff3333" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="589" y="40" width="520" height="390" fill="none" stroke="#6600cc" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 468px; height: 1px; padding-top: 47px; margin-left: 641px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OpenSearchドメイン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="641" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    OpenSearchドメイン
                </text>
            </switch>
        </g>
        <rect x="609" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="683.5" y="108.5">
                Availability Zone 1a
            </text>
        </g>
        <path d="M 589 40 L 629 40 L 629 80 L 589 80 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 599.47 53.53 L 593.59 53.53 C 593.26 53.53 593 53.8 593 54.12 L 593 75.29 C 593 75.61 593.26 75.88 593.59 75.88 L 599.47 75.88 C 599.79 75.88 600.06 75.61 600.06 75.29 L 600.06 54.12 C 600.06 53.8 599.79 53.53 599.47 53.53 Z M 594.18 74.7 L 594.18 54.71 L 598.88 54.71 L 598.88 74.7 Z M 602.41 57.65 L 601.23 57.65 L 601.23 50 C 601.23 49.68 601.5 49.42 601.82 49.42 L 607.7 49.42 C 608.03 49.42 608.29 49.68 608.29 50 L 608.29 52.36 L 607.11 52.36 L 607.11 50.59 L 602.41 50.59 Z M 607.11 71.17 L 608.29 71.17 L 608.29 75.29 C 608.29 75.61 608.03 75.88 607.7 75.88 L 601.82 75.88 C 601.5 75.88 601.23 75.61 601.23 75.29 L 601.23 66.47 L 602.41 66.47 L 602.41 74.7 L 607.11 74.7 Z M 610.64 51.77 L 609.46 51.77 L 609.46 44.71 C 609.46 44.39 609.73 44.12 610.05 44.12 L 615.93 44.12 C 616.26 44.12 616.52 44.39 616.52 44.71 L 616.52 52.94 L 615.34 52.94 L 615.34 45.3 L 610.64 45.3 Z M 615.34 72.35 L 616.52 72.35 L 616.52 75.29 C 616.52 75.61 616.26 75.88 615.93 75.88 L 610.05 75.88 C 609.73 75.88 609.46 75.61 609.46 75.29 L 609.46 71.76 L 610.64 71.76 L 610.64 74.7 L 615.34 74.7 Z M 624.75 48.24 L 624.75 68.23 L 623.58 68.23 L 623.58 48.83 L 618.87 48.83 L 618.87 55.3 L 617.7 55.3 L 617.7 48.24 C 617.7 47.92 617.96 47.65 618.28 47.65 L 624.17 47.65 C 624.49 47.65 624.75 47.92 624.75 48.24 Z M 618.91 65.61 C 619.45 64.5 619.76 63.26 619.76 61.95 C 619.76 57.33 616 53.57 611.38 53.57 C 606.76 53.57 603 57.33 603 61.95 C 603 66.57 606.76 70.33 611.38 70.33 C 613.09 70.33 614.67 69.82 616 68.94 L 621.06 73.5 C 621.48 73.88 622.01 74.07 622.54 74.07 C 623.14 74.07 623.75 73.82 624.19 73.34 C 625 72.43 624.93 71.03 624.02 70.21 Z M 604.18 61.95 C 604.18 57.98 607.41 54.75 611.38 54.75 C 615.35 54.75 618.58 57.98 618.58 61.95 C 618.58 65.92 615.35 69.16 611.38 69.16 C 607.41 69.16 604.18 65.92 604.18 61.95 Z M 623.31 72.55 C 622.93 72.98 622.27 73.01 621.84 72.63 L 616.94 68.21 C 617.46 67.74 617.92 67.23 618.31 66.65 L 623.23 71.08 C 623.66 71.47 623.69 72.13 623.31 72.55 Z M 611.38 56.07 C 608.14 56.07 605.5 58.71 605.5 61.95 C 605.5 65.2 608.14 67.84 611.38 67.84 C 614.63 67.84 617.27 65.2 617.27 61.95 C 617.27 58.71 614.63 56.07 611.38 56.07 Z M 611.38 66.66 C 608.78 66.66 606.67 64.55 606.67 61.95 C 606.67 59.36 608.78 57.24 611.38 57.24 C 613.98 57.24 616.09 59.36 616.09 61.95 C 616.09 64.55 613.98 66.66 611.38 66.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <rect x="665" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 713 143 L 713 140.82 L 707.55 140.82 L 707.55 137.55 C 707.55 136.94 707.06 136.45 706.45 136.45 L 703.18 136.45 L 703.18 131 L 701 131 L 701 136.45 L 696.64 136.45 L 696.64 131 L 694.45 131 L 694.45 136.45 L 690.09 136.45 L 690.09 131 L 687.91 131 L 687.91 136.45 L 683.55 136.45 L 683.55 131 L 681.36 131 L 681.36 136.45 L 677 136.45 L 677 131 L 674.82 131 L 674.82 136.45 L 671.55 136.45 C 670.94 136.45 670.45 136.94 670.45 137.55 L 670.45 140.82 L 665 140.82 L 665 143 L 670.45 143 L 670.45 147.36 L 665 147.36 L 665 149.55 L 670.45 149.55 L 670.45 153.91 L 665 153.91 L 665 156.09 L 670.45 156.09 L 670.45 160.45 L 665 160.45 L 665 162.64 L 670.45 162.64 L 670.45 167 L 665 167 L 665 169.18 L 670.45 169.18 L 670.45 172.45 C 670.45 173.06 670.94 173.55 671.55 173.55 L 674.82 173.55 L 674.82 179 L 677 179 L 677 173.55 L 681.36 173.55 L 681.36 179 L 683.55 179 L 683.55 173.55 L 687.91 173.55 L 687.91 179 L 690.09 179 L 690.09 173.55 L 694.45 173.55 L 694.45 179 L 696.64 179 L 696.64 173.55 L 701 173.55 L 701 179 L 703.18 179 L 703.18 173.55 L 706.45 173.55 C 707.06 173.55 707.55 173.06 707.55 172.45 L 707.55 169.18 L 713 169.18 L 713 167 L 707.55 167 L 707.55 162.64 L 713 162.64 L 713 160.45 L 707.55 160.45 L 707.55 156.09 L 713 156.09 L 713 153.91 L 707.55 153.91 L 707.55 149.55 L 713 149.55 L 713 147.36 L 707.55 147.36 L 707.55 143 Z M 672.64 171.36 L 672.64 138.64 L 705.36 138.64 L 705.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 689px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="689" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
                </text>
            </switch>
        </g>
        <rect x="779" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="853.5" y="108.5">
                Availability Zone 1c
            </text>
        </g>
        <rect x="939" y="91" width="150" height="319" fill="none" stroke="#147eba" stroke-dasharray="3 3" pointer-events="all"/>
        <g fill="#147EBA" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="1013.5" y="108.5">
                Availability Zone 1d
            </text>
        </g>
        <rect x="830" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 878 143 L 878 140.82 L 872.55 140.82 L 872.55 137.55 C 872.55 136.94 872.06 136.45 871.45 136.45 L 868.18 136.45 L 868.18 131 L 866 131 L 866 136.45 L 861.64 136.45 L 861.64 131 L 859.45 131 L 859.45 136.45 L 855.09 136.45 L 855.09 131 L 852.91 131 L 852.91 136.45 L 848.55 136.45 L 848.55 131 L 846.36 131 L 846.36 136.45 L 842 136.45 L 842 131 L 839.82 131 L 839.82 136.45 L 836.55 136.45 C 835.94 136.45 835.45 136.94 835.45 137.55 L 835.45 140.82 L 830 140.82 L 830 143 L 835.45 143 L 835.45 147.36 L 830 147.36 L 830 149.55 L 835.45 149.55 L 835.45 153.91 L 830 153.91 L 830 156.09 L 835.45 156.09 L 835.45 160.45 L 830 160.45 L 830 162.64 L 835.45 162.64 L 835.45 167 L 830 167 L 830 169.18 L 835.45 169.18 L 835.45 172.45 C 835.45 173.06 835.94 173.55 836.55 173.55 L 839.82 173.55 L 839.82 179 L 842 179 L 842 173.55 L 846.36 173.55 L 846.36 179 L 848.55 179 L 848.55 173.55 L 852.91 173.55 L 852.91 179 L 855.09 179 L 855.09 173.55 L 859.45 173.55 L 859.45 179 L 861.64 179 L 861.64 173.55 L 866 173.55 L 866 179 L 868.18 179 L 868.18 173.55 L 871.45 173.55 C 872.06 173.55 872.55 173.06 872.55 172.45 L 872.55 169.18 L 878 169.18 L 878 167 L 872.55 167 L 872.55 162.64 L 878 162.64 L 878 160.45 L 872.55 160.45 L 872.55 156.09 L 878 156.09 L 878 153.91 L 872.55 153.91 L 872.55 149.55 L 878 149.55 L 878 147.36 L 872.55 147.36 L 872.55 143 Z M 837.64 171.36 L 837.64 138.64 L 870.36 138.64 L 870.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 854px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="854" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター
                </text>
            </switch>
        </g>
        <rect x="990" y="131" width="48" height="48" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 1038 143 L 1038 140.82 L 1032.55 140.82 L 1032.55 137.55 C 1032.55 136.94 1032.06 136.45 1031.45 136.45 L 1028.18 136.45 L 1028.18 131 L 1026 131 L 1026 136.45 L 1021.64 136.45 L 1021.64 131 L 1019.45 131 L 1019.45 136.45 L 1015.09 136.45 L 1015.09 131 L 1012.91 131 L 1012.91 136.45 L 1008.55 136.45 L 1008.55 131 L 1006.36 131 L 1006.36 136.45 L 1002 136.45 L 1002 131 L 999.82 131 L 999.82 136.45 L 996.55 136.45 C 995.94 136.45 995.45 136.94 995.45 137.55 L 995.45 140.82 L 990 140.82 L 990 143 L 995.45 143 L 995.45 147.36 L 990 147.36 L 990 149.55 L 995.45 149.55 L 995.45 153.91 L 990 153.91 L 990 156.09 L 995.45 156.09 L 995.45 160.45 L 990 160.45 L 990 162.64 L 995.45 162.64 L 995.45 167 L 990 167 L 990 169.18 L 995.45 169.18 L 995.45 172.45 C 995.45 173.06 995.94 173.55 996.55 173.55 L 999.82 173.55 L 999.82 179 L 1002 179 L 1002 173.55 L 1006.36 173.55 L 1006.36 179 L 1008.55 179 L 1008.55 173.55 L 1012.91 173.55 L 1012.91 179 L 1015.09 179 L 1015.09 173.55 L 1019.45 173.55 L 1019.45 179 L 1021.64 179 L 1021.64 173.55 L 1026 173.55 L 1026 179 L 1028.18 179 L 1028.18 173.55 L 1031.45 173.55 C 1032.06 173.55 1032.55 173.06 1032.55 172.45 L 1032.55 169.18 L 1038 169.18 L 1038 167 L 1032.55 167 L 1032.55 162.64 L 1038 162.64 L 1038 160.45 L 1032.55 160.45 L 1032.55 156.09 L 1038 156.09 L 1038 153.91 L 1032.55 153.91 L 1032.55 149.55 L 1038 149.55 L 1038 147.36 L 1032.55 147.36 L 1032.55 143 Z M 997.64 171.36 L 997.64 138.64 L 1030.36 138.64 L 1030.36 171.36 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 186px; margin-left: 1014px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                マスター候補
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="198" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    マスター候補
                </text>
            </switch>
        </g>
        <path d="M 619 230 L 749 230 L 749 400 L 619 400 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 619 230 L 644 230 L 644 255 L 619 255 Z M 640.07 238.37 L 640.07 237.66 L 638.39 237.66 L 638.39 235.61 L 636.25 235.61 L 636.25 233.93 L 635.54 233.93 L 635.54 235.61 L 634 235.61 L 634 233.93 L 633.29 233.93 L 633.29 235.61 L 631.77 235.61 L 631.77 233.93 L 631.06 233.93 L 631.06 235.61 L 629.53 235.61 L 629.53 233.93 L 628.81 233.93 L 628.81 235.61 L 627.29 235.61 L 627.29 233.93 L 626.57 233.93 L 626.57 235.61 L 624.61 235.61 L 624.61 237.66 L 622.93 237.66 L 622.93 238.37 L 624.61 238.37 L 624.61 239.9 L 622.93 239.9 L 622.93 240.61 L 624.61 240.61 L 624.61 242.14 L 622.93 242.14 L 622.93 242.86 L 624.61 242.86 L 624.61 244.39 L 622.93 244.39 L 622.93 245.1 L 624.61 245.1 L 624.61 246.63 L 622.93 246.63 L 622.93 247.34 L 624.61 247.34 L 624.61 249.39 L 626.57 249.39 L 626.57 251.07 L 627.29 251.07 L 627.29 249.39 L 628.81 249.39 L 628.81 251.07 L 629.53 251.07 L 629.53 249.39 L 631.06 249.39 L 631.06 251.07 L 631.77 251.07 L 631.77 249.39 L 633.29 249.39 L 633.29 251.07 L 634 251.07 L 634 249.39 L 635.53 249.39 L 635.53 251.07 L 636.24 251.07 L 636.24 249.39 L 638.39 249.39 L 638.39 247.34 L 640.07 247.34 L 640.07 246.63 L 638.39 246.63 L 638.39 245.1 L 640.07 245.1 L 640.07 244.39 L 638.39 244.39 L 638.39 242.86 L 640.07 242.86 L 640.07 242.14 L 638.39 242.14 L 638.39 240.61 L 640.07 240.61 L 640.07 239.9 L 638.39 239.9 L 638.39 238.37 Z M 637.68 248.68 L 625.32 248.68 L 625.32 236.32 L 637.68 236.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 651px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="651" y="249" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="624" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 625px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="684" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <path d="M 789 230 L 919 230 L 919 400 L 789 400 Z" fill="#b3b3b3" stroke="none" pointer-events="none"/>
        <path d="M 789 230 L 814 230 L 814 255 L 789 255 Z M 810.07 238.37 L 810.07 237.66 L 808.39 237.66 L 808.39 235.61 L 806.25 235.61 L 806.25 233.93 L 805.54 233.93 L 805.54 235.61 L 804 235.61 L 804 233.93 L 803.29 233.93 L 803.29 235.61 L 801.77 235.61 L 801.77 233.93 L 801.06 233.93 L 801.06 235.61 L 799.53 235.61 L 799.53 233.93 L 798.81 233.93 L 798.81 235.61 L 797.29 235.61 L 797.29 233.93 L 796.57 233.93 L 796.57 235.61 L 794.61 235.61 L 794.61 237.66 L 792.93 237.66 L 792.93 238.37 L 794.61 238.37 L 794.61 239.9 L 792.93 239.9 L 792.93 240.61 L 794.61 240.61 L 794.61 242.14 L 792.93 242.14 L 792.93 242.86 L 794.61 242.86 L 794.61 244.39 L 792.93 244.39 L 792.93 245.1 L 794.61 245.1 L 794.61 246.63 L 792.93 246.63 L 792.93 247.34 L 794.61 247.34 L 794.61 249.39 L 796.57 249.39 L 796.57 251.07 L 797.29 251.07 L 797.29 249.39 L 798.81 249.39 L 798.81 251.07 L 799.53 251.07 L 799.53 249.39 L 801.06 249.39 L 801.06 251.07 L 801.77 251.07 L 801.77 249.39 L 803.29 249.39 L 803.29 251.07 L 804 251.07 L 804 249.39 L 805.53 249.39 L 805.53 251.07 L 806.24 251.07 L 806.24 249.39 L 808.39 249.39 L 808.39 247.34 L 810.07 247.34 L 810.07 246.63 L 808.39 246.63 L 808.39 245.1 L 810.07 245.1 L 810.07 244.39 L 808.39 244.39 L 808.39 242.86 L 810.07 242.86 L 810.07 242.14 L 808.39 242.14 L 808.39 240.61 L 810.07 240.61 L 810.07 239.9 L 808.39 239.9 L 808.39 238.37 Z M 807.68 248.68 L 795.32 248.68 L 795.32 236.32 L 807.68 236.32 Z" fill="#000000" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 821px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="821" y="249" fill="#333333" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="794" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 795px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="854" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="794" y="310" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 325px; margin-left: 795px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                プライマリ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="854" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <path d="M 822.13 364.93 L 848.06 364.93 L 848.06 338.65 L 861.03 338.65 L 861.03 364.93 L 886.96 364.93 L 886.96 377.89 L 861.03 377.89 L 861.03 404.17 L 848.06 404.17 L 848.06 377.89 L 822.13 377.89 Z" fill="#e51400" stroke="#b20000" stroke-miterlimit="10" transform="rotate(-45,854.55,371.41)" pointer-events="all"/>
        <path d="M 949 230 L 1079 230 L 1079 400 L 949 400 Z" fill="none" stroke="#d86613" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 949 230 L 974 230 L 974 255 L 949 255 Z M 970.07 238.37 L 970.07 237.66 L 968.39 237.66 L 968.39 235.61 L 966.25 235.61 L 966.25 233.93 L 965.54 233.93 L 965.54 235.61 L 964 235.61 L 964 233.93 L 963.29 233.93 L 963.29 235.61 L 961.77 235.61 L 961.77 233.93 L 961.06 233.93 L 961.06 235.61 L 959.53 235.61 L 959.53 233.93 L 958.81 233.93 L 958.81 235.61 L 957.29 235.61 L 957.29 233.93 L 956.57 233.93 L 956.57 235.61 L 954.61 235.61 L 954.61 237.66 L 952.93 237.66 L 952.93 238.37 L 954.61 238.37 L 954.61 239.9 L 952.93 239.9 L 952.93 240.61 L 954.61 240.61 L 954.61 242.14 L 952.93 242.14 L 952.93 242.86 L 954.61 242.86 L 954.61 244.39 L 952.93 244.39 L 952.93 245.1 L 954.61 245.1 L 954.61 246.63 L 952.93 246.63 L 952.93 247.34 L 954.61 247.34 L 954.61 249.39 L 956.57 249.39 L 956.57 251.07 L 957.29 251.07 L 957.29 249.39 L 958.81 249.39 L 958.81 251.07 L 959.53 251.07 L 959.53 249.39 L 961.06 249.39 L 961.06 251.07 L 961.77 251.07 L 961.77 249.39 L 963.29 249.39 L 963.29 251.07 L 964 251.07 L 964 249.39 L 965.53 249.39 L 965.53 251.07 L 966.24 251.07 L 966.24 249.39 L 968.39 249.39 L 968.39 247.34 L 970.07 247.34 L 970.07 246.63 L 968.39 246.63 L 968.39 245.1 L 970.07 245.1 L 970.07 244.39 L 968.39 244.39 L 968.39 242.86 L 970.07 242.86 L 970.07 242.14 L 968.39 242.14 L 968.39 240.61 L 970.07 240.61 L 970.07 239.9 L 968.39 239.9 L 968.39 238.37 Z M 967.68 248.68 L 955.32 248.68 L 955.32 236.32 L 967.68 236.32 Z" fill="#d86613" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 237px; margin-left: 981px;">
                        <div data-drawio-colors="color: #D86613; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(216, 102, 19); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                データノード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="981" y="249" fill="#D86613" font-family="Helvetica" font-size="12px">
                    データノード
                </text>
            </switch>
        </g>
        <rect x="954" y="260" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 275px; margin-left: 955px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード1
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード1...
                </text>
            </switch>
        </g>
        <rect x="954" y="308" width="120" height="32" rx="4.8" ry="4.8" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 324px; margin-left: 955px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    シャード2
                                    <br/>
                                    レプリカ
                                    <font color="#ff0000">
                                        →プライマリ
                                    </font>
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1014" y="328" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="624" y="310" width="120" height="30" rx="4.5" ry="4.5" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 325px; margin-left: 625px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                シャード2
                                <br/>
                                レプリカ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="684" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    シャード2...
                </text>
            </switch>
        </g>
        <rect x="155" y="0" width="200" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 255px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                OpenSearch 1.3の場合
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="255" y="20" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">
                    OpenSearch 1.3の場合
                </text>
            </switch>
        </g>
        <rect x="744" y="0" width="200" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 844px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                OpenSearch 2.3の場合
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="844" y="20" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">
                    OpenSearch 2.3の場合
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>