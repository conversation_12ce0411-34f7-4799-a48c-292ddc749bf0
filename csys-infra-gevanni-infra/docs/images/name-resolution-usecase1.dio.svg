<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="501px" height="491px" viewBox="-0.5 -0.5 501 491" content="&lt;mxfile&gt;&lt;diagram id=&quot;3Jfb9Ys_67MIZUrFFRst&quot; name=&quot;ページ1&quot;&gt;7Vpbj+o2EP41SO1DURzHkH3k2lbaSquu1MvTyiQGrDVx5JjL9tfXThxIYsMeDiwUNbtIZMb3zzPf2EM6cLTa/SxwuvyNx4R1fC/edeC44/sAwCf1pTUfRgOR0SwEjY3uoHil/xCj9Ix2TWOS1SpKzpmkaV0Z8SQhkazpsBB8W68256w+aooXxFK8RpjZ2j9pLJeFNvT7B/0vhC6WZmQUwqJghcu6ZiHZEsd8W1HBSQeOBOeyeFrtRoRp8EpYinbTI6X7eQmSyG9pAM2MN5itzdrMxORHuVjB10lMdAOvA4fbJZXkNcWRLt2q/VW6pVwxJQH1aLojQpLd0TmB/UqViRC+IlJ8qCqmwZPBxhjHT+Wmbw9II8/olhWUg7IhNru72Hd9QEA9GBDcgARXxmNOGRtxxoWSE56oSsNMCv5OSmXHh3G/1wOhKtGwUWVlA0YXiSqTPN1DauHnQPkopKBXx9T3bEwBcmDq966AqW9h+sfLyII15TSR+ShoqD5q3JHXQapkpKWujxqKptyvK4At6T7qiqbcrytAs3vQGB80J1hRWFKte68xvleZoPrAIV9LRhNlJiWDaWNbCBxTZQYNi6rYm8sc5zyRhkOBX8oGeN2rYkmJ1VjC9JHvBBGTDSk2pKjDGE4zOtu3EiRai4xuyO8kKzrXWkVoqX5e7Raa+7t4mwXdhXKZNJ/+r2osZ+nbJo1cvuEHYQgCtyO53QUbiZG51D0qLGiyeM6lMfTM+itDDAbD/lC7X4yz5d6vr+F1Xt3r+rbTBaGLyLxrON03MFmxL9ZC/SML3cdTPCt78E4C0KRyAG9KO+gRuHy/T5+b1W1Q61moTUavFnDZO5HR0uDmpG4nfbso3EnjNpXXquXk6hihqXTp+rYS2NVKPraVLp0r+DRbA0dr0Gh9nPobpKX+p3oDrZCgyqb9cOIFlbIxVWQtaU69CRfaiGrGrNqMPTQCfZdFz/O/Jj2W5v2MZ4S98Iya7mdcSr76lJwjoiNMw70+CVM4Sws45nSn5+GONYJkfC0iUkQaFaUyV8whUXZFbwyQg8TRFXyxb/kdidVFxIh29D++oAIUowsLncRiQcp65gKm+z+5bEEYlirq1+LCRYsMLcKZM7X1JIn1vInY0Ij8MPzxNAVd5DMGyKZLBMq7bZcwlf9z3tA8tYFjHqJs/83AeqkPmFKEuqgW6JEd58s7f81HwiuYz5NlPjMcvbfW87jWA8LbmQ+El1OszbAgsBkW3otggX0TGDwP2xPdA53ongI0nvrnnejQAHpD9P850TGcqem9MY7jtxlmONHX/lMOe8ZFHvgNigocV/kvOgTCwHLVKzBUeROvnQHDe1FUOZuWolqKainqeygKBnekKNDmjB7dfduc0ffnjM711caPca6s+JcdJuwU+DUOE46zxN3ySdDiIkc6adAmBG6WEDj/tH3XhAC62EOAIyGAbBcp7+V3yAjYv4yNGF/HU6Gsog3bDxS221P3p2E70pY9zy37Kr/q+4dkwC0Cdu9MOlILEx9/aRy7qBT/NrDmwnhXkz6q0gsRVE0xfxPk5EsQtehfvoFXTXZ6dwv/5Ya23NZyW8tt53FbEHwhtynx8GJrXlZ5PRhO/gU=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="500" height="490" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="70" y="210" width="150" height="260" fill="none" stroke="#d76618" pointer-events="all"/>
        <path d="M 10 80 L 490 80 L 490 480 L 10 480 Z" fill="none" stroke="#248814" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 20.59 86.65 C 20.53 86.65 20.48 86.65 20.42 86.65 L 20.42 86.65 C 19.11 86.68 18.03 87.24 17.14 88.25 C 17.13 88.25 17.13 88.25 17.13 88.25 C 16.2 89.36 15.87 90.52 15.96 91.73 C 14.81 92.06 14.12 92.92 13.76 93.74 C 13.75 93.75 13.75 93.76 13.74 93.78 C 13.33 95.05 13.68 96.36 14.24 97.16 C 14.25 97.17 14.25 97.17 14.26 97.18 C 14.94 98.05 15.97 98.53 17.02 98.53 L 28.17 98.53 C 29.19 98.53 30.07 98.16 30.8 97.37 C 31.25 96.94 31.49 96.29 31.58 95.59 C 31.67 94.9 31.61 94.16 31.32 93.55 C 31.31 93.54 31.31 93.53 31.31 93.52 C 30.8 92.62 29.95 91.81 28.76 91.64 C 28.74 90.79 28.28 89.99 27.68 89.56 C 27.67 89.55 27.66 89.55 27.65 89.54 C 27.01 89.18 26.4 89.14 25.91 89.3 C 25.6 89.4 25.36 89.56 25.14 89.74 C 24.51 88.36 23.43 87.18 21.81 86.79 C 21.81 86.79 21.81 86.79 21.81 86.79 C 21.38 86.7 20.97 86.65 20.59 86.65 Z M 20.43 87.38 C 20.8 87.38 21.2 87.43 21.64 87.53 C 23.16 87.89 24.15 89.07 24.66 90.48 C 24.71 90.6 24.81 90.69 24.94 90.72 C 25.07 90.74 25.2 90.7 25.29 90.61 C 25.54 90.34 25.83 90.11 26.14 90.01 C 26.44 89.91 26.78 89.92 27.26 90.18 C 27.67 90.49 28.11 91.31 28.03 91.9 C 28.01 92.01 28.05 92.12 28.12 92.2 C 28.19 92.28 28.29 92.33 28.39 92.33 C 29.46 92.34 30.16 93.02 30.64 93.88 C 30.85 94.3 30.91 94.92 30.84 95.5 C 30.76 96.07 30.53 96.59 30.28 96.83 C 30.27 96.84 30.27 96.85 30.26 96.85 C 29.65 97.53 29.03 97.78 28.17 97.78 L 17.02 97.78 C 16.2 97.78 15.39 97.41 14.85 96.73 C 14.44 96.13 14.14 95.02 14.46 94.02 C 14.79 93.27 15.36 92.55 16.41 92.36 C 16.6 92.32 16.74 92.14 16.71 91.94 C 16.56 90.79 16.8 89.81 17.7 88.74 C 18.49 87.85 19.33 87.39 20.43 87.38 Z M 22.2 90.7 C 21.77 90.7 21.4 90.93 21.13 91.21 C 20.85 91.5 20.64 91.85 20.64 92.25 L 20.64 92.71 L 20.14 92.71 C 20.04 92.71 19.94 92.75 19.87 92.82 C 19.8 92.89 19.76 92.98 19.76 93.08 L 19.76 95.7 C 19.76 95.8 19.8 95.89 19.87 95.96 C 19.94 96.03 20.04 96.07 20.14 96.07 L 24.16 96.07 C 24.26 96.07 24.35 96.03 24.42 95.96 C 24.49 95.89 24.53 95.8 24.53 95.7 L 24.53 93.08 C 24.53 92.98 24.49 92.89 24.42 92.82 C 24.35 92.75 24.26 92.71 24.16 92.71 L 23.68 92.71 L 23.68 92.25 C 23.68 91.84 23.47 91.47 23.21 91.2 C 22.94 90.92 22.61 90.7 22.2 90.7 Z M 22.2 91.45 C 22.29 91.45 22.5 91.54 22.67 91.72 C 22.83 91.89 22.93 92.11 22.93 92.25 L 22.93 92.71 L 21.39 92.71 L 21.39 92.25 C 21.39 92.15 21.49 91.91 21.66 91.74 C 21.83 91.56 22.06 91.45 22.2 91.45 Z M 20.51 93.46 L 23.78 93.46 L 23.78 95.32 L 20.51 95.32 Z M 10 105 L 10 80 L 35 80 L 35 105 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 448px; height: 1px; padding-top: 87px; margin-left: 42px;">
                        <div data-drawio-colors="color: #AAB7B8; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(170, 183, 184); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="42" y="99" fill="#AAB7B8" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="300" y="210" width="150" height="260" fill="none" stroke="#d76618" pointer-events="none"/>
        <path d="M 300 210 L 345 210 L 345 255 L 300 255 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 337.33 237.4 L 332.28 234.37 L 332.28 227.15 C 332.28 226.93 332.16 226.72 331.97 226.61 L 324.71 222.37 L 324.71 216.27 L 337.33 223.72 Z M 338.28 222.82 L 324.4 214.62 C 324.2 214.5 323.96 214.5 323.76 214.61 C 323.57 214.72 323.45 214.93 323.45 215.16 L 323.45 222.73 C 323.45 222.96 323.57 223.16 323.76 223.28 L 331.02 227.51 L 331.02 234.72 C 331.02 234.94 331.14 235.15 331.33 235.26 L 337.64 239.05 C 337.74 239.11 337.85 239.14 337.96 239.14 C 338.07 239.14 338.18 239.11 338.27 239.06 C 338.47 238.95 338.59 238.74 338.59 238.51 L 338.59 223.36 C 338.59 223.14 338.47 222.93 338.28 222.82 Z M 322.47 249.15 L 307.67 241.29 L 307.67 223.72 L 320.29 216.27 L 320.29 222.39 L 313.64 226.62 C 313.46 226.73 313.35 226.93 313.35 227.15 L 313.35 237.88 C 313.35 238.11 313.48 238.33 313.69 238.44 L 322.18 242.86 C 322.36 242.95 322.58 242.95 322.76 242.86 L 331 238.6 L 336.07 241.64 Z M 337.66 241.12 L 331.34 237.34 C 331.16 237.22 330.92 237.22 330.73 237.32 L 322.47 241.58 L 314.61 237.5 L 314.61 227.5 L 321.26 223.27 C 321.44 223.15 321.55 222.95 321.55 222.73 L 321.55 215.16 C 321.55 214.93 321.43 214.72 321.24 214.61 C 321.04 214.5 320.8 214.5 320.6 214.62 L 306.72 222.82 C 306.53 222.93 306.41 223.14 306.41 223.36 L 306.41 241.67 C 306.41 241.9 306.54 242.11 306.74 242.22 L 322.17 250.43 C 322.27 250.48 322.37 250.5 322.47 250.5 C 322.58 250.5 322.68 250.47 322.78 250.42 L 337.64 242.22 C 337.83 242.11 337.96 241.9 337.96 241.68 C 337.96 241.45 337.85 241.24 337.66 241.12 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 262px; margin-left: 323px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="323" y="274" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 375 308 L 375 383.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 375 388.88 L 371.5 381.88 L 375 383.63 L 378.5 381.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 376.29 299.51 L 390.19 299.51 L 390.19 297.32 L 376.29 297.32 Z M 376.29 288.69 L 390.19 288.69 L 390.19 286.51 L 376.29 286.51 Z M 376.29 277.88 L 390.19 277.88 L 390.19 275.7 L 376.29 275.7 Z M 370.43 299.64 L 370.43 297.19 L 372.88 297.19 L 372.88 299.64 Z M 369.34 301.82 L 373.97 301.82 C 374.57 301.82 375.06 301.33 375.06 300.73 L 375.06 296.1 C 375.06 295.49 374.57 295.01 373.97 295.01 L 369.34 295.01 C 368.73 295.01 368.25 295.49 368.25 296.1 L 368.25 300.73 C 368.25 301.33 368.73 301.82 369.34 301.82 Z M 370.43 288.83 L 370.43 286.38 L 372.88 286.38 L 372.88 288.83 Z M 369.34 291.01 L 373.97 291.01 C 374.57 291.01 375.06 290.52 375.06 289.92 L 375.06 285.29 C 375.06 284.68 374.57 284.2 373.97 284.2 L 369.34 284.2 C 368.73 284.2 368.25 284.68 368.25 285.29 L 368.25 289.92 C 368.25 290.52 368.73 291.01 369.34 291.01 Z M 370.43 278.02 L 370.43 275.57 L 372.88 275.57 L 372.88 278.02 Z M 369.34 280.2 L 373.97 280.2 C 374.57 280.2 375.06 279.71 375.06 279.11 L 375.06 274.48 C 375.06 273.87 374.57 273.39 373.97 273.39 L 369.34 273.39 C 368.73 273.39 368.25 273.87 368.25 274.48 L 368.25 279.11 C 368.25 279.71 368.73 280.2 369.34 280.2 Z M 366.57 305.82 L 366.57 270.93 L 392.19 270.93 L 392.19 305.82 Z M 364.38 269.84 L 364.38 301.19 L 361.93 301.19 L 361.93 266.3 L 387.55 266.3 L 387.55 268.75 L 365.48 268.75 C 364.87 268.75 364.38 269.24 364.38 269.84 Z M 359.75 265.21 L 359.75 297.07 L 357.81 297.07 L 357.81 262.18 L 383.43 262.18 L 383.43 264.12 L 360.84 264.12 C 360.24 264.12 359.75 264.61 359.75 265.21 Z M 393.28 268.75 L 389.73 268.75 L 389.73 265.21 C 389.73 264.61 389.25 264.12 388.64 264.12 L 385.61 264.12 L 385.61 261.09 C 385.61 260.49 385.13 260 384.52 260 L 356.72 260 C 356.12 260 355.63 260.49 355.63 261.09 L 355.63 298.16 C 355.63 298.76 356.12 299.25 356.72 299.25 L 359.75 299.25 L 359.75 302.28 C 359.75 302.88 360.24 303.37 360.84 303.37 L 364.38 303.37 L 364.38 306.91 C 364.38 307.51 364.87 308 365.48 308 L 393.28 308 C 393.88 308 394.37 307.51 394.37 306.91 L 394.37 269.84 C 394.37 269.24 393.88 268.75 393.28 268.75 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 315px; margin-left: 375px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                flontend service(B)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="375" y="327" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    flonte...
                </text>
            </switch>
        </g>
        <path d="M 376.29 429.51 L 390.19 429.51 L 390.19 427.32 L 376.29 427.32 Z M 376.29 418.69 L 390.19 418.69 L 390.19 416.51 L 376.29 416.51 Z M 376.29 407.88 L 390.19 407.88 L 390.19 405.7 L 376.29 405.7 Z M 370.43 429.64 L 370.43 427.19 L 372.88 427.19 L 372.88 429.64 Z M 369.34 431.82 L 373.97 431.82 C 374.57 431.82 375.06 431.33 375.06 430.73 L 375.06 426.1 C 375.06 425.49 374.57 425.01 373.97 425.01 L 369.34 425.01 C 368.73 425.01 368.25 425.49 368.25 426.1 L 368.25 430.73 C 368.25 431.33 368.73 431.82 369.34 431.82 Z M 370.43 418.83 L 370.43 416.38 L 372.88 416.38 L 372.88 418.83 Z M 369.34 421.01 L 373.97 421.01 C 374.57 421.01 375.06 420.52 375.06 419.92 L 375.06 415.29 C 375.06 414.68 374.57 414.2 373.97 414.2 L 369.34 414.2 C 368.73 414.2 368.25 414.68 368.25 415.29 L 368.25 419.92 C 368.25 420.52 368.73 421.01 369.34 421.01 Z M 370.43 408.02 L 370.43 405.57 L 372.88 405.57 L 372.88 408.02 Z M 369.34 410.2 L 373.97 410.2 C 374.57 410.2 375.06 409.71 375.06 409.11 L 375.06 404.48 C 375.06 403.87 374.57 403.39 373.97 403.39 L 369.34 403.39 C 368.73 403.39 368.25 403.87 368.25 404.48 L 368.25 409.11 C 368.25 409.71 368.73 410.2 369.34 410.2 Z M 366.57 435.82 L 366.57 400.93 L 392.19 400.93 L 392.19 435.82 Z M 364.38 399.84 L 364.38 431.19 L 361.93 431.19 L 361.93 396.3 L 387.55 396.3 L 387.55 398.75 L 365.48 398.75 C 364.87 398.75 364.38 399.24 364.38 399.84 Z M 359.75 395.21 L 359.75 427.07 L 357.81 427.07 L 357.81 392.18 L 383.43 392.18 L 383.43 394.12 L 360.84 394.12 C 360.24 394.12 359.75 394.61 359.75 395.21 Z M 393.28 398.75 L 389.73 398.75 L 389.73 395.21 C 389.73 394.61 389.25 394.12 388.64 394.12 L 385.61 394.12 L 385.61 391.09 C 385.61 390.49 385.13 390 384.52 390 L 356.72 390 C 356.12 390 355.63 390.49 355.63 391.09 L 355.63 428.16 C 355.63 428.76 356.12 429.25 356.72 429.25 L 359.75 429.25 L 359.75 432.28 C 359.75 432.88 360.24 433.37 360.84 433.37 L 364.38 433.37 L 364.38 436.91 C 364.38 437.51 364.87 438 365.48 438 L 393.28 438 C 393.88 438 394.37 437.51 394.37 436.91 L 394.37 399.84 C 394.37 399.24 393.88 398.75 393.28 398.75 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 445px; margin-left: 375px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                backend service(B)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="375" y="457" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backen...
                </text>
            </switch>
        </g>
        <path d="M 145 165 L 145 253.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 145 258.88 L 141.5 251.88 L 145 253.63 L 148.5 251.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 122.5 120 L 167.5 120 L 167.5 165 L 122.5 165 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 137.8 151.86 C 132.64 151.86 128.44 147.66 128.44 142.5 C 128.44 137.34 132.64 133.14 137.8 133.14 C 142.96 133.14 147.16 137.34 147.16 142.5 C 147.16 147.66 142.96 151.86 137.8 151.86 M 159.4 156.18 C 159.4 157.77 158.11 159.06 156.52 159.06 C 154.93 159.06 153.64 157.77 153.64 156.18 C 153.64 154.59 154.93 153.3 156.52 153.3 C 158.11 153.3 159.4 154.59 159.4 156.18 M 156.52 125.94 C 158.11 125.94 159.4 127.23 159.4 128.82 C 159.4 130.41 158.11 131.7 156.52 131.7 C 154.93 131.7 153.64 130.41 153.64 128.82 C 153.64 127.23 154.93 125.94 156.52 125.94 M 158.68 139.62 C 160.27 139.62 161.56 140.91 161.56 142.5 C 161.56 144.09 160.27 145.38 158.68 145.38 C 157.09 145.38 155.8 144.09 155.8 142.5 C 155.8 140.91 157.09 139.62 158.68 139.62 M 148.56 143.22 L 154.42 143.22 C 154.77 145.26 156.54 146.82 158.68 146.82 C 161.06 146.82 163 144.88 163 142.5 C 163 140.12 161.06 138.18 158.68 138.18 C 156.54 138.18 154.77 139.74 154.42 141.78 L 148.56 141.78 C 148.47 140.41 148.13 139.11 147.57 137.93 L 153.96 132.29 C 154.68 132.82 155.56 133.14 156.52 133.14 C 158.9 133.14 160.84 131.2 160.84 128.82 C 160.84 126.44 158.9 124.5 156.52 124.5 C 154.14 124.5 152.2 126.44 152.2 128.82 C 152.2 129.72 152.48 130.56 152.96 131.26 L 146.86 136.64 C 144.93 133.67 141.6 131.7 137.8 131.7 C 131.84 131.7 127 136.54 127 142.5 C 127 148.46 131.84 153.3 137.8 153.3 C 141.6 153.3 144.93 151.33 146.86 148.36 L 152.96 153.74 C 152.48 154.44 152.2 155.28 152.2 156.18 C 152.2 158.56 154.14 160.5 156.52 160.5 C 158.9 160.5 160.84 158.56 160.84 156.18 C 160.84 153.8 158.9 151.86 156.52 151.86 C 155.56 151.86 154.68 152.18 153.96 152.71 L 147.57 147.07 C 148.13 145.89 148.47 144.59 148.56 143.22" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 172px; margin-left: 145px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="145" y="184" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 375 165 L 375 253.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 375 258.88 L 371.5 251.88 L 375 253.63 L 378.5 251.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 352.5 120 L 397.5 120 L 397.5 165 L 352.5 165 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 367.8 151.86 C 362.64 151.86 358.44 147.66 358.44 142.5 C 358.44 137.34 362.64 133.14 367.8 133.14 C 372.96 133.14 377.16 137.34 377.16 142.5 C 377.16 147.66 372.96 151.86 367.8 151.86 M 389.4 156.18 C 389.4 157.77 388.11 159.06 386.52 159.06 C 384.93 159.06 383.64 157.77 383.64 156.18 C 383.64 154.59 384.93 153.3 386.52 153.3 C 388.11 153.3 389.4 154.59 389.4 156.18 M 386.52 125.94 C 388.11 125.94 389.4 127.23 389.4 128.82 C 389.4 130.41 388.11 131.7 386.52 131.7 C 384.93 131.7 383.64 130.41 383.64 128.82 C 383.64 127.23 384.93 125.94 386.52 125.94 M 388.68 139.62 C 390.27 139.62 391.56 140.91 391.56 142.5 C 391.56 144.09 390.27 145.38 388.68 145.38 C 387.09 145.38 385.8 144.09 385.8 142.5 C 385.8 140.91 387.09 139.62 388.68 139.62 M 378.56 143.22 L 384.42 143.22 C 384.77 145.26 386.54 146.82 388.68 146.82 C 391.06 146.82 393 144.88 393 142.5 C 393 140.12 391.06 138.18 388.68 138.18 C 386.54 138.18 384.77 139.74 384.42 141.78 L 378.56 141.78 C 378.47 140.41 378.13 139.11 377.57 137.93 L 383.96 132.29 C 384.68 132.82 385.56 133.14 386.52 133.14 C 388.9 133.14 390.84 131.2 390.84 128.82 C 390.84 126.44 388.9 124.5 386.52 124.5 C 384.14 124.5 382.2 126.44 382.2 128.82 C 382.2 129.72 382.48 130.56 382.96 131.26 L 376.86 136.64 C 374.93 133.67 371.6 131.7 367.8 131.7 C 361.84 131.7 357 136.54 357 142.5 C 357 148.46 361.84 153.3 367.8 153.3 C 371.6 153.3 374.93 151.33 376.86 148.36 L 382.96 153.74 C 382.48 154.44 382.2 155.28 382.2 156.18 C 382.2 158.56 384.14 160.5 386.52 160.5 C 388.9 160.5 390.84 158.56 390.84 156.18 C 390.84 153.8 388.9 151.86 386.52 151.86 C 385.56 151.86 384.68 152.18 383.96 152.71 L 377.57 147.07 C 378.13 145.89 378.47 144.59 378.56 143.22" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 172px; margin-left: 375px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="375" y="184" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 70 210 L 115 210 L 115 255 L 70 255 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 107.33 237.4 L 102.28 234.37 L 102.28 227.15 C 102.28 226.93 102.16 226.72 101.97 226.61 L 94.71 222.37 L 94.71 216.27 L 107.33 223.72 Z M 108.28 222.82 L 94.4 214.62 C 94.2 214.5 93.96 214.5 93.76 214.61 C 93.57 214.72 93.45 214.93 93.45 215.16 L 93.45 222.73 C 93.45 222.96 93.57 223.16 93.76 223.28 L 101.02 227.51 L 101.02 234.72 C 101.02 234.94 101.14 235.15 101.33 235.26 L 107.64 239.05 C 107.74 239.11 107.85 239.14 107.96 239.14 C 108.07 239.14 108.18 239.11 108.27 239.06 C 108.47 238.95 108.59 238.74 108.59 238.51 L 108.59 223.36 C 108.59 223.14 108.47 222.93 108.28 222.82 Z M 92.47 249.15 L 77.67 241.29 L 77.67 223.72 L 90.29 216.27 L 90.29 222.39 L 83.64 226.62 C 83.46 226.73 83.35 226.93 83.35 227.15 L 83.35 237.88 C 83.35 238.11 83.48 238.33 83.69 238.44 L 92.18 242.86 C 92.36 242.95 92.58 242.95 92.76 242.86 L 101 238.6 L 106.07 241.64 Z M 107.66 241.12 L 101.34 237.34 C 101.16 237.22 100.92 237.22 100.73 237.32 L 92.47 241.58 L 84.61 237.5 L 84.61 227.5 L 91.26 223.27 C 91.44 223.15 91.55 222.95 91.55 222.73 L 91.55 215.16 C 91.55 214.93 91.43 214.72 91.24 214.61 C 91.04 214.5 90.8 214.5 90.6 214.62 L 76.72 222.82 C 76.53 222.93 76.41 223.14 76.41 223.36 L 76.41 241.67 C 76.41 241.9 76.54 242.11 76.74 242.22 L 92.17 250.43 C 92.27 250.48 92.37 250.5 92.47 250.5 C 92.58 250.5 92.68 250.47 92.78 250.42 L 107.64 242.22 C 107.83 242.11 107.96 241.9 107.96 241.68 C 107.96 241.45 107.85 241.24 107.66 241.12 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 262px; margin-left: 93px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="93" y="274" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 164.37 294.95 L 350.09 399.92" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 354.66 402.5 L 346.84 402.11 L 350.09 399.92 L 350.29 396.01 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 146.29 299.51 L 160.19 299.51 L 160.19 297.32 L 146.29 297.32 Z M 146.29 288.69 L 160.19 288.69 L 160.19 286.51 L 146.29 286.51 Z M 146.29 277.88 L 160.19 277.88 L 160.19 275.7 L 146.29 275.7 Z M 140.43 299.64 L 140.43 297.19 L 142.88 297.19 L 142.88 299.64 Z M 139.34 301.82 L 143.97 301.82 C 144.57 301.82 145.06 301.33 145.06 300.73 L 145.06 296.1 C 145.06 295.49 144.57 295.01 143.97 295.01 L 139.34 295.01 C 138.73 295.01 138.25 295.49 138.25 296.1 L 138.25 300.73 C 138.25 301.33 138.73 301.82 139.34 301.82 Z M 140.43 288.83 L 140.43 286.38 L 142.88 286.38 L 142.88 288.83 Z M 139.34 291.01 L 143.97 291.01 C 144.57 291.01 145.06 290.52 145.06 289.92 L 145.06 285.29 C 145.06 284.68 144.57 284.2 143.97 284.2 L 139.34 284.2 C 138.73 284.2 138.25 284.68 138.25 285.29 L 138.25 289.92 C 138.25 290.52 138.73 291.01 139.34 291.01 Z M 140.43 278.02 L 140.43 275.57 L 142.88 275.57 L 142.88 278.02 Z M 139.34 280.2 L 143.97 280.2 C 144.57 280.2 145.06 279.71 145.06 279.11 L 145.06 274.48 C 145.06 273.87 144.57 273.39 143.97 273.39 L 139.34 273.39 C 138.73 273.39 138.25 273.87 138.25 274.48 L 138.25 279.11 C 138.25 279.71 138.73 280.2 139.34 280.2 Z M 136.57 305.82 L 136.57 270.93 L 162.19 270.93 L 162.19 305.82 Z M 134.38 269.84 L 134.38 301.19 L 131.93 301.19 L 131.93 266.3 L 157.55 266.3 L 157.55 268.75 L 135.48 268.75 C 134.87 268.75 134.38 269.24 134.38 269.84 Z M 129.75 265.21 L 129.75 297.07 L 127.81 297.07 L 127.81 262.18 L 153.43 262.18 L 153.43 264.12 L 130.84 264.12 C 130.24 264.12 129.75 264.61 129.75 265.21 Z M 163.28 268.75 L 159.73 268.75 L 159.73 265.21 C 159.73 264.61 159.25 264.12 158.64 264.12 L 155.61 264.12 L 155.61 261.09 C 155.61 260.49 155.13 260 154.52 260 L 126.72 260 C 126.12 260 125.63 260.49 125.63 261.09 L 125.63 298.16 C 125.63 298.76 126.12 299.25 126.72 299.25 L 129.75 299.25 L 129.75 302.28 C 129.75 302.88 130.24 303.37 130.84 303.37 L 134.38 303.37 L 134.38 306.91 C 134.38 307.51 134.87 308 135.48 308 L 163.28 308 C 163.88 308 164.37 307.51 164.37 306.91 L 164.37 269.84 C 164.37 269.24 163.88 268.75 163.28 268.75 Z" fill="#d45b07" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 315px; margin-left: 145px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                flontend service(A)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="145" y="327" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    flonte...
                </text>
            </switch>
        </g>
        <path d="M 145 55 L 145 113.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 145 118.88 L 141.5 111.88 L 145 113.63 L 148.5 111.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 122.5 10 L 167.5 10 L 167.5 55 L 122.5 55 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 154.72 40.29 C 154.72 39.23 153.86 38.36 152.79 38.36 C 151.73 38.36 150.86 39.23 150.86 40.29 C 150.86 41.36 151.73 42.22 152.79 42.22 C 153.86 42.22 154.72 41.36 154.72 40.29 Z M 156.01 40.29 C 156.01 42.07 154.57 43.51 152.79 43.51 C 151.02 43.51 149.58 42.07 149.58 40.29 C 149.58 38.52 151.02 37.08 152.79 37.08 C 154.57 37.08 156.01 38.52 156.01 40.29 Z M 138.82 31.02 C 138.82 29.95 137.96 29.09 136.89 29.09 C 135.83 29.09 134.97 29.95 134.97 31.02 C 134.97 32.08 135.83 32.94 136.89 32.94 C 137.96 32.94 138.82 32.08 138.82 31.02 Z M 140.11 31.02 C 140.11 32.79 138.67 34.23 136.89 34.23 C 135.12 34.23 133.68 32.79 133.68 31.02 C 133.68 29.24 135.12 27.8 136.89 27.8 C 138.67 27.8 140.11 29.24 140.11 31.02 Z M 144.83 20.93 C 144.83 22 145.69 22.86 146.75 22.86 C 147.82 22.86 148.68 22 148.68 20.93 C 148.68 19.87 147.82 19 146.75 19 C 145.69 19 144.83 19.87 144.83 20.93 Z M 143.54 20.93 C 143.54 19.16 144.98 17.72 146.75 17.72 C 148.53 17.72 149.97 19.16 149.97 20.93 C 149.97 22.71 148.53 24.15 146.75 24.15 C 144.98 24.15 143.54 22.71 143.54 20.93 Z M 161.71 32.5 C 161.71 26.54 158.52 21.02 153.36 18.04 C 152.44 18.23 151.55 18.48 150.43 18.89 L 150 17.68 C 150.58 17.47 151.1 17.3 151.6 17.15 C 149.53 16.26 147.28 15.79 145 15.79 C 143.91 15.79 142.85 15.9 141.8 16.1 C 142.56 16.54 143.23 16.99 143.88 17.48 L 143.1 18.5 C 142.18 17.81 141.23 17.22 139.98 16.56 C 133.67 18.55 129.13 24.12 128.4 30.65 C 129.73 30.39 131 30.24 132.41 30.21 L 132.44 31.49 C 130.96 31.53 129.69 31.68 128.3 31.99 C 128.3 32.16 128.29 32.33 128.29 32.5 C 128.29 38.07 131.04 43.19 135.57 46.29 C 134.76 43.89 134.36 41.62 134.36 39.41 C 134.36 38.15 134.58 37.11 134.81 36.01 C 134.86 35.76 134.91 35.5 134.97 35.23 L 136.23 35.48 C 136.18 35.75 136.12 36.02 136.07 36.28 C 135.84 37.35 135.65 38.28 135.65 39.41 C 135.65 41.92 136.2 44.52 137.32 47.34 C 139.71 48.58 142.29 49.21 145 49.21 C 146.77 49.21 148.5 48.93 150.15 48.39 C 150.8 47.11 151.28 45.9 151.68 44.52 L 152.91 44.88 C 152.62 45.89 152.28 46.81 151.88 47.72 C 152.92 47.25 153.9 46.68 154.82 46 C 154.6 45.46 154.37 44.92 154.1 44.39 L 155.25 43.82 C 155.48 44.26 155.68 44.72 155.88 45.18 C 159.6 42 161.71 37.42 161.71 32.5 Z M 163 32.5 C 163 38.11 160.45 43.3 156.01 46.74 C 154.91 47.59 153.71 48.3 152.45 48.87 C 151.91 49.12 151.37 49.34 150.81 49.53 C 148.96 50.17 147 50.5 145 50.5 C 142.04 50.5 139.11 49.76 136.51 48.37 C 130.64 45.23 127 39.15 127 32.5 C 127 32.06 127.01 31.72 127.04 31.41 C 127.48 23.9 132.63 17.4 139.86 15.25 C 141.5 14.75 143.24 14.5 145 14.5 C 148.09 14.5 151.13 15.3 153.8 16.8 C 159.47 19.98 163 26 163 32.5 Z M 143.27 23.15 L 142.43 22.19 C 140.99 23.44 139.87 24.78 138.56 26.8 L 139.64 27.5 C 140.87 25.59 141.93 24.33 143.27 23.15 Z M 141.32 31.49 L 140.9 32.7 C 143.85 33.72 146.43 35.34 149.01 37.79 L 149.9 36.86 C 147.18 34.27 144.45 32.56 141.32 31.49 Z M 149.92 23.93 C 152.33 27.61 153.69 31.65 153.95 35.93 L 152.67 36.01 C 152.42 31.95 151.13 28.12 148.84 24.63 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 62px; margin-left: 145px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="145" y="74" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudFr...
                </text>
            </switch>
        </g>
        <path d="M 375 55 L 375 113.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 375 118.88 L 371.5 111.88 L 375 113.63 L 378.5 111.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 352.5 10 L 397.5 10 L 397.5 55 L 352.5 55 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 384.72 40.29 C 384.72 39.23 383.86 38.36 382.79 38.36 C 381.73 38.36 380.86 39.23 380.86 40.29 C 380.86 41.36 381.73 42.22 382.79 42.22 C 383.86 42.22 384.72 41.36 384.72 40.29 Z M 386.01 40.29 C 386.01 42.07 384.57 43.51 382.79 43.51 C 381.02 43.51 379.58 42.07 379.58 40.29 C 379.58 38.52 381.02 37.08 382.79 37.08 C 384.57 37.08 386.01 38.52 386.01 40.29 Z M 368.82 31.02 C 368.82 29.95 367.96 29.09 366.89 29.09 C 365.83 29.09 364.97 29.95 364.97 31.02 C 364.97 32.08 365.83 32.94 366.89 32.94 C 367.96 32.94 368.82 32.08 368.82 31.02 Z M 370.11 31.02 C 370.11 32.79 368.67 34.23 366.89 34.23 C 365.12 34.23 363.68 32.79 363.68 31.02 C 363.68 29.24 365.12 27.8 366.89 27.8 C 368.67 27.8 370.11 29.24 370.11 31.02 Z M 374.83 20.93 C 374.83 22 375.69 22.86 376.75 22.86 C 377.82 22.86 378.68 22 378.68 20.93 C 378.68 19.87 377.82 19 376.75 19 C 375.69 19 374.83 19.87 374.83 20.93 Z M 373.54 20.93 C 373.54 19.16 374.98 17.72 376.75 17.72 C 378.53 17.72 379.97 19.16 379.97 20.93 C 379.97 22.71 378.53 24.15 376.75 24.15 C 374.98 24.15 373.54 22.71 373.54 20.93 Z M 391.71 32.5 C 391.71 26.54 388.52 21.02 383.36 18.04 C 382.44 18.23 381.55 18.48 380.43 18.89 L 380 17.68 C 380.58 17.47 381.1 17.3 381.6 17.15 C 379.53 16.26 377.28 15.79 375 15.79 C 373.91 15.79 372.85 15.9 371.8 16.1 C 372.56 16.54 373.23 16.99 373.88 17.48 L 373.1 18.5 C 372.18 17.81 371.23 17.22 369.98 16.56 C 363.67 18.55 359.13 24.12 358.4 30.65 C 359.73 30.39 361 30.24 362.41 30.21 L 362.44 31.49 C 360.96 31.53 359.69 31.68 358.3 31.99 C 358.3 32.16 358.29 32.33 358.29 32.5 C 358.29 38.07 361.04 43.19 365.57 46.29 C 364.76 43.89 364.36 41.62 364.36 39.41 C 364.36 38.15 364.58 37.11 364.81 36.01 C 364.86 35.76 364.91 35.5 364.97 35.23 L 366.23 35.48 C 366.18 35.75 366.12 36.02 366.07 36.28 C 365.84 37.35 365.65 38.28 365.65 39.41 C 365.65 41.92 366.2 44.52 367.32 47.34 C 369.71 48.58 372.29 49.21 375 49.21 C 376.77 49.21 378.5 48.93 380.15 48.39 C 380.8 47.11 381.28 45.9 381.68 44.52 L 382.91 44.88 C 382.62 45.89 382.28 46.81 381.88 47.72 C 382.92 47.25 383.9 46.68 384.82 46 C 384.6 45.46 384.37 44.92 384.1 44.39 L 385.25 43.82 C 385.48 44.26 385.68 44.72 385.88 45.18 C 389.6 42 391.71 37.42 391.71 32.5 Z M 393 32.5 C 393 38.11 390.45 43.3 386.01 46.74 C 384.91 47.59 383.71 48.3 382.45 48.87 C 381.91 49.12 381.37 49.34 380.81 49.53 C 378.96 50.17 377 50.5 375 50.5 C 372.04 50.5 369.11 49.76 366.51 48.37 C 360.64 45.23 357 39.15 357 32.5 C 357 32.06 357.01 31.72 357.04 31.41 C 357.48 23.9 362.63 17.4 369.86 15.25 C 371.5 14.75 373.24 14.5 375 14.5 C 378.09 14.5 381.13 15.3 383.8 16.8 C 389.47 19.98 393 26 393 32.5 Z M 373.27 23.15 L 372.43 22.19 C 370.99 23.44 369.87 24.78 368.56 26.8 L 369.64 27.5 C 370.87 25.59 371.93 24.33 373.27 23.15 Z M 371.32 31.49 L 370.9 32.7 C 373.85 33.72 376.43 35.34 379.01 37.79 L 379.9 36.86 C 377.18 34.27 374.45 32.56 371.32 31.49 Z M 379.92 23.93 C 382.33 27.61 383.69 31.65 383.95 35.93 L 382.67 36.01 C 382.42 31.95 381.13 28.12 378.84 24.63 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 62px; margin-left: 375px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="375" y="74" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudFr...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>