# HowToSetupChatbot

- Chatbot を使用して Slack に通知を飛ばす際、デプロイ前に事前作業が必要である。
- Chatbot に Slack ワークスペースが未登録の場合、デプロイが失敗するため留意すること。

> [!IMPORTANT]
> AWS Chatbot が Amazon Q Developer に名称変更されました。
> https://docs.aws.amazon.com/ja_jp/Chatbot/latest/adminguide/service-rename.htm
> CDK コード上では Chatbot として扱っているため、ドキュメントでは AWS Chatbot で記載します。

## Chatbot デプロイの事前準備

1.  Chatbot へチャットクライアント追加
1.  Slack への AWS ユーザ追加

### 1. Chatbot へチャットクライアント追加

- Chatbot (Amazon Q Developer in chat applications)コンソールを開く。
- 新しいクライアントを設定を押下。
  ![](../images/slack-workspace-registration01.png)
- クライアントの種類に Slack を選択。
  ![](../images/slack-workspace-registration02.png)
- 登録したいワークスペースの URL を入力し、指示に従って Slack の認証を進める。  
  　![](../images/Signin_slack_workspace.png)

- 認証が完了すると、設定済みクライアントに登録される。  
   ![](../images/done_register_slack_workspace.png)
  ※CDK デプロイすることで、 設定したチャンネルが登録されるため、新規登録時の設定済みチャンネル数は 0 である。

### 2. Slack への AWS ユーザ追加

送信先に指定する Slack チャンネルにて、チャット投稿と同じ要領で、下記のコマンドを入力し、AWS ユーザをチャットに招待する。

```
/invite @Amazon Q
```

AWS ユーザが追加されたことを確認し、完了。
![](../images/inviteCheck.png)

## [参考]Slack 情報の取得方法

・下記の URL に記載されている方法を使用し、Slack のワークスペース URL を取得。

https://slack.com/intl/ja-jp/help/articles/221769328-%E8%87%AA%E5%88%86%E3%81%8C%E5%8F%82%E5%8A%A0%E3%81%97%E3%81%A6%E3%81%84%E3%82%8B%E3%83%AF%E3%83%BC%E3%82%AF%E3%82%B9%E3%83%9A%E3%83%BC%E3%82%B9%E3%81%AE-Slack-URL-%E3%82%92%E7%A2%BA%E8%AA%8D%E3%81%99%E3%82%8B

- 取得した URL を使用し、Slack の Web 版にアクセスする。

- 左ペインから通知先に指定したいチャンネルをクリックする。

- URL を確認し、ワークスペース ID とチャンネル ID を取得する。

```
https://app.slack.com/client/ワークスペースID/チャンネルID
```
