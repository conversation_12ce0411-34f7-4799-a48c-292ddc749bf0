# OIDC(OpenIdConnect)スタックの利用方法について

ここでは、OIDC スタックの利用方法と GitHubActions との連携方法を記載する。

## 概要

- GitHubActions から AWS リソースへアクセスするケースを想定し、その手法として OpenIdConnect を利用する。
- アクセスするリソースは要件によって異なるため、AWS の最小権限に準拠し要件毎にロールを分ける方針とする。
- 下図に GitHubActions から OIDC を利用した AWS リソースへのアクセス例を示す。
  1. GitHubActions を実行する。
     ※実行パターンは手動実行とプルリクエストやマージをトリガーとする自動の２パターンがある。
  1. GitHubActions から任意の IAM ロールへアクセスする。
  1. 引き受けたロールにアタッチされたポリシーの範囲内でリソースへの操作を実行する。
     ![](../images/OIDC.dio.png)

## 利用手順

利用手順は以下のとおり。

1.  IAM ロールを取得
1.  GitHub Secrets へ登録
1.  GitHub Actions 実行

### IAM ロールを取得

1.  `params/` ファイルに GitHub の `Organization 名`, `リポジトリ名` を記入する。
    - リポジトリを複数利用する場合には、キーとリポジトリ名を適宜追加する。

**usecases/common/params/\*\*.ts**

```typescript
export const OidcParam: inf.IOidcParam = {
  oidcProviderArn: '',
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    InfraRepositoryName: 'csys-infra-gevanni-infra',
    MasterBucketRepositoryName: 'csys-infra-gevanni-infra',
  },
};
```

2.  OIDC スタックをデプロイし、GitHubActions で利用する IAM ロールの ARN を控える
    - CloudFormation から OIDC スタックのリソースタブを表示し、利用する IAM ロールを押下する。\
      ![](../images/OIDC_GitHubActionsRole.png)
    - 押下すると IAM サービスの画面に遷移するので ARN を控える。当 ARN は GitHubActions の workflows ファイルを実装する上で利用する。\
      ![](../images/OIDC_GitHubActionsRoleARN.png)

### GitHub Secrets へ登録

1.  GitHub Actions を実行するリポジトリの `Settings` ページを開く。
1.  左ペインメニューから `Secrets and variables` > `Actions` を選択。
1.  画面中央の `Secrets` タブを選択肢、`New repository secret` をクリック。\
    ![](/docs/images/oidc-github-actions-secrets.png)
1.  [IAM ロールを取得](#iam-ロールを取得) で控えた ARN を `Secret` に記入する。

### GitHub Actions 実行

1.  GHA ワークフローファイルに `aws-actions/configure-aws-credentials` を利用しスイッチして利用する。
    