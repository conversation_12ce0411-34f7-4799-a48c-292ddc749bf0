# Gevanni Rolling パイプライン 移行手順書
## 概要

- アプリチームが ECS デプロイを管理しやすくする目的で、デプロイパイプラインをアプリアカウントへ移行する。
- 問題なくデプロイできることを確認するため、Gevanni アカウントへ存在するパイプラインを残したまま、新バージョンのパイプラインをデプロイしテストを行う。確認したのち、Gevanni アカウントに存在する旧パイプラインを削除し完全に移行する。
- パラメーターの書き換えとデプロイに順序が存在するため、手順を以下に記載する。

## 手順

手順は以下の通り。

1.  (App) 新パイプラインデプロイ
1.  (<PERSON><PERSON><PERSON><PERSON>) Gevanni パイプライン更新
1.  (Geva<PERSON>i・App) 疎通確認
1.  (<PERSON><PERSON><PERSON><PERSON>) 旧パイプライン削除

- 作業アカウントは以下の通り。
  - **(Gevanni)**: Gevanni アカウント側作業  
  - **(App)**: アプリ用アカウント側作業

## (App) 新パイプラインデプロイ

1.  最新の main ブランチの差分を取り込む。
1.  以下のパラメーターを記入する。  
    ![](/docs/images/HowToSwitchingRollingPipeline/image01.png)  
    - CrossAccountEventBusParam
      - `crossAccountIds`: Gevanni アカウント ID。
    - EcspressoPipelinesParam
      - `crossAccessAccountId`: Gevanni アカウント ID。
      - `pipelineParams`:
        - `appName`: ECS サービス名。
        - `bucketKey`: ecspresso conf のファイル名。
        - `crossAccessAccountEnvName`: 
          - 紐づける Gevanni 環境名
          - 例）Dev01-GEVANNI, Stg01-GEVANNI, Prod01-GEVANNI
        - `crossAccessAccountService`:
          - 紐づける Gevanni サービス ID
          - 例）sample01-dev01-ab
        - `functionLogLevel`: 
          - `NOTSET` | `DEBUG` | `INFO` | `WARNING` | `ERROR` | `CRITICAL`
          - Lambda 関数の出力ログレベルを指定。
        - `pipelineExecutionMode`: CodePipeline の実行モードを指定。
          - `QUEUED` モードを推奨。
          - 実行モードそれぞれの処理方法は以下の通り。
            - `QUEUED`: [QUEUED モードでの実行の処理方法](https://docs.aws.amazon.com/ja_jp/codepipeline/latest/userguide/concepts-how-it-works.html#concepts-how-it-works-executions-queued)
            - `SUPERSEDED`: [SUPERSEDED モードでの実行の処理方法](https://docs.aws.amazon.com/ja_jp/codepipeline/latest/userguide/concepts-how-it-works.html#concepts-how-it-works-executions)
            - `PARALLEL`: [PARALLEL モードでの実行の処理方法](https://docs.aws.amazon.com/ja_jp/codepipeline/latest/userguide/concepts-how-it-works.html#concepts-how-it-works-executions-parallel)
        - `isBackend`: 
          - `true` | `false`
          - バックエンドパイプラインのパラメーターを設定する場合、`true` を設定する。  
1.  EcspressoPipelines スタックをデプロイする。
    ```bash
    # sample
    $ npx cdk deploy Devgevanni-cf-sample-EcspressoPipelines -c environment=dev
    ```

## (Gevanni) Gevanni パイプライン更新

1.  最新の develop ブランチの差分を取り込む。
1.  `EcspressoFrontDeployPipelineParam`, `EcspressoBackDeployPipelineParam` パラメーターを記入する。
    - `crossAccessAccountId`: パイプラインをデプロイしたアプリアカウント ID
    - `crossAccessEnvName`: 
      - パイプラインをデプロイした環境名
      - 例）Dev, Stg, Prod
        ![](/docs/images/HowToSwitchingRollingPipeline/image02.png)
    - `crossAccessPjPrefix`: 
      - パイプラインをデプロイした環境のプレフィックス名
      - 例）gevanni-cf-sample
1.  パイプラインのデプロイ。
    - ECS スタックを更新する。
      ```bash
      # sample
      $ npx cdk deploy Dev01-sample-sample01-dev01-ab-ECS -c environment=dev01 -c service=sample01-dev01-ab
      ```

## (Gevanni・App) 疎通確認      

1.  **(Gevanni)**
    1.  ワークフローを実行するか、手動で `image_(front|backend).zip` を AppPipelineSourceBucket へ配置する。  
        ![](/docs/images/HowToSwitchingRollingPipeline/image03.png)
1.  **(App)**
    1.  アプリアカウントの CodePipeline コンソールを開き、デプロイが成功したことを確認する。  
        ![](/docs/images/HowToSwitchingRollingPipeline/image04.png)
1.  **(Gevanni)**
    1.  (オプション)※ 何らかの要因で ECS タスクのデプロイ失敗し、ECS スタックのロールバックを行う場合、`EcspressoFrontDeployPipelineParam`, `EcspressoBackDeployPipelineParam` パラメーターを params ファイルから削除して ECS スタックを更新すること。

## (Gevanni) 旧パイプライン削除    

1.  ※ 旧パイプラインの Artifact Bucket の中身が空でないと削除エラーとなるため、旧パイプラインの削除を行う前に以下のどちらかを実行すること。
    - バケットを残す場合。
      - `OtherRemovalPolicyParam` を `RetainRemovalPolicyParam` に変更し、バケットの削除ポリシーを Retrain にする。
      ```typescript
      // export const OtherRemovalPolicyParam = inf.DestroyRemovalPolicyParam;
      // 上記を以下のように変更する。
      export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;
      ```
    - バケットを削除する場合。
      - オブジェクト自動削除のカスタムリソースが存在しないため、手動で Artifact Bucket のオブジェクトを全て削除すること。
1.  `deleteOldPipeline` をコメントインする。  
    ![](/docs/images/HowToSwitchingRollingPipeline/image05.png)
1.  ECS スタックを更新し、旧パイプラインを削除する。
    ```bash
    # sample
    $ npx cdk deploy Dev01-sample-sample01-dev01-ab-ECS -c environment=dev01 -c service=sample01-dev01-ab
    ```
