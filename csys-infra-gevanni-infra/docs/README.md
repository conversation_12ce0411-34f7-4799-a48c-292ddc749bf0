# ディレクトリ構成概要

`docs/` 配下のディレクトリ構成は以下の通り。採番の順番で読むことを推奨する。

```
docs/
├─ 01_Systemdesign/
├─ 02_Detaildesign/
├─ 03_HowToUse/
├─ images/
├─ poc/
└─ README.md
```

- `01_Systemdesign/`
  - <PERSON><PERSON><PERSON><PERSON> の全体概要と設計方針を記載したドキュメント
  - Systemdesign 配下のドキュメントも採番されており、順番通りに読むことを推奨する。
- `02_Detaildesign/`
  - Gevanni を構成する各コンポーネントの詳細設計を記載したドキュメント
- `03_HowToUse/`
  - Gevanni の運用手順書
- `poc/`
  - 検証結果をまとめたドキュメント
