# 運用方針

## 基本方針

- システムの安定稼働を実現するために運用方式を記載する
- だれが、いつ、どこで、どのように使うのかといった観点で、体制を含めシステム運用でどういった作業が必要か整理をおこなう

### サービス提供時間

- Gevanni：24 時間 365 日

### メンテナンス時間

- 定期的なメンテナンス時間を設けることはせず、システム停止を伴う AWS リソースのメンテナンス時等必要に応じてメンテナンスを実施する。

### 運用体制

| No  | 担当             | 担当会社・部署           | 役割                                                                                                                                                                                                                                                        |
| --- | ---------------- | ------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | 運用担当         | JIG-SAW                  | システムのモニタリングを行う システム障害が発生した際の一次対応を行う。システムの設定変更・アプリケーションの再デプロイやサポートへの連携を伴う等の対応が必要な場合は、各担当への連絡を行う。また、アプリ担当からの依頼に応じて各監視項目の静観対応を行う。 |
| 2   | アプリ担当       | マイナビアプリ担当       | システムのアプリケーション管理を行う アプリケーションで障害が発生した際の切り分け、原因調査及び障害対応を行う また、閉塞作業とそれに伴う監視静観設定を行う。                                                                                                |
| 3   | インフラ担当     | マイナビインフラ担当     | AWS 基盤の管理を行う AWS 基盤上で提供される AWS サービスに障害が発生した際、原因調査及び障害対応を行う。                                                                                                                                                    |
| 4   | セキュリティ担当 | マイナビセキュリティ担当 | セキュリティ関連のアラート(GuardDuty)が上がった際、検知内容をシステム担当へ連携する。                                                                                                                                                                       |

### アプリチームまたは事業部からの受付窓口

- 問い合わせの窓口は 2 パターンあり、アプリチームや事業部からの問い合わせに対応する。

  1. Gevanni 問い合わせ用 Slack チャンネルからの問い合わせ

     - ユースケース
       1. Gevanni 上で既に稼働しているプロジェクトのアプリチームが Gevanni の機能追加の要望がある場合

  2. 各内製案件のインフラ担当者を経由して Gevanni 担当者への問い合わせ
     - ユースケース
       1. 事業部側で個別案件として既にリリースされているリソースを Gevanni に移行を検討している場合
       1. Gevanni 上で既に稼働しているプロジェクトのアプリチームからの問い合わせに関して、インフラ担当者のみでの解決が難しい場合  
          ※仕様変更を依頼したい場合は`1. Gevanni 問い合わせ用 Slack チャンネル`から問い合わせを行う

### AWS 利用料金の費用振替

- 費用振替の識別子として各事業部ごとの請求用タグを CDK コード上で設定しており、インフラチーム側で識別子を元に月に一回の頻度で費用振替を行う。
- Gevanni の AWS 利用料金はクラスメソッドポータルから確認する方針とする。
  - ※クラスメソッド経由で AWS アカウントを契約しており、AWS マネジメントコンソール上からはクラスメソッドの割引金額が反映されていないため。
- インフラチームとアプリチームで AWS 利用料金を確認する方法とタイミングは異なる。
  - インフラチーム：
    - クラスメソッドポータルの Gevanni の請求プロジェクトから AWS 利用料金をリアルタイムで確認可能。
  - アプリチーム：
    - インフラチームによる費用振替のタイミングで AWS 利用料金を確認する。
    - クラスメソッドポータルの Gevanni の請求プロジェクトのアクセス権限は付与しない。
    - ※(2025 年 2 月 時点) 今後、アプリチームからリアルタイムで AWS 利用料金を閲覧したいという要望が出た場合には、各プロジェクトの責任者のみに請求プロジェクト閲覧権限を付与する。

## 監視方針

### アラート検知方法

- Gevanni の共有リソースと各プロジェクトの専有リソースともに、システムのメトリクス監視を JIG-SAW 社と連携して行う。
- メトリクスのアラートが発砲された場合 JIG-SAW 社が検知し、JIG-SAW 社からメールまたは電話などで連絡が入る。その後は運用フローに従いアプリ担当・インフラ担当が原因分析および復旧作業を実施する。
  - ※ アラートのしきい値や監視対象、一次対応はプロジェクト毎に依頼内容が異なるため、JIG-SAW 社からの通知フローはアプリチーム毎に方針を決める。
- 障害管理は障害運用フローに従い、必要に応じてバックアップ・リストア方式に記載の方式で復旧を行う。
- 切り分けや復旧が難しい場合、AWS サポートへの問い合わせを実施する。

  ![](../images/irregular_operation.dio.svg)

### ログ運用

- ログは監視用と監査用の 2 つの用途別に取得・管理する。

  - 監視用ログ
    - エラー検知や動作確認などのためにログを取得・保管する。
    - アプリチームは Gevanni AWS アカウントのマネジメントコンソール上からのアプリケーションログは閲覧できないため、NewRelic に転送し NewRelic から閲覧する。
    - コスト観点から NewRelic のログ保管期間は 30 日とする。
    - 監視対象のログは[05_Monitoring.md](05_Monitoring.md)を参照すること。
  - 監査用ログ
    - 問題発生時の原因特定や不正利用の抑止のためにログを取得・保管する。
    - マイナビセキュリティ対策チェックシートに準拠し、取得したログは 5 年保管とする。
    - 監査対象のログは[監査対象ログ](#監査対象ログ)を参照すること。

  ![](/docs/images/logging-arch.dio.svg)

#### 監査対象ログ

- マイナビの Web アプリケーションセキュリティ対策チェックシート記載の「監査ログ」項目に準拠し収集すべきログを以下に記載する。
  - アクセスログ
    - 記録する送信元 IP アドレスは、（プロキシなどの中継ノードではなく）実際の送信元ノードのものにする。
  - データベースの監査ログ
  - セキュリティ関連のシステムログ
  - ネットワークフローローグ
  - DNS クエリログ
    - Route53 のクエリログを収集する。
  - メールに関するログ
  - アプリケーションログ
    - 認証および機密情報に対するアクセスを、アプリケーションログとして記録する。
  - システムおよびプラットフォームの操作ログ
    - システム管理者（開発者、運用者）によるシステムの操作や破壊的な変更、機密情報へのアクセスなどといった特権的な操作履歴を追跡できるようにする。

#### アカウント別の監査ログ一覧

- 本体用アカウント
  | サービス | ログ | ログ保存先 |
  | --------------------- | --------------------- | --------------- |
  | CloudTrail | 操作ログ | S3 |
  | Config | 構成変更ログ | S3 |
  | Route53 | クエリログ | S3 |
  | VPC | フローログ | S3 |
  | ALB | アクセスログ | S3 |
  | ECS | アプリケーションログ | S3 |
  | ECS | Firelens コンテナログ | CloudWatch Logs |
  | ECS | バッチログ | S3 |
  | Aurora[1] | エラーログ | CloudWatch Logs |
  | Aurora[1] | スロークエリログ | CloudWatch Logs |
  | Aurora[1] | 全般ログ | CloudWatch Logs |
  | Aurora[1] | 監査ログ | CloudWatch Logs |
  | ElastiCache | スローログ | S3 |
  | ElastiCache | エンジンログ | S3 |
  | Data Firehose | フローログ | S3 |

- Route53 用アカウント
  | サービス | ログ | ログ保存先 |
  |-|-|-|
  |Route53|クエリログ|S3|

- アプリ管理用アカウント
  | サービス | ログ | ログ保存先 |
  | --------------------- | --------------------- | --------------- |
  | CloudFront | アクセスログ | S3 |
  | S3 | アクセスログ | S3 |
  | Data Firehose | フローログ | S3 |
  | CodeBuild | ビルドログ | S3 |
  | OpenSearch Service[2] | 検索スローログ | S3 |
  | OpenSearch Service[2] | エラーログ | S3 |
  | OpenSearch Service[2] | アクセスログ | S3 |

[1]:(2025/02 時点)現状 Gevanni は Aurora 未対応だが、既存システムからの以降の場合、TiDB だと対応できない可能性があるため、選択肢として実装する予定。
[2]:(2025/02 時点)OpenSearch Service は Gevanni では未対応だが、将来的には対応予定

## リソース運用方針

- リリース管理対象を整理し、管理対象ごとにリリース方式を記載する。
- 基本的には検証環境上で動作確認を行ったうえで本番環境のリリースを行う。

### リソース管理対象

- Gevanni を構成するリソースについて、アプリ/インフラ チームが管轄する範囲ついて以下に記載する。
- Gevanni アカウントにデプロイされる AWS リソースは 専有/共有 で区分される。
  - 専有：各プロジェクトに割り当てられたリソース（ECS 等）
  - 共有：プロジェクト間で共用されるリソース（VPC 等）
- 基本的には 共有/専有 リソースは CDK コードでインフラチームが管理する。
  - 共有リソースおよび専有リソースは共有の CI/CD パイプラインを利用してデプロイされる。しかし共有リソースの初回デプロイ時は、CI/CD パイプラインがデプロイされておらず存在しないため、初回に限り CDK CLI コマンドで全ての共有リソーススタックの手動デプロイを行う方針とする。
  - CI/CD パイプライン自体は手動でデプロイする必要があるため、インフラチームによって手動で CLI コマンドを実行してデプロイされる。
- 例外としてアプリケーション関連のリソース (ECS サービス・ECS タスク・アプリケーション) はアプリチームで管理する。
  - ただし、ecspresso のバージョン追従の観点から、インフラチームが ECS 各種設定ファイル (ecspresso 設定ファイル、ECS サービス/タスク 定義ファイル) を管理する。
  - インフラチームが管理する ECS 各種設定ファイルをコピーし、アプリケーションの要件に合わせて ECS 各種設定ファイルのパラメーターをカスタマイズする。
  - コピーしカスタマイズしたファイルはそれ以降アプリケーションチームが管理する方針とする。
- CDK コードや各種設定ファイルはインフラ/アプリ チーム管理の GitHub リポジトリにて管理する。
- 管理対象は以下の通り。

**インフラチーム管理**

| 管理対象                                                                | トリガー       | 実行方式                | ロールバック方式 |
| ----------------------------------------------------------------------- | -------------- | ----------------------- | ---------------- |
| 共有リソースデプロイ用 CI/CD パイプラインの初回デプロイ                 | 手動           | AWS CDK CLI(cdk deploy) | CDK ロールバック |
| 共有リソース                                                            | GitHub Actions | CodeBuild(cdk deploy)   | CDK ロールバック |
| 専有リソース                                                            | Google Forms   | CodeBuild(cdk deploy)   | CDK ロールバック |
| ECS 各種設定ファイル (ECS サービス/タスク 定義、ecspresso 設定ファイル) | GitHub Actions | AWS CLI                 | なし             |

**アプリチーム管理**

| 管理対象                                                | トリガー       | 実行方式                                  | ロールバック方式                            |
| ------------------------------------------------------- | -------------- | ----------------------------------------- | ------------------------------------------- |
| Dockerfile                                              | GitHub Actions | CodeBuild(ecspresso deploy)               | ECS デプロイサーキットブレーカー/CodeDeploy |
| ECS サービス/タスク、アプリケーション                   | スクリプト     | CodeBuild(ecspresso deploy)               | ECS デプロイサーキットブレーカー/CodeDeploy |
| バッチモジュール                                        | Google Forms   | CodeBuild(python)                         | なし                                        |
| CloudFront,WAF（アプリ管理アカウント上の AWS リソース） | GitHub Actions | CI/CD パイプライン<br/>(AWS CodePipeline) | CDK ロールバック                            |

### デプロイ方式

#### CI/CD パイプライン

![](/docs/images/cicd-pipeline-architecture.dio.svg)

- 共有/専有 リソースの作成・更新は CI/CD パイプラインを通してデプロイする。
- CI/CD パイプライン自体は初回に限り手動でデプロイし、以降は CI/CD パイプラインを通してリソースの更新を行う。
- CI/CD パイプラインは 共有/専有 毎に専用のパイプラインが存在し、各パイプラインの特徴は以下の通り。
  - 共有リソース 専用パイプライン
    - 共有リソースの更新を行う。
    - パイプラインと共有リソースの関係性は **1 対 1**
  - 専有リソース 専用パイプライン
    - 各プロジェクトの専有リソースの更新を行う。
    - **共有リソース** とし、複数プロジェクトで共通の１つのパイプラインを利用して更新を行う。
    - パイプラインとプロジェクト毎のの関係性は **1 対 多**

#### 共有リソース

- 初回デプロイ時は、CI/CD パイプラインがデプロイされていないため、初回に限り CDK CLI `npx cdk deploy --all` コマンドで全ての共有リソーススタックの手動デプロイを行う方針とする。
- ２回目以降はプルリクエストのマージをトリガーに GitHub Actions を経由して、CI/CD パイプラインによって CDK CLI を実行されデプロイする。

#### 専有リソース

- 専有リソースは申請フォーム (Google Forms) を経由して、CI/CD パイプラインによってデプロイする。
- 詳細は [専有リソースの申請フロー](./../02_Detaildesign/DedicatedDeploymentFlow.md) を参照。

#### アプリ管理アカウント上の AWS リソース

- アプリ管理アカウント上の AWS リソースも本体用アカウントと同様に CDK で構築・管理する方針とする。
  - CDK の修正は、各アプリの要件に合わせてアプリチームで行う。
    - WAF ルール
    - CloudFront パスルール
    - CloudFront キャッシュ
  - 可能な限りアプリチームのみで CDK の管理もしてもらいたいが、難しい場合にはインフラチームがサポートに入る。
- 反映も同様にプルリクエストのマージをトリガーに GitHub Actions を経由して、CI/CD パイプラインによってデプロイする。
  - プルリクエストのマージ条件としてインフラチームメンバーの 1 名以上の承認
- 初回デプロイ時は、CI/CD パイプラインがデプロイされていないため、初回に限り CDK CLI `npx cdk deploy --all` コマンドで手動デプロイを行う方針とする。

#### ECS

- Gevanni では ECS のデプロイ方式に Ecspresso を使用した Rolling デプロイと BlueGreen デプロイを採用しているため、クラスターは CDK で管理・更新され、サービスとタスクの更新は Ecspresso を利用して更新する。
- ECS サービス/タスク へのデプロイには ecspresso を利用しており、ecspresso 最新バージョンへの追従等の観点から設定ファイルのマスターはインフラ管轄とし、アプリ毎のカスタマイズされた設定ファイルはアプリ管轄とする。
- インフラ管轄マスタファイルの更新は、GitHub Actions をトリガーに、S3 内の Ecspresso 設定ファイルを更新し、アプリ管轄のカスタマイズファイルはローカル環境上で更新スクリプトを実行することで Ecspresso 設定ファイルを更新・ECS サービス・タスクに反映する。
  - 参考：[docs/02_Detaildesign/ECS_Ecspresso.md](/docs/02_Detaildesign/ECS_Ecspresso.md)
    ![](/docs/images/gevannni-how-to-manage-ecspresso-conf.dio.png)
- ロールバックは、Rolling デプロイでは、ECS デプロイサーキットブレーカーを、BlueGreen デプロイでは CodeDeploy のロールバック機能を利用する。
  - 詳細は公式ドキュメントを参考すること。
  - ECS 公式ドキュメント: [Amazon ECS デプロイサーキットブレーカーが障害を検出する方法](https://docs.aws.amazon.com/ja_jp/AmazonECS/latest/developerguide/deployment-circuit-breaker.html)

#### Dockerfile

- Dockerfile はアプリリーム側のリポジトリで管理する。
- ECS タスクを更新することで、Dockerfile の更新を反映する。
- GitHub Actions ワークフローによって `Docker イメージをビルド` > `ECR へ Docker イメージをプッシュ` > `S3 バケットへ Ecspresso 設定ファイルをアップロード` を実行する。
- Ecspresso 設定ファイルが S3 バケットへアップロードされたことをトリガーに CI/CD パイプラインを実行する。
- 詳細フローは [csys-infra-gevanni-workflow-sample/docs/JPN/HowToUseEcspresso.md](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample/blob/main/docs/JPN/HowToUseEcspresso.md) に記載。

  ![](/docs/images/cicd-pipeline-architecture.dio.png)

#### バッチモジュール

- 申請フォーム (Google Forms) 経由して、バッチシステム専用のパイプラインから EventBridge や Step Functions などのバッチ関連リソースをデプロイする。
- EventBridge スケジュールまたは API Gateway をトリガーに Step Functions が単発で ECS タスクを起動する。
- 参考：[docs/03_HowToUse/HowToBatchDeployScript.md](/docs/03_HowToUse/HowToBatchDeployScript.md)

  ![](/docs/images/batch-overall-architecture.dio.png)

### デストロイ方式

#### アプリ管理アカウント上の AWS リソース

- サービスを終了する際は、CloudFront のオリジンを ALB からサービス終了画面表示用 S3 に切り替え、Gevanni のリソースを削除する前に、ユーザが ECS などにアクセスにアクセスしないようにしておく。
- 基本的にデストロイは 1 回限りのため、削除用のパイプラインは作成しない。デストロイの際は手動で cdk destroy コマンドを実行する。

#### 専有リソース

- リソース削除はデプロイ時と同様に Google Forms からの申請をトリガーに CI/CD パイプラインによって、Gevanni 上の不要リソースをデストロイする。
- ★ クローズしたサービスのパラメータファイルをどのように管理するのかは要設計

#### ECS

- クラスターは CDK で管理され、サービスとタスクは Ecspresso で更新されているため、CDK テンプレート削除用 CI/CD パイプライン実行時にクラスター内にサービスとタスクが残存しているとエラーでデストロイが失敗する。
- そのため、ECS 関連リソースを削除する際は先に ECS サービスとタスクの手動削除の対応を取る方針とする。

#### バッチモジュール

- バッチ関連リソースの削除は申請フォーム (Google Forms) 経由で行う。
- [リリース方式 - バッチモジュール](#バッチモジュール) で作成したリソースは、申請フォームから実行される削除パイプラインの中で削除される。
- 削除するリソースは、デプロイスクリプトによって付与されたタグによって識別する。

### システムメンテナンス方式

#### AWS リソースのメンテナンス

- インスタンスの OS やパッチ適用は AWS がマネージドで管理・適用している。
- サービスへの影響が最小な時間帯をメンテナンス可能時間枠として設定することで、アップデートがあった場合には、その設定時間内で自動的にパッチ適用される。
- マイナーバージョンアップは意図しないアップグレードによるサービス停止を予防するため無効とする。
- ただし、OpenSearch Serverless と ElastiCache Serverless などのメンテナスを制御できないリソースに関しては、AWS 側でサービス影響のない形で自動的に実施される。

| リソース                | メンテナンス時間枠 | 自動マイナーバージョンアップ | メジャーバージョンアップ |
| ----------------------- | ------------------ | ---------------------------- | ------------------------ |
| Aurora                  | 設定可能           | 無効化                       | 手動                     |
| Aurora Serverless       | 設定可能           | 無効化                       | 手動                     |
| OpenSearch Service      | 設定可能           | なし                         | 手動                     |
| OpenSearch Serverless   | なし               | なし                         | 自動                     |
| ElastiCacahe            | 設定可能           | 無効化                       | 手動                     |
| ElastiCacahe Serverless | なし               | なし                         | 自動                     |

#### システムメンテナンス（メンテナンス画面の表示）

- アプリケーションリリース時やテーブル内容の変更など一般ユーザのアクセスを拒否したい場合があるため、メンテナンス画面表示を伴うシステムメンテナンスを可能とする。
- メンテナンス時は一般ユーザからの通信を拒否するためにアクセス IP 制限を行い、拒否した通信をメンテナンス画面へ振り分ける。さらに、許可された IP の中でも限られた社内ユーザしかアクセスさせないために Basic 認証を行う。
- メンテナンス画面への切り替えはアプリチーム担当者が行う。
- 切り替え方法はアプリチーム担当者の利用しやすさの観点から、GitHub Actionss ワークフローを手動で実行する方法を採用する。

  ![](/docs/images/waf-githubactions.dio.svg)

### AWS リソースの棚卸

- 不要なアクセス権限や古いユーザーを特定・削除することでセキュリティリスクを低減でき、不要な AWS リソースを削除することでコスト削減に繋がる。
- 個人に紐づく権限に関しては、定期的に棚卸を実施し、その他の AWS リソースに関しては、任意のタイミングで実施する。

- 個人に紐づく設定の棚卸し

  - 対象: IAM ユーザ、IAM ロール、SSO 権限
  - 頻度: 年 1 回
  - 手順: 対象者に一斉メールを送信し、確認依頼を実施

- その他リソースの棚卸し
  - 定期的な棚卸し: 実施しない
  - 見直しの条件: 費用が予算想定より増加している場合など

## プロジェクト課題管理方針

- Gevanni では開発に伴う課題は全て GitHub に issue を起票して管理する。
- issue は GitHub Project を利用して管理し、課題はスクラム開発のスプリントの考え方に基づき管理・進行する。
  - 運用方針については Gevanni リポジトリの wiki に詳細を記載しているため、下記リンクを参照すること。
    - [スプリント開発 運用フロー](https://github.com/mynavi-group/csys-infra-gevanni-infra/wiki/%E3%82%B9%E3%83%97%E3%83%AA%E3%83%B3%E3%83%88%E9%96%8B%E7%99%BA-%E9%81%8B%E7%94%A8%E3%83%95%E3%83%AD%E3%83%BC)

### 運用ルール

- **運用イメージ**
  ![](/docs/images/srint-architecture.dio.svg)

  - スプリントは大まかに `プランニング` > `進捗報告` > `期日調整・トリアージ` > `進捗報告` を 1 セットのサイクルとし、このサイクルを繰り返し進める。
  - コミュニケーション、進捗管理、トリアージについて下記の通り。

- **コミュニケーション**

  - 主に Slack を利用して、各メンバーがタスクの進捗や課題を報告する。
  - GitHub Project ではタスクの詳細な進捗状況を記載する。
    - ※ カンバンボード上のステータス変更や issue のコメントを利用。
  - スプリントの開始前に、各担当者は自己判断により実施可能なタスクを選定し Slack 上で PM と優先順位や期日を相談して実施タスクを決定する。

- **進捗管理**

  - 課題は全て GitHub に issue の起票を行う。
  - GitHub Project 上でタスクの状態 (In Progress, In Review, Done など) を管理し、全体の進捗が一目でわかるようにする。

- **トリアージとフィードバック**
  - 進捗報告と、定例会議でスプリントの振り返りを行い、PM が進捗状況を確認し必要に応じてタスクの優先順位やスケジュールの調整を行う。
