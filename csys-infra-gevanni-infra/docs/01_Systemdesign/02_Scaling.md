# 拡張方式

## 基本方針

- サービス提供で使用する AWS サービスを整理し、利用者側で拡張可能なサービスについて、各サービスごとに詳細な拡張方式を記載する。

- サービス影響のある AWS 側の上限値はあらかじめ本設計で整理しておき、上限値を超えそうになった際は自動的に検知を行う。詳細は[クォーターの抵触検知方法](#クォーターの抵触検知方法)

- 各 AWS サービスの拡張方式を以下に記載する。

| No  | サービス名　                       | 拡張基本方式                   | 拡張項目        | 備考                                                                            |
| --- | ---------------------------------- | ------------------------------ | --------------- | ------------------------------------------------------------------------------- |
| 1   | CloudFront                         | AWS 管理                       | -               |                                                                                 |
| 2   | ECS(Fargate)                       | スケールアウト（タスク数増加） | ECS タスク      | 状況に応じてスケールアップ（CPU、メモリ）                                       |
| 3   | Elastic Load Balancing             | AWS 管理                       | -               |                                                                                 |
| 4   | Aurora（プライマリ、レプリカ）     | スケールアップ（ACU に基づく） | CPU,メモリ      | Aurora Capacity Unit（ACU）に基づき自動でスケールアップ                         |
| 5   | Aurora（ストレージ）               | AWS 管理                       | ストレージ      | AWS 側で自動拡張される                                                          |
| 6   | OpenSearch Service（インスタンス） | スケールアウト（インスタンス） | ノード          | 状況に応じてスケールアップ（インスタンスサイズ）                                |
| 7   | OpenSearch Service（ストレージ）   | スケールアップ                 | EBS ディスク    |                                                                                 |
| 7   | OpenSearch Serverless              | AWS 管理                       | OCU             |                                                                                 |
| 8   | ElastiCache                        | スケールアウト（シャード増加） | Valkey シャード | Valkey クラスターモードを採用、状況に応じてスケールアップ（インスタンスサイズ） |
| 9   | ElastiCache Serverless             | AWS 管理                       | -               |                                                                                 |
| 10  | S3                                 | AWS 管理                       | -               |                                                                                 |
| 11  | ECR                                | AWS 管理                       | -               |                                                                                 |
| 12  | AWS WAF                            | AWS 管理                       | -               |

- サードパーティの拡張方式を以下に記載する。

| No  | サービス名　               | 拡張基本方式   | 拡張項目 | 備考                       |
| --- | -------------------------- | -------------- | -------- | -------------------------- |
| 1   | TiDB Cloud Dedicated(TiDB) | スケールアウト | ノード   | 状況に応じてスケールアップ |
| 2   | TiDB Cloud Dedicated(TiKV) | スケールアウト | ノード   | 状況に応じてスケールアップ |
| 2   | TiDB Cloud Dedicated(PD)   | スケールアウト | ノード   | 状況に応じてスケールアップ |
| 3   | TiDB Cloud Serverless      | -              | -        | -                          |

## Gevanni 本体用アカウント戦略

### アカウントのスケーリング

- 本体用アカウント上にデプロイされるサービスの増加に伴い、AWS アカウントのクォータに達してしまうため、必要に応じてスケールアウトしていく。スケーリングは本体アカウントのみで、Route53 アカウントやマーケットプレイスアカウントはスケーリングは行わない。
- `usecases/common/params`配下に prod01,prod02,...のように、環境ファイル用フォルダを追加し、追加した環境用のリリースブランチを追加する。
- 追加した環境用のリリースブランチからデプロイし、Gevanni 本体アカウントをスケーリングさせる。新規で作成されるサービスは追加した環境へデプロイしていく。

  **スケーリングのイメージ図**  
  ![](../images/scaling-account-strategy.dio.svg)

## クォーターの抵触検知方法

- Trusted Advisor と Service Quotas を使用して、クォータの監視を行い、制限の 80%に達した場合に通知することで事前検知を行う。検知した場合には、アカウントのスケーリングを実施する。  
   参考：https://aws.amazon.com/jp/builders-flash/202210/quota-monitor/  
  ※参考サイトでは集約役のプライマリアカウントと監視役のセカンダリアカウントのクロスアカウント構成を取っているが、アカウントスケーリング数は限定的なため、各アカウントにプライマリアカウントの AWS リソースを作成する方針とする。

### 通知フロー

![](../images/scaling-quota-monitor.png)

- クォータ検知から通知までのフローの概要は以下の通り。
  1. 24 時間毎に実行される Lambda 関数により、Trusted Advisor によるサービス制限のチェックをおこなう
  2. Amazon EC2 におけるクォータの確認に特化した Lambda 関数が、5 分毎に実行される。
  3. 2 つの Lambda 関数の実行結果は、Amazon EventBridge によってフィルタリングされる。そして、メール配信用の Amazon SNS、Slack 配信用の Lambda 関数、ステータス履歴保存用の Amazon SQS へそれぞれ配信する。
  4. Slack の Webhook URL は AWS Systems Manager Parameter Store に保存する。
  5. SQS に格納されたステータスチェック履歴について、Lambda 関数がポーリングを行い Amazon DynamoDB に書き込む。書き込みに失敗した場合はデットレターキューに格納する。

(Gevanni 向けのカスタマイズは設計中)
