# コンテナ(アプリケーション)のデプロイ設計

## 概要

- アプリケーションコンテナの作成・更新方法は 3 パターンに分かれている。
- CDK で改修が必要なものはインフラチームが対応するが、基本的にはコンテナ周りの管理はアプリチーム側で行う。

## 構成

![](../images/ecs-deploy-arch.dio.svg)

### リソース一覧

#### 共有リソース

- S3（マスターファイル）

#### 専有リソース

- ECS クラスター
- セキュリティグループ
- タスク実行ロール・タスクロール
- ECR
- コンテナデプロイパイプライン
  - S3
  - CodeBuild
- Secrets Manager

## Ecspresso の選定理由

- Gevanni では ECS のデプロイ方式に Rolling デプロイと BlueGreen デプロイを提供している。
- サービス定義やタスク定義をアプリチーム側で更新できるように、どちらのデプロイパターンも Ecspresso を利用する。

## 要件

### 基本

- 以下の AWS リソースは、CDK ではなく ecspresso で管理
  - ECS サービス
  - ECS タスク定義
  - ECS タスク

### アプリチーム/インフラチームの管理範囲

#### アプリチーム

- Dockerfile の更新
- コンテナビルド/ECR への push 用の Github Actions ワークフローの更新
  - ※インフラチームでワークフローのサンプルファイルは用意する。
- 環境変数の登録・更新
- ecspresso conf の一部パラメータ更新

#### インフラチーム

- マスターの ecspresso conf の更新
- ECS クラスターやタスクロールなどの CDK で管理する AWS リソース

### ecspresso 実行部分のワークフローに関する補足

- ecspresso conf とは、`ecs-service-def.json`,`ecs-task-def.json`,`ecspresso.yml`などの ecspresso に関わる設定ファイルを指す。
- 他の内製 PJ では、ecspresso conf はアプリチーム側のリポジトリで管理することが多い。
- Gevanni では以下の理由から、マスターの ecspresso conf はインフラチーム管理とする。
  - アプリチームの管理範囲を可能な限り減らしたい。
  - ecspresso の新機能への追従しやすい。
    - ecspresso conf の管理をアプリチームのリポジトリにすると、更新のタイミングがアプリ側に依存してしまう。
    - したがって、インフラチームが任意のタイミングで更新できる場所である、S3 上で ecspresso conf を管理する。
- AWS アカウント毎に更新作業が行えるように、Gevanni の 1 AWS アカウントに対して、1 つのマスターバケットを用意する。
- 各内製 PJ のパイプラインはそのマスターバケットから ecspresso conf を取得する。

  ![](../images/gevannni-how-to-manage-ecspresso-conf.dio.png)

### コスト配分タグの設定

#### ECS タスクのタグの伝播元設定

- ECS タスクへのタグ付けのため、タグの伝播元設定が必要
  - 参考: [ECS タスクのタグ付けはタグの伝播元を設定しよう](https://dev.classmethod.jp/articles/ecs-using-tags/)
- タグの伝播元は「ECS サービス」とする。

## ECS サービス(フロントエンド/バックエンド)ごと作成する AWS リソース

- ECS サービス(フロントエンド)のみ作成
  - ALB
- ECS サービス(フロントエンド/バックエンド)の両方で共通
  - CodePipeline
  - CodeBuild
  - CodeDeploy(BlueGreen デプロイのみ)
  - S3(パイプライントリガー)
  - ECR
  - Secrets Manager(コンテナ環境変数用)
  - Systems Manager Parameter Store（ECR のタグ格納用）
  - IAM ロール(OIDC)
    - アプリ用リポジトリの Github Actions から ECR への push 用

## パイプラインの概要

- パイプラインは S3 へのファイル配置をトリガーに起動する。
- CodeBuild 内で`ecspresso deploy`コマンドを実行し、ECS タスク・ECS サービスの作成および更新を行う。  
  ![](../images/gevannni-ecspresso-pipeline.dio.png)

## ユースケースごとのフロー

### フロー 1: アプリの初回デプロイ・更新

- 想定ケース
  - アプリケーションコードの初回デプロイ
  - アプリケーションコードの変更
  - Dockerfile の変更
- (必要な場合)環境変数の事前設定
- アプリチームのリポジトリ上でコードや Dockerfile を変更し、ブランチを更新
- ブランチ更新をトリガーに、GitHub Actions でコンテナビルド/ECR への push
- GitHub Actions から build.sh を実行する。処理の内容は大きく分けて以下の 3 つ。
  1. Git のコミット ID（先頭 7 文字） を取得し、その値で Systems Manager パラメータを更新（Git のコミット ID を ECR のタグ値として利用する）
  1. Docker Build を実行し、イメージを ECR にプッシュする
  1. image.zip の存在有無を確認する
     - ecspresso conf のマスターはアカウント内に 1 つだけ存在するマスターバケット上に配置されている。
     - ECS サービスの初回デプロイは、マスターバケットから image.zip を取得し、プロジェクト専有の Conf バケット上に配置し、配置をトリガーにパイプラインが起動する。
     - 2 回目以降は Conf バケットの image.zip をコピー＆ペーストし、再配置をトリガーにパイプラインが起動する。  
       ![](../images/gevannni-ecspresso-pipeline-build-shell.dio.png)

### フロー 2: アプリの更新なしでコンテナ数などを更新

- 想定ケース
  - コンテナスペックの変更
  - オートスケール設定の変更
- アプリチームは Conf バケット上のファイルを直接編集することはできないので、編集専用の update_ecspresso_conf.py を利用してローカル環境から実行する。ダウンロードとアップロードの処理は以下のとおり。

  - **ダウンロード時**

    1. アプリチームが update_ecspresso_conf.py を実行
    1. update_ecspresso_conf.py が Conf バケットから image.zip をダウンロード
    1. update_ecspresso_conf.py が image.zip を解凍
    1. アプリチームが解凍された ecspresso conf を修正  
       ![](../images/gevannni-ecspresso-pipeline-download.dio.png)

  - **アップロード時**
    1. アプリチームが update_ecspresso_conf.py を実行
    1. update_ecspresso_conf.py が ecspresso conf を image.zip に圧縮
    1. update_ecspresso_conf.py が image.zip を Conf バケットにアップロード  
       ![](../images/gevannni-ecspresso-pipeline-upload.dio.png)

### フロー 3: ポートの変更

- 想定ケース
  - ポートを変更
- 前提
  - ポートは CDK のパラメータファイルで管理している。
  - CDK で以下の項目が設定される。
    - セキュリティグループのインバウンドルール
    - CodeBuild 上の環境変数
  - CodeBuild 上の環境変数は ecspresso に自動連携されるため、ecspresso conf の変更は不要
    - 環境変数反映のため、コンテナ更新は必要
- ポートの追加・削除ともに、申請フォーム経由で、専有リソース作成・更新用パイプラインを使用して、ポート番号を変更する。
- パイプラインの詳細は[DedicatedDeploymentFlow.md](DedicatedDeploymentFlow.md)を参照

## 環境変数の設定方法

- 環境変数は Systems Manager Parameter Store と Secrets Manager を併用する。使い分けは以下のとおり。

### Systems Manager Parameter Store

- インフラチームが主に利用
- 共有リソース（VPC）や専有リソース（パイプライン周り）などのリソース名や ID を格納
- 管理コストの観点から選定

### Secrets Manager

- アプリチームが主に利用
- アプリケーション周りの環境変数を格納する。
  - NewRelic の API キーも Secrets Manager に格納する。
- シークレット単位で一度権限付与をするだけで済むため、有料ではあるがインフラチームの管理面の観点から選定

#### Secrets Manager の運用方針

- シークレットの作成自体はインフラチームが CDK で行い、キーバリュー値の追加はアプリチーム側で実施する。
- CDK で再デプロイしても初期化やドリフト検出が起きないことは確認済
- CDK でキーバリュー値に意図しない変更が発生した場合は、Secrets Manager のバージョニング機能を使用して復元する。
- ECS サービス単位で、アプリチームに AWS リソース操作権限を付与

#### Secrets Manager への登録

- アプリチームは Gevanni アカウントのコンソールにアクセスできず、登録したシークレットを確認できないため、アプリチーム側で自由に確認できるようにする。
- Secrets Manager に登録する値は、`GutHub Secrets` または `Google スプレッドシート` で管理する。
  - ※ `GutHub Secrets` または `Google スプレッドシート` どちら一方のみを使用する。
  - 以下の背景によって、シークレット情報を `Googgle スプレッドシート` で管理する選択肢が存在する。
    - 開発チーム内で 管理/共有 がしやすい。
    - 下記の理由からセキュリティが担保されている。
      - マイナビの Google Workspace では、組織レベルのポリシーで組織外の Google アカウントからのアクセスを完全に禁止している。
      - そのため、マイナビ社外にスプレッドシートの情報が洩れることはない。
    - そもそもマイナビの「エンジニアリング統括本部：開発標準」でスプレッドシート管理が推奨されている。
      - 下記「エンジニアリング統括本部：開発標準」の引用。
        > 基本的にスプシで管理する。\
        > env の sample だけソース管理しておいて.env 自体は ignore を推奨。\
        > DB 情報など不変なものはソース管理外とし、変わるものはソース管理しても良い。\
- Dockerfile を更新するタイミングで自動的に GutHub Secrets または Google スプレッドシートの値を Secrets Manager に反映し、コンテナへの反映も完了する。

![](../images/ecs-secrets-flow.dio.svg)
