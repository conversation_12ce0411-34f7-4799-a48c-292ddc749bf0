# New Relic 設計

## 概要

- <PERSON><PERSON>nni ではアプリケーションチームに AWS マネジメントコンソールへのアクセス権限を与えない方針である。
- そのため、アプリケーションログを NewRelic へ転送し、インフラ/アプリ 共に NewRelic から閲覧する方針とする。
- 本ドキュメントでは、New Relic の詳細設計方針について記載する。

## 構成

### データ連携

<div align="center">
  <img src="../images/new_relic_integration.png" width="100%"/>
</div>

### 対応リソース

**共有リソース**

- DataFirehose(メトリクス)
- S3 バケット（DataFirehose 送信失敗データ）
- SecretManager(データ連携用 API キー管理)
- Clouwatch Metric Streams
- IAM ロール（NewRelic）
- Log グループ(DataFirehose)
- Lambda Layer

**専有リソース**
| | アクセスログ | アプリログ |
| ---------------------------- | ------------ | ---------- |
| Secret Manager（API キー管理） | 〇 | 〇 |
| S3 バケット（ALB ログ） | 〇 | |
| S3 バケット（監査） | | 〇 |
| DataFirehose（ログ） | | 〇 |
| DataFirehose（メトリクス） | | |
| Lambda | 〇 | |
| FireLens コンテナ | | 〇 |
| MetricStreams | | |
| IAM ロール（NewRelic） | | |
| Log グループ（DataFirehose） | | 〇 |
| Log グループ（FireLens） | | 〇 |

※ SecretManager の取り扱い

- SecretMangaer は値が空の状態でデプロイし、後から API キーを追加する。
- NewRelic の License キーは SecretManager に`api_key`キーで登録する。

## 要件

### 目的

- システムトラブル発生時、アプリチームが発生検知から原因の特定までを迅速に行うことができる基盤を提供する。
- インフラチームのトラブルシューティングを支援するサービス横断の監視基盤を提供する。
- Gevanni の AWS アカウントにログインせずともシステムのログやメトリクスを確認できるようにする。

### 利用者

- Gevanni 管理者
- Gevanni 管理者以外のインフラチームメンバー
- アプリチームメンバー

### 監視対象

- サービス専有リソースのメトリクス
- ECS コンテナ内のアプリケーションログ
- ALB のアクセスログ
- CodeBuild のビルドログ
- ElastiCache のスローログとエンジンログ

### メトリクス

[AWS Integration][AWS Integration] ([Metric streams 方式][Metric streams統合])で連携する。

共有リソースを用いてメトリクス連携用 NewRelic アカウントに対して連携する。 共有リソースとメトリクス連携用 NewRelic アカウントが 1 対 1 で対応する。

**詳細**

- cloudwatch MetricStreams と DataFirehose を用いて連携する。
  - CloudwatchMetric Streams のロールで`firehose:PutRecord, firehose:PutRecordBatch`を許可する。
  - CloudwatchMetric Streams の includeFilter を用いて連携するメトリクスを限定する。
- NewRelic Infrastructure と AWS アカウントを紐づける必要がある。方法は[ドキュメント][AWS NewRelicマッピング]を参考に行う。必要となる情報は以下の通りである。
  - New Relic 連携用 IAM ロールの ARN(ExternalId として NewRelic のアカウント ID を指定する)
  - 連携する AWS アカウント ID
- アカウント連携後、最大 30 分以内にすべてのメトリクスが[New Relic Infrastructure][NewRelicメトリクス]に連携される。
- DataFirehose が送信に失敗したデータを S3 バケットに保存する。
- DataFirehose は SecretMangaer から License キーを参照する。

**ダッシュボード**

- アプリチーム用にインフラメトリクスのダッシュボードを作成して可視化する。
- メトリクス連携用 NewRelic アカウントのデータから API 経由で各サービスアカウントのダッシュボードを作成する。
- ダッシュボード作成時にディメンションを指定し、各サービスごとにデータを振り分ける。

データのフィルタリングに必要なディメンション

- ECS クラスター名
- ALB 名

<div align="center">
  <img src="../images/new_relic_metrics_integration.png" width="100%"/>
</div>

### アプリケーションログ

[DataFirehose 連携][DataFirehose連携]を用いて連携する。
アプリケーションコンテナにサイドカーコンテナとして FireLens をデプロイし、DataFirehose を経由して NewRelic に連携する。

**詳細**

- ECS タスク定義でログルーターに FireLens を指定する。
  - ECS タスクロールで`firehose:PutRecord, firehose:PutRecordBatch`を許可する。
- Frontend/Backend コンテナは FireLens を介して共用の DataFirehose にログを送信する。
- FireLens 自身のログは CloudWatchLogs に連携する。
- DataFirehose に送信する全てのデータを S3 にバックアップする。
- DataFirehose は SecretMangaer から License キーを参照する。

### ALB アクセスログ

Lambda を用いて S3 に保存される ALB アクセスログを New Relic に連携する。
※ Lambda 関数は[New Relic 公式][New Relic S3]のコードをベースに SecretManager から API キーを参照するように変更。

**詳細**

- ALB アクセスログは S3 バケットに保存される。
- ログ保存用 S3 バケットの PutObjectEvent をトリガーに連携用 Lambda が起動するように設定する。
- Lambda Layer を使用して Python ライブラリを使用する。

### CodeBuild ビルドログ

CloudWatch ロググループから DataFirehose を経由して NewRelic に連携する。  
![](../images/new_relic_codebuild_arch.dio.svg)

**詳細**

- CodeBuild のログを CloudWatch ロググループに出力する。
- CloudWatch ロググループのサブスクリプションフィルターを使用して、全てのログを Data Firehose に連携する。
- NewRelic への連携に失敗したログのみを S3 に保存する。

### ElastiCache クエリログ

ElastiCache のクエリログは直接 Data Firehose にデータを送信し、NewRelic に連携する。
![](../images/new_relic_elasticache_arch.dio.svg)

**詳細**

- ElastiCache のクエリログは Data Firehose へ直接送信する。
- DataFirehose に送信する全てのデータを S3 にバックアップする。

## アラート戦略

### 方針

- New Relic に連携したメトリクスに対するアラートの設定はアプリチームが行う。
- New Relic infrastructure のでアラート条件を設定する。
  - infrastructure > Settings > Alerts から設定する。
- アプリチームには[よくある設定例][アラート設定例]を提示する。
- アラートの設定は REST [API][アラートAPI]経由でも可能である。

## アカウント戦略

### 方針

- Gevanni サービスと開発プロジェクトは 1 対 1 で対応する。
- [マルチアカウントで運用][マルチアカウントアプローチ]し、各プロジェクトのアカウントを疎結合にする。
- ユーザー管理の仕組みと合わせて柔軟なアクセス制御を実現する。
- Gevanni Organization 内ですべてのアカウントを管理する。
- プロジェクト単位で STG/PROD 用のサブアカウントを作成する。（必要であれば STG を複数）

<div align="center">
  <img src="../images/new_relic_accounts.png" width="50%"/>
</div>

## ユーザー管理

### 方針

- 利用者は email と password で New Relic にログインする。
- [New Relic ユーザー][ユーザー管理ガイド]は Organization 内で共有される。
- 各ユーザーは 1 つ以上のグループに所属する。
- グループにはロールが紐づけられ、各アカウントへのアクセスを制御する。
- プロジェクト単位で管理者とメンバーのグループを作成する。（Service メンバー/Service 管理者グループ）
- インフラチーム用のグループを作成する。

### 運用

- Service メンバーグループのユーザー管理権限を Service 管理者グループに移譲する。
- Gevanni 管理者がアプリチームのリーダーを Service 管理者グループに追加する。
- アプリチームのリーダーが必要に応じてメンバーを Service メンバーグループに追加申請を行う。
<div align="center">
  <img src="../images/new_relic_user_management.png" width="50%"/>
</div>

## 権限管理

### 方針

- グループに紐づけたロール × パーミッションによってアクセス制御を行う。
- Service メンバー/Service 管理者グループは対応するプロジェクトの STG/PROD 用アカウントへのアクセス権限を与える。
  - 必要に応じて Service 管理者グループのみ編集権限を与えるなど柔軟に対応する。
- インフラグループにすべてのサービスアカウントへのアクセス権限を与える。
- Gevanni 管理者のみ Organization の設定ができるようにする。

<div align="center">
  <img src="../images/new_relic_role.png" width="100%"/>
</div>

### 権限設計

- Gevanni 管理者

  - 全 Service の New Relic Infrastructure
  - 全 Service の New Relic Logs
  - 全 Service のアラート設定
  - 請求管理
  - Gevanni Organization 機能
  - 全ユーザー管理

- インフラチーム

  - 全 Service の New Relic Infrastructure
  - 全 Service の New Relic Logs
  - 全 Service のアラート設定

- アプリチームリーダー

  - 担当 Service の New Relic Infrastructure ダッシュボード閲覧
  - 担当 Service の New Relic Logs
  - 担当 Service のアラート設定
  - 担当 Service のアプリチームユーザー管理

- アプリチームメンバー
  - 担当 Service の New Relic Infrastructure ダッシュボード閲覧
  - 担当 Service の New Relic Logs

| ユーザーグループ名 | ユーザータイプ | グループロール                                                                    | 対象アカウント     |
| ------------------ | -------------- | --------------------------------------------------------------------------------- | ------------------ |
| GevanniAdmin       | Full           | 全て                                                                              | 全アカウント       |
| Infra              | Full           | AllProductAdmin                                                                   | 全アカウント       |
| ServiceID_admin    | Basic          | AllProductAdmin, Authentication Domain Add Users,GroupAdmin(対象：ServiceXMember) | サービスアカウント |
| ServiceID_member   | Basic          | ReadOnly                                                                          | サービスアカウント |

## 課金体系

- 請求は Organization 単位で行われる。
- データの取り込みとユーザー数に対して[コスト][料金プラン]が発生する。
- [ユーザータイプ][ユーザータイプ比較]によってコストが変動する。

## セキュリティ・コンプライアンス

### データの暗号化

- 全てのドメインで TLS1.2 以降を用いて暗号化されている。([セキュリティポリシー][New Relic Network])
  - Metric Streams : `aws-api.newrelic.com`
  - ログ API : `log-api.newrelic.com`
- New Relic に保存されるすべてのデータは暗号化される。（FIPS140-2 準拠）
- ALB アクセスログ保存用 S3 バケットは S3 管理キー（SSE-S3）によって暗号化される。

### アクセス制御

- AWS に対するアクセス制御
  - ReadOnlyAccess[マネージドポリシー][統合とマネージドポリシー]を付与した IAM ロールを用いて接続する。
- New Relic 内のアクセス制御
  - グループとロールを用いてアクセス制御を行う。
  - アプリチームのメンバー社員は基本的に読み取り権限のみを持つ。

### データの保持期間

マイナビ社内規定により、監査用にログを 5 年間保存する。 Gevanni 側でログの内容を制御できないため NewRelic に連携するすべてのログに適用する。
連携に使用する FireLens コンテナ自身のログは CwLogs で 3 ヵ月間保存することとする。

- ログ

  - NewRelic: 30 日間
  - S3 バケット（監査用）: 5 年間

- メトリクス
  - NewRelic: 395 日間（13 ヵ月）
  - CloudwatchMetrics: 15 ヵ月間（1 分間隔）

## 命名規則

NewRelic リソースの命名規則を以下に示す。

- アカウント名
  - 形式 : {専有リソースのサービス ID}\_{連携先 AWS アカウント ID}
  - 例: example01-stg-ab_0123456789
- グループ名
  - 形式 : {サービス ID のプレフィックス}-{ランダムな英小文字\*3}\_{権限}
  - 例: example01-abc_member
- ユーザー名
  - 形式: {mynavi.jp のユーザー名}
  - 例: mynavi.taro.dx
- ダッシュボード名
  - 形式: 任意（API で自動生成）
  - 例: AWS Infrastructure Metrics Dashboard
- API キー名
  - 形式: 任意
  - 例: service_key
- アラート名
  - 形式: 任意
  - 例: high_cpu_usage_alert

## セットアップ

ここでは新規 Gevanni サービス作成時の NewRelic セットアップフローを示す。
利用申請を受けて NewRelic アカウントを作成する。 その後、以下の 2 つのフローを進める。
※ 共有リソースの新規デプロイ時は AWS と NewRelic Infrastructure との連携を行う。

- Service グループを作成し、権限を割り当てる。
- SecretManager に License キーを登録する。

詳細な流れは以下の図を参考

<div align="center">
  <img src="../images/new_relic_setup_flow.png" width="80%"/>
</div>

<!-- 参照リンクまとめ -->

[ELBのメトリクス]: https://docs.aws.amazon.com/ja_jp/elasticloadbalancing/latest/application/load-balancer-cloudwatch-metrics.html
[AWS Integration]: https://docs.newrelic.com/jp/docs/infrastructure/amazon-integrations/get-started/introduction-aws-integrations/
[Metric streams統合]: https://docs.newrelic.com/jp/docs/infrastructure/amazon-integrations/connect/aws-metric-stream-setup/
[AWS NewRelicマッピング]: https://dev.classmethod.jp/articles/cloudwatch-new-relic-integration/
[NewRelicメトリクス]: https://docs.newrelic.com/jp/docs/infrastructure/amazon-integrations/get-started/aws-integrations-metrics/
[DataFirehose連携]: https://docs.newrelic.com/jp/docs/logs/forward-logs/stream-logs-using-kinesis-data-firehose/
[New Relic S3]: https://serverlessrepo.aws.amazon.com/applications/us-west-2/************/NewRelic-log-ingestion-s3
[アラート設定例]: https://docs.newrelic.com/jp/docs/infrastructure/new-relic-infrastructure/infrastructure-alert-conditions/infrastructure-alerting-examples/
[アラートAPI]: https://docs.newrelic.com/jp/docs/infrastructure/new-relic-infrastructure/infrastructure-alert-conditions/rest-api-calls-new-relic-infrastructure-alerts/
[マルチアカウントアプローチ]: https://docs.newrelic.com/jp/docs/accounts/accounts-billing/account-structure/create-manage-accounts/
[ユーザー管理ガイド]: https://newrelic-kk-public-docs.s3.ap-northeast-1.amazonaws.com/%E6%96%B0%E3%81%97%E3%81%84%E3%83%A6%E3%83%BC%E3%82%B6%E3%83%BC%E7%AE%A1%E7%90%86%E3%83%A2%E3%83%87%E3%83%AB%E3%81%AB%E3%81%8A%E3%81%91%E3%82%8B+%E3%82%A2%E3%82%AB%E3%82%A6%E3%83%B3%E3%83%88%E7%AE%A1%E7%90%86%E3%82%AC%E3%82%A4%E3%83%89.pdf
[ユーザータイプ比較]: https://docs.newrelic.com/jp/docs/accounts/accounts-billing/new-relic-one-user-management/user-type/#user-type-comparison-table
[料金プラン]: https://newrelic.com/jp/pricing
[New Relic Network]: https://docs.newrelic.com/jp/docs/new-relic-solutions/get-started/networks/
[統合とマネージドポリシー]: https://docs.newrelic.com/jp/docs/infrastructure/amazon-integrations/get-started/integrations-managed-policies/
[データ保持期間]: https://docs.newrelic.com/jp/docs/data-apis/manage-data/manage-data-retention/#infrastructure-data
