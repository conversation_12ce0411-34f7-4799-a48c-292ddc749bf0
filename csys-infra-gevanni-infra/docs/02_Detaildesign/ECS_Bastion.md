# 踏み台コンテナ設計

## 概要

- ECS タスクを踏み台として使用することで、バックエンドコンテナやデータベースなどの外部から直接アクセスできないリソースへ一時的なアクセスが可能となる。
- 全プロジェクト共有の踏み台コンテナ用 ECS クラスターを共有リソースとして作成し、プロジェクトごとにクラスター内に ECS タスクを起動していく。
- アプリチームがローカル環境から run_task を実行するスクリプトを実行し、踏み台コンテナを起動する。立ち上がったタスクは一定時間が経過すると自動で停止する。

## 構成

![](../images/bastion-arch.dio.svg)

### リソース一覧

#### 共有リソース

- 踏み台コンテナ用 ECS クラスター
- 踏み台コンテナ用 Dockerfile
- ECR

#### 専有リソース

**インフラ管理**

- ECS タスク定義
- セキュリティグループ
- タスクロール
- ロググループ

**アプリ管理**

- 踏み台コンテナ起動スクリプト

## 要件

### 想定利用者

- アプリチームのメンバー
  - マイナビ社内/開発会社どちらも

### 利用の前提条件

- サービス単位で作成する IAM ロールにスイッチしている状態であること
- 踏み台コンテナ自体もサービス単位で分かれている。
  - そのため、複数サービスの IAM ロールへのスイッチ権限を持っている場合、接続先のサービスにあわせて、IAM ロールを切り替える必要がある。

### 接続先

- DB(TiDB Cloud)接続
  - テーブル定義やデータインポート
    - TiDB Cloud のドキュメントによると、コンソールから DB 接続する方法はなし。
  - GUI データベースツールの利用
    - (注意)マイナビ内では MySQL Workbench の利用が多いが、MySQL Workbench は TiDB を完全にはサポートしていないらしい。
      - TiDB のドキュメントによると、他の接続ツールの利用が推奨とのこと
    - 利用者向けドキュメントで、上記について記載しておく。
- ECS サービス(バックエンド)の API を VPC 内でコール
  - 名前解決は、Service Connect ではなく Service Discovery による Route53 経由でおこなう。
    - CloudMap サービスは CDK 管理
    - Service Discovery はバックエンドのサービス定義から有効化

**今後対応予定**

- DB(Aurora)接続
- ElastiCache 接続
- EFS 接続
  - 通常時のファイル更新は、共用 Transfer Family を使ってもらう想定だが、ファイルのパーミッション管理などは踏み台から作業が必要になるため。

### 踏み台コンテナから各リソースへのアクセス制御

- 踏み台コンテナから各リソースへのアクセスは、セキュリティグループで制御する。
- 同一サービス（スタック）内の踏み台コンテナからのみ接続を許可し、他サービスからの接続は拒否する。
- そのため、同一プロジェクトでも異なるサービスのリソースにアクセスしたい場合は、踏み台コンテナを作成し直す必要がある。
- 例外
  - TiDB 用 VPC エンドポイントとそのセキュリティグループは共有リソースのため、DB ユーザー/パスワードのみでアクセス制御

## 実装

### 踏み台コンテナの自動停止

- 踏台コンテナにはプロセスの監視ロジックが実装されいる。
- 30 分ごとにコンテナ内の SSM エージェントのプロセスを確認して、SSM エージェントのプロセス数、つまり SSM ユーザーのログイン数によって LOGIN_USER フラグを切り替える処理を行う。
- LOGIN_USER フラグが"no exist"になってから 15 分後にログイン数が 0 であればスクリプトを終了し、コンテナが停止する。処理フロー図は以下のとおり。  
  ![](../images/bastion-shell-logic.png)

### 踏み台コンテナ起動スクリプト

- SSM からリソース ID を取得している。

  - 共有リソース
    - ECS クラスター名
    - プライベートサブネット ID
    - TiDB エンドポイント
  - 専有リソース
    - タスク定義
    - セキュリティグループ
    - バックエンドコンテナの DNS 名
    - バックエンドコンテナのポート番号

- スクリプトには AWS CLI コマンドを使用し、`run_task`で単発の ECS タスクを起動する。
- 起動後はプロセス監視で自動的にタスクが停止される。

### コスト配分タグの設定

- 踏み台コンテナ起動スクリプトで ECS タスク起動時にタグを付与する。

#### ECS タスクのタグの伝播元設定

- ECS タスクへのタグ付けのため、タグの伝播元設定が必要
  - 参考: [ECS タスクのタグ付けはタグの伝播元を設定しよう](https://dev.classmethod.jp/articles/ecs-using-tags/)
- タグの伝播元は「ECS タスク定義」とする。
