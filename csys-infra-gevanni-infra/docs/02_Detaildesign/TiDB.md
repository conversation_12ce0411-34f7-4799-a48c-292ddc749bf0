# TiDB 設計方針

## 概要

- コストと運用負荷の観点から stg/prod どちらともサーバーレスのみの利用とする。
- 操作権限は インフラチーム / アプリチーム / 費用振替担当者 毎に、組織 / プロジェクト レベルで分ける。

## 構成

![](/docs/images/tidb-architecture.dio.svg)

### リソース

- TiDB Cloud Serverless

**共有リソース**

- Interface VPC Endpoint
  - TiDB 接続用

## 設計方針

### 接続経路

- PrivateLink 経由で接続する。
  - 参考: [プライベートエンドポイント経由で TiDB サーバーレスに接続する](https://docs.pingcap.com/ja/tidbcloud/set-up-private-endpoint-connections-serverless)

### 接続元の制限

- PrivateLink にアタッチするセキュリティグループは Private サブネット全体からの接続のみを許可するルールとする。
  - すべての ECS タスクから TiDB サーバーレスクラスターへの接続を許可したい。かつ PrivateLink は共有リソースのため専有リソースである各案件の ECS タスクのセキュリティグループ ID を取得するのは手間がかかる。
    - Frontend
    - Backend
    - Bastion
    - Batch
  - すべての ECS タスクは Private サブネットに配置されており、現状では Private サブネットには ECS しか存在しない。
  - そのため、Private サブネット全体からの接続を許可すれば、すべての ECS タスクから接続を許可でき、かつ ECS タスク以外からの接続は許可しないことになる。

### プロビジョニング方式

- デプロイ環境問わず、サーバーレスを利用する。
  - 下記の背景から、stg / prod どちらともサーバーレスを利用する。
    - サーバレスであれば、スケーリングやメンテナンスなどをマネージドで実施されるため運用負担が小さい。
    - TiDB Dedicated は最小ノードが「4 vCPU, 16 GiB」で、Auora の T 系 インスタンスに相当するものがない。
    - そのため ElastiCache と異なり、stg も費用的にサーバレスの方がよい。
      - 参考:
        - [TiDB Serverless 価格](https://pingcap.co.jp/tidb-cloud-serverless-pricing-details/)
        - [TiDB Dedicated 価格](https://pingcap.co.jp/tidb-cloud-pricing-details/)

### AWS アカウント作成単位

- 本体用アカウントがスケーリングした際にデータ移行の負担を無くすために、TiDB 用のアカウントを作成する。
- TiDB は ServiceProviderAccount に作成されるため、TiDB 購入用アカウントのスケールアウトは不要である。

## 権限方針

- 操作権限は インフラチーム / アプリチーム / 費用振替担当者 毎に、組織 / プロジェクト レベルで分ける。
- 各権限については下記リンクのドキュメントを参照。
  - [アイデンティティアクセス管理](https://docs.pingcap.com/ja/tidbcloud/manage-user-access)

### 各チームに付与する担当範囲と権限

### インフラチーム

- 担当範囲
  - プロジェクトの作成
  - 組織と新規プロジェクトにアプリチームメンバーのユーザー追加
    - 組織は Gevanni 全体で共通
    - プロジェクトはサービス単位
- 操作権限
  - 組織レベル
    - Organization Owner
  - プロジェクトレベル
    - Project Owner
      - ※ Organization Owner の場合、自動的にこの権限になる。

### アプリチーム

- 担当範囲
  - 既存プロジェクトへのユーザー追加・削除
    - 対象アカウントが組織に所属していない場合、デフォルトで Organization Viewer として組織に招待される。
  - クラスターの 作成 / 変更 / 削除
  - 支出制限の管理
- 操作権限
  - 組織レベル
    - Organization Viewer
  - プロジェクトレベル
    - Project Owner

### 費用振替担当者

- 担当範囲
  - 各プロジェクトの費用確認
- 操作権限
  - 組織レベル
    - Organization Billing Manager
  - プロジェクトレベル
    - 権限なし
      - ※ 組織招待時のデフォルト

> [!NOTE]
> ロールの名称が変更されました。
>
> - `Organization Billing Admin` から `Organization Billing Manager` へ変更
> - `Organization Member` から `Organization Viewer` へ変更
