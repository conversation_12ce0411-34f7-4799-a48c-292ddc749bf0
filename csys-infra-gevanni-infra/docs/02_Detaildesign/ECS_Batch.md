# Gevanni バッチ全体構成

## 概要

- バッチシステムは、Step Functions と ECS を使用して柔軟なバッチ処理を実現し、API Gateway や EventBridge 経由で実行する。
- バッチ関連リソースの設定はアプリチーム管轄とし、作成・更新はパイプラインで行う。

## 構成

![システム構成図](/docs/images/batch-overall-architecture.dio.png)

### リソース一覧

#### 共有リソース一覧

- Lambda (イベント重複実行回避用)

#### 専有リソース一覧

- セキュリティグループ
- ECS (バッチ)
  - ECS タスク
  - ECR
  - コンテナデプロイパイプライン
  - S3 (コンテナ環境変数用)
  - Secrets Manager (コンテナ環境変数用)
- API Gateway
- EventBridge
- Step Function

## 要件

- バッチの実行に関して
  - 実行される時間を指定できること
  - 定期的に実行できること
  - 特定の処理 (S3 バケットにオブジェクトが置かれた、SaaS のアプリケーションで変更が行われたなど) をトリガーに実行できること
  - 特定のバッチ処理の実行後に、別のバッチを実行できること
    - 例: ECS タスク A の実行後 ECS タスク B を実行する、ECS タスク A と ECS タスク B を同時に実行するなど
  - 同一トリガーのバッチが重複実行されないこと
  - IAM 認証を使用する API 経由でバッチが実行できる
      - アプリケーション上の操作等の、イベントを起因としたバッチの実行をサポートするため
  - バッチが実行するコマンドを任意のコマンドに上書きできる (バッチはコンテナで実行される)
- バッチコンテナについて
  - バッチコンテナとそのデプロイパイプラインを専有リソースとすること
  - 複数のバッチコンテナを実行できること
    - バッチコンテナの場合、フロントエンド/バックエンドのように 1 サービスにつき 1 つだと使いづらいと思われるため
  - TiDB/ElastiCache/EFS/バックエンドコンテナ にアクセスできること
    - アクセスする/しないを選択できること
  - 環境変数をサポートし、その値は SecretsManager・Parameter Store に保管すること
- API キーを使用する API を作成するときの要件
  - キーの漏洩検知ができること
  - キーのローテーションができること

## バッチ設計方針

### バッチシステム全体の設計方針

- 要件の異なるバッチ処理をなるべくサポートするため、柔軟性の高い Step Functions + ECS を使用
  - Step Functions から RunTask API もしくは、RunTask.sync API を実行し、バッチ処理用のワンショットタスクを作成・実行する
- Step Functions、バッチコンテナのタスク定義、実行トリガーは、専用のパイプラインから作成
  - ロジック部分は、アプリリポジトリに保管され、CDK とデプロイのタイミングが異なるため
- バッチは API Gateway・EventBridge 経由で実行
  - cron 実行したいものは、EventBridge のスケジュールタイプのルールから実行
  - 特定の処理をトリガーにしたいものは、API Gateway 経由で実行
- EventBridge の重複実行を防ぐため、EventBridge から直接 Step Functions を実行せず Lambda から実行する
- API 経由でのバッチ実行には、REST API タイプの API Gateway を使用する
    - API Gateway コンソールから作成できる API キーは、REST API でしか使用できず、今後 API キーでの認証もサポートすることを考慮して REST API とする
    - API Gateway がリクエストを受けてから、Step Functions を実行するための方法として、Step Functions 実行用 Lambda を使用する
        - API Gateway から Step Functions の `StartExecution` API を実行するための方法としては、Lambda を使用する方法と、AWS 統合を使用して、API Gateway が受け取ったリクエストを `StartExecution` API のリクエストにマッピングす方法が考えられる
            - 後者のリクエストをマッピングする方式は、マッピングテンプレートの保守性が悪いため、AWS 統合を使用せず、Lambda からリクエストを実行する方法とする
        - Lambda のログは CloudWatch Logs にのみ出力する
            - Lambda の実行結果は API Gateway のレスポンスで返却し、アプリチームも確認可能なため
- API Gateway を使用するかどうかは、バッチ毎に確認し、サービス内のバッチが1つでも API Gateway を使用する場合にリソースを作成する
    - 上記の Step Functions を実行する Lambda も API Gateway と統合されることから、同様の条件で作成する
    - バッチごとに API Gateway を作成するとリソースの作成上限に達する可能性があるため、サービスごとに作成する
    - どのバッチを呼び出すかは、リクエストボディにバッチ名を含めて API リクエストをしてもらうことで識別する
- Step Functions 実行用 Lambda は、申請の際、バッチを使用するとして申請されたバッチのみ呼び出せるような権限とする
    - API Gateway からの呼び出しを想定していないバッチの呼び出しや、他のサービスのバッチの呼び出しができないようにするため
- API のアクセスログは Gevanni チーム管理とし、NewRelic との連携は行わない
    - 理由は以下の通り
        - API のアクセスログにはバッチの処理結果等は記載されておらず、API の送信元や、呼び出したユーザーのようなアプリチームが確認したい情報は含まれていないため
        - アプリチームが確認する可能性がある API のステータスコードやレスポンスのメッセージに関しては、API Gateway が返すレスポンスで確認できるため

### バッチ基盤設計方針

ECS・Step Functions・呼び出し用 Lambda をバッチ基盤とし、この部分の設計方針を示す。

- Step Functions・ECS はパイプライン内でスクリプトを実行し作成する
  - パイプラインの設計方針に関しては後述
- Step Functions には、`ecs:RunTask` を実行するために必要なポリシーのみを許可した IAM Role をアタッチする
  - このバッチ基盤では、ECS の実行のみサポートするため
- バックエンドタスクと通信する場合は、ServiceDiscovery を使用する
  - ECS タスクを直接起動させる関係上、ServiceConnect は使用できず、ALB はアカウント単位で作成できる数の上限が低いため
  - 通信するかしないかは、Gevanni 利用の際の申請フォームで制御
  - 通信する場合は、ServiceDiscovery 用の CloudMap を CDK で新たに作成し、バックエンドコンテナに割り当てた後、バックエンドタスクのサービス定義に `serviceRegistries` を追加する
- バッチタスクはサイドカー方式もサポートし、1 つの ECS タスクに複数のコンテナを含める
- Step Functions 内で実行される ECS タスクに必要なリソースは CDK で管理する
  - 実際に作成するリソースは以下の通り
    - コンテナ定義ごとに作成するもの
      - ECR
        - イメージのタグをコミットハッシュにする都合で、コンテナ定義ごとに分けて ECR を作成しないとイメージが更新されてしまうため
      - パラメータストア
        - バッチアプリビルドワークフロー内でタスク定義に含まれるコンテナと、ECR を対応付けるために使用される
    - タスク定義ごとに作成するもの
      - タスクロール
        - アプリチームの CloudFront アカウント内の S3 にアクセスするなどの要件がありそうなため
        - 申請フォームから操作したいリソースの ID と アクションをもらって権限を追加する想定で考えているがこの部分は未実装で後日対応予定
    - 各サービスごとに使用するもの
      - タスク実行ロール
- Step Functions とその呼出で使用する以下のリソースは CDK で管理する
  - バッチ基盤のために作成されるものは以下の通り
    - 全サービスで共通で作成するもの
      - 重複イベント回避用 Lambda
        - 基本的には `StartExecution` API を実行するのみで、サービスごとに固有の操作が行われないため
      - ステートマシンが使用する IAM Role
        - CDK で作成する関係上、すべてのタスク定義の `run_task` を許可する形となり、最小権限の原則に反してしまうが許容する
    - サービス毎に作成するもの
      - セキュリティグループ
        - 各サービスごとにあらかじめ以下のセキュリティグループを作成しておき、パイプラインの中でどのセキュリティグループと紐づけるかを制御する
          - DB と通信可能なもの
          - バックエンド・DB と通信可能なもの
        - 理由は、バッチ内に含まれる ECS タスクをいくつにするか、どこと疎通ができるかをアプリケーションチームに委ねたいため
  - 以下のリソースは既に作成されたものを使用する
    - 全サービスで共通
      - サブネット
- バッチのログは、New Relic に出力する
  - アプリケーション部分の同様に、`fluentbit` をサイドカーとしたタスクを作成し、`Data Firehose` 経由で New Relic へログを転送する
    - fluentbit コンテナにはヘルスチェックを設定し、バッチコンテナに fluentbit コンテナとの HEALTHY タイプの依存関係を作成する
    - サイドカーコンテナも同様に `fluentbit` と依存関係を作成する
  - バッチログ転送部分で作成されるものは以下の通り
    - サービス毎に作成するもの
      - Data Firehose
      - S3 バケット
        - 監査ログ集約のため
        - バッチごとにプレフィックスを分けて管理する
    - タスク定義ごとに作成するもの
      - CloudWatch Logs ロググループ
        - fluentbit のログを記録するため
        - ログはアプリ側と同じ 3 ヶ月保管とする
- バッチタスクが別アカウントのリソースにアクセスする方式は、アプリ・インフラチームの運用負荷軽減のためアプリ側と同様の方法を取る
  - バッチはタスク定義ごとにタスクロールを作成しているため、申請フォームにはタスク名とそのタスクでスイッチするときに使用する IAM ロールの ARN を入力してもらう

### 申請フォーム入力内容設計方針

このバッチ基盤を利用する際、事前に以下の値をフォームに入力して貰う必要がある。

- バッチ名
  - ステートマシン名の一部、ファイル名に使用
  - 例: `sample_batch`, `DailyDataInsertionBatch`
  - バッチごとに入力してもらう
- バッチに含まれるタスク名
  - ステートマシンの Task タイプのステート名、プレースホルダーと値を紐づけるために使用
  - 例: `task1`, `DataInsertionTask`
  - バッチごとに入力してもらう
- タスクに含まれるコンテナ名
  - ECR と各コンテナ定義を紐づけるために使用
  - 例: `batchApp`, `side_car`
  - タスクごとに入力してもらう
- バックエンドアプリと通信するかどうか
  - タスクに付与するセキュリティグループを決める際使用
  - バッチごとに入力してもらう
- クロスアカウントアクセスを行うタスクのタスク名とその際使用する IAM ロールの ARN (任意)
  - クロスアカウントアクセスの際使用
- API Gateway を使用するかどうか
  - `boolean` 型で記入。

これらの値は、CDK のパラメータとなり CDK デプロイの際使用される。

変換後のパラメータの例

```typescript
[
  {
    shouldConnectAppContainer: true,
    batchName: 'Sample',
    enableApiTrigger: true,
    taskParam: [
      {
        taskName: 'SampleTask',
        containerNames: ['SampleContainer'],
        // optional
        crossAccountRoleArns: ['arn:aws:iam:...'],
      },
    ],
  },
];
```

これらの値はリソース名にも使用されるため、以下の値のみ使用可能とする

- 英数字
- アンダースコア
- ハイフン

### パラメータストアパラメータ名設計方針

パラメータストアに含めるパラメータは、アプリ側を参考に、他のパラメータと重複しないよう、以下のフォーマットとする。

- 専有リソース
  - `/<環境名>-GEVANNI-<Gevanni サービスID>/batch/<バッチ名>/<リソース名>`
    - 例: `/Dev01-GEVANNI-sample-dev-01-ab/batch/batch1/repository`
    - 例: `/Dev01-GEVANNI-sample-dev-01-ab/batch/batch1/repository/task/task1/container/batch`
- 共有リソース
  - `/<環境名>-GEVANNI-common/batch/<リソース名>`
    - 例: `/Dev01-GEVANNI-common/batch/lambda`

リソース名の後は、パラメータストアのパラメータ名に含めることができる任意の値を使用して良いこととする。

### バッチ呼び出し部分設計方針

EventBridge Scheduler・API Gateway をバッチ呼び出し部分とし、この部分の設計方針を示す。

- EventBridge Scheduler・API Gateway のリソースはパイプライン内でスクリプトを実行し作成する。
- **EventBridge Scheduler**
  - EventBridge Scheduler で使用する以下のリソースは CDK で管理する
    - EventBridge Scheduler 用 IAM Role (全サービス共通)
      - この IAM Role は重複イベント回避用 Lambda に対して `lambda:InvokeFuntion` の実行が許可されている
  - EventBridge Scheduler は以下の値を重複イベント回避用 Lambda の Input に渡し、その値を利用してステートマシンを実行する
    - EventBridge Scheduler の実行 ID
    - 実行するステートマシンの ARN
    - ステートマシンの一番最初のタスクに与えるコマンド (任意)
- **API Gateway**
  - API での呼び出しで使用する以下のリソースは CDK で管理する (なお、作成されるリソースはすべて専有リソースとなる)
      - API Gateway 本体
      - API Gateway のアクセスログの送信先 CloudWatch Logs ロググループ
      - Step Functions 実行用 Lambda
      - Lambda のロググループ
      - Lambda が使用する IAM Role
  - また、API での呼び出しで使用する以下のリソースはパイプラインで作成する
      - API Gateway のパス・メソッド・メソッドリクエスト・統合・ステージ・デプロイ
          - 上記のリソースは、アプリリポジトリに配置する設定ファイルから、API に使用するパスを読み取って作成する
          - 上記のリソースを CDK で作成する場合は、申請フォームに、API のパスを指定する必要があり、アプリチームが設定を容易に確認できないためパイプラインで作成とする
      - ここで作成するメソッドはリクエストボディに以下のパラメータを要求する
          - 実行したいバッチ名
          - バッチコンテナにわたす上書きコマンド (任意で指定)
- 各 API Gateway メソッドに設けられている IAM 認証で許可される IAM はアプリチーム用ロールと、バックエンドコンテナのタスクロールとする
  - アプリチームの動作確認、バックエンドコンテナからのリクエストでのバッチ実行のため
- API Gateway のルートメソッドに、モック統合タイプの GET メソッドを追加する
  - アプリチームやインフラチームの API Gateway の呼び出し動作確認のため
  - このメソッドは特にパラメータを要求せず、API Gateway で呼び出し可能なバッチのバッチ名を返却する

### パイプライン設計方針

前述のバッチ基盤・バッチ呼び出し部分にて、パイプラインで作成するリソースを作成するパイプラインの設計方針を示す。

- このパイプラインは、CodePipeline で作成され、以下のステージを持つ
  - ソースステージ
  - デプロイステージ
- パイプライン本体や、各ステージで使用する AWS サービスは CDK で作成する
- バッチのイメージや各種設定ファイルはアプリケーションリポジトリ内の GitHub Actions を利用してビルド・プッシュ・S3 バケットへアップロードを行う
  - アプリケーション部分と仕様を統一して、運用負荷を軽減するため

#### ソースステージ設計方針

基本的には、アプリケーション部分のパイプラインを踏襲し、仕様を統一化して、運用負荷軽減を狙う。

- ソースステージでは、S3 バケットを指定し、当該バケットにファイルが配置されるとデプロイステージに移行する
- ソースステージにアップロードされるファイルは、以下のファイルを zip 圧縮したものとする
  - ステートマシン定義ファイル
  - タスク定義ファイル
  - バッチ呼び出し定義ファイル
  - リソースデプロイスクリプト
- 上記ファイルのうち、パイプライン内で実行するスクリプトは、全サービスで共通で使用する S3 バケットに配置する
  - スクリプトはインフラチームの保守範囲であり、全サービスで同一のものを使用するため
  - 当該 S3 バケットには、本リポジトリの GitHub Actions 経由でスクリプトをアップロードすることとする
- アプリケーションリポジトリに配置する GitHub Actions にて、上記の共通 S3 バケットから、スクリプトをダウンロードし、zip 圧縮してソースステージの S3 バケットに配置する

#### デプロイステージ設計方針

- デプロイステージでは、CodeBuild が動作し、スクリプトを実行して以下のリソースを作成する
  - ECS タスク定義
  - Step Functions ステートマシン
  - EventBridge Scheduler
  - API Gateway に関するリソース
    - パス
    - メソッド
        - この API は単純にバッチを実行するために使用されるため、使用する HTTP メソッドは POST 固定とする
    - 統合
        - 前述のとおり、Lambda を使用するため [Lambda プロキシ統合](https://docs.aws.amazon.com/ja_jp/apigateway/latest/developerguide/set-up-lambda-proxy-integrations.html) を使用する
    - メソッドリクエスト
    - ステージ
    - デプロイ
- 実行するスクリプトは python で記述する
  - システム内で使用する言語を統一し、運用負荷を軽減するため
- スクリプトで使用する環境変数は、CodeBuild の環境変数に設定しておき、スクリプト内で参照する
- スクリプトでは、ソースバケット内の定義ファイルから各種リソースを作成する
  - 定義ファイルは雛形となっており、スクリプト内で値を埋めることで利用する

### バッチ定義ファイル設計方針

以下の 4 つのファイルをバッチ定義ファイルとし、以下に設計方針を記す。  
※ コンテナと ECR の対応表ファイルに関する設計は後述  
コンテナと ECR の対応表ファイル以外のファイルはアプリチームが管理するリポジトリに配置される。

- バッチ呼び出し定義ファイル
- タスク定義ファイル
- ステートマシン定義ファイル
- コンテナと ECR の対応表ファイル (これはアプリチームリポジトリ管理外)

これらの定義ファイルは、以下の理由で 1 つのファイルにマージせず分割して管理を行う

- 1 ファイルの記述量をなるべく減らし、見通しを良くするため
  - 特にステートマシン定義ファイルは複雑なものになるほど見通しが悪くなる
- アプリ側と同様のフォーマットのタスク定義ファイルを使用し、アプリチームの使いやすさを重視するため

### バッチ呼び出し定義ファイル設計方針

バッチ呼び出し定義ファイルを以下の方針で設計する。

- バッチ呼び出し定義ファイルには以下の情報を含める
  - ステートマシンを実行する API Gateway もしくは、EventBridge Scheduler を作成するために必要な情報
    - EventBridge Scheduler の場合
      - cron 式 (cron 式を用いてバッチを定期実行させるため)
      - 作成されたスケジュールの状態
        - スケジュールが不要になったときは、無効に設定してもらいスケジュールの削除はバッチの削除とタイミングを合わせる方針とする
    - API Gateway の場合
      - バッチの実行に使用する API のパス名
      - API Gateway を使用する場合は、ステートマシンの入力に渡す値は設定できない方針とする
          - ステートマシンの入力に渡す値は、現状コンテナのコマンドの上書きのためにしか使用されておらず、設定ファイルを通して上書きコマンドを指定するよりも、API のリクエストボディに含めるほうが柔軟性があがるため
  - ステートマシンの入力に渡す値
    - ECS の `RunTask` API に渡す値を含め、アプリチームにバッチの処理内容を柔軟に制御してもらうため
    - アプリチームの利便性向上のため、この値はバッチを起動させるリソースによらず、共通のフォーマットを利用する

### Step Functions 実行用 Lambda 設計方針

ここでは、Step Functions を実行する、以下の2つの Lambda の設計方針について記載する。

- 重複回避用 Lambda
- API Gateway から実行される Step Functions 実行用 Lambda

#### それぞれに共通する方針

- ランタイムは Python とする
    - パイプライン設計方針にも記載しているが、システム内で使用する言語を統一し、運用負荷を軽減するため
- 内部では、`StartExecution` API を実行し Step Functions を起動する
    - Step Functions に渡される入力は、後述の方法によって Lambda に渡され、`StartExecution` API に渡す

#### それぞれで異なる方針

- 重複回避用 Lambda
    - 実行する Step Functions の ARN さえわかれば、どの Step Functions も実行できるため、共有リソースとする
    - EventBridge Scheduler から実行される
        - このとき、Step Functions に渡される入力の値を Lambda の入力に渡す
    - Step Functions の重複実行を避けるため、`StartExecution` の `Name` パラメータに EventBridge Scheduler が発行するイベント ID を指定する
- API Gateway から実行される Step Functions 実行用 Lambda
    - 各 gevanni サービスごとに作成する API Gateway が統合として使用する Lambda であるため、専有リソースとする
    - 前述の通り、API Gateway から実行される
        - このとき、Step Functions に渡される入力の値は、リクエストのボディに含まれる
    - EventBridge Scheduler とは違って、アプリケーション側の不備以外で同時に複数回バッチが実行されることはないため、他にパラメータを指定せず、`StartExecution` API を実行する

### タスク定義ファイル設計方針

アプリ側と殆ど変わらないが、以下の部分が異なるためここで記述する。

- コンテナ名をアプリチームに記入してもらうこととする
  - サイドカーコンテナをサポートする関係で、コンテナ定義が増える可能性があり、任意のコンテナ名を使用してもらうため
- コンテナ定義内の以下の部分には決まったフォーマットのプレースホルダーを記述する
  - image
    - フォーマット: `<英数字とアンダースコアからなる任意の値>`
      - バッチデプロイパイプラインで、値を埋める際、このフォーマットを使用するため
        - 例: `image0`
  - cpu
    - フォーマット: `タスク名_cpu`
    - 例: `task1_cpu`
  - taskRoleArn
    - フォーマット: `タスク名_taskRoleArn`
    - 例: `task1_taskRoleArn`
  - memory
    - フォーマット: `タスク名_memory`
    - 例: `task1_memory`
- 環境変数の追加方法については、アプリ・インフラチームの運用負荷軽減のためアプリ側で使用している方法と同様の方法をとる
  - アプリ側と異なる点は以下
    - シークレットはタスク定義ごとに作成する
      - コンテナごとに設定される環境変数は異なるが、ある程度まとまりを持って管理したほうが管理しやすいため

### ステートマシン定義ファイル設計方針

以下にステートマシン定義ファイルの設計方針を記述する。

- フォーマットは json とする
  - タスク定義は json で管理しており、フォーマットを合わせるため
  - 要望が多ければ yaml もサポートする
- いくつかのパターンを用意し、アプリチームに利用してもらう
  - ASL の学習コストを軽減するため
- タスク定義を指定する部分では、以下のフォーマットのプレースホルダーを記述する
  - フォーマット: `<タスク名>_task_def`
    - 例: `task1_task_def`

### バッチ環境ファイル設計方針

アプリリポジトリに配置する、開発、検証、本番環境用に異なるパラメータを管理するファイルをバッチ環境ファイルとし、以下に設計方針を示す。

- フォーマットは toml とする
  - 理由は以下
    - アプリで使用している ini ファイルでは、階層構造をとることができないため
    - 書き方が ini ファイルに近いため
    - python で標準でパーサーが提供されているため
- 環境ファイルには以下の値を含める
  - コンテナのビルドに使用するパラメータ
    - バッチのビルドで使用されるイメージはアプリチームが任意の場所に配置するため、ビルド時のコンテキストを得るために使用する
  - Gevanni の環境名とサービスの ID
    - ビルドスクリプトでパラメータストアを参照する際使用するため
  - タスク定義中に記載されるプレースホルダー
    - デプロイスクリプトで参照するため
  - タスクのスペック

## デプロイスクリプト設計方針

CodeBuild 内で使用される python スクリプトをデプロイスクリプトとし、以下に設計方針を記載する。

- デプロイスクリプトでは以下の操作を行う
  - リソースの作成
    - 作成されるリソースには専用のタグを付与する
      - 削除時に参照するため
      - 付与する値は、Gevanni で使用するサービス名と、参照しているバッチ名とする
  - リソースの更新
    - 対象のリソースに変更がなくても更新の処理を行う
      - 定義ファイルの内容と現在作成されているリソースを比較し、変更されているかを確認する処理を実装すると複雑になるため
- ファイル内で必要な値は、CodeBuild 環境変数から取得する
  - ecspresso と同様の方法を使用して管理コストを軽減するため
- スクリプトは本リポジトリのアップロード用ワークフロー経由でバッチ用の共有バケットにアップロードする
  - バッチの機能追加に伴いデプロイスクリプトも変更することが考えられるため、すべての環境で共通で参照できるように共有バケットにアップロードする

### リソース削除設計方針

リソースの削除はアプリ側と同様に、申請フォーム経由で行う。  
バッチパイプラインで作成したリソースは、申請フォームから実行される削除パイプラインの中で削除される。
削除は以下の方針に従って行うこととする。

- CDK パラメータファイルに記載のバッチ名がタグについているリソースをすべて取得
  - ResourceGroupTaggingAPI の `TagResource` API を使用する想定
- そのリソースについて、削除 API を実行
  - タスク定義以外のリソースは削除とする
  - タスク定義は、他のバッチで使用されている場合があるためタスク定義のタグを確認し、他のバッチで利用されていない場合は登録解除とする
    - ecspresso と同様に登録解除するだけで削除は行わない

### バッチアプリビルドワークフロー設計

以下では、アプリチームリポジトリに保管する、バッチアプリビルドワークフローの設計について記載する。  
このワークフローはアプリチームリポジトリで実行される GitHub Actions を指す。  
基本的には、アプリチーム、インフラチームの運用負荷軽減のため、アプリ側で既に実装されているものを踏襲する。

#### バッチアプリビルドワークフロー要件

- サイドカーをサポートし、一つのタスク定義内に複数のコンテナが存在する

#### 設計方針

- バッチアプリビルドワークフロー内で ECR とコンテナの対応関係を作成する
  - ECR は CDK で作成するため、申請フォーム経由で使用するコンテナ数を取得し、その分作成する
  - ECR、タスク定義のコード管理方法が違う関係上、対応関係が作成できるタイミングはビルドワークフローか、バッチのデプロイパイプラインのみとなる
    - バッチアプリビルドワークフローのほうがバッチのデプロイパイプラインよりも先に実行されるため、この時点で対応関係を作成する
- 対応関係は以下の形式の json ファイルで表現する

  ```json
  {
    // 定義
    "<タスク定義名>_<コンテナ名>": "<ECR リポジトリの URI>",
    // サンプル
    "batch1-task1-task_batchApp": "012345678912.dkr.ecr.ap-northeast-1.amazonaws.com/batch1:a1b2c3d"
  }
  ```

- このファイルとバッチ定義ファイル、環境ファイル、共有リソースの S3 バケットにあるバッチデプロイスクリプトを zip ファイルにして、パイプラインを起動するバケットにアップロードする
