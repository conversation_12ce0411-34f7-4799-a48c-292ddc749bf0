# サービス専有リソースのパラメータファイル管理方法設計

## 概要

- Gevanni は [BLEA コード](https://github.com/aws-samples/baseline-environment-on-aws) をカスタムし派生した [ベースコード](https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/) を基に、さらに集約基盤用にカスタマイズをかけた CDK コードである。
- Gevanni は内製案件の集約基盤として複数サービスのインフラ環境をデプロイするため、環境単位で作成する AWS リソースとは別に、各サービス毎に作成する AWS リソースが存在する。
  - サービス毎にデプロイする AWS リソースの設定項目を記述したパラメーターファイルを作成し、パラメーターファイルを指定してデプロイする。
- サービス専有リソースのパラメータファイルの管理・指定方法と、CDK ベースコードからの変更内容について記載する。

## 構成図

**パラメーター指定**

![](/docs/images/params-architecture.dio.svg)

**CDK コードのカスタマイズ**

![](/docs/images/cdk-template-history.dio.svg)

### リソース一覧

- BLEA コード
  - [baseline-environment-on-aws](https://github.com/aws-samples/baseline-environment-on-aws)
  - ベースコード、Gevanni コードの派生元となった、AWS 管理の AWS のセキュリティのベストプラクティスを実装した環境を迅速に実現するためのテンプレート。
- ベースコード
  - [csys-infra-baseline-environment-on-aws-change-homemade](https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/)
  - Gevanni コード派生元となったマイナビ用にカスタマイズした汎用テンプレート。
- Gevanni コード
  - [csys-infra-gevanni-infra](https://github.com/mynavi-group/csys-infra-gevanni-infra/)
  - CodePipeline
    - 申請フォームをトリガーに専有リソースをデプロイする CI/CD パイプライン。詳細は [DedicatedDeploymentFlow.md](/docs/02_Detaildesign/DedicatedDeploymentFlow.md) に記載。
  - [csys-infra-gevanni-infra/usecases/services/params/*](/usecases/services/params/)
    - 開発環境、サービス毎の環境パラメーターファイルを格納する。

## 要件

- 環境単位で作成する共有リソースと、各サービス単位で作成する専有リソースが存在する。
  - 共有リソースの例：VPC、NAT Gateway
  - 専有リソースの例：ALB、WAF、ECS クラスター、ECS サービス、DB
- 専有リソース作成時には、サービスごとに異なるパラメータを設定する必要がある。
  - 例：コスト配分タグ
- サービス専有リソースを管理するスタックのライフサイクル
  - (1) 基本
    - サービスごとに異なる時期に、作成/更新/削除
    - サービス数が増えてきたら、複数サービスの並行更新もありうる。
  - (2) 例外(半年に 1 回くらい)
    - 専有リソースの共通設定の変更を、既存サービスに一括で適用する。
    - stg
      - 同じ日の同じ時間帯に一括で変更
    - prod
      - いくつかのグループに分け、何日かに分けて変更
- ライフサイクル 1 のため、cdk deploy はサービス単位で実行できる必要がある。
- ライフサイクル 2 については、複数サービスの cdk deploy を一括で行うツールを別途作成する。
  - こちらの対応は 2025 年 4 月以降に対応。

## サービス ID の命名規則

- 全環境で一意の ID
  - 「prod01/stg01 で対になるサービスは ID が同一」も検討したが、それだと stg01 に検証環境が 2 つ以上存在するケースに対応できない。
  - 既存サービスで、用途の異なる複数の検証環境が存在するサービスがある。
- 命名規則
  - <利用者が決められる文字列>-<環境>-<ランダムな英小文字\*2>
- <利用者が決められる文字列>
  - 使用可能文字: 英小文字数字ハイフン
  - 新規サービス申請時に、利用者が自身にとってわかりやすい文字列を設定
- <環境>
  - prod01,prod02,...,stg01,stg02,...,dev01,dev02
  - 数字(01/02/03)入りにする理由
    - 異なるサービスの VPC 内通信は同環境内でしかできないので、利用者にも数字付きでどの環境にあるかがわかるようにした方がよい。
    - サービスのアカウント移行(ex:prod01->prod02)の可能性があるため、数字付きの方が新環境/旧環境の区別がしやすい。
- <ランダムな英小文字\*2>
  - <利用者が決められる文字列>が重複する可能性があるため。

## CDK コマンドの実行イメージ

- 「環境名」について
  - 共有リソース/専有リソースともに、数字込みで指定
  - prod01,prod02,...,stg01,stg02,...,dev01,dev02
- 共有リソース(VPC など)のデプロイ

```
cdk deploy --all -c environment=環境名

(ex)
cdk deploy --all -c environment=dev01
```

- 専有リソースのデプロイ

```
cdk deploy --all -c environment=環境名 -c service=サービスID

(ex)
cdk deploy --all -c environment=dev01 -c service=sample01-dev01-ab
```

## CDK ベースコードからの変更内容

### ディレクトリの分割

#### 概要

- 共有リソースと専有リソースを独立してデプロイするため、元の BLEA 構成のように、usecase ごとにディレクトリを分割する。
  - https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/tree/dev/usecases

#### 分割方針

- app ファイル(bin/blea-guest-ecsapp-sample.ts)を分割し、それぞれのディレクトリに配置する。
- lib/params/test ディレクトリも、それぞれのディレクトリに配置する。
- lib 配下の stack ファイルと construct ファイルは、それぞれの app ファイルで使用するファイルのみを配置する。
- params 配下のパラメータファイルも分割する。
- 専有リソースのパラメータファイルは、サービス/環境ごとに作成する。
  - 例：prod01/<サービス ID>.ts、stg01/<サービス ID>.ts

### app ファイル

#### argContext の読み込み

- 変更箇所

```
const argContext = 'environment';
const envKey = app.node.tryGetContext(argContext);
```

- 共有リソース: 変更なし
- 専有リソース: service の読み込みを追加

```
const argContextService = 'service';
const serviceKey = app.node.tryGetContext(argContextService);
```

#### パラメータファイルの読み込み

- 変更箇所

```
const config: IConfig = require('../params/' + envKey);
```

- 共有リソース: 変更なし
- 専有リソース: envKey と serviceKey で読み込み

```
const config: IConfig = require('../params/' + envKey + '/' + serviceKey);
```

#### pjPrefix の変更

- 変更箇所

```
const pjPrefix = config.Env.envName + 'BLEA';
```

- 共有リソース: `BLEA` -> `-GEVANNI-common`

```
config.Env.envName + '-GEVANNI-common';
```

- 専有リソース: pjPrefix に serrviceId を追加

```
config.Env.envName + '-GEVANNI-' + config.Service.serrviceId;
```
