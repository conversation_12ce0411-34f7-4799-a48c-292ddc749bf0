# ECS のデプロイ設計

## 概要

- ECS タスクのデプロイ方法として、Rolling デプロイ方式と BlueGreen デプロイ方式の 2 パターンを用意している。
- デプロイ方式によって、アーキテクチャやデプロイフローが一部異なる。

## 構成

- Rolling デプロイ  
  ![](./../images/ecspresso-deploy.dio.svg)
- BlueGreen デプロイ  
  ![](./../images/bluegreen-deploy.dio.svg)

### リソース一覧

#### Gevanni 本体アカウント

- 共有リソース

  - S3（マスターファイル）

- 専有リソース

  - ECS クラスター
  - ECR
  - S3（ソースバケット）
  - EventBridge(パイプライントリガー)

#### アプリアカウント

- コンテナデプロイパイプライン
  - CodePipeline
  - S3(アーティファクト)
  - EventBridge
    - カスタムイベントパス
  - CodeBuild
  - CodeDeploy(BlueGreen のみ)
- ロールバック
  - EventBridge
  - Lambda

## デプロイ方式の概要

![](./../images/howabout-ecs-deployment.dio.svg)

### Rolling デプロイ

- 既存の ECS タスクを段階的に新しいバージョンに置き換えていく方法

### BlueGreen デプロイ

- 新旧の環境（Blue と Green）を並行して用意し、切り替えによってデプロイを行う方式
- 新バージョンのタスクへのトラフィックを切り替える前に、テストが可能

## デプロイ方法ごとの実装内容

### 共通

- パイプラインとトリガーの S3 は別アカウントに配置しており、EventBridge のターゲットに別アカウントの S3 が指定できないため、クロスアカウントがサポートされているカスタムイベントバス経由でパイプラインを起動する。

### Rolling デプロイ

- ecspresso を使用して、ECS サービスと ECS タスクの作成・更新を行う
  - パイプラインのステージ
    1. S3
    2. CodeBuild（ecspresso deploy 実行・config.py 実行）

#### デプロイフロー

1.  Gevanni 本体アカウントに AWS リソースをデプロイする。

    - crossAccessAccountId：アプリアカウントのアカウント ID
    - crossAccessEnvName：クロスアカウント先の環境名
    - crossAccessPjPrefix：アプリアカウントの pjPrefix 名

    **csys-infra-gevanni-infra/usecases/services/params/stg01/sample01-stg01-ab.ts**

    ```typescript
    export const EcspressoFrontDeployPipelineParam: inf.ICrossAccountDeployPipelineParam = {
      crossAccessAccountId: '************',
      crossAccessEnvName: 'Stg',
      crossAccessPjPrefix: 'gevanni-cf-sample',
    };
    ```

2.  デプロイするパイプラインの数だけオブジェクトを追加し、アプリアカウントに Rolling デプロイ用パイプラインをデプロイする。

    - crossAccessAccountId：Gevanni 本体アカウント ID
    - appName：ECS サービス名
    - bucketKey：`image_front.zip`または`image_backend.zip`
    - crossAccessAccountEnvName：Gevanni 本体アカウント の CommonPrefix 名※大文字小文字に注意
    - crossAccessAccountService：サービス ID
    - functionLogLevel：ロールバック時の Lambda ログ出力レベル（参照：https://docs.python.org/3/library/logging.html#logging-levels）
    - pipelineExecutionMode：パイプラインの実行モード
      - QUEUED：実行中の処理が終わるまで、次の実行は待機
      - SUPERSEDED：新しい実行が来たら、古い実行はキャンセル

    **csys-infra-gevanni-cf-sample/params/stage.ts**

    ```typescript
    export const EcspressoPipelinesParam: inf.IEcspressoPipelinesParam = {
      crossAccessAccountId: '************',
      pipelineParams: [
        {
          appName: 'EcsAppBg',
          bucketKey: 'image_bg_front.zip',
          crossAccessAccountEnvName: 'Stg01-MEMBER-NAME-PREFIX',
          crossAccessAccountService: 'sample01-stg01-ab',
          functionLogLevel: 'DEBUG',
          pipelineExecutionMode: pipeline.ExecutionMode.QUEUED,
        },
      ],
    };
    ```

3.  これ以降はアプリチームのみで、コンテナの作成・更新が可能となる。

### BlueGreen デプロイ

- ECS サービスと ECS タスクの作成は ecspresso が実施し、それ以降の更新は ecspresso が CodeDeploy を呼び出す形で、CodeDeploy が行う。
- CodeDeploy によるデプロイは、一部コンソールでの手動操作が必要となるが、アプリチームは Gevanni 本体アカウントのコンソールにアクセスできない。
- そのため、アプリアカウント上のパイプラインからクロスアカウントアクセスで CodeDeploy に対して CLI 実行することで、GUI での操作を実現する。
  - デフォルトでは、CodeDeploy によるデプロイが完了するまで次のステージに進まないため、ecspresso deploy にオプション（`--no-wait`）を付与することで、CodeDeploy の呼び出しが完了したら、次ステージに遷移させる。
  - パイプラインのステージ
    1. S3
    2. CodeBuild（ecspresso deploy 実行）
       - 初回実行は ECS サービスとタスクを作成し、終了
       - 更新時は CodeDeploy を呼び出し、次のステージへ進む
    3. 手動承認
    4. CodeBuild（トラフィック切替：CodeDeploy CLI 実行）
    5. 手動承認
    6. CodeBuild（旧タスク停止：CodeDeploy CLI 実行）
    7. CodeBuild（config.py 実行）
  - 手動承認が「却下」された場合または、パイプライン上の処理が失敗した場合は、CodeDeploy によるロールバックを開始する。

#### デプロイフロー

- AWS リソース間の依存関係の影響で、ECS の初回デプロイ完了後に Gevanni 本体アカウントに CodeDeploy を追加する必要がある。

  ![](./../images/bluegreen-pipeline.dio.svg)

1.  Gevanni 本体アカウントに AWS リソースをデプロイする。

    - crossAccessAccountId：アプリアカウントのアカウント ID
    - crossAccessEnvName：クロスアカウント先の環境名
    - crossAccessPjPrefix：アプリアカウントの pjPrefix 名
    - isEcsServiceDeployed：初回デプロイでは`false`のまま※ECS サービスが存在しない状態で True にすると、エラー発生

    **csys-infra-gevanni-infra/usecases/services/params/stg01/sample01-stg01-ab.ts**

    ```typescript
    export const BlueGreenFrontDeployPipelineParam: inf.IBlueGreenPipelineParam = {
      crossAccessAccountId: '************',
      crossAccessEnvName: 'Stg',
      crossAccessPjPrefix: 'gevanni-cf-sample',
      // CodeDeploy をデプロイするには、ECS Servce をデプロイ後に isEcsServiceDeployed を true に書き換えてデプロイする。
      isEcsServiceDeployed: false,
    };
    ```

2.  デプロイするパイプラインの数だけオブジェクトを追加し、アプリアカウントに BlueGreen デプロイ用パイプラインをデプロイする。

    - crossAccessAccountId：Gevanni 本体アカウント ID
    - appName：ECS サービス名
    - bucketKey：`image_bg_front.zip`または`image_bg_backend.zip`
    - crossAccessAccountEnvName：Gevanni 本体アカウント の CommonPrefix 名※大文字小文字に注意
    - crossAccessAccountService：サービス ID
    - functionLogLevel：ロールバック時の Lambda ログ出力レベル（参照：https://docs.python.org/3/library/logging.html#logging-levels）
    - pipelineExecutionMode：パイプラインの実行モード
      - QUEUED：実行中の処理が終わるまで、次の実行は待機
      - SUPERSEDED：新しい実行が来たら、古い実行はキャンセル

    **csys-infra-gevanni-cf-sample/params/stage.ts**

    ```typescript
    export const BlueGreenPipelinesParam: inf.IBlueGreenPipelinesParam = {
      crossAccessAccountId: '************',
      pipelineParams: [
        {
          appName: 'EcsAppBg',
          bucketKey: 'image_bg_front.zip',
          crossAccessAccountEnvName: 'Stg01-MEMBER-NAME-PREFIX',
          crossAccessAccountService: 'sample01-stg01-ab',
          functionLogLevel: 'DEBUG',
          pipelineExecutionMode: pipeline.ExecutionMode.QUEUED,
        },
      ],
    };
    ```

3.  GitHubAction から、ECS サービスを初回デプロイする。
4.  ECS のデプロイ成功後、`isEcsServiceDeployed`を`true`に書き換えて、ECS スタックをデプロイすることで、Gevanni 本体アカウントに CodeDeploy が作成される。

    **csys-infra-gevanni-infra/usecases/services/params/stg01/sample01-stg01-ab.ts**

    ```typescript
    export const BlueGreenFrontDeployPipelineParam: inf.IBlueGreenFrontDeployPipelineParam = {
      crossAccessAccountId: '************',
      crossAccessEnvName: 'Stg',
      crossAccessPjPrefix: 'gevanni-cf-sample',
      // CodeDeploy をデプロイするには、ECS Servce をデプロイ後に isEcsServiceDeployed を true に書き換えてデプロイする。
      isEcsServiceDeployed: true, // このタイミングでtrueに変更する
    };
    ```

5.  CodeDeploy の作成が完了すると、これ以降はアプリチームのみで、コンテナの更新が可能となる。
