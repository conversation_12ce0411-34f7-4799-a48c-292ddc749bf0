# 費用振替の設計方針

## 概要

- 事業部門 (サービス利用者) への費用振替をインフラチームが月に一回の頻度で行う。
- AWS リソースにタグを付与し、タグを識別子として費用振替を行う。
- AWS マネジメントコンソールにはクラスメソッドの割引金額が反映されていないため、クラスメソッドポータルから請求情報を確認する。

## 構成

![](/docs/images/billing-tag-architecture.dio.svg)

### リソース一覧

- クラスメソッドポータル
  - 請求代行・請求 PJ 閲覧。
- Gevanni AWS リソース
  - Gevanni PJ において費用発生する AWS リソース。
- AWS CDK
  - AWS リソースのデプロイ。
  - 請求タグ付与。
- python デプロイスクリプト
  - 踏み台・バッチ ECS コンテナデプロイ。
  - 請求タグ付与。

## 要件

- 請求タグは専有リソースに設定する。
  - 基本的には CDK コードによってタグ付けを制御する。
  - CDK 外で作成するリソースにも請求タグを付与する。
- 専有リソースの中でも、インフラチームが利用するリソースについては請求タグを設定しない。対象は以下の通り。
  - DEV 環境で作成するリソース
    - DEV 環境で作成するリソースはインフラチームが Gevanni の開発のために使用するリソースのため、請求タグを設定しない。
  - ダッシュボード
    - ダッシュボードの利用はインフラチームが各サービスの状況を横断的に確認するリソースのため、請求タグを設定しない。

## 請求タグの命名規則

- クラスメソッドポータルから請求情報を確認するため、クラスメソッドが用意した請求タグ `CmBillingGroup` を利用する。

- KEY: `CmBillingGroup`
- VALUE: `pjPrefix`(<環境名>-GEVANNI-<利用者が決められる文字列>-<環境名>-<ランダムな英小文字>)

## 請求タグの付与方法

- 請求タグの付与方法は大まかに 2 パターンある。
- CDK 管理下リソース
  - service 配下のリソースに請求タグを設定するように CDK コードで制御。
- CDK 管理外リソース
  - ECS サービス/タスク は CDK で管理していないため、サービス定義またはタスク定義ファイルからタグを伝搬させる。
    - フロントエンド・バックエンド
      - デプロイパイプラインの環境変数に請求タグを設定。
      - ecspresso サービス定義ファイルに環境変数から請求タグを設定するように設定する。
    - 踏み台・バッチ
      - ECS サービスとしてではなく、ECS タスク単体でデプロイする必要のあるコンテナは python スクリプトによってデプロイされる。
      - タスク定義に請求タグを設定し、`--propagate-tags` オプションによってタグを伝搬させる。
  - バッチリソースの Step Functions や EventBridge などの python スクリプトでデプロイするリソースはスクリプト上でタグを付与する。

## AWS 利用料金の費用振替

- インフラチームが識別子を元に、月に一回の頻度で費用振替を行う。
- インフラチームとアプリチームで AWS 利用料金を確認する方法とタイミングは異なる。
  - インフラチーム：
    - クラスメソッドポータルの Gevanni の請求プロジェクトから AWS 利用料金をリアルタイムで確認可能。
  - アプリチーム：
    - インフラチームによる費用振替のタイミングで AWS 利用料金を確認する。
    - クラスメソッドポータルの Gevanni の請求プロジェクトのアクセス権限は付与しない。
    - 基本的にはアプリチームはクラスメソッドの請求プロジェクトへのアクセス権限を与えない方針だが、リアルタイムで AWS 利用料金を閲覧したいという要望が出た場合には、各プロジェクトの責任者のみに請求プロジェクト閲覧権限を与える運用とする。
