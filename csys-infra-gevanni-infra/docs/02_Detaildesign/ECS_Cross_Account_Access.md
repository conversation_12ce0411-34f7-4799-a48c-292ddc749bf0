# Fargate Task Cross Account Access 設計

## 概要

- アプリケーションの仕様次第では、アプリチーム AWS アカウント内リソース(CloudFront や S3 バケットなど)に、Gevanni 本体 AWS アカウントの Fargate タスクからクロスアカウントアクセスする必要が生じる。

## 構成

![クロスアカウントアクセスイメージ](/docs/images/Fargate-Task-XAA.png)

### リソース一覧

#### 専有リソース

- IAM ロール

## 要件

### アクセス元の制御

#### Fargate タスクのクロスアカウント権限は、ECS サービス単位で制御する。

- 同一サービスでも、フロントエンド/バックエンドでは、権限を分けられるようにする。
- バッチコンテナについては、別途追記する。
- 踏み台コンテナは、クロスアカウントアクセスの対象外とする。

### アクセス先の制御

#### AWS アカウントだけでなく、リソース単位でも制限をかける。

- 例) S3
- アクセス可能先は、特定の S3 バケットのみに限定

#### AWS アカウント ID やリソース Arn は、専有リソースのパラメータファイルで管理する。

### 対象サービス

#### アプリチームからの要望しだいで、10 月以降も順次追加

- S3
  - フロントエンド用
  - バックエンド用
- CloudFront
- Cognito
- OpenSearch

### 想定ケースと必要な権限

#### S3 バケット(フロントエンド用)

- **想定ケース**
  - Web 管理画面からページを動的に作成する機能がある。
  - ページには画像を埋め込むことができ、その画像は管理画面から作成/更新/削除ができる。
  - アップロードされた画像は S3 に保存され、ユーザーアクセス時には CloudFront 経由で配信される。
- **必要な権限**
  - オブジェクトの作成/更新/削除

#### S3 バケット(バックエンド用)

- **想定ケース**
  - CDN 経由での Web アクセスはないが、Fargate からアクセスする必要のあるファイルを S3 上で管理する。
    - 例 1) 他システムからのインポートファイル
    - 例 2) 他システムへのエクスポートファイル
- **必要な権限**
  - オブジェクトの作成/更新/削除

#### CloudFront

- **想定ケース**
  - マイナビ社員(非システム職)用管理画面に以下の機能が存在する。
    - キャッシュクリア機能
    - 一時 URL 発行機能
- **必要な権限**
  - キャッシュクリア
  - 署名付き URL 発行

#### Cognito

- **想定ケース**
  - Cognito を用いた認証機能を実装する。
- **必要な権限**
  - 要検証

#### OpenSearch

- **想定ケース**
  - OpenSearch を用いた検索機能を実装する。
- **必要な権限**
  - OpenSearch 内のデータに対する作成/更新/削除

## 実装方法

対象サービスは全てアイデンティティベースポリシーに対応しているため、AssumeRole でクロスアカウントアクセスを実装する。
※ 注意点: あるロールの認証情報を使って別のロールを引き受けることを「ロールチェーン（role chaining）」と呼ぶ。ロールチェーンを使用した場合、新しい認証情報の有効期間は最大で1時間に制限される。したがって、ECS Fargate タスクでロールを引き受ける場合、その期間は1時間以内でなければならない。

### 手順

1.  【Gevanni インフラチームメンバー 対応】接続先 AWS アカウントで フロントエンドコンテナとバックエンドコンテナに許可する IAM ロールを作成
2.  【プロジェクトインフラメンバー 対応】接続先 AWS アカウントの IAM ロールを引き受ける IAM ポリシー作成と コンテナへ IAM ロールのアタッチ
3.  【アプリチーム 対応】（アプリケーション実行時）一時認証情報の取得

#### 1. 【Gevanni インフラチームメンバー 対応】接続先 AWS アカウントで フロントエンドコンテナとバックエンドコンテナに許可する IAM ロールを作成

- Gevanni 集約基盤の運用インフラチームのメンバーが対応を行う。
- 接続先アカウント側での設定

  - Fargate タスクがクロスアカウントで接続する IAM ロールを作成する。
  - 接続先 AWS アカウントで、下記のような信頼ポリシーを持つ IAM ロールを作成

  **信頼ポリシーの例**

  ```json
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Principal": {
          "AWS": "arn:aws:iam::<GevanniのAWSアカウントID>:root"
        },
        "Action": "sts:AssumeRole",
        "Condition": {}
      }
    ]
  }
  ```

  - 権限は Fargate タスクが接続するのに必要な権限を追加する。

#### 2. 【プロジェクトインフラメンバー 対応】接続先 AWS アカウントの IAM ロールを引き受ける IAM ポリシー作成と コンテナへ IAM ロールのアタッチ

- Gevanni にのせるプロジェクトへアサインされたインフラチームメンバーが対応を行う。
- Gevanni 側での設定

  - Gevanni 側では、接続先 IAM ロールを引き受けるポリシーを ECS タスクロールにアタッチするようにパラメーターファイルを改修する。
    - パラメーターの追記箇所
      ```typescript
      export const EcsFrontTaskRole: inf.IEcsTaskRoleParam = {
        policyStatements: [],
        managedPolicy: [],
        crossAccountRoleArns: [ << "接続先 IAM ロール ARN" >> ],
      };
      ```
  - フロントエンドとバックエンドそれぞれの`crossAccountRoleArns`パラメータに接続先アカウントの引き受ける IAM ロール ARN を設定する。

  **作成される IAM ポリシーの例**

  ```json
  {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Effect": "Allow",
        "Action": "sts:AssumeRole",
        "Resource": "<接続先IAMロールArn>"
      }
    ]
  }
  ```

### 3. （アプリケーション実行時）一時認証情報を取得

- アプリチームメンバーが対応を行う。
- アプリケーションで設定

  - クロスアカウント先リソースへのアクセス時に一時認証情報の取得が必要

  **実装の例**

  ```python
  def list_buckets_from_assumed_role(
      assume_role_arn, session_name, sts_client
  ):
      response = sts_client.assume_role(
          RoleArn=assume_role_arn,
          RoleSessionName=session_name,
      )
      temp_credentials = response["Credentials"]
      print(f"Assumed role {assume_role_arn} and got temporary credentials.")

      s3_resource = boto3.resource(
          "s3",
          aws_access_key_id=temp_credentials["AccessKeyId"],
          aws_secret_access_key=temp_credentials["SecretAccessKey"],
          aws_session_token=temp_credentials["SessionToken"],
      )

      print(f"Listing buckets for the assumed role's account:")
      for bucket in s3_resource.buckets.all():
          print(bucket.name)
  ```
