name: upload image to ConfMasterBucket

on:
  pull_request_target:
    branches: ["develop"]
    paths: ["container/image-frontend/**", "container/image-backend/**"]
    types:
      - closed

env:
  # parameter is case-sensitive.
  environment: Stg01

permissions:
  id-token: write
  contents: read

jobs:
  build:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{vars.confmasterbucketrole}}
          role-session-name: GitHubActions
          aws-region: ap-northeast-1

      - name: Get ConfMasterBucket
        run: |
          confmasterbucket=$(aws ssm get-parameter --name "/${{env.environment}}-GEVANNI-common/ConfMasterBucketName" --query "Parameter.Value" | tr -d """)
          echo "ConfMasterBucket is ${confmasterbucket}"
          echo "confmasterbucket=$confmasterbucket" >> $GITHUB_ENV

      - name: (frontend) Zip conf file & Upload image.zip
        run: |
          cd container/image-frontend
          chmod 700 config.py
          zip image_front.zip *
          aws s3 cp image_front.zip s3://$confmasterbucket

      - name: (backend) Zip conf file & Upload image.zip
        run: |
          cd container/image-backend
          chmod 700 config.py
          zip image_backend.zip *
          aws s3 cp image_backend.zip s3://$confmasterbucket
