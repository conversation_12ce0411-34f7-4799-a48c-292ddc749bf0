import boto3
import json

client = boto3.client('cloudformation')


def on_event(event, context):
    print(event)
    request_type = event['RequestType']
    if request_type == 'Create':
        return on_create(event)
    if request_type == 'Update':
        return on_update(event)
    if request_type == 'Delete':
        return on_delete(event)
    return event


def on_create(event):
    props = event["ResourceProperties"]
    print("create new resource with props %s" % props)
    physical_id = event.get("PhysicalResourceId", "default_physical_id")
    set_stack_policy(props)
    return {'PhysicalResourceId': physical_id}


def on_update(event):
    physical_id = event["PhysicalResourceId"]
    props = event["ResourceProperties"]
    print("update resource %s with props %s" % (physical_id, props))
    set_stack_policy(props)
    return {'PhysicalResourceId': physical_id}


def on_delete(event):
    physical_id = event["PhysicalResourceId"]
    print("delete resource %s" % physical_id)
    return {'PhysicalResourceId': physical_id}


def set_stack_policy(props):
    # Define stack policy for enabling protection
    enable_protection_policy = json.dumps({
      "Statement": [
        {
          "Effect": "Allow",
          "Action": "Update:*",
          "Principal": "*",
          "Resource": "*"
        },
        # Deny delete and replace for EC2 instances and RDS DB Cluster
        {
          "Effect": "Deny",
          "Action": ["Update:Delete", "Update:Replace"],
          "Principal": "*",
          "Resource": "*",
          "Condition": {
            "StringEquals": {
              "ResourceType": [
                "AWS::EC2::Instance",
                "AWS::RDS::DBCluster",
                "AWS::ECS::Cluster",
                "AWS::EFS::FileSystem",
                "AWS::ElastiCache::ReplicationGroup",
                "AWS::ElastiCache::ServerlessCache"
              ]
            }
          }
        },
        # Deny delete for RDS DB instances
        { 
          "Effect": "Deny",
          "Action": ["Update:Delete"],
          "Principal": "*",
          "Resource": "*",
          "Condition": {
            "StringEquals": {
              "ResourceType": [
                "AWS::RDS::DBInstance",
              ]
            }
          }
        }
      ]
    })

    # Define stack policy for disabling protection
    disable_protection_policy = json.dumps({
      "Statement": [
        {
          "Effect": "Allow",
          "Action": "Update:*",
          "Principal": "*",
          "Resource": "*"
        }
      ]
    })

    # Apply stack policies
    apply_stack_policy(props.get('StackEnableProtection', ''), enable_protection_policy)
    apply_stack_policy(props.get('StackDisableProtection', ''), disable_protection_policy)

def apply_stack_policy(stack_list, stack_policy_body):
  for stack in stack_list.split(','):
    stack_name = stack.strip()
    if not stack_name:
      continue
    print("set stack policy for stack %s" % stack_name)
    print("set  policy for stack %s" % stack_policy_body)
    response = client.set_stack_policy(
      StackName=stack_name,
      StackPolicyBody=stack_policy_body
    )
    print("set stack policy response %s" % response)
