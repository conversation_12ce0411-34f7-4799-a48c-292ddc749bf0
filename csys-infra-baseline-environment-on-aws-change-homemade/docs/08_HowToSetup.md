# HowToSetup

ここでは各種設定の HowTo について記載する。

- [Git の pre-commit hook のセットアップ](#Git-の-pre-commit-hook-のセットアップ)
- [ACM の作成と登録申請](#acm-の作成と登録申請)
- [AWSChatbot 用に Slack を設定する](#AWSChatbot-用に-Slack-を設定する)

---

## Git の pre-commit hook のセットアップ

### simple-git-hooks のセットアップ

BLEA ソースコードのトップディレクトリで以下のコマンドを実行。

```sh
# BLEAのトップディレクトリで
npx simple-git-hooks
```

Git の hook が設定され(.git/hooks/pre-commit) コミット前に lint-staged が実行されるようになる。

### git-secrets のインストールと設定

lint-staged では eslint と prettier によるチェックに加え、git-secrets によるチェックが実行されるよう設定されている(package.json)。

[awslabs/git-secrets - GitHub](https://github.com/awslabs/git-secrets) を README に従いインストールしてください。その後、以下のコマンドを実行して git コマンドと連携させる。

```sh
git secrets --register-aws --global
```

次に、ダミーの値として許可される認証情報のリストを登録。 `~/.gitconfig` を編集し、上述のコマンドによって生成された `[secrets]` セクションを探して以下を追記する。  
以下コードを参考に、使用する環境のアカウント ID を追記する。

```text
    # Fictitious AWS Account ID
    allowed = ************
    allowed = ************
    allowed = ************
    allowed = ************
    allowed = ************
    allowed = ************
    allowed = ************
    allowed = ************
```

> NOTE
>
> `git secrets install` は実行**しない**。本プロジェクトでは `simple-git-hooks`を使用して pre-commit をフックし、ここから git-secrets を呼び出している。 `git secrets install` を実行するとフックが競合してしまう。

---

## ACM の作成と登録申請

### ACM の作成

ドメインのパブリック証明書を作成する。

ACM はバージニア北部に CloudFront 用の ACM と東京リージョンに ALB 用の ACM をそれぞれ作成する。  
※CloudFront を使用しない場合は `バージニア北部への ACM 作成は不要。

### DNS 登録申請

ACM を作成したら、DNS 登録申請を行うため、マネジメントコンソール上から CSV をエクスポートし、CSV と関連する Cloudfront の FQDN を担当者へ連携する。  
 ![](./images/csv_export_from_acm.png)

## AWSChatbot 用に Slack を設定する

アラームを Slack に送るためには MonitorStack スタックをデプロイする。これらのスタックをデプロイする前に、AWS Chatbot に対してチャットクライアントのセットアップが必要です。この作業を行なっていないとスタックのデプロイに失敗する。
AWS Chatbot の設定手順は以下の通りです。

1.  Slack への AWS ユーザ追加と Slack 情報取得【GUI】
1.  params ファイルへの Slack 情報の追加【コード編集】
1.  AWS への Slack ワークスペース登録【GUI】

### 1. Slack への AWS ユーザ追加と Slack 情報取得【GUI】

#### Slack への AWS ユーザ追加(プライベートチャンネルの場合必須)

CodeBuild 完了通知の送信先に指定する Slack チャンネルにて、チャット投稿と同じ要領で、下記のコマンドを入力し、AWS ユーザをチャットに招待する。

```
/invite @aws
```

![](./images/inviteAws.png)

AWS ユーザが追加されたことを確認し、完了。

![](./images/inviteCheck.png)

#### Slack 情報取得

[リンク先](https://slack.com/intl/ja-jp/help/articles/221769328-%E8%87%AA%E5%88%86%E3%81%8C%E5%8F%82%E5%8A%A0%E3%81%97%E3%81%A6%E3%81%84%E3%82%8B%E3%83%AF%E3%83%BC%E3%82%AF%E3%82%B9%E3%83%9A%E3%83%BC%E3%82%B9%E3%81%AE-Slack-URL-%E3%82%92%E7%A2%BA%E8%AA%8D%E3%81%99%E3%82%8B)に記載されている方法を使用し、Slack のワークスペース URL を取得。

取得した URL を使用し、Slack の Web 版にアクセスする。

左ペインから通知先に指定したいチャンネルをクリック。※この時、チャンネル名もメモしておく。

URL を確認し、ワークスペース ID とチャンネル ID を取得し、メモする。

```
https://app.slack.com/client/ワークスペースID/チャンネルID
```

### 2. params ファイルへの Slack 情報の追加【コード編集】

先ほど取得した、チャンネル名、ワークスペース ID、チャンネル ID を下記の要領で params ファイルに記載する。

```
export const InfraResourcesPipelineParam: inf.IInfraResourcesPipelineParam = {
  slackChannelName: 'チャンネル名',
  slackWorkspaceId: 'ワークスペースID',
  slackChannelId: 'チャンネルID',
};
```

### 3. AWS への Slack ワークスペース登録【GUI】

ChatBot コンソールの設定済みクライアントにアクセスする。

認定済みクライアントに通知先の Slack ワークスペースが登録されているか確認する。  
 ワークスペースが登録されていない場合、以下手順で登録する。  
 ![](./images/not_register_slack_workspace.png)

ワークスペースを登録する。  
 新しいクライアントを設定をクリックし、プルダウンから Slack を選択する。  
 ![](./images/register_slack_workspace.png)

登録するワークスペースの URL をメモしておく。  
 ![](./images/get_slack_workspace.png)

登録したいワークスペースの URL を入力し、指示に従って Slack の認証を進める。  
 　![](./images/Signin_slack_workspace.png)

認証が完了すると、設定済みクライアントに登録される。  
 ![](./images/done_register_slack_workspace.png)
※InfraResourcesPipeline をデプロイすることで、 手順 2 で設定したチャンネルが登録されるため、新規登録時の設定済みチャンネル数は 0 である。

---
