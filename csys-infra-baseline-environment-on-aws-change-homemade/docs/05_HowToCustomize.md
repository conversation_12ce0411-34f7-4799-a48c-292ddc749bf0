# HowToCustomize

ここでは、スタックをカスタマイズするための方法について記載する。

## スタックのカスタマイズ方法

スタックをカスタマイズする方法は 2 パターン存在する。

1. params 配下にある環境ファイルからカスタマイズ
1. params 配下にある環境ファイル**外**からカスタマイズ

### 1. params 配下にある環境ファイルからカスタマイズ

- スペックの変更や登録情報などは 環境ファイルの値を書き換えることで変更可能
- 変更可能なパラメータは、`params`配下にある環境ファイルを参照すること

### 2. params 配下にある環境ファイル外からカスタマイズ

- リソースをカスタマイズする際は、ほとんどのリソースは環境ファイルから変更を加えられるが、一部リソースは直接**スタックやコンストラクトファイルを書き換える**必要がある。
- 修正が必要なリソース
  | No. | リソース | 内容 | ファイル名 |
  | - | - | - | - |
  | 1 | ECS | デプロイ方式の選択 | bin/blea-guest-ecsapp-sample.ts |
  | 2 | CloudFront | オリジン・ビヘイビアの追加 | lib/construct/cloudfront-construct.ts |
  | 3 | ElastiCache | セキュリティグループの追加 | lib/stack/elasticache-stack.ts |
  | 4 | OpenSearch | セキュリティグループ・アクセスポリシーの追加 | lib/stack/opensearch-stack.ts</br>lib/construct/opensearch-constructs/opensearch-serverless-construct.ts または opensearch-construct.ts |

#### 1. ECS

- ECS のデプロイ方式をカスタマイズする。

- デプロイパターンとして、以下の２パターン用意する。
  - [ecspresso](https://github.com/kayac/ecspresso)を使用したローリングデプロイ
  - CodeDeploy を使用した Blue/Green デプロイ
- デプロイパターン 2 種類 × パブリック（Frontend）/プライベート（Backend）2 種類で以下 4 パターンのサンプルをテンプレートとして用意している。  
  ![ECSデプロイパターン](./02_HowToUseStacks/images/ECS-Template-Patterns.dio.png)
- カスタマイズ手順は以下の通りである。

  1. 環境ファイルの修正

     - ECS は他の AWS リソースと比較してパラメータファイルの記載方法が特殊なため、ここに記載する。
     - デフォルトでは全 4 パラメータ全ての ECS サービスがデプロイされるため、不要なコードをコメントアウトもしくは削除する。
     - ここで設定したオブジェクト分の ECS サービスが作成される。
       サンプルコードではフロントエンド側で 2 つ、バックエンド側で 2 つの計 4 つの ECS サービスが作成される。

       (例)フロントエンド側で Rolling 方式を採用する場合  
       **params/環境ファイル**

       ```typescript
       export const EcsFrontTasks: inf.IEcsAlbParam = [
         {
           appName: 'EcsApp',
           portNumber: 80,
         },
         {
           appName: 'EcsApp2',
           portNumber: 80,
           path: '/path',
         },
       ];

       // export const EcsFrontBgTasks: inf.IEcsAlbParam = [
       //   {
       //     appName: 'EcsAppBg',
       //     portNumber: 80,
       //   },
       //   {
       //     appName: 'EcsApp2Bg',
       //     portNumber: 80,
       //     path: '/path',
       //   },
       // ];
       ```

  2. bin/blea-guest-ecsapp-sample.ts の修正  
      [TODO] BG は追加改修が必要なため、BG デプロイ方式は使用できない
     - デフォルトは Frontend 側 Backend 側ともに、Rolling 方式のコードになっているため、BG 方式を採用する場合は以下スタックの初期化処理を修正する。
       - WafAlb
       - Cloudfront
       - DBAurora
       - OpenSearch
       - ElastiCache
       - MonitorStack

#### 2. CloudFront

- CloudFront ディストリビューションのオリジンとビヘイビアをカスタマイズする。
- ビヘイビアとオリジンを追加する場合は、サンプルコードを基に実装する。
- オリジンに ALB を追加する場合は、初期化処理で`appAlbs`パラメータの配列に ALB を追加する。  
  **lib/construct/cloudfront-construct.ts**

  ```typescript
  additionalBehaviors: {
    //2個目のALBターゲットを指定する場合はパスを指定する
    '/backend2nd/*': {
      origin: new origins.LoadBalancerV2Origin(props.appAlbs[1], {
        protocolPolicy: sslFlag
          ? cloudfront.OriginProtocolPolicy.HTTPS_ONLY
          : cloudfront.OriginProtocolPolicy.HTTP_ONLY,
      }),
      viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
      allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
      cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
      originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER,
    },
    '/static/*': {
      origin: new origins.S3Origin(webContentBucket),
      viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
      cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
    },
  },
  ```

#### 3. ElastiCache

- ElastiCache のセキュリティグループをカスタマイズする。
- セキュリティグループのルールを追加する場合は、サンプルコードを基に実装する。
- ルールの設定方法は以下 2 パターンのサンプルコードを用意している。

  1. 特定セキュリティグループ ID からの許可：特定のコンテナリソースなどアクセス元リソースが絞れる場合
  2. サブネットの指定：特定リソースの絞り込みが難しく、サブネット全体で指定したい場合

  **lib/stack/elasticache-stack.ts**

  ```typescript
  const securityGroupForElastiCache = new ec2.SecurityGroup(this, 'ElastiCacheSecuritygGroup', {
    vpc: props.myVpc,
  });
  // 1. 特定セキュリティグループＩＤからの許可 を使用する場合はこちらをコメントイン。
  securityGroupForElastiCache.connections.allowFrom(props.appServerSecurityGroup, ec2.Port.tcp(6379));

  // 「2. サブネットの指定」を使用する場合はこちらをコメントイン
  // private subnetのCIDR内からのアクセスをすべて許可するインバウンドルール追加
  // private subnet内に多くのサービスがあり、個別の設定を受け付けるのが難しい場合使用
  props.myVpc.selectSubnets({ subnets: props.myVpc.privateSubnets }).subnets.forEach((x: ec2.ISubnet) => {
    securityGroupForElastiCache.addIngressRule(ec2.Peer.ipv4(x.ipv4CidrBlock), ec2.Port.tcp(6379));
  });
  ```

#### 4. OpneSearch

- OpenSearch ドメインのセキュリティグループとアクセスポリシーをカスタマイズする。

##### セキュリティグループのカスタマイズ

- セキュリティグループのルールを追加する場合は、サンプルコードを基に実装する。
- インバウンドルールの設定方法は以下 2 パターンのサンプルコードを用意している。アウトバウンドルールはフル開放で設定されている。

  1. 特定セキュリティグループ ID からの許可：特定のコンテナリソースなどアクセス元リソースが絞れる場合
  2. サブネットの指定：特定リソースの絞り込みが難しく、サブネット全体で指定したい場合

  **lib/stack/opensearch-stack.ts**

  ```typescript
  const domainsg = new ec2.SecurityGroup(this, 'domainsg', {
    vpc: props.vpc,
    allowAllOutbound: true,
  });
  // 1. 特定セキュリティグループＩＤからの許可 を使用する場合はこちらをコメントイン
  // 許可するSecurityGroupはpropsなど別スタックやIDで指定する
  if (props.appServerSecurityGroup) {
    domainsg.connections.allowFrom(props.appServerSecurityGroup, ec2.Port.tcp(443));
  }

  // 「2. サブネットの指定」を使用する場合はこちらをコメントイン
  // private subnetのCIDR内からのアクセスをすべて許可するインバウンドルール追加
  // private subnet内に多くのサービスがあり、個別の設定を受け付けるのが難しい場合使用
  props.myVpc.selectSubnets({ subnets: props.myVpc.privateSubnets }).subnets.forEach((x: ec2.ISubnet) => {
    domainsg.addIngressRule(ec2.Peer.ipv4(x.ipv4CidrBlock), ec2.Port.tcp(443));
  });
  ```

##### OpenSearch 接続用 IAM ロールのカスタマイズ

- OpenSearch へのアクセスを許可する IAM ロールを追加する場合は、サンプルコードを基に実装する。

  **lib/construct/opensearch-constructs/opensearch-serverless-construct.ts または opensearch-construct.ts**

  ```typescript
  // 特定のIAM RoleからOpenSarchドメインへアクセスを許可する場合は以降のサンプルをコメントインして使用する
  // OpenSearchを利用するサービスに付与するroleに紐づけるポリシー生成
  const rolePolicy = new iam.PolicyStatement({
    actions: ['es:ESHttp*'],
    resources: [domain.domainArn + '/*'],
    effect: iam.Effect.ALLOW,
  });

  const ecsRole = new iam.Role(this, 'ecsRole', {
    assumedBy: new iam.ServicePrincipal('ecs.amazonaws.com'),
  });
  ecsRole.addToPolicy(rolePolicy);
  ```
