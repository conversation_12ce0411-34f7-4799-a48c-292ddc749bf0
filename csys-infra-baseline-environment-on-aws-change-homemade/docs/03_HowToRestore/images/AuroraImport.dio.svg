<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="971px" height="631px" viewBox="-0.5 -0.5 971 631" content="&lt;mxfile&gt;&lt;diagram name=&quot;Page-1のコピー&quot; id=&quot;_CB-omHLXsE8ykfsfXeA&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;wmKGbYHDQA5ucB2pvP3L&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;bPGQBK4DHz4bbMepbiCu&quot; name=&quot;ページ2&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 330 15.5 L 211 15.5 Q 201 15.5 201.01 25.5 L 201.04 63.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="8 8" pointer-events="stroke"/>
        <path d="M 201.04 68.88 L 197.53 61.88 L 201.04 63.63 L 204.53 61.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 378 15.5 L 512 15.5 Q 522 15.5 522 25.5 L 522 62.58" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 522 67.83 L 518.5 60.83 L 522 62.58 L 525.5 60.83 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 18px; margin-left: 431px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 15px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                2. 接続先切替
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="431" y="23" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="15px" text-anchor="middle">
                    2. 接続先切替
                </text>
            </switch>
        </g>
        <rect x="330" y="0" width="48" height="31" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 376.91 0.09 L 331.09 0.09 C 330.49 0.09 330 0.58 330 1.18 L 330 29.82 C 330 30.42 330.49 30.91 331.09 30.91 L 376.91 30.91 C 377.51 30.91 378 30.42 378 29.82 L 378 1.18 C 378 0.58 377.51 0.09 376.91 0.09 Z M 332.18 28.73 L 332.18 2.27 L 375.82 2.27 L 375.82 28.73 Z M 335.73 26 L 337.91 26 L 337.91 5 L 335.73 5 Z M 341.45 26 L 343.64 26 L 343.64 5 L 341.45 5 Z M 347.18 26 L 349.36 26 L 349.36 5 L 347.18 5 Z M 352.91 26 L 355.09 26 L 355.09 5 L 352.91 5 Z M 358.63 26 L 360.82 26 L 360.82 5 L 358.63 5 Z M 364.36 26 L 366.55 26 L 366.55 5 L 364.36 5 Z M 370.09 26 L 372.27 26 L 372.27 5 L 370.09 5 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 38px; margin-left: 354px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="354" y="50" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Container
                </text>
            </switch>
        </g>
        <rect x="70" y="70" width="240" height="210" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 126 162 L 166 162 L 166 202 L 126 202 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 148.81 169.89 L 147.15 169.89 L 147.15 168.77 L 148.81 168.77 L 148.81 167.1 L 149.92 167.1 L 149.92 168.77 L 151.58 168.77 L 151.58 169.89 L 149.92 169.89 L 149.92 171.56 L 148.81 171.56 Z M 155.45 176.01 L 153.79 176.01 L 153.79 174.9 L 155.45 174.9 L 155.45 173.23 L 156.55 173.23 L 156.55 174.9 L 158.21 174.9 L 158.21 176.01 L 156.55 176.01 L 156.55 177.68 L 155.45 177.68 Z M 152.57 193.6 C 151.51 190.92 149.07 188.47 146.4 187.4 C 149.07 186.34 151.51 183.89 152.57 181.2 C 153.62 183.89 156.06 186.34 158.73 187.4 C 156.06 188.47 153.62 190.92 152.57 193.6 Z M 161.45 186.85 C 157.56 186.85 153.12 182.38 153.12 178.47 C 153.12 178.16 152.87 177.91 152.57 177.91 C 152.26 177.91 152.01 178.16 152.01 178.47 C 152.01 182.38 147.58 186.85 143.68 186.85 C 143.38 186.85 143.13 187.1 143.13 187.4 C 143.13 187.71 143.38 187.96 143.68 187.96 C 147.58 187.96 152.01 192.42 152.01 196.34 C 152.01 196.65 152.26 196.9 152.57 196.9 C 152.87 196.9 153.12 196.65 153.12 196.34 C 153.12 192.42 157.56 187.96 161.45 187.96 C 161.75 187.96 162 187.71 162 187.4 C 162 187.1 161.75 186.85 161.45 186.85 Z M 131.11 175.88 C 132.72 177.06 135.85 177.68 138.85 177.68 C 141.85 177.68 144.99 177.06 146.6 175.88 L 146.6 181.21 C 145.8 182.28 142.87 183.33 138.96 183.33 C 134.47 183.33 131.11 181.91 131.11 180.65 Z M 138.85 171.56 C 143.65 171.56 146.6 173.02 146.6 174.06 C 146.6 175.11 143.65 176.57 138.85 176.57 C 134.05 176.57 131.11 175.11 131.11 174.06 C 131.11 173.02 134.05 171.56 138.85 171.56 Z M 146.6 192.45 C 146.6 193.73 143.28 195.17 138.85 195.17 C 134.42 195.17 131.11 193.73 131.11 192.45 L 131.11 188.89 C 132.74 190.14 135.93 190.8 138.99 190.8 C 141.12 190.8 143.18 190.49 144.79 189.94 L 144.44 188.89 C 142.94 189.4 141.01 189.68 138.99 189.68 C 134.48 189.68 131.11 188.27 131.11 187.01 L 131.11 182.54 C 132.73 183.78 135.91 184.44 138.96 184.44 C 142.23 184.44 145.03 183.76 146.6 182.69 L 146.6 184.36 L 147.7 184.36 L 147.7 174.06 C 147.7 171.71 143.14 170.44 138.85 170.44 C 134.73 170.44 130.38 171.61 130.03 173.78 L 130 173.78 L 130 192.45 C 130 194.94 134.56 196.28 138.85 196.28 C 143.14 196.28 147.7 194.94 147.7 192.45 L 147.7 190.48 L 146.6 190.48 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 209px; margin-left: 146px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Aurora
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="146" y="221" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora
                </text>
            </switch>
        </g>
        <rect x="213" y="142" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 157px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    ParameterG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="161" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ParameterG
                </text>
            </switch>
        </g>
        <path d="M 166 182 L 213 157" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="213" y="102" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 117px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SecurityG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="121" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SecurityG
                </text>
            </switch>
        </g>
        <path d="M 166 182 L 213 117" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="213" y="182" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 197px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SubnetG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="201" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SubnetG
                </text>
            </switch>
        </g>
        <path d="M 166 182 L 213 197" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="213" y="222" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 237px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    Secret
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="241" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Secret
                </text>
            </switch>
        </g>
        <path d="M 166 182 L 213 244.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <image x="69.5" y="69.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAA/UlEQVRYR+3X2w6CMBBF0QHK/SL//7UWelAmtIVSSVBnPQ2F7JgKGOlP9EO8Bq1hjNdd0ypVjJq1FMZzCmkFkVYYaYXhrSKNUbJWtLu37qpudmSkMDmVaO3uVz7dQX6H9z6s1VRO+uzcGnBs0bKW5xnq0fJ86/wZkpa0pMX8TGt8uKHlv2Td8jItrytaabYjJcLkhtYnlTZEOSZOfwBMTI6Wdb+IGkxcTQkmxrv351tdu9bpk3Orx8JCX2laOIYp8G7x+z5dWhUWFvrPnWklWDD4fS+tF2lNpLX21a24d47+CV21tuaWhWltXNEq8P5niBQmLqcEE1OgJY4iegL6NG/nDkXO1gAAAABJRU5ErkJggg==" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="48" y="118" width="86" height="15" stroke-width="0"/>
            <text x="89.5" y="127.5">
                Auroraスタック
            </text>
        </g>
        <rect x="420" y="70" width="240" height="210" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 476 162 L 516 162 L 516 202 L 476 202 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 498.81 169.89 L 497.15 169.89 L 497.15 168.77 L 498.81 168.77 L 498.81 167.1 L 499.92 167.1 L 499.92 168.77 L 501.58 168.77 L 501.58 169.89 L 499.92 169.89 L 499.92 171.56 L 498.81 171.56 Z M 505.45 176.01 L 503.79 176.01 L 503.79 174.9 L 505.45 174.9 L 505.45 173.23 L 506.55 173.23 L 506.55 174.9 L 508.21 174.9 L 508.21 176.01 L 506.55 176.01 L 506.55 177.68 L 505.45 177.68 Z M 502.57 193.6 C 501.51 190.92 499.07 188.47 496.4 187.4 C 499.07 186.34 501.51 183.89 502.57 181.2 C 503.62 183.89 506.06 186.34 508.73 187.4 C 506.06 188.47 503.62 190.92 502.57 193.6 Z M 511.45 186.85 C 507.56 186.85 503.12 182.38 503.12 178.47 C 503.12 178.16 502.87 177.91 502.57 177.91 C 502.26 177.91 502.01 178.16 502.01 178.47 C 502.01 182.38 497.58 186.85 493.68 186.85 C 493.38 186.85 493.13 187.1 493.13 187.4 C 493.13 187.71 493.38 187.96 493.68 187.96 C 497.58 187.96 502.01 192.42 502.01 196.34 C 502.01 196.65 502.26 196.9 502.57 196.9 C 502.87 196.9 503.12 196.65 503.12 196.34 C 503.12 192.42 507.56 187.96 511.45 187.96 C 511.75 187.96 512 187.71 512 187.4 C 512 187.1 511.75 186.85 511.45 186.85 Z M 481.11 175.88 C 482.72 177.06 485.85 177.68 488.85 177.68 C 491.85 177.68 494.99 177.06 496.6 175.88 L 496.6 181.21 C 495.8 182.28 492.87 183.33 488.96 183.33 C 484.47 183.33 481.11 181.91 481.11 180.65 Z M 488.85 171.56 C 493.65 171.56 496.6 173.02 496.6 174.06 C 496.6 175.11 493.65 176.57 488.85 176.57 C 484.05 176.57 481.11 175.11 481.11 174.06 C 481.11 173.02 484.05 171.56 488.85 171.56 Z M 496.6 192.45 C 496.6 193.73 493.28 195.17 488.85 195.17 C 484.42 195.17 481.11 193.73 481.11 192.45 L 481.11 188.89 C 482.74 190.14 485.93 190.8 488.99 190.8 C 491.12 190.8 493.18 190.49 494.79 189.94 L 494.44 188.89 C 492.94 189.4 491.01 189.68 488.99 189.68 C 484.48 189.68 481.11 188.27 481.11 187.01 L 481.11 182.54 C 482.73 183.78 485.91 184.44 488.96 184.44 C 492.23 184.44 495.03 183.76 496.6 182.69 L 496.6 184.36 L 497.7 184.36 L 497.7 174.06 C 497.7 171.71 493.14 170.44 488.85 170.44 C 484.73 170.44 480.38 171.61 480.03 173.78 L 480 173.78 L 480 192.45 C 480 194.94 484.56 196.28 488.85 196.28 C 493.14 196.28 497.7 194.94 497.7 192.45 L 497.7 190.48 L 496.6 190.48 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 209px; margin-left: 496px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                Aurora
                                <br/>
                                （PITR利用）
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="496" y="221" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora...
                </text>
            </switch>
        </g>
        <rect x="563" y="142" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 157px; margin-left: 564px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    ParameterG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="593" y="161" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ParameterG
                </text>
            </switch>
        </g>
        <path d="M 516 182 L 563 157" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="563" y="102" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 117px; margin-left: 564px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SecurityG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="593" y="121" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SecurityG
                </text>
            </switch>
        </g>
        <path d="M 516 182 L 563 117" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="563" y="182" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 197px; margin-left: 564px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SubnetG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="593" y="201" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SubnetG
                </text>
            </switch>
        </g>
        <path d="M 516 182 L 563 197" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="563" y="222" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 237px; margin-left: 564px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    Secret
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="593" y="241" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Secret
                </text>
            </switch>
        </g>
        <path d="M 516 182 L 563 244.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.5" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="70" y="420" width="240" height="210" fill="#d1d1d1" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <rect x="213" y="492" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 507px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 10px;">
                                    ParameterG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="511" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ParameterG
                </text>
            </switch>
        </g>
        <rect x="213" y="452" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 467px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SecurityG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="471" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SecurityG
                </text>
            </switch>
        </g>
        <rect x="213" y="532" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 547px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    SubnetG
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="551" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SubnetG
                </text>
            </switch>
        </g>
        <rect x="213" y="572" width="60" height="30" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 587px; margin-left: 214px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 11px;">
                                    Secret
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="243" y="591" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Secret
                </text>
            </switch>
        </g>
        <path d="M 0 463 L 196 463 L 196 500 L 169.16 500 L 190.12 532 L 139.16 500 L 0 500 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 194px; height: 1px; padding-top: 482px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <span style="font-size: 12px;">
                                    Retain状態でdestroyし
                                </span>
                                <br style="border-color: var(--border-color); font-size: 12px;"/>
                                <span style="border-color: var(--border-color); font-family: Consolas, &quot;Courier New&quot;, monospace; font-size: 14px;">
                                    CloudFormation
                                </span>
                                <span style="font-size: 12px;">
                                    管理外にする
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="98" y="486" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">
                    Retain状態でdestroyし...
                </text>
            </switch>
        </g>
        <path d="M 190 280 L 190 413.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 190 418.88 L 186.5 411.88 L 190 413.63 L 193.5 411.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 351px; margin-left: 191px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    3.cdk destroy
                                </span>
                                <br style="border-color: var(--border-color); color: rgb(0, 0, 0); font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"/>
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    (Retain)
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="191" y="354" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    3.cdk destroy...
                </text>
            </switch>
        </g>
        <path d="M 310 175 L 413.63 175" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 418.88 175 L 411.88 178.5 L 413.63 175 L 411.88 171.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 176px; margin-left: 361px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <span style="font-size: 16px;">
                                    1.PITR
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="361" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    1.PITR
                </text>
            </switch>
        </g>
        <path d="M 660 175 L 763.63 175" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 768.88 175 L 761.88 178.5 L 763.63 175 L 761.88 171.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 176px; margin-left: 711px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <span style="font-size: 16px;">
                                    4.cdk import
                                    <br/>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="711" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    4.cdk import
                </text>
            </switch>
        </g>
        <path d="M 770 227.5 L 700 227.5 Q 690 227.5 680 227.5 L 666.37 227.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 661.12 227.5 L 668.12 224 L 666.37 227.5 L 668.12 231 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 228px; margin-left: 721px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <span style="font-size: 16px;">
                                    5.cdk deploy
                                    <br/>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="721" y="231" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    5.cdk deploy
                </text>
            </switch>
        </g>
        <rect x="770" y="120" width="200" height="151" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <path d="M 770 120 L 809 120 L 809 159 L 770 159 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 805.04 139.18 C 805.04 143.02 802.41 144.43 800.85 144.93 L 800.51 143.87 C 802.07 143.38 803.94 142.16 803.94 139.18 C 803.94 135.41 800.85 134.47 799.53 134.24 C 799.23 134.19 799.03 133.91 799.07 133.61 L 799.07 133.61 C 798.94 131.89 798.14 130.66 796.93 130.3 C 795.92 130 794.84 130.4 794.18 131.3 C 794.05 131.47 793.85 131.55 793.65 131.52 C 793.45 131.49 793.28 131.35 793.21 131.16 C 792.76 129.85 792.09 128.76 791.24 127.9 C 790.2 126.84 787.34 124.58 783.29 126.3 C 780.96 127.3 779.13 130.17 779.13 132.84 C 779.13 133.13 779.14 133.43 779.18 133.72 C 779.21 134 779.04 134.26 778.77 134.33 C 777.38 134.68 775.06 135.78 775.06 139.13 C 775.06 139.24 775.07 139.35 775.07 139.45 C 775.18 141.61 776.75 143.46 778.91 143.96 L 778.66 145.04 C 776.03 144.43 774.1 142.16 773.97 139.51 C 773.97 139.39 773.96 139.26 773.96 139.13 C 773.96 135.3 776.54 133.88 778.04 133.39 C 778.03 133.2 778.02 133.02 778.02 132.84 C 778.02 129.7 780.1 126.46 782.86 125.28 C 786.1 123.9 789.52 124.59 792.02 127.11 C 792.8 127.9 793.44 128.86 793.92 129.96 C 794.85 129.18 796.08 128.89 797.24 129.23 C 798.84 129.7 799.91 131.19 800.14 133.23 C 801.71 133.6 805.04 134.89 805.04 139.18 Z M 798.88 151.39 L 795.03 153.55 L 795.03 148.75 L 798.88 146.56 Z M 788.95 151.38 L 785.11 153.59 L 785.11 148.73 L 788.95 146.55 Z M 784.56 147.77 L 780.72 145.61 L 784.56 143.46 L 788.37 145.6 Z M 780.13 146.56 L 784.01 148.73 L 784.01 153.59 L 780.13 151.37 Z M 789.52 135.15 L 793.34 137.29 L 789.5 139.43 L 785.7 137.3 Z M 790.63 145.6 L 794.47 143.45 L 798.3 145.61 L 794.48 147.79 Z M 790.05 146.55 L 793.93 148.75 L 793.93 153.55 L 790.05 151.37 Z M 793.92 142.49 L 790.05 144.66 L 790.05 140.39 L 793.92 138.23 Z M 785.11 138.24 L 788.95 140.39 L 788.95 144.65 L 785.11 142.5 Z M 799.71 145.13 C 799.7 145.13 799.7 145.13 799.7 145.13 L 799.7 145.13 L 795.02 142.49 L 795.02 137.29 C 795.02 137.09 794.92 136.91 794.75 136.81 C 794.75 136.81 794.74 136.81 794.74 136.8 L 794.74 136.8 L 789.78 134.03 C 789.62 133.94 789.41 133.94 789.25 134.03 L 784.3 136.81 L 784.3 136.81 C 784.29 136.82 784.29 136.82 784.28 136.82 C 784.12 136.92 784.01 137.1 784.01 137.3 L 784.01 142.5 L 779.32 145.13 L 779.32 145.13 C 779.31 145.13 779.31 145.13 779.3 145.14 C 779.14 145.23 779.03 145.42 779.03 145.61 L 779.03 151.7 C 779.03 151.9 779.14 152.08 779.31 152.18 L 784.28 155.03 L 784.29 155.03 L 784.29 155.03 C 784.37 155.08 784.46 155.1 784.56 155.1 C 784.65 155.1 784.75 155.08 784.83 155.03 L 789.5 152.33 L 794.21 154.97 L 794.21 154.97 C 794.29 155.02 794.39 155.04 794.48 155.04 C 794.57 155.04 794.66 155.02 794.75 154.97 L 799.7 152.2 C 799.87 152.1 799.98 151.92 799.98 151.72 L 799.98 145.61 C 799.98 145.41 799.88 145.23 799.71 145.13 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 166px; margin-left: 790px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                CDK
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="790" y="178" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CDK
                </text>
            </switch>
        </g>
        <path d="M 855 211 L 855 161 L 882.47 161 L 895 173.55 L 895 211 Z M 857.51 208.49 L 892.54 208.49 L 892.54 174.7 L 881.32 174.7 L 881.32 163.51 L 857.51 163.51 Z M 863.82 202.27 L 863.82 199.76 L 886.28 199.76 L 886.28 202.27 Z M 886.28 195.99 L 863.82 195.99 L 863.82 193.48 L 886.28 193.48 Z M 863.82 189.71 L 863.82 187.31 L 886.28 187.31 L 886.28 189.71 Z M 886.28 183.44 L 863.82 183.44 L 863.82 180.98 L 886.28 180.98 Z" fill="#000000" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 258px; margin-left: 875px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <p style="">
                                    <font face="Meiryo UI">
                                        Auroraスタックファイル
                                    </font>
                                </p>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="875" y="258" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora...
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 44px; height: 1px; padding-top: 95px; margin-left: 431px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font color="#ff0000" style="font-size: 14px;">
                                    <b>
                                        NEW
                                    </b>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="453" y="99" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NEW
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>