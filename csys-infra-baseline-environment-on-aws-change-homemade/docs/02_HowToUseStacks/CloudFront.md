# HowToUseCloudFront

ここでは、Cloudfront スタックの利用方法を記載する

## 概要

- S3 や ALB (ECS) をオリジンとする CloudFront をデプロイするスタックである。
- CloudFront ディストリビューションのオリジンを切替える機能を実装している。
  - サービス運用時には ALB をオリジンとしているが、サービス終了時には案内ページを ClosedBucket で表示する目的で切り替え機能を実装した。

## CloudFront ディストリビューションのオリジン切替について

- この操作はサービス終了時に実施するものである。
- 料金節約のため、サービス終了画面表示用に CloudFront は残した状態で、利用しない ECS や Arora などのリソースは削除する。
- しかし、デフォルトの構成では CloudFront スタックが ECS スタックを参照しているため、ECS スタックを削除できない。
- そこで、CloudFront のオリジンを切り替えることで、CloudFront スタックと ECS スタック間の依存関係を解消する。

  ![CloudFrontスタック間の依存関係](./images/CloudFront-Dependencies.dio.svg)

- オリジンの切替は params ファイルで管理されており、`createClosedBucket` フラグで制御されている。
- サービス運用時(createClosedBucket フラグが false の状態) は ALB をオリジンとしている。
- サービス終了時には、createClosedBucket フラグを true に設定することで、ClosedBucket がデプロイされ、ClosedBucket にオリジンが切り替わる。
- オリジンを切替えるには、パラメーターを変更し、再デプロイする必要がある。

  ![CloudFrontオリジン切替](images/CloudFront-Detail-Architecture.dio.svg)

## 独自 FQDN の適用手順

- 独自の FQDN を適応するには、環境パラメーターファイルに下記の手順が必要になる。

  - CertificateIdentifier に SSL 証明書の識別子を設定する。

  ```
  export const CertificateIdentifier: inf.ICertificateIdentifier = {
    identifier: '',
  };
  ```

  - CloudFrontParam に FQDN を設定する。

  ```
  export const CloudFrontParam: inf.ICloudFrontParam = {
    fqdn: '',
    createClosedBucket: false,
  };
  ```

- 独自の FQDN を適応する必要がない場合は設定する必要がなく、そのままデプロイが可能である。
