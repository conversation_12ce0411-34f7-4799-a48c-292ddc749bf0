# HowToUseElastiCache

ここでは、ElastiCache スタックの利用方法を記載する

## 概要

- ElastiCache をデプロイするスタックである。
- ElastiCache では、比較的コストが高いサービスのため、dev や stg 環境では極力、最小スペックで設定すること。

## ElastiCache の engine について

- Valkey を第一選択とするが、Valkey で対応できない場合のために Redis を残しておく。

### `params/` 配下の環境ファイル

- サーバレスの場合は engine の種類及びバージョンを、プロビジョンドの場合は加えてパラメータグループを設定する。
- パラメータグループは engine とセットで更新する。
- engineのバージョンやパラメータグループのファミリーは以下を参考
  https://docs.aws.amazon.com/cdk/api/v2/docs/aws-cdk-lib.aws_elasticache.CfnReplicationGroup.html
  https://docs.aws.amazon.com/cdk/api/v2/docs/aws-cdk-lib.aws_elasticache.CfnServerlessCache.html

```typescript
export const ElastiCacheParam: inf.IElastiCacheParam = {
  ElastiCacheSelfDesignedParam: {
    // valkeyかredisか、及びバージョンを設定する
    engine: 'valkey',
    engineVersion: '7.2',
    ...(略)
    elastiCacheCustomParam: {
      // 使用するengineによってパラメータグループを変更する
      cacheParameterGroupFamily: 'valkey7',
      description: 'CustomParameterGroupForElastiCache',
      properties: {
        'cluster-enabled': 'yes',
      },
    },
  },
  ElastiCacheServerlessParam: {
    // valkeyかredisか、及びバージョンを設定する
    engine: 'valkey',
    engineVersion: '7',
    ...(略)
  },
};
```

## ElastiCache の AutoScalling について

- インスタンスタイプによっては、AutoScaling に未対応の物もある。  
  参考：https://docs.aws.amazon.com/ja_jp/AmazonElastiCache/latest/red-ug/AutoScaling.html#AutoScaling-Prerequisites
- そのため、AutoScaling を有効/無効化する `enableAutoScale` フラグによって管理する方式とする。
- 有効/無効化状態のインスタンスタイプを設定しておくことで、`enableAutoScale` フラグに応じてインスタンスタイプを選択できる。

### `params/` 配下の環境ファイル

- AutoScaling の有効無効化フラグと、それぞれの場合のインスタンスタイプを設定する。

```typescript
const ElastiCacheParam: inf.IElastiCacheParam = {
  ...(略)
  enableAutoScale: false,
  //AutoScaling有効化状態のインスタンスタイプ
  cacheNodeTypeEnableAutoScale: 'cache.m5.large',
  //AutoScaling無効化状態のインスタンスタイプ
  cacheNodeTypeDisableAutoScale: 'cache.t3.small',
}
```

## enableAutoScale フラグによる遷移

フラグの状況に応じて事前に設定されたインスタンスタイプが適用される。

![enableAutoScaleフラグ](./images/ElastiCache-stack-flowchart.dio.svg)
