# HowToUseBackup

ここでは、Backup Plan スタックとの利用方法を記載する

## 概要

- リソースのバックアップを目的とした Backup Plan をデプロイするスタックである。
- Backup Vault スタックと併用してデプロイする必要がある。
  - Backup Plan スタックをデプロイすることで、Backup Vault スタックも同時にデプロイ可能である。
- Backup Plan ではバックアップ対象のリソース指定、スケジュールなどの設定を行う。

## ディザスタリカバリ対策

- ディザスタリカバリ（以下 DR）対策に対応しており、DR 対策の有無を params ファイルで制御可能である。
- `BackupParam` の `backupDisasterRecovery` フラグで管理しており、`true` に変更することで DR 対策が可能である。
- DR 対策用のリージョンに別途 Backup Vault を作成することで、どちらか片方のリージョン全体が障害に見舞われてもデータの復旧が可能である。

## 保存対象

- `AWSBackup` キーかつ値が `true` に設定されたタグが付与されたリソースをバックアップ対象としている。

## Backup Plan 構成図

Backup Plan の構成図は以下のとおり。

### DR 対策なし

![DR対策なし](images/BackupPlan-Architecture.dio.svg)

- デフォルトリージョン内で全て完結する。
- 暗号化に使用する KMS Key は ShareResources スタックでデプロイしたものを使用する。

### DR 対策あり

![DR対策あり](images/BackupPlan-DR-Architecture.dio.svg)

- DR 用に別リージョンにセカンダリ Backup Vault を作成する。
- リージョンが異なるため、プライマリ用とは別にセカンダリ用 KMS Key を作成する。
