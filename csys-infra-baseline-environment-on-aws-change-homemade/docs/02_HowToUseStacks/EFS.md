# HowToUseEFS

ここでは、EFS スタックの利用方法を記載する

## 概要

- マウント用の EFS をデプロイするスタックである。
- 内製案件において EFS を Fargate からマウントする構成を想定している。
- EFS へのファイルアップロードは TransferFamily から行う想定である。
  - TransferFamily を使用するかどうかのフラグを環境パラメーターで制御している。
  - TransferFamily を使用する場合は、EFS スタックのデプロイ後にパラメーターを編集し、再デプロイする必要がある。
- 作成した EFS のバックアップ用に AWS Backup を採用している。
  - データの保存期間・実行開始の cron 設定はパラメーターで管理している。

## EFS 構成図

- EFS の構成図については以下の通り。

![EFS構成図](images/EFS-Detail-Architecture.dio.svg)

## TransferFamily を有効化（オプション）

TransferFamily を有効化するには、EFS をデプロイした後に下記のパラメーターを変更する必要がある。

- `isUseSharedTransferFamily`: `true` に変更する。
- `sharedTransferFamilyAccountID`: TransferFamily がデプロイされている AWS アカウント ID

```typescript
export const EfsParam: inf.IEfsParam = {
  ...,
  // Please change isUseSharedTransferFamily to true after creating the EFS stack
  isUseSharedTransferFamily: false,
  // Please change the information accordingly
  sharedTransferFamilyAccountID: '************',
  ...,
  },
};
```
