# HowToUseMonitor

ここでは、Monitor スタックの利用方法を記載する

## 概要

- メトリクス監視を目的とした AWS ClouWatch dashboards をデプロイするスタックである。
- リクエスト数やエラー率などのメトリクスをダッシュボードにまとめ、一元管理できる。

## Monitor 構成図

Monitor の構成図については以下の通り

![Monitorスタック構成図](images/Monitor-Detail-Architecture.dio.svg)

- 各スタックでデプロイしたリソースのメトリクスを一元管理する。
- マネジメントコンソール上から CloudWatch > Dashboards > ${pjPrefix}-ECSApp の順でアクセスできる。

## 監視対象

ベースコードのテンプレート（デフォルト）では、監視対象は以下のとおりである。

- CloudFront
- EcsApp
  - ALB
  - TargetGroup
  - ECS  
    ※ ECS Service 単位で監視
- Aurora
  - Writer
  - Reader
