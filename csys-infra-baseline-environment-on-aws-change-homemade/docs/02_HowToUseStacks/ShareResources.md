# HowToUseShareResources

ここでは、ShareResources スタックの利用方法を記載する

### ToDo

- **コメントイン/アウトで制御からパラメーター値で制御にアップデート予定**

## 概要

- 複数スタック間で共有するリソースをデプロイするスタックである。

## ShareResources 構成図

ShareResources の構成図については以下の通り

![ShareResources構成図](images/ShareResources-Detail-Architecture.dio.svg)

## コンストラクト一覧

以下は ShareResources スタックで参照されているコンストラクトファイルの 役割・リソース 一覧である。

| ファイル名           | 役割                                                                          | デプロイされるリソース                                   |
| -------------------- | ----------------------------------------------------------------------------- | -------------------------------------------------------- |
| sns-construct.ts     | アラート通知用の SNS Topic を作成する                                         | SNS Topic, SNS Subscription                              |
| chatbot-construct.ts | 指定した Slack チャンネルにアラート通知を送信する設定を行う                   | ChatBot Slack Channel                                    |
| cognito-construct.ts | ログイン認証用の Cognito ユーザープールを作成する                             | Cognito User Pools                                       |
| kms-key-construct.ts | 汎用テンプレートでデプロイするリソースで、共有して使用する KMS Key を作成する | KMS Key                                                  |
| vpc-construct.ts     | 汎用テンプレートでデプロイするリソースを配置する VPC を作成する               | VPC, VPC FlowLog, KMS Key, S3 Bucket, NACL, VPC Endpoint |
