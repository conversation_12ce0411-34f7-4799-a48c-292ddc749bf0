<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="481px" height="358px" viewBox="-0.5 -0.5 481 358" content="&lt;mxfile&gt;&lt;diagram name=&quot;240919&quot; id=&quot;SnIymqD7k1__mhMwZc9s&quot;&gt;7Vptc6M2EP41zLQfkgHEmz8CNm2nSZsZz02aTx0FBGaCkSPk2L5f3xUgGxDnjHPnS9LE4zjSs6vVy+4jrbA1FC63vzG8WlzThBSaqSdbDU0105wgDz4FsGsAS3cbIGN50kDGAZjnX0kL6i26zhNS9RQ5pQXPV30wpmVJYt7DMGN001dLadHvdYUzogDzGBcqepsnfNGgnuke8N9Jni1kz4YzaSRLLJXbmVQLnNBNB0IzDYWMUt6UltuQFGLt5LpE29Vf+JpHxp9V5NwtrL8f8cVFYyw6pcl+CoyU/MWmPdOz7243j9FdcHNHyfLx6xf7wmxMP+Fi3a5XXNB1kjJa8ouK4/ihnTvfyQXdLHJO5isci/oGYkZDwYIvC6gZUEzzoghpQRnUS1qCUlDge1Lc0CrnOS0BjmEaBOTBE2E8B1ddDRQ4FUZxkWej6n4ruKec0yUI2kmAmGwHXn9myYy9HyH+CV0SznbQrrXiOK3r29g3vLa+OUSSIcNj0Y0i2RC30ZvtbR88BIXWSSc4DB1zGHCo4vGaKz477h9G12VCklbh/XrL63trT9yOt7wxZ+k/wFmj4/UUZ21wevHJsIPPkP48w0adhqxzOU3dEmunCWqxdaxy63Rv/U/4hszn+WZaI77bO/SH+85RfDeHDVMP1vEDAc85BYwguGdQykTpl1ucXtGsEf+qeLYCNBYT0UF3RfOS1+O1A3jDDMLmzwbVUCCXpj0CjmGuChqqGvwzxnoYgmOYq4KGqiZqctR9cAxzbXXEw9bGSGtj0BreKKBrXuQlCfdZn1jjFPZEyRUN4guZkXB+kDGc5KQnc3QfIbcjm+YMDDUMKSkTAdjjnrDnupDeAQ5Mpg+kI0nrF0gSXC1qZupHCLhn1pBy32BmdzeAGbY5smHKehtxoktcrZrlSPOtGEcASedKCJfbTKTnl3hTWZeMVHTNYvJHLMYTQLUp9bUqdEbyW4PD1tJV8o9x/2zbtqtSf349QvkbzDD0CI4x9TmnjHzy/mfy/nM539Q2GiHL8szTttEgNJD9gbbRXcXJsvp3iUuc1QPp529nS6+c19thR6+eurLDhuImE4mbzOce+o5IP7HsaXQi6W0f6YH9YUh/uKOfkeHu4PK7p+5r5VCWwnBY1DzWZp7m+5o3rQueFoTaDGmeofmuNrO1INI8uxY5AmxFvpp71SIk1JpCa8fUgi5iaxO97gsQXRpE2sSQyhNpJ+rbgTeILDGeyVTY8afaxFN2JpJkRIaNiHWa0RIXswPajTlwL9v9IwLs0pbVuzbe6sp026vturUbwvIm1WzAZiii/xdEEcyhjtsjenajxzHLyDF77tGo1C8RvBpTjBSY50/98Y6FXW3NZwzvOgrttn/o7EYAnRNucIdA7uAJ9mn6UGhGMGgth0PTtCJcIcl+BV7OG/WhrAzRaRu0/kzGKpJhrH9fZL6VYHKeCSbHcdxXCSbzeDDZ+lH91wsmWwmmW9jpRp5dkXtQ88Orzwvse0q+IttyrclpyVfoGsiIPkzytcGpfC4e4Pghq+9bctIJSfG6OGdaNrx4/cy0bPTiZYwfL3CqyEIgUyEPyYKh7Ard46X9JmI00+nlOYe0541nOt++sh45nKwXHk7KaaJ89zj8ArgZattq7Fj6noMFqoefITTqh99yoNl/&lt;/diagram&gt;&lt;diagram name=&quot;240827&quot; id=&quot;jIyY93TUZ7x5-tAiMjUD&quot;&gt;7VhZb+M2EP41BNqHBJKoy4+SfPRIgGC9bXb7UtASLRGRRJeiYzu/focSZUuWmtZFku5iEzgw55vhkJyLYyIcFfuFIJvslic0R5aR7BGeIssysevAl0IODeJYZgOkgiVa6AQs2RPVoKHRLUto1ROUnOeSbfpgzMuSxrKHESH4ri+25nl/1Q1J6QBYxiQfovcskVmD+pZ3wn+iLM3alU130nAK0grrk1QZSfiuA+EZwpHgXDajYh/RXBmvtUux/f2vtdzdhr8Ziz9S/PljsPpw1SibXzLleARBS/myqq1G9SPJt9peO7K+Al9UUmzBG83B5aG15i5jki43JFb0DiIG4TCTRQ6UCcM1y/OI51wAXfIShELBt2VCEy2QkxXN73jFJOMlYDGciIJ0+EiFZOC1mzMBydUSJGfpqHigGSsuJS+A8S9NpU2q1NB9J1C06RaUF1SKA4hoLm6jQKfBMSp2p6CyXI1lnYA6ChIdyOlR98lZMND+usB3eOA7NMPIxyiY1gMLBTONhFgjoTFwKU0gXzSpfdbx6GX2rPhWxPQZOV1MJBEpfU6f28ipvT3rHUFzItljP9df3NL2wNIhqViMZj4KAuRP64GPwqi2sokCD80cFM6R79QsV4GaFSDLzeHo4UrAKFWj1m1u662o9VYXcdDEqNcCxGgVYjQxW+FJq2fe1wMfYNlqP5Op0gPxMfGfjQMuZMZTXpJ8dkK7YQHRIA6fgDBa4rMirp2WnO67zOmhS91RwcAxKpFr8H+KMe+lY6yeGghBDh2BDWelrDqa7xRwKiy2eVZY3LMy/g/y2DfO4rvZwSnaj0f57wngDBLgHqJsEMg/3NMViAXRzY+D8KoeqIwz7XFtFGA4IXzgBFHz74BopJBryxkBxzBvCJpDMfgyx1Y4B8cwbwiaQzFFtbvug2OY5wx3fD7bHJltns2GDw75VuaspNGxhVI2XvNStncxgvsLW3Pl8TAVJGG0x5s7tmdPOrwpE6CouYFLVQzO7naYE3kmNiHoQugU+APtcNb1H3ASUmX1zW88c8Efb+7zK/1vbv5utwEn1A2nabW0jji1JKk2jTnWbK/2EUIHt1HMYp+qZvea7Cr7WtCmovwcq/2EQDajvhS0Ra/ZXbhnRcAedhf2SHNhv1Zv4Q4SfgnthhFuY0jjscQn6xueNuz33P+Wct81Aoy9y3Lf8jz4nfTd5H6FXzH1bf8rS31vmPrL25GUvyOCNF2cZSwlF/Q9798y79/N+VWV0Tm2bd+6rIyGkYmd76iMHipJi+rPgpQkrTfSfxt6qwp7bLbeoMJW1e3H4n6RfPo1+vCU/vL4tLBd/U43eHTL+TZZC3DAVSVJ/PACb2/f7mub0XeZ6Q9ddpTpvbb5lzsNyNMjbvOL+fQUjmdfAA==&lt;/diagram&gt;&lt;diagram id=&quot;sn1t80STWCxECPuZ5oly&quot; name=&quot;240816&quot;&gt;7Vjrj6M2EP9rLLUfbgWYVz4CSa6V9qSV8mHbT5UXHLAOcGScTdK/vmMw4WF2e4/cXavbiCie34zH43l4HBBOqvN7QQ7FB57REjlWdkZ4jRzHth0PfhRy6RDPdjogFyzTQgOwY39TDVoaPbKMNhNByXkp2WEKpryuaSonGBGCn6Zie15OVz2QnBrALiWliT6yTBYdGjrBgP9GWV70K9v+quNUpBfWO2kKkvHTCMIbhBPBuexG1TmhpXJe75du3vYF7tUwQWv5KRN0IJ5JedR7O5H9O/BbI8URPNcZKS/9zk8Fk3R3IKmiTxBdhONCViVQNgz3rCwTXnIBdM1rEIoFP9YZzbRASZ5o+cAbJhmvAUvBTgrS8TMVkoGH72cCkqslSMnyRfFIM564lLwChukA7RM1g55HkHbIe8orKsUFRDQX98HR2XkN1mmIteNrrBjF+SpIdH7lV91DCGCgo7AcEdeICNpgFGIUrduBg6KNRmKskdgyAkUzyFhN6kiM4vSilxp+FKlWoWtSEpFTLYU7SCl/1ZOClkSy52m5fI1XAsMrMWlYijYhiiIUrttBiOKk9YiNogBtPBRvUei1LF+BmhUhxy9hQ/GTgFGuRr2L/d6zSe/ZMeKhldWuBYjVK8RoZffCq17PdqoHHmC5yp7VWumBWK7CV2PGhSx4zmtSbgZ0HEKInLj8AYTVE38q4s7ryfV5zFxfxtQDFQxioEqpBb80H/xb50M7NRKCXEYCB85q2Yw0PyhgKFjXnhWsPzv0/kUeh9YsFzsLhsy8buWTktUxkvURMsJIul8e6ROIRcn9r0YqNB+pTAsdHb1/YHgxPGBs0n09EE0UcqcOcgNcwgITtE0x+LGXVpiDS1hggrYppqje6im4hAWeafF8tr0w257NhgfH/ChLVtPkejlQPt7zWvadC0ELwM5WBTfOBckYnfC2nhu4qxFvzQQo6vpVrQp31glhThLY2Ib8iqGv8o90xNm3H+BkpCnaPmm90g6vfW7eAF/ok+PeDDvUVym4bmlaZ5xakjSHzh17dlZ2xHA3OShmdc7VNe6OnBr3TtDuSPg9VfbEQHajqRRcIm7Ui/1ZabtmL3YXWrF7g06MjTLeAWTFxxSKc6mcyf6e5x37raL/TxXtWxHGwedVtBMEcK//aSq6wbcpaDf8cQXtmwW9+7BQyA9EkO525Fg7yQV9q+bvWc1v7vxPHY5b7Lqh83mHY5zY2PuJDsdLI2nV/FWRmuStIdO3Ht/g3LxejG5/bgI5vIDq/v4Mr/Hw5h8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="360" y="17" width="120" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 14px; margin-left: 361px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cloudfront-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    cloudfront-stack
                </text>
            </switch>
        </g>
        <rect x="380" y="57" width="80" height="100" rx="12" ry="12" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 54px; margin-left: 420px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                cloudfront-constcut
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    cloudfront-co...
                </text>
            </switch>
        </g>
        <rect x="0" y="17" width="280" height="340" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                waf-cloudfront-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    waf-cloudfront-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="57" width="240" height="280" rx="36" ry="36" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 54px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                waf-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="140" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    waf-construct
                </text>
            </switch>
        </g>
        <path d="M 180 237 L 220 237 L 220 277 L 180 277 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 211.94 258.65 L 212.16 257.11 C 214.18 258.32 214.21 258.82 214.21 258.83 C 214.2 258.84 213.86 259.13 211.94 258.65 Z M 210.83 258.34 C 207.33 257.29 202.46 255.05 200.49 254.12 C 200.49 254.11 200.49 254.11 200.49 254.1 C 200.49 253.34 199.88 252.72 199.12 252.72 C 198.36 252.72 197.75 253.34 197.75 254.1 C 197.75 254.85 198.36 255.47 199.12 255.47 C 199.45 255.47 199.75 255.35 199.99 255.15 C 202.31 256.25 207.14 258.45 210.67 259.49 L 209.27 269.32 C 209.27 269.35 209.27 269.37 209.27 269.4 C 209.27 270.27 205.43 271.86 199.17 271.86 C 192.84 271.86 188.97 270.27 188.97 269.4 C 188.97 269.37 188.97 269.35 188.97 269.32 L 186.05 248.06 C 188.57 249.8 193.99 250.71 199.18 250.71 C 204.35 250.71 209.76 249.8 212.29 248.07 Z M 185.75 245.84 C 185.79 245.09 190.11 242.14 199.18 242.14 C 208.24 242.14 212.56 245.09 212.6 245.84 L 212.6 246.1 C 212.11 247.79 206.51 249.57 199.18 249.57 C 191.83 249.57 186.23 247.78 185.75 246.09 Z M 213.75 245.86 C 213.75 243.88 208.07 241 199.18 241 C 190.28 241 184.6 243.88 184.6 245.86 L 184.66 246.29 L 187.83 269.44 C 187.9 272.03 194.81 273 199.17 273 C 204.59 273 210.34 271.76 210.41 269.45 L 211.78 259.79 C 212.54 259.97 213.17 260.07 213.67 260.07 C 214.35 260.07 214.8 259.9 215.08 259.57 C 215.31 259.3 215.4 258.97 215.33 258.62 C 215.18 257.83 214.24 256.98 212.33 255.89 L 213.69 246.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 200px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (WafLogBucket)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="296" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buck...
                </text>
            </switch>
        </g>
        <path d="M 60 237 L 100 237 L 100 277 L 60 277 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 83.91 265.73 L 91.19 265.73 L 91.19 266.85 L 83.91 266.85 L 83.91 267.97 L 82.8 267.97 L 82.8 266.85 L 80.56 266.85 L 80.56 265.73 L 82.8 265.73 L 82.8 265.17 L 83.91 265.17 Z M 86.15 262.37 L 91.19 262.37 L 91.19 263.49 L 86.15 263.49 L 86.15 264.61 L 85.03 264.61 L 85.03 263.49 L 80.56 263.49 L 80.56 262.37 L 85.03 262.37 L 85.03 261.81 L 86.15 261.81 Z M 88.95 259.02 L 91.19 259.02 L 91.19 260.13 L 88.95 260.13 L 88.95 261.25 L 87.83 261.25 L 87.83 260.13 L 80.56 260.13 L 80.56 259.02 L 87.83 259.02 L 87.83 258.46 L 88.95 258.46 Z M 69.37 261.25 L 74.41 261.25 L 74.41 262.37 L 69.37 262.37 C 68.67 262.37 67.92 262.16 67.37 261.81 C 66.24 261.09 64.34 259.42 64.34 256.25 C 64.34 252.4 66.95 250.98 68.47 250.49 L 68.44 249.98 C 68.44 246.81 70.53 243.56 73.31 242.38 C 76.57 241 80.02 241.69 82.54 244.22 C 83.32 245 83.97 245.96 84.46 247.07 C 85.45 246.24 86.8 245.96 88.05 246.37 C 89.65 246.89 90.64 248.37 90.75 250.35 C 92.34 250.73 95.66 252.03 95.66 256.3 C 95.66 256.46 95.65 256.61 95.65 256.76 L 94.53 256.73 C 94.53 256.58 94.54 256.44 94.54 256.3 C 94.54 252.53 91.43 251.58 90.1 251.35 C 89.95 251.33 89.81 251.24 89.73 251.11 C 89.64 250.99 89.61 250.83 89.64 250.68 C 89.63 249.02 88.92 247.83 87.7 247.43 C 86.61 247.07 85.41 247.47 84.72 248.42 C 84.59 248.58 84.39 248.67 84.18 248.64 C 83.98 248.61 83.81 248.47 83.74 248.27 C 83.27 246.96 82.6 245.86 81.75 245.01 C 79.56 242.81 76.57 242.22 73.75 243.41 C 71.4 244.41 69.56 247.28 69.56 249.94 L 69.61 250.87 C 69.63 251.14 69.45 251.38 69.19 251.44 C 67.8 251.8 65.46 252.89 65.46 256.25 C 65.46 258.75 66.82 260.14 67.97 260.86 C 68.35 261.1 68.88 261.25 69.37 261.25 Z M 94.54 264.23 L 93.55 264.17 C 93.29 264.16 93.04 264.34 92.98 264.61 C 92.79 265.45 92.46 266.24 92 266.97 C 91.85 267.2 91.9 267.5 92.1 267.69 L 92.84 268.34 L 91 270.18 L 90.35 269.44 C 90.17 269.24 89.86 269.2 89.63 269.34 C 88.9 269.8 88.11 270.13 87.27 270.32 C 87 270.38 86.82 270.63 86.83 270.9 L 86.89 271.88 L 84.3 271.88 L 84.35 270.9 C 84.37 270.63 84.18 270.38 83.92 270.32 C 83.08 270.13 82.28 269.8 81.55 269.34 C 81.32 269.19 81.02 269.24 80.84 269.44 L 80.18 270.18 L 78.35 268.34 L 79.08 267.69 C 79.29 267.5 79.33 267.2 79.18 266.97 C 78.72 266.24 78.4 265.45 78.21 264.61 C 78.15 264.34 77.88 264.16 77.63 264.17 L 76.64 264.23 L 76.64 261.63 L 77.63 261.69 C 77.89 261.71 78.15 261.52 78.21 261.26 C 78.4 260.42 78.73 259.63 79.19 258.9 C 79.34 258.67 79.29 258.36 79.09 258.18 L 78.35 257.52 L 80.18 255.69 L 80.84 256.43 C 81.03 256.63 81.33 256.67 81.56 256.53 C 82.29 256.07 83.08 255.74 83.92 255.55 C 84.18 255.49 84.37 255.25 84.35 254.97 L 84.3 253.98 L 86.89 253.98 L 86.83 254.97 C 86.82 255.25 87 255.49 87.27 255.55 C 88.1 255.74 88.9 256.07 89.63 256.53 C 89.86 256.67 90.16 256.63 90.34 256.43 L 91 255.69 L 92.84 257.52 L 92.1 258.18 C 91.89 258.36 91.85 258.67 92 258.9 C 92.46 259.62 92.79 260.42 92.98 261.26 C 93.04 261.52 93.29 261.71 93.55 261.69 L 94.54 261.63 Z M 95.07 260.48 L 93.95 260.55 C 93.77 259.91 93.51 259.29 93.19 258.71 L 94.02 257.96 C 94.14 257.86 94.21 257.71 94.21 257.56 C 94.22 257.41 94.16 257.26 94.05 257.15 L 91.37 254.48 C 91.27 254.37 91.11 254.3 90.96 254.31 C 90.81 254.32 90.66 254.38 90.56 254.5 L 89.81 255.34 C 89.23 255.01 88.62 254.76 87.98 254.58 L 88.04 253.45 C 88.05 253.3 87.99 253.15 87.89 253.04 C 87.78 252.93 87.64 252.86 87.48 252.86 L 83.7 252.86 C 83.55 252.86 83.4 252.93 83.3 253.04 C 83.19 253.15 83.14 253.3 83.14 253.45 L 83.21 254.58 C 82.57 254.76 81.95 255.01 81.37 255.34 L 80.62 254.5 C 80.52 254.38 80.38 254.32 80.22 254.31 C 80.06 254.31 79.92 254.37 79.81 254.48 L 77.14 257.15 C 77.03 257.26 76.97 257.41 76.97 257.56 C 76.98 257.71 77.05 257.86 77.16 257.96 L 78 258.71 C 77.68 259.29 77.42 259.91 77.24 260.55 L 76.12 260.48 C 75.97 260.47 75.81 260.53 75.7 260.63 C 75.59 260.74 75.53 260.89 75.53 261.04 L 75.53 264.82 C 75.53 264.98 75.59 265.12 75.7 265.23 C 75.81 265.33 75.97 265.39 76.12 265.38 L 77.23 265.32 C 77.42 265.96 77.67 266.57 78 267.16 L 77.16 267.9 C 77.05 268 76.98 268.15 76.97 268.3 C 76.97 268.46 77.03 268.61 77.14 268.71 L 79.81 271.39 C 79.92 271.5 80.08 271.56 80.22 271.55 C 80.38 271.55 80.52 271.48 80.62 271.36 L 81.37 270.53 C 81.95 270.86 82.57 271.11 83.21 271.29 L 83.14 272.41 C 83.14 272.56 83.19 272.71 83.3 272.82 C 83.4 272.94 83.55 273 83.7 273 L 87.48 273 C 87.64 273 87.78 272.94 87.89 272.82 C 87.99 272.71 88.05 272.56 88.04 272.41 L 87.98 271.3 C 88.62 271.11 89.24 270.86 89.82 270.53 L 90.56 271.36 C 90.66 271.48 90.81 271.55 90.96 271.55 C 91.11 271.56 91.27 271.5 91.37 271.39 L 94.05 268.71 C 94.16 268.61 94.22 268.46 94.21 268.3 C 94.21 268.15 94.14 268 94.02 267.9 L 93.19 267.16 C 93.51 266.57 93.77 265.96 93.95 265.32 L 95.07 265.38 C 95.22 265.39 95.37 265.33 95.48 265.23 C 95.6 265.12 95.66 264.98 95.66 264.82 L 95.66 261.04 C 95.66 260.89 95.6 260.74 95.48 260.63 C 95.37 260.53 95.22 260.47 95.07 260.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                SSM
                                <br/>
                                (Parameter Store)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="296" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SSM...
                </text>
            </switch>
        </g>
        <path d="M 400 77 L 440 77 L 440 117 L 400 117 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 428.64 103.93 C 428.64 102.98 427.87 102.21 426.93 102.21 C 425.98 102.21 425.21 102.98 425.21 103.93 C 425.21 104.87 425.98 105.64 426.93 105.64 C 427.87 105.64 428.64 104.87 428.64 103.93 Z M 429.78 103.93 C 429.78 105.5 428.5 106.78 426.93 106.78 C 425.35 106.78 424.07 105.5 424.07 103.93 C 424.07 102.35 425.35 101.07 426.93 101.07 C 428.5 101.07 429.78 102.35 429.78 103.93 Z M 414.51 95.68 C 414.51 94.74 413.74 93.97 412.8 93.97 C 411.85 93.97 411.08 94.74 411.08 95.68 C 411.08 96.63 411.85 97.4 412.8 97.4 C 413.74 97.4 414.51 96.63 414.51 95.68 Z M 415.65 95.68 C 415.65 97.26 414.37 98.54 412.8 98.54 C 411.22 98.54 409.94 97.26 409.94 95.68 C 409.94 94.11 411.22 92.82 412.8 92.82 C 414.37 92.82 415.65 94.11 415.65 95.68 Z M 419.85 86.72 C 419.85 87.66 420.61 88.43 421.56 88.43 C 422.51 88.43 423.27 87.66 423.27 86.72 C 423.27 85.77 422.51 85 421.56 85 C 420.61 85 419.85 85.77 419.85 86.72 Z M 418.7 86.72 C 418.7 85.14 419.99 83.86 421.56 83.86 C 423.14 83.86 424.42 85.14 424.42 86.72 C 424.42 88.29 423.14 89.58 421.56 89.58 C 419.99 89.58 418.7 88.29 418.7 86.72 Z M 434.86 97 C 434.86 91.7 432.02 86.8 427.44 84.15 C 426.61 84.31 425.82 84.54 424.83 84.9 L 424.44 83.82 C 424.96 83.64 425.42 83.49 425.87 83.36 C 424.03 82.56 422.03 82.14 420 82.14 C 419.03 82.14 418.09 82.24 417.16 82.42 C 417.83 82.82 418.43 83.21 419 83.65 L 418.31 84.56 C 417.5 83.94 416.65 83.42 415.54 82.83 C 409.93 84.6 405.89 89.55 405.25 95.36 C 406.42 95.12 407.55 94.99 408.81 94.96 L 408.84 96.1 C 407.52 96.13 406.39 96.27 405.16 96.55 C 405.15 96.7 405.14 96.85 405.14 97 C 405.14 101.95 407.59 106.51 411.62 109.26 C 410.9 107.12 410.54 105.11 410.54 103.14 C 410.54 102.02 410.74 101.1 410.94 100.12 C 410.99 99.9 411.04 99.67 411.08 99.43 L 412.2 99.65 C 412.16 99.89 412.11 100.13 412.06 100.36 C 411.86 101.31 411.69 102.14 411.69 103.14 C 411.69 105.37 412.18 107.68 413.17 110.19 C 415.3 111.29 417.59 111.86 420 111.86 C 421.57 111.86 423.11 111.61 424.58 111.12 C 425.15 109.99 425.58 108.92 425.94 107.69 L 427.03 108 C 426.77 108.9 426.48 109.72 426.12 110.53 C 427.04 110.11 427.91 109.6 428.73 109 C 428.54 108.52 428.32 108.04 428.09 107.57 L 429.11 107.06 C 429.31 107.46 429.49 107.86 429.67 108.27 C 432.97 105.44 434.86 101.38 434.86 97 Z M 436 97 C 436 101.99 433.73 106.6 429.78 109.66 C 428.81 110.42 427.74 111.05 426.62 111.56 C 426.15 111.77 425.66 111.97 425.16 112.14 C 423.52 112.71 421.78 113 420 113 C 417.37 113 414.76 112.35 412.45 111.11 C 407.24 108.32 404 102.91 404 97 C 404 96.61 404.01 96.31 404.03 96.03 C 404.42 89.36 409 83.58 415.43 81.67 C 416.89 81.22 418.43 81 420 81 C 422.75 81 425.45 81.71 427.82 83.05 C 432.86 85.87 436 91.22 436 97 Z M 418.47 88.69 L 417.71 87.83 C 416.43 88.95 415.44 90.14 414.27 91.93 L 415.23 92.55 C 416.33 90.85 417.27 89.74 418.47 88.69 Z M 416.73 96.1 L 416.36 97.18 C 418.98 98.08 421.27 99.52 423.56 101.71 L 424.35 100.88 C 421.94 98.58 419.51 97.06 416.73 96.1 Z M 424.37 89.38 C 426.51 92.65 427.72 96.24 427.96 100.05 L 426.82 100.12 C 426.59 96.51 425.45 93.11 423.41 90.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 420px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="420" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudFr...
                </text>
            </switch>
        </g>
        <path d="M 80 117 L 80 197 Q 80 207 80 217 L 80 230.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 80 235.88 L 76.5 228.88 L 80 230.63 L 83.5 228.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 197px; margin-left: 80px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Basic認証で必要な
                                <br/>
                                ユーザー名、パスワード保存
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="200" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Basic認証で必要な...
                </text>
            </switch>
        </g>
        <path d="M 80 117 L 80 147 Q 80 157 90 157 L 190 157 Q 200 157 200 167 L 200 230.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 200 235.88 L 196.5 228.88 L 200 230.63 L 203.5 228.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 197px; margin-left: 200px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ロギング
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="200" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ロギング
                </text>
            </switch>
        </g>
        <path d="M 60 77 L 100 77 L 100 117 L 60 117 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 65.37 97.57 L 64 97.57 L 64 96.43 L 65.37 96.43 C 65.49 93.54 66.42 90.79 68.12 88.44 L 69.05 89.11 C 67.49 91.26 66.63 93.78 66.51 96.43 L 68 96.43 L 68 97.57 L 66.51 97.57 C 66.62 100.24 67.48 102.77 69.05 104.93 L 68.12 105.6 C 66.41 103.24 65.48 100.48 65.37 97.57 Z M 88.58 108.9 C 86.23 110.6 83.47 111.54 80.57 111.65 L 80.57 113 L 79.43 113 L 79.43 111.65 C 76.53 111.54 73.77 110.6 71.42 108.9 L 72.09 107.98 C 74.25 109.54 76.77 110.4 79.43 110.51 L 79.43 109 L 80.57 109 L 80.57 110.51 C 83.23 110.4 85.75 109.53 87.91 107.98 Z M 71.42 85.14 C 73.77 83.44 76.53 82.5 79.43 82.39 L 79.43 81 L 80.57 81 L 80.57 82.39 C 83.47 82.5 86.23 83.44 88.58 85.14 L 87.91 86.07 C 85.75 84.51 83.23 83.64 80.57 83.53 L 80.57 85 L 79.43 85 L 79.43 83.53 C 76.77 83.64 74.25 84.51 72.09 86.07 Z M 96 96.43 L 96 97.57 L 94.63 97.57 C 94.52 100.48 93.59 103.24 91.88 105.6 L 90.95 104.93 C 92.52 102.77 93.38 100.24 93.49 97.57 L 92 97.57 L 92 96.43 L 93.49 96.43 C 93.37 93.78 92.51 91.26 90.95 89.11 L 91.88 88.44 C 93.58 90.79 94.51 93.54 94.63 96.43 Z M 88.56 87.66 L 93.04 83.18 L 93.84 83.99 L 89.36 88.47 Z M 71.44 106.39 L 66.96 110.87 L 66.16 110.06 L 70.64 105.58 Z M 72.72 90.52 L 64.24 82.04 L 65.04 81.24 L 73.52 89.72 Z M 87.26 103.45 L 95.76 111.96 L 94.96 112.76 L 86.45 104.26 Z M 75.24 97.42 C 75.28 97.34 75.33 97.27 75.37 97.19 C 76.33 95.68 76.06 93.62 75.75 92.38 C 76.58 92.92 77.32 94.04 77.57 94.51 C 77.68 94.71 77.89 94.83 78.12 94.81 C 78.35 94.79 78.54 94.64 78.62 94.43 C 79.47 92.01 79.03 90.19 78.43 89.01 C 79.16 89.44 79.73 90.05 80.1 90.82 C 80.91 92.51 80.73 94.81 79.63 96.83 C 78.13 99.59 78.43 102.49 78.77 104.05 C 77.91 103.68 77.15 103.26 76.49 102.8 C 74.76 101.6 74.21 99.23 75.24 97.42 Z M 82.91 97.23 C 82.87 97.46 82.98 97.69 83.19 97.81 C 83.39 97.93 83.65 97.91 83.83 97.76 C 83.88 97.73 84.82 96.95 85.27 95.08 C 85.82 95.88 86.39 97.48 85.63 100.43 C 84.88 103.35 81.3 104.17 80.03 104.37 C 79.72 103.27 79.1 100.2 80.63 97.37 C 81.72 95.37 82.01 93.1 81.47 91.23 C 82.5 92.44 83.37 94.36 82.91 97.23 Z M 74.24 96.86 C 72.93 99.18 73.63 102.2 75.83 103.74 C 76.83 104.44 78.05 105.04 79.43 105.54 C 79.5 105.56 79.56 105.57 79.63 105.57 C 79.64 105.57 79.65 105.57 79.66 105.56 L 79.66 105.57 C 79.9 105.55 85.61 105.05 86.73 100.72 C 88.18 95.14 85.29 93.48 85.17 93.41 C 85 93.32 84.8 93.32 84.63 93.41 C 84.46 93.5 84.34 93.66 84.32 93.85 C 84.28 94.24 84.22 94.58 84.14 94.89 C 83.75 90.51 80.27 88.72 79.57 88.41 C 78.85 87.85 77.98 87.47 76.96 87.29 C 76.71 87.25 76.45 87.38 76.35 87.62 C 76.24 87.85 76.3 88.13 76.5 88.3 C 76.58 88.37 78.36 89.91 77.85 92.85 C 77.19 91.98 76.13 90.93 74.89 90.93 C 74.7 90.93 74.53 91.02 74.42 91.18 C 74.31 91.34 74.29 91.53 74.36 91.71 C 74.37 91.74 75.54 94.81 74.41 96.58 C 74.35 96.67 74.29 96.77 74.24 96.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                WAF
                                <br/>
                                (Web ACL)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    WAF...
                </text>
            </switch>
        </g>
        <path d="M 100 97 L 393.63 97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 398.88 97 L 391.88 100.5 L 393.63 97 L 391.88 93.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 97px; margin-left: 320px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アタッチ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="320" y="100" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アタッチ
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>