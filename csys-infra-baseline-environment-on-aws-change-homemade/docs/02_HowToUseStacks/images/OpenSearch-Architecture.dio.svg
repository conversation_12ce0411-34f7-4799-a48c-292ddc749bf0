<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="482px" height="178px" viewBox="-0.5 -0.5 482 178" content="&lt;mxfile&gt;&lt;diagram id=&quot;LsVHoZSOylgrq0-CM5EX&quot; name=&quot;240822&quot;&gt;7Vjfb5swEP5rkLaHVvwmeSxJ203atGl52GPlgANWDEa206T763cGQwDTapVYp6lJk8b+7lyf7/N3V2F5q+J0z1GVf2UpppZrpyfLW1uuG0Uh/FbAUwP4zrIBMk7SBnLOwIb8whq0NXogKRYDR8kYlaQaggkrS5zIAYY4Z8eh247R4a4VyrABbBJETfQnSWXeoAs3OuOfMMnydmcn1OcrUOusTyJylLJjD/JuLW/FGZPNqDitMFW5a/PSrLt7xtoFxnEp/2iBjuMR0YM+HKtwKTBP8ishUbLXccqn9vDHnEi8qVCi5kfg1/LiXBYUZg4MOTuUKU71bEcoXTHKOMxLVsKSmKItpt+ZIJKwEuAEQsVgjx8xlwSS/GXkIJnaAlGSTbrfaMOWSckKMJg50GlRK/CpB+mc3GNWYMmfwKW1LnRe9AX1fD0/nun2W5+8T3WoQaSvWNb97TMLMNBETJOyfIYTVJOCOZyDYiGu4HoLyQ9wwV/J0QQrQ9r+OkcpEnm32wyEufaIsIVJmDNJmD0DYZ5B2DcgbFMTBvimo8xyQwobx1sOo0yNPgAPFGoUpPGjQaPYY5mo6G3wrBgpZR1kEMMbwl41nwBcVwq5doMJcAqLTNAx3eDLmdphDE5hkQk6ppuatVEPwSksCsyIx6udidXOaDW8vZgdJCUlXnUtQuV4x0rZCsNyPfi5U4zHGUcpwQPb0g/Wd27Ptia84bEWFVe3biA0WBPceHYcAA6yZXvcs+zqV18Y9gtq62Q01tczMuxLH06oG6rjtnN949SWSFRNOnbkpOKIoUNVylicMtXLr9FR+NccC3bgCf6cqHhimDajoRemSEB4TeF6UIWLJF0HiKG3ZHXVadOQ4h061PKYoyBEw4Lg2xMVfKIe+DOUA98sBz8MbcM5pHETdDE2CJwo2D1Ox7egIGmqtomn2sC50NvzZNpbjjLtBEamw4lMezNkevFSp7y0x44jP/yX7TF4sT0aHXHNCkQu3fDSDd9HN5xD3uMS/IbNLjTEfRtvLtr9j7Qb2jeeF71Ou24UOU743rT7sKUs2T8IyfhMyg3e7t9UmJ6fK9W23sM57/Y3&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="16" width="480" height="160" rx="24" ry="24" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 478px; height: 1px; padding-top: 13px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                openserch-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="13" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    openserch-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="56" width="180" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 53px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                opensearch-serverless-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    opensearch-serverless-construct
                </text>
            </switch>
        </g>
        <path d="M 90 76 L 130 76 L 130 116 L 90 116 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 100.47 89.53 L 94.59 89.53 C 94.26 89.53 94 89.8 94 90.12 L 94 111.29 C 94 111.61 94.26 111.88 94.59 111.88 L 100.47 111.88 C 100.79 111.88 101.06 111.61 101.06 111.29 L 101.06 90.12 C 101.06 89.8 100.79 89.53 100.47 89.53 Z M 95.18 110.7 L 95.18 90.71 L 99.88 90.71 L 99.88 110.7 Z M 103.41 93.65 L 102.23 93.65 L 102.23 86 C 102.23 85.68 102.5 85.42 102.82 85.42 L 108.7 85.42 C 109.03 85.42 109.29 85.68 109.29 86 L 109.29 88.36 L 108.11 88.36 L 108.11 86.59 L 103.41 86.59 Z M 108.11 107.17 L 109.29 107.17 L 109.29 111.29 C 109.29 111.61 109.03 111.88 108.7 111.88 L 102.82 111.88 C 102.5 111.88 102.23 111.61 102.23 111.29 L 102.23 102.47 L 103.41 102.47 L 103.41 110.7 L 108.11 110.7 Z M 111.64 87.77 L 110.46 87.77 L 110.46 80.71 C 110.46 80.39 110.73 80.12 111.05 80.12 L 116.93 80.12 C 117.26 80.12 117.52 80.39 117.52 80.71 L 117.52 88.94 L 116.34 88.94 L 116.34 81.3 L 111.64 81.3 Z M 116.34 108.35 L 117.52 108.35 L 117.52 111.29 C 117.52 111.61 117.26 111.88 116.93 111.88 L 111.05 111.88 C 110.73 111.88 110.46 111.61 110.46 111.29 L 110.46 107.76 L 111.64 107.76 L 111.64 110.7 L 116.34 110.7 Z M 125.75 84.24 L 125.75 104.23 L 124.58 104.23 L 124.58 84.83 L 119.87 84.83 L 119.87 91.3 L 118.7 91.3 L 118.7 84.24 C 118.7 83.92 118.96 83.65 119.28 83.65 L 125.17 83.65 C 125.49 83.65 125.75 83.92 125.75 84.24 Z M 119.91 101.61 C 120.45 100.5 120.76 99.26 120.76 97.95 C 120.76 93.33 117 89.57 112.38 89.57 C 107.76 89.57 104 93.33 104 97.95 C 104 102.57 107.76 106.33 112.38 106.33 C 114.09 106.33 115.67 105.82 117 104.94 L 122.06 109.5 C 122.48 109.88 123.01 110.07 123.54 110.07 C 124.14 110.07 124.75 109.82 125.19 109.34 C 126 108.43 125.93 107.03 125.02 106.21 Z M 105.18 97.95 C 105.18 93.98 108.41 90.75 112.38 90.75 C 116.35 90.75 119.58 93.98 119.58 97.95 C 119.58 101.92 116.35 105.16 112.38 105.16 C 108.41 105.16 105.18 101.92 105.18 97.95 Z M 124.31 108.55 C 123.93 108.98 123.27 109.01 122.84 108.63 L 117.94 104.21 C 118.46 103.74 118.92 103.23 119.31 102.65 L 124.23 107.08 C 124.66 107.47 124.69 108.13 124.31 108.55 Z M 112.38 92.07 C 109.14 92.07 106.5 94.71 106.5 97.95 C 106.5 101.2 109.14 103.84 112.38 103.84 C 115.63 103.84 118.27 101.2 118.27 97.95 C 118.27 94.71 115.63 92.07 112.38 92.07 Z M 112.38 102.66 C 109.78 102.66 107.67 100.55 107.67 97.95 C 107.67 95.36 109.78 93.24 112.38 93.24 C 114.98 93.24 117.09 95.36 117.09 97.95 C 117.09 100.55 114.98 102.66 112.38 102.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 110px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                OpenSearch Serverless
                                <br/>
                                (Collection)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="135" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenSe...
                </text>
            </switch>
        </g>
        <rect x="210" y="91" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 106px; margin-left: 211px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OR
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="110" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OR
                </text>
            </switch>
        </g>
        <rect x="280" y="56" width="180" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 53px; margin-left: 281px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                opensearch-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="370" y="53" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    opensearch-construct
                </text>
            </switch>
        </g>
        <path d="M 310 76 L 350 76 L 350 116 L 310 116 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 320.47 89.53 L 314.59 89.53 C 314.26 89.53 314 89.8 314 90.12 L 314 111.29 C 314 111.61 314.26 111.88 314.59 111.88 L 320.47 111.88 C 320.79 111.88 321.06 111.61 321.06 111.29 L 321.06 90.12 C 321.06 89.8 320.79 89.53 320.47 89.53 Z M 315.18 110.7 L 315.18 90.71 L 319.88 90.71 L 319.88 110.7 Z M 323.41 93.65 L 322.23 93.65 L 322.23 86 C 322.23 85.68 322.5 85.42 322.82 85.42 L 328.7 85.42 C 329.03 85.42 329.29 85.68 329.29 86 L 329.29 88.36 L 328.11 88.36 L 328.11 86.59 L 323.41 86.59 Z M 328.11 107.17 L 329.29 107.17 L 329.29 111.29 C 329.29 111.61 329.03 111.88 328.7 111.88 L 322.82 111.88 C 322.5 111.88 322.23 111.61 322.23 111.29 L 322.23 102.47 L 323.41 102.47 L 323.41 110.7 L 328.11 110.7 Z M 331.64 87.77 L 330.46 87.77 L 330.46 80.71 C 330.46 80.39 330.73 80.12 331.05 80.12 L 336.93 80.12 C 337.26 80.12 337.52 80.39 337.52 80.71 L 337.52 88.94 L 336.34 88.94 L 336.34 81.3 L 331.64 81.3 Z M 336.34 108.35 L 337.52 108.35 L 337.52 111.29 C 337.52 111.61 337.26 111.88 336.93 111.88 L 331.05 111.88 C 330.73 111.88 330.46 111.61 330.46 111.29 L 330.46 107.76 L 331.64 107.76 L 331.64 110.7 L 336.34 110.7 Z M 345.75 84.24 L 345.75 104.23 L 344.58 104.23 L 344.58 84.83 L 339.87 84.83 L 339.87 91.3 L 338.7 91.3 L 338.7 84.24 C 338.7 83.92 338.96 83.65 339.28 83.65 L 345.17 83.65 C 345.49 83.65 345.75 83.92 345.75 84.24 Z M 339.91 101.61 C 340.45 100.5 340.76 99.26 340.76 97.95 C 340.76 93.33 337 89.57 332.38 89.57 C 327.76 89.57 324 93.33 324 97.95 C 324 102.57 327.76 106.33 332.38 106.33 C 334.09 106.33 335.67 105.82 337 104.94 L 342.06 109.5 C 342.48 109.88 343.01 110.07 343.54 110.07 C 344.14 110.07 344.75 109.82 345.19 109.34 C 346 108.43 345.93 107.03 345.02 106.21 Z M 325.18 97.95 C 325.18 93.98 328.41 90.75 332.38 90.75 C 336.35 90.75 339.58 93.98 339.58 97.95 C 339.58 101.92 336.35 105.16 332.38 105.16 C 328.41 105.16 325.18 101.92 325.18 97.95 Z M 344.31 108.55 C 343.93 108.98 343.27 109.01 342.84 108.63 L 337.94 104.21 C 338.46 103.74 338.92 103.23 339.31 102.65 L 344.23 107.08 C 344.66 107.47 344.69 108.13 344.31 108.55 Z M 332.38 92.07 C 329.14 92.07 326.5 94.71 326.5 97.95 C 326.5 101.2 329.14 103.84 332.38 103.84 C 335.63 103.84 338.27 101.2 338.27 97.95 C 338.27 94.71 335.63 92.07 332.38 92.07 Z M 332.38 102.66 C 329.78 102.66 327.67 100.55 327.67 97.95 C 327.67 95.36 329.78 93.24 332.38 93.24 C 334.98 93.24 337.09 95.36 337.09 97.95 C 337.09 100.55 334.98 102.66 332.38 102.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 330px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                OpenSearch
                                <br/>
                                (Domain)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="330" y="135" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenSe...
                </text>
            </switch>
        </g>
        <path d="M 390 76 L 430 76 L 430 116 L 390 116 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 424.86 106.29 L 426 106.29 L 426 112 L 420.29 112 L 420.29 110.86 L 424.05 110.86 L 419.31 106.12 L 420.12 105.31 L 424.86 110.05 Z M 400.69 106.12 L 395.95 110.86 L 399.71 110.86 L 399.71 112 L 394 112 L 394 106.29 L 395.14 106.29 L 395.14 110.05 L 399.88 105.31 Z M 426 80 L 426 85.71 L 424.86 85.71 L 424.86 81.95 L 420.12 86.69 L 419.31 85.88 L 424.05 81.14 L 420.29 81.14 L 420.29 80 Z M 395.95 81.14 L 400.69 85.88 L 399.88 86.69 L 395.14 81.95 L 395.14 85.71 L 394 85.71 L 394 80 L 399.71 80 L 399.71 81.14 Z M 416.1 103.81 C 415.44 104.48 413.22 105.17 409.75 105.17 C 405.52 105.17 403.16 104.13 403.14 103.43 L 403.14 90.2 C 404.63 91.08 407.33 91.43 409.75 91.43 C 412.15 91.43 414.81 91.08 416.29 90.21 L 416.29 103.44 C 416.29 103.49 416.29 103.61 416.1 103.81 Z M 409.75 86.86 C 413.24 86.86 415.45 87.53 416.11 88.2 C 416.29 88.39 416.29 88.52 416.29 88.56 L 416.29 88.57 C 416.29 89.27 413.96 90.29 409.75 90.29 C 405.67 90.29 403.16 89.29 403.14 88.58 C 403.18 87.85 405.48 86.86 409.75 86.86 Z M 417.43 88.57 C 417.43 88.28 417.35 87.84 416.92 87.4 C 415.89 86.35 413.21 85.71 409.75 85.71 C 406.21 85.71 402.05 86.46 402 88.57 L 402 103.44 C 402.05 105.56 406.21 106.31 409.75 106.31 C 412.41 106.31 415.69 105.86 416.92 104.6 C 417.35 104.16 417.44 103.71 417.43 103.43 L 417.43 88.58 L 417.43 88.57 L 417.43 88.57 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 123px; margin-left: 410px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EBS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="135" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EBS
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>