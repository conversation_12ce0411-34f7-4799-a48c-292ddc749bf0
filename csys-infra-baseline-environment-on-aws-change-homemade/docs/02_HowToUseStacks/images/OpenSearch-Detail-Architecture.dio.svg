<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="482px" height="259px" viewBox="-0.5 -0.5 482 259" content="&lt;mxfile&gt;&lt;diagram name=&quot;240912&quot; id=&quot;MwmYpY4GW4A1-UfTRohk&quot;&gt;7VlZb+M2EP41BtqHBDot+zHy0WyxRbZrFNv2JaAlWmJDiQZFH9lfv0OJOkmnm0XVtFgntkN+HJLD+eZg5Im7yM4/cbRPf2ExphPHis8TdzlxnLk7g08JPFeAZ/kVkHASV5DdAhvyGSvQUuiBxLjoCQrGqCD7PhixPMeR6GGIc3bqi+0Y7e+6RwnWgE2EqI5+IrFIK3TmBC1+j0mS1jvb03k1kqFaWJ2kSFHMTh3IXU3cBWdMVK3svMBU2q62y8do66xm+C//wbr/eYlWvx23wU212Po1U5ojcJyLf3Zpp1r6iOhB2YvtcV5gHqU3hUDRkzq6eK7teUqJwJs9imT/BC4zccNUZBR6NjQ5O+Qxllta0NsRSheMMg79nOUwJaRoi+kHVhBBWA5wBGfCMB4eMRcEeHs/EBBMboEoSYzid2pgy4RgGQyoE8EwPg9c4G/sZzekQjBglmHBn2FevcpM+YEKBNdT/VPrVl4tk3ZdaqpApFw5adZu6YKGYuwV7LkX2EMlfZiDESguihuIrULwA0TXK9k08NcSbP8bbMaoSJvdxqLWsQbUznRqbSO11ljUBhq1D0DtpqQW8E1D7sSZUtAm3HJoJbL1AzBGIZWCwX/UCC+esIhSFZ57RnJRau6H8IKzLKq3D6ILidw6vgE0YYEO2roY/LFNOwxBExbooK2LyV6tdR80YYGvazycbRtm24PZ8HJDdhCU5HjRVLIyBbJc1CE0cVz4XUs3CBOOYoJ7Y3PPX66dztiS8IrHMvy4dMVeSMIc/861Qh9wCHD2hDsju/KnG0LWC3HZBNwwEi8EbDdJwAlV3beduq88Tm6Jin1ljh05Sz1CKKR7OZidE3nluEWnwrvluGAHHuF3kdQnhG7V6kthigpQr0pxjzLFkaipKiHUq6TMT7UZYrxDhzI8RksdQT91eJahKhgyhzdW4pjpieOjlgXACELzGZXgNaoNRaDD/tBfMhLHcpvQVFr6t4OxOHHnQ058jZOpgRN3LE7mL9Xpa3F+mU1v+p8rzvX/Nheqs1aQlyxD5FqMr8X4+yjGo2UCLa+/ca21bS0PrMLNNcz/R2E+te5cN3hdmDtBYNvT7y3MH7eURU+PhWB8zCD33/BC/ed772HjcBq56P5X8cfv7z5F5MbT4hnHCa6NLT2EJSxHdNWil5hqL71yiW+wGqhREvaCuo7ZuhxYFOTY39RkKzX1g0xTnSv14OGXNzS3QDzBQs1qLX7HOXruiKnsd3EfzzHv0xJYrdjS2Zzx2xn2rwzr1+w3Yvir9RrVI/TH4sAeygrNUTpu0C/sS1MCJ1n5ZYWWvRW+JFkC6lKyhU/0+cCxPGeCc8wRqLxeE4pvi2PyNY/ZtQIw2qVs8OzUd0bL19Btv22pqG6/snJXXwA=&lt;/diagram&gt;&lt;diagram id=&quot;LsVHoZSOylgrq0-CM5EX&quot; name=&quot;240821&quot;&gt;7Vnfc6M2EP5rPHN9SAYQYPvR2EnuZtpppn5o+5RRQAZNBPIJObb713eFxE/hXDKluXYuiTNov13Baj9pd3FmaJ2f7gTeZ7/whLCZ5ySnGdrMPM91vQAuCjlrxF+EGkgFTYxRC2zpX8SAjkEPNCFlz1ByziTd98GYFwWJZQ/DQvBj32zHWf+pe5wSC9jGmNno7zSRmUYX3rzFPxOaZvWT3XCpNTmujc1Kygwn/NiB0M0MrQXnUo/y05owFbw6Lnre7QVt45gghXzVBOPHM2YHszi+J0VJRJxdlRLHT8ZPea4Xf8yoJNs9jpV8BIJnKMpkzkByYSj4oUhIYqQdZWzNGRcgF7yAKRHDj4Td85JKyguAY3CVgD56JkJSCPLPAwPJ1SMwo+mo+cooHrmUPAeFHQMTFjWDnDqQickd4TmR4gwmtXZh4mI2KPKNfGzp9mubrEt1aEBstlja3LtlAQaGiHFSlhc4wRUpRMA6GCnLK9jepRQH2OBv5GiElT5t/zpHCS6z5mkTEOY5A8IWNmHuKGHOBIS5nsUASSBjGJELmfGUF5jdtGiXD1i3OP/RFf4EwbkOanGjluk00rkr3RNBwWMV7wq8GM2SH0Rs3DMOSyxSYqyQhpTjL0ZcEIYlfe6nwrHoVVNXQuBzx2DPaSHLzp3vFdASiZZ9Iv1hAnubPQy0By2PzVJeRy2aiFrnO1Ib/Ceo9YO3UfsN+39MrWel2ZkXMqnyI6/8aDkPvx54rbgqq35kBQbL/alipVbDKFXXDc8xLeq7PYoa397VGPimH6IV9h5jDJoZtZe+kcpxudcdzo6eVEKdIpn6g2TayN3qN5JL/QlSKbJI+RVq37aqfYBvm+pnR/cTlDQGwYCK9JMV0PKJyDgz58jsVFAEEXzA7bX+C8B0rZBrdWYscAyb26Brm8HFHXvCEBzD5jbo2mZKqr3ug2PYPLA9Hs52R2a7g9nwQRE/SEYLsm66bccclbrHmHkIfm8V41EqcEJJT7f0g82t19FtqNA8Vv2JUHuu17PAnGCFnEglUeiA+BPpaHbVT7fHcF5oXJqOZNiqXOhoul0UrNC8m7heLZsd54wdTWj290qZn1L1XnSNj6V/LYhO3l9i5U8Eoh71rSAxl+Ce7gEfVA9I46aZjqBNT6sGrg5DQnb4wOQ06cCbf7904Nvp4DfrbMM6pLUTTF9rETjS+3Y4He6CnCYJu5SG2575xeL8+kgPmxkU2pEORyKNJoj0wop056Xj402jLY7hgKN3fdMIXiyPVkXUjchHNfyohj9ENZzieA/fJ9+x2IXW4b6Jth9n9390dkNnhdD8bWfXm89dN/zRzu7DI+Px00MpuZjo5Abv16aC2H5Fr795aP/RgW7+Bg==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="17" width="480" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 478px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                openserch-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    openserch-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="57" width="180" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 54px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                opensearch-serverless-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    opensearch-serverless-construct
                </text>
            </switch>
        </g>
        <path d="M 90 77 L 130 77 L 130 117 L 90 117 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 100.47 90.53 L 94.59 90.53 C 94.26 90.53 94 90.8 94 91.12 L 94 112.29 C 94 112.61 94.26 112.88 94.59 112.88 L 100.47 112.88 C 100.79 112.88 101.06 112.61 101.06 112.29 L 101.06 91.12 C 101.06 90.8 100.79 90.53 100.47 90.53 Z M 95.18 111.7 L 95.18 91.71 L 99.88 91.71 L 99.88 111.7 Z M 103.41 94.65 L 102.23 94.65 L 102.23 87 C 102.23 86.68 102.5 86.42 102.82 86.42 L 108.7 86.42 C 109.03 86.42 109.29 86.68 109.29 87 L 109.29 89.36 L 108.11 89.36 L 108.11 87.59 L 103.41 87.59 Z M 108.11 108.17 L 109.29 108.17 L 109.29 112.29 C 109.29 112.61 109.03 112.88 108.7 112.88 L 102.82 112.88 C 102.5 112.88 102.23 112.61 102.23 112.29 L 102.23 103.47 L 103.41 103.47 L 103.41 111.7 L 108.11 111.7 Z M 111.64 88.77 L 110.46 88.77 L 110.46 81.71 C 110.46 81.39 110.73 81.12 111.05 81.12 L 116.93 81.12 C 117.26 81.12 117.52 81.39 117.52 81.71 L 117.52 89.94 L 116.34 89.94 L 116.34 82.3 L 111.64 82.3 Z M 116.34 109.35 L 117.52 109.35 L 117.52 112.29 C 117.52 112.61 117.26 112.88 116.93 112.88 L 111.05 112.88 C 110.73 112.88 110.46 112.61 110.46 112.29 L 110.46 108.76 L 111.64 108.76 L 111.64 111.7 L 116.34 111.7 Z M 125.75 85.24 L 125.75 105.23 L 124.58 105.23 L 124.58 85.83 L 119.87 85.83 L 119.87 92.3 L 118.7 92.3 L 118.7 85.24 C 118.7 84.92 118.96 84.65 119.28 84.65 L 125.17 84.65 C 125.49 84.65 125.75 84.92 125.75 85.24 Z M 119.91 102.61 C 120.45 101.5 120.76 100.26 120.76 98.95 C 120.76 94.33 117 90.57 112.38 90.57 C 107.76 90.57 104 94.33 104 98.95 C 104 103.57 107.76 107.33 112.38 107.33 C 114.09 107.33 115.67 106.82 117 105.94 L 122.06 110.5 C 122.48 110.88 123.01 111.07 123.54 111.07 C 124.14 111.07 124.75 110.82 125.19 110.34 C 126 109.43 125.93 108.03 125.02 107.21 Z M 105.18 98.95 C 105.18 94.98 108.41 91.75 112.38 91.75 C 116.35 91.75 119.58 94.98 119.58 98.95 C 119.58 102.92 116.35 106.16 112.38 106.16 C 108.41 106.16 105.18 102.92 105.18 98.95 Z M 124.31 109.55 C 123.93 109.98 123.27 110.01 122.84 109.63 L 117.94 105.21 C 118.46 104.74 118.92 104.23 119.31 103.65 L 124.23 108.08 C 124.66 108.47 124.69 109.13 124.31 109.55 Z M 112.38 93.07 C 109.14 93.07 106.5 95.71 106.5 98.95 C 106.5 102.2 109.14 104.84 112.38 104.84 C 115.63 104.84 118.27 102.2 118.27 98.95 C 118.27 95.71 115.63 93.07 112.38 93.07 Z M 112.38 103.66 C 109.78 103.66 107.67 101.55 107.67 98.95 C 107.67 96.36 109.78 94.24 112.38 94.24 C 114.98 94.24 117.09 96.36 117.09 98.95 C 117.09 101.55 114.98 103.66 112.38 103.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 110px;">
                        <div data-drawio-colors="color: #232F3E; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                OpenSearch Serverless
                                <br/>
                                (Collection)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenSea...
                </text>
            </switch>
        </g>
        <rect x="210" y="82" width="60" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 97px; margin-left: 211px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                OR
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="101" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OR
                </text>
            </switch>
        </g>
        <rect x="280" y="57" width="180" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 54px; margin-left: 281px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                opensearch-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="370" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    opensearch-construct
                </text>
            </switch>
        </g>
        <path d="M 310 77 L 350 77 L 350 117 L 310 117 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 320.47 90.53 L 314.59 90.53 C 314.26 90.53 314 90.8 314 91.12 L 314 112.29 C 314 112.61 314.26 112.88 314.59 112.88 L 320.47 112.88 C 320.79 112.88 321.06 112.61 321.06 112.29 L 321.06 91.12 C 321.06 90.8 320.79 90.53 320.47 90.53 Z M 315.18 111.7 L 315.18 91.71 L 319.88 91.71 L 319.88 111.7 Z M 323.41 94.65 L 322.23 94.65 L 322.23 87 C 322.23 86.68 322.5 86.42 322.82 86.42 L 328.7 86.42 C 329.03 86.42 329.29 86.68 329.29 87 L 329.29 89.36 L 328.11 89.36 L 328.11 87.59 L 323.41 87.59 Z M 328.11 108.17 L 329.29 108.17 L 329.29 112.29 C 329.29 112.61 329.03 112.88 328.7 112.88 L 322.82 112.88 C 322.5 112.88 322.23 112.61 322.23 112.29 L 322.23 103.47 L 323.41 103.47 L 323.41 111.7 L 328.11 111.7 Z M 331.64 88.77 L 330.46 88.77 L 330.46 81.71 C 330.46 81.39 330.73 81.12 331.05 81.12 L 336.93 81.12 C 337.26 81.12 337.52 81.39 337.52 81.71 L 337.52 89.94 L 336.34 89.94 L 336.34 82.3 L 331.64 82.3 Z M 336.34 109.35 L 337.52 109.35 L 337.52 112.29 C 337.52 112.61 337.26 112.88 336.93 112.88 L 331.05 112.88 C 330.73 112.88 330.46 112.61 330.46 112.29 L 330.46 108.76 L 331.64 108.76 L 331.64 111.7 L 336.34 111.7 Z M 345.75 85.24 L 345.75 105.23 L 344.58 105.23 L 344.58 85.83 L 339.87 85.83 L 339.87 92.3 L 338.7 92.3 L 338.7 85.24 C 338.7 84.92 338.96 84.65 339.28 84.65 L 345.17 84.65 C 345.49 84.65 345.75 84.92 345.75 85.24 Z M 339.91 102.61 C 340.45 101.5 340.76 100.26 340.76 98.95 C 340.76 94.33 337 90.57 332.38 90.57 C 327.76 90.57 324 94.33 324 98.95 C 324 103.57 327.76 107.33 332.38 107.33 C 334.09 107.33 335.67 106.82 337 105.94 L 342.06 110.5 C 342.48 110.88 343.01 111.07 343.54 111.07 C 344.14 111.07 344.75 110.82 345.19 110.34 C 346 109.43 345.93 108.03 345.02 107.21 Z M 325.18 98.95 C 325.18 94.98 328.41 91.75 332.38 91.75 C 336.35 91.75 339.58 94.98 339.58 98.95 C 339.58 102.92 336.35 106.16 332.38 106.16 C 328.41 106.16 325.18 102.92 325.18 98.95 Z M 344.31 109.55 C 343.93 109.98 343.27 110.01 342.84 109.63 L 337.94 105.21 C 338.46 104.74 338.92 104.23 339.31 103.65 L 344.23 108.08 C 344.66 108.47 344.69 109.13 344.31 109.55 Z M 332.38 93.07 C 329.14 93.07 326.5 95.71 326.5 98.95 C 326.5 102.2 329.14 104.84 332.38 104.84 C 335.63 104.84 338.27 102.2 338.27 98.95 C 338.27 95.71 335.63 93.07 332.38 93.07 Z M 332.38 103.66 C 329.78 103.66 327.67 101.55 327.67 98.95 C 327.67 96.36 329.78 94.24 332.38 94.24 C 334.98 94.24 337.09 96.36 337.09 98.95 C 337.09 101.55 334.98 103.66 332.38 103.66 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 330px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                OpenSearch
                                <br/>
                                (Domain)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="330" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    OpenSea...
                </text>
            </switch>
        </g>
        <path d="M 390 77 L 430 77 L 430 117 L 390 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 424.86 107.29 L 426 107.29 L 426 113 L 420.29 113 L 420.29 111.86 L 424.05 111.86 L 419.31 107.12 L 420.12 106.31 L 424.86 111.05 Z M 400.69 107.12 L 395.95 111.86 L 399.71 111.86 L 399.71 113 L 394 113 L 394 107.29 L 395.14 107.29 L 395.14 111.05 L 399.88 106.31 Z M 426 81 L 426 86.71 L 424.86 86.71 L 424.86 82.95 L 420.12 87.69 L 419.31 86.88 L 424.05 82.14 L 420.29 82.14 L 420.29 81 Z M 395.95 82.14 L 400.69 86.88 L 399.88 87.69 L 395.14 82.95 L 395.14 86.71 L 394 86.71 L 394 81 L 399.71 81 L 399.71 82.14 Z M 416.1 104.81 C 415.44 105.48 413.22 106.17 409.75 106.17 C 405.52 106.17 403.16 105.13 403.14 104.43 L 403.14 91.2 C 404.63 92.08 407.33 92.43 409.75 92.43 C 412.15 92.43 414.81 92.08 416.29 91.21 L 416.29 104.44 C 416.29 104.49 416.29 104.61 416.1 104.81 Z M 409.75 87.86 C 413.24 87.86 415.45 88.53 416.11 89.2 C 416.29 89.39 416.29 89.52 416.29 89.56 L 416.29 89.57 C 416.29 90.27 413.96 91.29 409.75 91.29 C 405.67 91.29 403.16 90.29 403.14 89.58 C 403.18 88.85 405.48 87.86 409.75 87.86 Z M 417.43 89.57 C 417.43 89.28 417.35 88.84 416.92 88.4 C 415.89 87.35 413.21 86.71 409.75 86.71 C 406.21 86.71 402.05 87.46 402 89.57 L 402 104.44 C 402.05 106.56 406.21 107.31 409.75 107.31 C 412.41 107.31 415.69 106.86 416.92 105.6 C 417.35 105.16 417.44 104.71 417.43 104.43 L 417.43 89.58 L 417.43 89.57 L 417.43 89.57 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 410px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EBS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="410" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EBS
                </text>
            </switch>
        </g>
        <path d="M 240 197 L 240 127 Q 240 117 230 117 L 206.37 117" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 201.12 117 L 208.12 113.5 L 206.37 117 L 208.12 120.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 240 197 L 240 127 Q 240 117 250 117 L 273.63 117" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 278.88 117 L 271.88 120.5 L 273.63 117 L 271.88 113.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <image x="219.5" y="196.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,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"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 244px; margin-left: 240px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                params
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="256" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    params
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>