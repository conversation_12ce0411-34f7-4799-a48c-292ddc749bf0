<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="561px" height="361px" viewBox="-0.5 -0.5 561 361" content="&lt;mxfile&gt;&lt;diagram name=&quot;241001&quot; id=&quot;Q4b1qZdkdNrcMcNiyktG&quot;&gt;7VrbUuM4EP0aP0L5GtuPviTs1DC7zLBV7O5LSrFlxzWK5ZEVEvbrV7KlxLZMIAMEZiEEkI5aF3erW0edaFa02l4QUC2/4BQizdTTrWbFmmm6rsn+cuCuBRzPaIGcFGkLdYDr4l8oQF2g6yKFdU+QYoxoUfXBBJclTGgPA4TgTV8sw6g/awVyqADXCUAqelOkdNminunu8d9gkS/lzMbEb1tWQAqLJ6mXIMWbDmRNNSsiGNO2tNpGEHHdSb18LegaX51NppUZ+T8+J1lEyVk72OyYLrtHILCkzzu0MO4tQGuhrwjhdTojmM3UPjW9k6qsv0OacJ3omhVWuChpYxsnZG82d9T+Okw04si56YyAY5irgoYqxv4ZYzMMwTHMVUFDFeM1ueo+OIa5jrriYW9jpLcx6M3eVojXFBUljHZewHWcMStEGGHS6N9iPzNu2zAnIC1gr823nXhmdtrigrCBClyy9hITvpfDrECo08cJLD10GF5Tgr/DTkvWvFhLCuolTMVybiGhBfOsS7CA6ArXhRh+gSnFq45AgIqcN1BcMRSIWsJWBdkE4ZKuEKsb4glFzDBMWRc7jk8J6qpVR1Zs+TpC5oQVb1xtcx6uzsGmts8JrPGaJPBTwtcTsmpb6kslfGdnzc62wke6lHA9/mBw2wkowsUuIF5BSu6YiGid6CJaiHBp2qK+2QcfCS07cUdiQIS7fDfy3qVZQXj1ER5uKR4+ja4/XPsXcu2Z6011+zjXjnUnMtx349owqU/o08bklX3aVnw6WBNMwIdb/0JubccuazzOrdnLDv1349ag3dWn82zLfGXPdhQXhim7zogq3xQ4xyVA0z3aNQ5TD7n7S1iiqfzNK+eOrMbbbmN8161dQVKwp+AGb8DjlN7a84CcuHBRQHJ4aDxBWPhzHzQhgQjQ4rZ/zXt2g0yeZpCX0eHz6UZ0veJHwwG3GO731oii117DASHgriMmTpx753HMwTz64Ar7kLytDwzcrmBv7p1Ofn4HeMphq01dLZhqgcELvq55E23qaR4rqO4rox9IKA/V4WZZUHhdgcbMGxbq+vsl7ZwBENT8ppLgVZHI44UdB3+wzgXlGjB0fXcUDFE0iPi7UP7gkaAE//Ez4qVCsmH3bWx6rxySfZVs3bALlN4kShR7j5IshWANyZVCrPqkSiEsQ7KiEJU+ZVJY1ZB6KfysT+EUFjWkWgofO0iShmSoxOUgbI55yUPEg/EHCthc8vhqLAHJ9Ba2BmllEAJVXSx2vZi7rUnNQuU3WLeD6/dxlpzgddUsf4yrNK1zVpw3eYY5QHSMp+0YYZfZCQUcJmMIZs2I3NHL/LKpxdYh0tmjgt0w8lK+aw181xihU9ZkxHl34LN7r0xD98K3pXmW5k9FwYubgqmFnkB4ZG8KoSVlPClj9ZsYEkskkt2ZgieN+ReElfIm38SbDM0LRS/PF4XAlIWZHFDKhP7/lw16j2SDj2U8Ygeesejjytz+z7IgKYKzrIZU2YLHcYpF9uXrn/98Kz7lP5ILlH22fgfWmbopg8vw8O39SddNEWAGt0k7Nt0gUKKUEH5zF8lhQDfuC9SgqhBbC1/iHGGQzhcAgTJpFnCqm6StvzJtcZUt1hCWG8C3lBKeYmbsBQZEJTQfKaSXSyF9qPNNZeRmlm17R36GFkaG5UzeTUau4bYbHkPm5gtGU3v4KdoJL4GjB7ahxMU3kAbqkqdDNOOZ00WnzvLY5gmyPIbco8o9YXcZ8HbXg0Ch7JLN+01yyA+1wOI5oWCi+U/MIL5lEv/YlK55NIk3fP8VSTyr7r/W04rvvxtlTf8D&lt;/diagram&gt;&lt;diagram id=&quot;7VhTTWeUjBOK4oORqccR&quot; name=&quot;240910&quot;&gt;7Vldc5s4FP01PDbDh8HwaMBud6fbzdTbze5TRgYZa4uRR8ixvb9+r0DCgLCTbJw2bZI4E+no6oN77zmSsOFE6/17hjar32iKc8M2073hxIZtW5btwj+BHGrE9a0ayBhJpdERmJN/sQRNiW5JisuOIac052TTBRNaFDjhHQwxRnddsyXNu7NuUIY1YJ6gXEdvSMpXNerb4yP+AZNspWa2vKBuWSNlLJ+kXKGU7lqQMzWciFHK69J6H+FcOE/5pe43O9HaLIzhgj+kQ/YJW//cfJl9/qP89GsS7harP3fvLDnMHcq38omjnG7TGaMwar1uflDOKL9inoinMg0n3FBS8Mq7bggfmDyq/1wwjQRyJYKvgUPYWAct3Qz+WUMz9MEhbKyDlm4mamrVXXAIG7v6ivu9rYHeVq83fJyQbnlOChw1eSx8vIQoRDSnrPK/A78zEdwwYygluNMWjNx4ZrfaYsJgIEILaC8oE9kYLkmet/q4E8cMXcBLzuhX3GpZVj/QkqJyhVO5nDvMOAFufEQLnF/TksjhF5Rzum4ZTHKSiQZON4AiWUtgVRgmCFd8nUPdkk8oWW/Zqi4zTkyJyk3tjiXZi3WEQKONaFzvM6E4V2hXjq4YLumWJfiXRKwnhGpd6lolIrOXVWY7oU4fRQV4BrxvQZJO7zFdY84OYCJbPVNSW2qbPZL13VEpFLRqiYTCkNSmrBn5SF8oSAY/hs22xuZpNH+j8Q9E49nYn5qjx9E4Nt3IGr8aGuOkfB7+Wt735q+j8XeyZZShNwr/QBQexWNofByF4WcUBq+GwqjO6mdhsWN/bxaPNLriFC4VsioSgGa0QPn0iLYDAa5gh7+k16vK36Jy5apqvG83xod27RozAo8hgluBJx1ch+ncY3jyuoVYhvkDTh3iIc8GjOEccXLXvVld3v3u09z/dI9dzhGy67UQ+DMZ30/lOmSy19GdE8bQoWUm942T87h2dx7b690w77FX6zpGs17BMbaNT54Qbm/4BnuDxAZpe7k47C8YlDJRikFFFxSx9G1L/YZb6ps7X9QJZeaMRv4j3xWEkeW43qs5oVTvCnZCQ27ty5xTRi/ubcFYU05jOjYmU2NiiUJgGr5nTH3Dh4K+qSrnooSLTAh3K8LxfIOqLXIHnuwGL22lGEal0OKErkmishey7XfoTLhwj2WaTab10byXUE2m3JtxWm4Np+AFYt3cJF9MrH39ZnkzB6DaK7XYDuq1ptV9ndY0uqvPmvb1dU/TvK76agLdV3FN6ru7gSbIfdXWpP2s3vZ1taBF7yQ5xIj7NAykiCOYi8kxqkhgNr3DdUBqmzxHm5Isml5ArS0r4UT5GZf14OYp+csY3W6q5Q/JXtV6C8XbSgBvUXV80iS/2Vzam4R0wHldz/GyGlGQusg+VrXYObd/dXaVtmRcgKdOj6fWwN2xuU+2iWr7z8bUYECVHcN3jGAqC35cFWwj9CUiBLsqhI6y8ZWN020CJFZIpLqH+kG5arIMP5S9/EAWJrYqzNSAyiYMfo6b7/ihN9+HXvhkur0DVal7/N8boDKhy2WJuZZsF7hPqWzX869JMr9Ju4mWCipLguosEYTGxBFHiIlnBN7PkRwPfi1iXfptwAldgerxW+Q6DY7fxTvT/wA=&lt;/diagram&gt;&lt;diagram id=&quot;ro4P5XoElkqycRmznk5o&quot; name=&quot;240904&quot;&gt;7VnbcqM4EP0aXlOAwIZHbp7Zqtna1OYhu08pGWSsGhm5hBzb+/XbAoEBkcykJpdJJY6TqE9LSHSrz0G2hZLd6YvA++2fvCDMcu3iZKHUct0QBfBXAecW8Gy/BUpBixZyLsAN/Y9o0NbogRakHnWUnDNJ92Mw51VFcjnCsBD8OO624Ww86x6XxABucsxM9JYWctuigbu84F8JLbfdzM4ibD073HXWd1JvccGPAwhlFkoE57Jt7U4JYSp2XVzacasHvP3CBKnkzwxw2wH3mB30vSWMH4qV4DC+XaE8d7ddfycyV+u3LRTvOa1kE0c/hjdMk7S/PnRNFHLl+jPgHLY0QcfsBv+cuRmm4By2NEHH7KasbtVjcA5b+uaKp6OdmdHOZDS8UcwPktGKJP2OVTHeQBYSzrho4o/gZ6XSGJcCF5SMfKHnpyt34EupgAtRXoG/4kLtu3hDGRuM8SNkxz7gtRT8Oxl4Ns0LPAWut6TQy7knQlKogm94Tdg1r6m+/JpLyXeDDhGjpXJIvgcUayuHVRGYIN7KHQPb0Xeo69txO1vvODUlrvdtODb0pNYRQ8HslXN3KhW1XOFj7V0JUvODyMkfuVpPDGbbGvfK1c7eNDsbxWah6NpR90BOA0gXzhfCd0SKM3TpvAtdxOeJfbxwgqeh7YAOOgxrFir7K18KFRq6VufrdmEUJymAorSp0s1LXmGWXdBh2OGmxfkfHePG+FcZV35npqehMz0PrWsiKCxYpbIBHwxlm5QWQpqmsSiJ7qU5Xy380XALwrCk92Pu/ZXgIYP0suTmk+3eEdutlkFme09ju9T2E2f5YdiO5PXL0FzweiwXvDeW834jlvMMlosOggv8SXTviOi8dAnOpxEdvLw4/DBEh9td/SJc17PYK5CdP38Uu8WqON0FU0+tawGtUrVSyOCaY1F8lvMrlvNnOH8rdlwhzwueeOiNEwf5iw/Djs2h96g45M59Ho5Eb3jsXRocaWXICpAVOboRBB0SNQ3Xila6AZqYLa0wtiJkZYEVLazw3R2j3Vd4wNRDrxXXDbrwzaYm0shWP8NPJTCcSeDSirImgZAc2woWKjlwyAh8U9p0HeBcqqKNj1sqyc0eN9E5wqYfp6cYsAHBtZLNnO9o3hENEMNfMJhKlRfHtntSmKJsUvt9Uf+QHAwamGeLZyhLP3i7snQcI63R7Q0AzROMkcZZFTUUdKqehnKOVdNQpKkaGUo01kRDNqfaagjwWKMNmZxqqSG4j6rgVO0qXk3IZ27z/0hZQCAkhrmEvkaTCSKye9ImpO3DGN7XdN2Pgio6iBqo429Stxe3HxKlUvDDvln+nBg13jto3jWydIebh1pDiHvJH0q3DsDjasvIprmiqt+q/NZYKXrsqWKk9UN2eI7ThDsuyZnDBHJnSrL/iOWXatJ+SCvDrNPKtFPGYCqjMRrrqXtBdAOQtEOSbnhsHlQal2MFsR4VhJ0uu4ZAxxelfh5d7o2X1uXw7T74AfPyfWEryZcvXVH2Pw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 460 120 L 500 120 L 500 160 L 460 160 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 488.64 146.93 C 488.64 145.98 487.87 145.21 486.93 145.21 C 485.98 145.21 485.21 145.98 485.21 146.93 C 485.21 147.87 485.98 148.64 486.93 148.64 C 487.87 148.64 488.64 147.87 488.64 146.93 Z M 489.78 146.93 C 489.78 148.5 488.5 149.78 486.93 149.78 C 485.35 149.78 484.07 148.5 484.07 146.93 C 484.07 145.35 485.35 144.07 486.93 144.07 C 488.5 144.07 489.78 145.35 489.78 146.93 Z M 474.51 138.68 C 474.51 137.74 473.74 136.97 472.8 136.97 C 471.85 136.97 471.08 137.74 471.08 138.68 C 471.08 139.63 471.85 140.4 472.8 140.4 C 473.74 140.4 474.51 139.63 474.51 138.68 Z M 475.65 138.68 C 475.65 140.26 474.37 141.54 472.8 141.54 C 471.22 141.54 469.94 140.26 469.94 138.68 C 469.94 137.11 471.22 135.82 472.8 135.82 C 474.37 135.82 475.65 137.11 475.65 138.68 Z M 479.85 129.72 C 479.85 130.66 480.61 131.43 481.56 131.43 C 482.51 131.43 483.27 130.66 483.27 129.72 C 483.27 128.77 482.51 128 481.56 128 C 480.61 128 479.85 128.77 479.85 129.72 Z M 478.7 129.72 C 478.7 128.14 479.99 126.86 481.56 126.86 C 483.14 126.86 484.42 128.14 484.42 129.72 C 484.42 131.29 483.14 132.58 481.56 132.58 C 479.99 132.58 478.7 131.29 478.7 129.72 Z M 494.86 140 C 494.86 134.7 492.02 129.8 487.44 127.15 C 486.61 127.31 485.82 127.54 484.83 127.9 L 484.44 126.82 C 484.96 126.64 485.42 126.49 485.87 126.36 C 484.03 125.56 482.03 125.14 480 125.14 C 479.03 125.14 478.09 125.24 477.16 125.42 C 477.83 125.82 478.43 126.21 479 126.65 L 478.31 127.56 C 477.5 126.94 476.65 126.42 475.54 125.83 C 469.93 127.6 465.89 132.55 465.25 138.36 C 466.42 138.12 467.55 137.99 468.81 137.96 L 468.84 139.1 C 467.52 139.13 466.39 139.27 465.16 139.55 C 465.15 139.7 465.14 139.85 465.14 140 C 465.14 144.95 467.59 149.51 471.62 152.26 C 470.9 150.12 470.54 148.11 470.54 146.14 C 470.54 145.02 470.74 144.1 470.94 143.12 C 470.99 142.9 471.04 142.67 471.08 142.43 L 472.2 142.65 C 472.16 142.89 472.11 143.13 472.06 143.36 C 471.86 144.31 471.69 145.14 471.69 146.14 C 471.69 148.37 472.18 150.68 473.17 153.19 C 475.3 154.29 477.59 154.86 480 154.86 C 481.57 154.86 483.11 154.61 484.58 154.12 C 485.15 152.99 485.58 151.92 485.94 150.69 L 487.03 151 C 486.77 151.9 486.48 152.72 486.12 153.53 C 487.04 153.11 487.91 152.6 488.73 152 C 488.54 151.52 488.32 151.04 488.09 150.57 L 489.11 150.06 C 489.31 150.46 489.49 150.86 489.67 151.27 C 492.97 148.44 494.86 144.38 494.86 140 Z M 496 140 C 496 144.99 493.73 149.6 489.78 152.66 C 488.81 153.42 487.74 154.05 486.62 154.56 C 486.15 154.77 485.66 154.97 485.16 155.14 C 483.52 155.71 481.78 156 480 156 C 477.37 156 474.76 155.35 472.45 154.11 C 467.24 151.32 464 145.91 464 140 C 464 139.61 464.01 139.31 464.03 139.03 C 464.42 132.36 469 126.58 475.43 124.67 C 476.89 124.22 478.43 124 480 124 C 482.75 124 485.45 124.71 487.82 126.05 C 492.86 128.87 496 134.22 496 140 Z M 478.47 131.69 L 477.71 130.83 C 476.43 131.95 475.44 133.14 474.27 134.93 L 475.23 135.55 C 476.33 133.85 477.27 132.74 478.47 131.69 Z M 476.73 139.1 L 476.36 140.18 C 478.98 141.08 481.27 142.52 483.56 144.71 L 484.35 143.88 C 481.94 141.58 479.51 140.06 476.73 139.1 Z M 484.37 132.38 C 486.51 135.65 487.72 139.24 487.96 143.05 L 486.82 143.12 C 486.59 139.51 485.45 136.11 483.41 133.01 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudFront
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudF...
                </text>
            </switch>
        </g>
        <path d="M 460 40 L 500 40 L 500 80 L 460 80 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 493.18 64.35 L 488.69 61.66 L 488.69 55.24 C 488.69 55.05 488.59 54.86 488.42 54.76 L 481.96 51 L 481.96 45.57 L 493.18 52.2 Z M 494.03 51.4 L 481.69 44.1 C 481.51 44 481.3 44 481.12 44.1 C 480.95 44.2 480.84 44.39 480.84 44.59 L 480.84 51.32 C 480.84 51.52 480.95 51.7 481.12 51.8 L 487.57 55.57 L 487.57 61.98 C 487.57 62.17 487.68 62.36 487.85 62.46 L 493.45 65.82 C 493.54 65.88 493.64 65.9 493.74 65.9 C 493.84 65.9 493.93 65.88 494.02 65.83 C 494.2 65.73 494.3 65.54 494.3 65.34 L 494.3 51.88 C 494.3 51.68 494.2 51.5 494.03 51.4 Z M 479.97 74.8 L 466.82 67.81 L 466.82 52.2 L 478.04 45.57 L 478.04 51.01 L 472.13 54.77 C 471.96 54.87 471.87 55.05 471.87 55.24 L 471.87 64.78 C 471.87 64.99 471.98 65.18 472.17 65.28 L 479.71 69.21 C 479.88 69.29 480.07 69.29 480.23 69.21 L 487.55 65.42 L 492.06 68.13 Z M 493.47 67.67 L 487.86 64.3 C 487.69 64.2 487.49 64.19 487.32 64.28 L 479.97 68.08 L 472.99 64.44 L 472.99 55.55 L 478.9 51.79 C 479.06 51.69 479.16 51.51 479.16 51.32 L 479.16 44.59 C 479.16 44.39 479.05 44.2 478.88 44.1 C 478.7 44 478.49 44 478.31 44.1 L 465.97 51.4 C 465.8 51.5 465.7 51.68 465.7 51.88 L 465.7 68.15 C 465.7 68.35 465.81 68.54 465.99 68.64 L 479.71 75.93 C 479.79 75.98 479.88 76 479.97 76 C 480.07 76 480.16 75.98 480.25 75.93 L 493.45 68.64 C 493.63 68.54 493.74 68.36 493.74 68.16 C 493.75 67.96 493.64 67.77 493.47 67.67 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 87px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECS
                </text>
            </switch>
        </g>
        <path d="M 460 200 L 500 200 L 500 240 L 460 240 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 482.81 207.89 L 481.15 207.89 L 481.15 206.77 L 482.81 206.77 L 482.81 205.1 L 483.92 205.1 L 483.92 206.77 L 485.58 206.77 L 485.58 207.89 L 483.92 207.89 L 483.92 209.56 L 482.81 209.56 Z M 489.45 214.01 L 487.79 214.01 L 487.79 212.9 L 489.45 212.9 L 489.45 211.23 L 490.55 211.23 L 490.55 212.9 L 492.21 212.9 L 492.21 214.01 L 490.55 214.01 L 490.55 215.68 L 489.45 215.68 Z M 486.57 231.6 C 485.51 228.92 483.07 226.47 480.4 225.4 C 483.07 224.34 485.51 221.89 486.57 219.2 C 487.62 221.89 490.06 224.34 492.73 225.4 C 490.06 226.47 487.62 228.92 486.57 231.6 Z M 495.45 224.85 C 491.56 224.85 487.12 220.38 487.12 216.47 C 487.12 216.16 486.87 215.91 486.57 215.91 C 486.26 215.91 486.01 216.16 486.01 216.47 C 486.01 220.38 481.58 224.85 477.68 224.85 C 477.38 224.85 477.13 225.1 477.13 225.4 C 477.13 225.71 477.38 225.96 477.68 225.96 C 481.58 225.96 486.01 230.42 486.01 234.34 C 486.01 234.65 486.26 234.9 486.57 234.9 C 486.87 234.9 487.12 234.65 487.12 234.34 C 487.12 230.42 491.56 225.96 495.45 225.96 C 495.75 225.96 496 225.71 496 225.4 C 496 225.1 495.75 224.85 495.45 224.85 Z M 465.11 213.88 C 466.72 215.06 469.85 215.68 472.85 215.68 C 475.85 215.68 478.99 215.06 480.6 213.88 L 480.6 219.21 C 479.8 220.28 476.87 221.33 472.96 221.33 C 468.47 221.33 465.11 219.91 465.11 218.65 Z M 472.85 209.56 C 477.65 209.56 480.6 211.02 480.6 212.06 C 480.6 213.11 477.65 214.57 472.85 214.57 C 468.05 214.57 465.11 213.11 465.11 212.06 C 465.11 211.02 468.05 209.56 472.85 209.56 Z M 480.6 230.45 C 480.6 231.73 477.28 233.17 472.85 233.17 C 468.42 233.17 465.11 231.73 465.11 230.45 L 465.11 226.89 C 466.74 228.14 469.93 228.8 472.99 228.8 C 475.12 228.8 477.18 228.49 478.79 227.94 L 478.44 226.89 C 476.94 227.4 475.01 227.68 472.99 227.68 C 468.48 227.68 465.11 226.27 465.11 225.01 L 465.11 220.54 C 466.73 221.78 469.91 222.44 472.96 222.44 C 476.23 222.44 479.03 221.76 480.6 220.69 L 480.6 222.36 L 481.7 222.36 L 481.7 212.06 C 481.7 209.71 477.14 208.44 472.85 208.44 C 468.73 208.44 464.38 209.61 464.03 211.78 L 464 211.78 L 464 230.45 C 464 232.94 468.56 234.28 472.85 234.28 C 477.14 234.28 481.7 232.94 481.7 230.45 L 481.7 228.48 L 480.6 228.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 247px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Aurora
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="259" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Aurora
                </text>
            </switch>
        </g>
        <path d="M 300 180 L 370 180 Q 380 180 380 170 L 380 70 Q 380 60 390 60 L 453.63 60" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 458.88 60 L 451.88 63.5 L 453.63 60 L 451.88 56.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 300 180 L 370 180 Q 380 180 380 190 L 380 210 Q 380 220 390 220 L 453.63 220" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 458.88 220 L 451.88 223.5 L 453.63 220 L 451.88 216.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 0 200 C 0 184 0 176 20 176 C 6.67 176 6.67 160 20 160 C 33.33 160 33.33 176 20 176 C 40 176 40 184 40 200 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 207px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                管理者
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="20" y="219" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    管理者
                </text>
            </switch>
        </g>
        <path d="M 200 0 L 560 0 L 560 360 L 200 360 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 206.09 7.18 C 206.01 7.18 205.93 7.19 205.85 7.19 C 205.5 7.19 205.15 7.23 204.81 7.32 C 204.53 7.39 204.25 7.49 203.98 7.62 C 203.9 7.65 203.84 7.7 203.79 7.76 C 203.75 7.83 203.74 7.91 203.74 7.99 L 203.74 8.32 C 203.74 8.46 203.78 8.53 203.88 8.53 L 203.99 8.53 L 204.22 8.44 C 204.45 8.35 204.69 8.27 204.94 8.21 C 205.17 8.16 205.41 8.13 205.65 8.13 C 206.04 8.09 206.43 8.2 206.74 8.44 C 206.97 8.74 207.09 9.12 207.05 9.5 L 207.05 9.99 C 206.78 9.93 206.54 9.88 206.29 9.84 C 206.05 9.81 205.81 9.79 205.57 9.79 C 204.98 9.76 204.4 9.94 203.94 10.31 C 203.54 10.65 203.32 11.15 203.34 11.68 C 203.31 12.15 203.49 12.62 203.82 12.96 C 204.18 13.29 204.66 13.46 205.15 13.44 C 205.91 13.45 206.63 13.11 207.11 12.51 C 207.18 12.66 207.24 12.79 207.31 12.91 C 207.38 13.02 207.46 13.12 207.55 13.21 C 207.6 13.27 207.67 13.31 207.75 13.31 C 207.81 13.31 207.87 13.29 207.92 13.25 L 208.34 12.97 C 208.41 12.93 208.46 12.86 208.47 12.77 C 208.47 12.72 208.45 12.67 208.42 12.62 C 208.34 12.47 208.26 12.31 208.21 12.14 C 208.15 11.95 208.12 11.75 208.13 11.55 L 208.14 9.37 C 208.2 8.77 208 8.18 207.59 7.74 C 207.17 7.39 206.64 7.19 206.09 7.18 Z M 219.89 7.19 C 219.78 7.19 219.68 7.19 219.57 7.2 C 219.29 7.2 219 7.24 218.73 7.31 C 218.47 7.38 218.23 7.5 218.01 7.66 C 217.82 7.81 217.66 7.99 217.54 8.21 C 217.42 8.43 217.35 8.67 217.36 8.92 C 217.36 9.27 217.48 9.61 217.69 9.89 C 217.97 10.22 218.34 10.46 218.76 10.56 L 219.72 10.87 C 219.97 10.93 220.2 11.05 220.39 11.22 C 220.51 11.35 220.58 11.51 220.57 11.69 C 220.58 11.94 220.45 12.18 220.23 12.31 C 219.93 12.48 219.6 12.56 219.26 12.54 C 218.99 12.54 218.72 12.51 218.46 12.45 C 218.22 12.4 217.98 12.32 217.75 12.22 L 217.59 12.15 C 217.54 12.14 217.5 12.14 217.46 12.15 C 217.36 12.15 217.31 12.22 217.31 12.36 L 217.31 12.69 C 217.31 12.76 217.32 12.82 217.35 12.89 C 217.4 12.97 217.47 13.03 217.56 13.07 C 217.8 13.19 218.06 13.28 218.32 13.34 C 218.66 13.41 219 13.45 219.35 13.45 L 219.33 13.46 C 219.66 13.45 219.98 13.4 220.29 13.3 C 220.55 13.22 220.8 13.09 221.01 12.92 C 221.21 12.77 221.38 12.57 221.49 12.34 C 221.61 12.1 221.67 11.83 221.66 11.56 C 221.67 11.23 221.56 10.9 221.36 10.63 C 221.09 10.32 220.73 10.09 220.33 9.99 L 219.39 9.69 C 219.13 9.61 218.88 9.49 218.67 9.32 C 218.54 9.2 218.47 9.03 218.47 8.85 C 218.46 8.61 218.58 8.38 218.79 8.25 C 219.06 8.11 219.36 8.05 219.67 8.06 C 220.11 8.06 220.55 8.14 220.96 8.32 C 221.04 8.37 221.12 8.4 221.21 8.41 C 221.31 8.41 221.36 8.34 221.36 8.19 L 221.36 7.88 C 221.37 7.8 221.35 7.72 221.31 7.66 C 221.25 7.59 221.18 7.54 221.11 7.49 L 220.83 7.38 L 220.45 7.27 L 220.01 7.2 C 219.97 7.2 219.93 7.19 219.89 7.19 Z M 216.02 7.36 C 215.94 7.35 215.86 7.38 215.79 7.42 C 215.72 7.5 215.68 7.59 215.66 7.69 L 214.51 12.14 L 213.47 7.71 C 213.45 7.61 213.41 7.52 213.34 7.44 C 213.26 7.39 213.17 7.37 213.07 7.38 L 212.54 7.38 C 212.44 7.37 212.35 7.39 212.27 7.44 C 212.2 7.51 212.15 7.61 212.14 7.71 L 211.09 12.14 L 209.97 7.7 C 209.95 7.6 209.91 7.51 209.84 7.44 C 209.76 7.39 209.67 7.36 209.58 7.37 L 208.92 7.37 C 208.81 7.37 208.76 7.43 208.76 7.54 C 208.77 7.63 208.79 7.72 208.82 7.81 L 210.38 12.95 C 210.4 13.05 210.45 13.14 210.52 13.21 C 210.6 13.26 210.69 13.29 210.78 13.28 L 211.36 13.26 C 211.46 13.27 211.55 13.25 211.63 13.19 C 211.7 13.12 211.74 13.03 211.76 12.93 L 212.79 8.64 L 213.82 12.93 C 213.83 13.03 213.88 13.12 213.95 13.19 C 214.03 13.25 214.12 13.27 214.21 13.26 L 214.78 13.26 C 214.88 13.27 214.97 13.25 215.04 13.2 C 215.11 13.13 215.16 13.03 215.18 12.94 L 216.79 7.79 C 216.84 7.72 216.84 7.63 216.84 7.63 C 216.84 7.59 216.84 7.56 216.84 7.52 C 216.84 7.48 216.82 7.43 216.79 7.4 C 216.76 7.37 216.72 7.35 216.67 7.36 L 216.05 7.36 C 216.04 7.36 216.03 7.36 216.02 7.36 Z M 205.65 10.62 C 205.7 10.62 205.75 10.62 205.8 10.62 L 206.43 10.62 C 206.64 10.64 206.85 10.67 207.06 10.71 L 207.06 11.01 C 207.07 11.21 207.05 11.4 207 11.59 C 206.96 11.75 206.88 11.9 206.77 12.01 C 206.61 12.21 206.39 12.36 206.14 12.44 C 205.91 12.52 205.67 12.56 205.43 12.56 C 205.18 12.6 204.93 12.53 204.73 12.37 C 204.55 12.18 204.46 11.92 204.49 11.66 C 204.47 11.36 204.59 11.08 204.81 10.89 C 205.06 10.72 205.35 10.62 205.65 10.62 Z M 221.04 14.72 C 220.34 14.73 219.51 14.89 218.88 15.33 C 218.69 15.46 218.72 15.63 218.94 15.63 C 219.64 15.54 221.21 15.35 221.5 15.71 C 221.78 16.06 221.19 17.54 220.94 18.21 C 220.86 18.41 221.03 18.49 221.21 18.34 C 222.39 17.36 222.72 15.3 222.46 15 C 222.32 14.85 221.74 14.71 221.04 14.72 Z M 202.65 15.1 C 202.5 15.12 202.42 15.3 202.58 15.44 C 205.29 17.89 208.82 19.23 212.48 19.21 C 215.37 19.22 218.2 18.36 220.59 16.74 C 220.95 16.47 220.63 16.07 220.26 16.23 C 217.87 17.24 215.3 17.76 212.71 17.77 C 209.23 17.78 205.82 16.87 202.81 15.14 C 202.75 15.11 202.69 15.1 202.65 15.1 Z M 200 0 L 225 0 L 225 25 L 200 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 7px; margin-left: 232px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS Cloud
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS Cloud
                </text>
            </switch>
        </g>
        <path d="M 40 180 L 253.63 180" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 258.88 180 L 251.88 183.5 L 253.63 180 L 251.88 176.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 120px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                マネジメントコンソール
                                <br/>
                                からアクセス
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="120" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    マネジメントコンソール
からアクセス
                </text>
            </switch>
        </g>
        <rect x="460" y="280" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 480 280 C 468.97 280 460 288.97 460 300 C 460 311.03 468.97 320 480 320 C 491.03 320 500 311.03 500 300 C 500 288.97 491.03 280 480 280 Z M 480 318.18 C 469.97 318.18 461.82 310.03 461.82 300 C 461.82 289.97 469.97 281.82 480 281.82 C 490.03 281.82 498.18 289.97 498.18 300 C 498.18 310.03 490.03 318.18 480 318.18 Z M 491.85 305.45 L 490.45 305.45 L 490.45 302.39 C 490.45 301.88 490.05 301.48 489.55 301.48 L 487.27 301.48 L 487.27 298.41 C 487.27 297.91 486.87 297.5 486.36 297.5 L 480.91 297.5 L 480.91 295.34 L 486.36 295.34 C 486.87 295.34 487.27 294.93 487.27 294.43 L 487.27 287.27 C 487.27 286.77 486.87 286.36 486.36 286.36 L 473.64 286.36 C 473.13 286.36 472.73 286.77 472.73 287.27 L 472.73 294.43 C 472.73 294.93 473.13 295.34 473.64 295.34 L 479.09 295.34 L 479.09 297.5 L 473.64 297.5 C 473.13 297.5 472.73 297.91 472.73 298.41 L 472.73 301.48 L 470.46 301.48 C 469.95 301.48 469.55 301.88 469.55 302.39 L 469.55 305.45 L 468.15 305.45 C 467.65 305.45 467.24 305.86 467.24 306.36 L 467.24 310.34 C 467.24 310.84 467.65 311.25 468.15 311.25 L 472.05 311.25 C 472.55 311.25 472.96 310.84 472.96 310.34 L 472.96 306.36 C 472.96 305.86 472.55 305.45 472.05 305.45 L 471.36 305.45 L 471.36 303.3 L 475.11 303.3 L 475.11 305.45 L 474.43 305.45 C 473.93 305.45 473.52 305.86 473.52 306.36 L 473.52 310.34 C 473.52 310.84 473.93 311.25 474.43 311.25 L 478.41 311.25 C 478.91 311.25 479.32 310.84 479.32 310.34 L 479.32 306.36 C 479.32 305.86 478.91 305.45 478.41 305.45 L 476.93 305.45 L 476.93 302.39 C 476.93 301.88 476.53 301.48 476.02 301.48 L 474.55 301.48 L 474.55 299.32 L 485.45 299.32 L 485.45 301.48 L 483.98 301.48 C 483.47 301.48 483.07 301.88 483.07 302.39 L 483.07 305.45 L 481.59 305.45 C 481.09 305.45 480.68 305.86 480.68 306.36 L 480.68 310.34 C 480.68 310.84 481.09 311.25 481.59 311.25 L 485.57 311.25 C 486.07 311.25 486.48 310.84 486.48 310.34 L 486.48 306.36 C 486.48 305.86 486.07 305.45 485.57 305.45 L 484.89 305.45 L 484.89 303.3 L 488.64 303.3 L 488.64 305.45 L 487.9 305.45 C 487.4 305.45 486.99 305.86 486.99 306.36 L 486.99 310.34 C 486.99 310.84 487.4 311.25 487.9 311.25 L 491.85 311.25 C 492.35 311.25 492.76 310.84 492.76 310.34 L 492.76 306.36 C 492.76 305.86 492.35 305.45 491.85 305.45 Z M 474.55 293.52 L 474.55 288.18 L 485.45 288.18 L 485.45 293.52 Z M 469.06 309.43 L 469.06 307.27 L 471.14 307.27 L 471.14 309.43 Z M 475.34 309.43 L 475.34 307.27 L 477.5 307.27 L 477.5 309.43 Z M 482.5 309.43 L 482.5 307.27 L 484.66 307.27 L 484.66 309.43 Z M 488.81 309.43 L 488.81 307.27 L 490.94 307.27 L 490.94 309.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 327px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="339" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
        <path d="M 260 160 L 300 160 L 300 200 L 260 200 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 288.43 184.18 C 288.43 181.96 286.62 180.16 284.38 180.16 C 282.15 180.16 280.34 181.96 280.34 184.18 C 280.34 186.4 282.15 188.21 284.38 188.21 C 286.62 188.21 288.43 186.4 288.43 184.18 M 289.58 184.18 C 289.58 187.03 287.25 189.34 284.38 189.34 C 281.52 189.34 279.19 187.03 279.19 184.18 C 279.19 181.34 281.52 179.03 284.38 179.03 C 287.25 179.03 289.58 181.34 289.58 184.18 M 294.37 191.95 L 290.39 188.4 C 290.07 188.84 289.7 189.26 289.28 189.62 L 293.25 193.18 C 293.59 193.48 294.12 193.46 294.43 193.12 C 294.73 192.78 294.7 192.26 294.37 191.95 M 284.38 190.36 C 287.81 190.36 290.6 187.59 290.6 184.18 C 290.6 180.78 287.81 178.01 284.38 178.01 C 280.96 178.01 278.17 180.78 278.17 184.18 C 278.17 187.59 280.96 190.36 284.38 190.36 M 295.27 193.88 C 294.89 194.31 294.35 194.52 293.81 194.52 C 293.34 194.52 292.87 194.36 292.49 194.02 L 288.36 190.32 C 287.21 191.06 285.85 191.49 284.38 191.49 C 280.33 191.49 277.03 188.21 277.03 184.18 C 277.03 180.15 280.33 176.87 284.38 176.87 C 288.44 176.87 291.74 180.15 291.74 184.18 C 291.74 185.34 291.47 186.43 290.98 187.4 L 295.13 191.11 C 295.93 191.84 296 193.08 295.27 193.88 M 269.27 174.32 C 269.27 174.61 269.28 174.9 269.32 175.19 C 269.34 175.35 269.29 175.51 269.18 175.63 C 269.09 175.73 268.98 175.79 268.85 175.82 C 267.45 176.18 265.14 177.27 265.14 180.53 C 265.14 183 266.51 184.36 267.66 185.07 C 268.05 185.32 268.52 185.45 269.01 185.45 L 275.89 185.46 L 275.88 186.59 L 269 186.58 C 268.29 186.58 267.62 186.39 267.06 186.03 C 265.92 185.33 264 183.68 264 180.53 C 264 176.74 266.61 175.34 268.14 174.84 C 268.13 174.67 268.13 174.49 268.13 174.32 C 268.13 171.22 270.24 168 273.05 166.84 C 276.33 165.48 279.8 166.15 282.34 168.65 C 283.13 169.43 283.78 170.37 284.28 171.45 C 284.94 170.9 285.77 170.6 286.63 170.6 C 288.34 170.6 290.25 171.89 290.6 174.7 C 292.19 175.06 295.56 176.34 295.56 180.58 C 295.56 182.27 295.03 183.67 293.98 184.73 L 293.16 183.93 C 294 183.09 294.42 181.96 294.42 180.58 C 294.42 176.87 291.3 175.95 289.95 175.72 C 289.8 175.7 289.67 175.61 289.58 175.48 C 289.49 175.36 289.46 175.21 289.49 175.07 C 289.3 172.77 287.93 171.73 286.63 171.73 C 285.82 171.73 285.05 172.13 284.54 172.82 C 284.41 172.98 284.2 173.07 283.99 173.04 C 283.79 173.01 283.61 172.87 283.54 172.67 C 283.08 171.38 282.4 170.3 281.54 169.46 C 279.34 167.29 276.33 166.71 273.49 167.89 C 271.12 168.87 269.27 171.69 269.27 174.32" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 207px; margin-left: 280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudWatch
                                <br/>
                                Dashboard
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="219" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudW...
                </text>
            </switch>
        </g>
        <path d="M 300 180 L 370 180 Q 380 180 380 190 L 380 290 Q 380 300 390 300 L 453.63 300" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 458.88 300 L 451.88 303.5 L 453.63 300 L 451.88 296.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 300 180 L 370 180 Q 380 180 380 170 L 380 150 Q 380 140 390 140 L 453.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 458.88 140 L 451.88 143.5 L 453.63 140 L 451.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 180px; margin-left: 380px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                メトリクスを監視
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="380" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    メトリクスを監視
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>