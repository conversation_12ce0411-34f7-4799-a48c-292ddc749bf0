<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="502px" height="232px" viewBox="-0.5 -0.5 502 232" content="&lt;mxfile&gt;&lt;diagram id=&quot;rxyWzuQO3aeQyH_nDuKp&quot; name=&quot;Page-1&quot;&gt;zVZdb5swFP01lraHRoBLGh4hod3DvqRuavvogANeAUfGNEl//a7hEqAkUqJl66Iqvfdcf55zHJvQeb69U2ydfpExz4hjxVtCF8Rxpp4D3wbYNYDjXl83SKJE3GB2B9yLV46ghWglYl4OGmopMy3WQzCSRcEjPcCYUnIzbLaS2XDWNUv4CLiPWDZGH0Ss0wadOTcd/omLJG1ntqdeU1my6DlRsipwPuLQVf1pyjlrx8KNlimL5aYH0ZDQuZJSN1G+nfPMcNvS1vS7PVLdr1vxQp/UwZ7iQvSu3TyPgQtMC1nAvyDVeQaZDSHfCv3Yi58gtiYuZgtjAatNdpiMV4ULLWWlopZzm6LSTCV8zywaxyyq1xV3c8dlzrXaQQPFM6bFy1BAhj5I9u06LiBAOo5Rg5q+sKzCYVcsK/lBwj6zJZyBAVMsE0kBcQS75gqAF660AJP5WMhFHJsxAsVL8cqW9XiGr7UUha6X7gbEXQCykoXGYwI0HWS0VdLMwreHjgnOMLDagEPsZU0c6mJXPMFXmJ7MMg7+3eykG/mq1bjtIlerErR+K8t+TScqdXOuiQsY/LFzrkmfWueapPNxnV3CyO67Gbn9sekZWavqv/XxzaV8fGVNqHVZH1/Yt3QkDC/M7vxKy+Y6cqYZEBMsFUSJiT6YWgk1USQkpGRmEz8k4Yz48LcgoWtSz69LlHguBr5XBw4JrI8j4VUq82UFWws2qdD8fs1qL2/gch9a4aj/R2IdFcXx3ghiO/ha2HQ37f5+THu37Mw6+yz8LLn6tvxlngiOlTXWNl0jFqX8KzxbfuzWfCHKszifEm9OfL+m+pYEoQmCWwCRWGj13JtmoumkzJlR3hmIf93jCZ3R0+TvK0HHUtgHpGhPUF+K6flSQNpT43R1wvMOxB9q4r63JrOhJF57D/8bRQDq3p7NL1r3wKfhbw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);">
    <defs/>
    <g>
        <path d="M 220 40 L 293.63 40" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 298.88 40 L 291.88 43.5 L 293.63 40 L 291.88 36.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 42px; margin-left: 257px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                false
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="257" y="45" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle">
                    false
                </text>
            </switch>
        </g>
        <path d="M 109.85 79.94 L 109.52 164.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 109.5 169.88 L 106.03 162.87 L 109.52 164.63 L 113.03 162.9 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 112px; margin-left: 109px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                true
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="109" y="116" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle">
                    true
                </text>
            </switch>
        </g>
        <path d="M 110 0 L 220 40 L 110 80 L 0 40 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 40px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                enableAutoScale
                                <br/>
                                (Autoscalingの設定フラグ)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="110" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    enableAutoScale...
                </text>
            </switch>
        </g>
        <a xlink:href="cache.t3.small">
            <rect x="300" y="10" width="201" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 199px; height: 1px; padding-top: 40px; margin-left: 301px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    cacheNodeTypeDisableAutoScale
                                    <br/>
                                    (Autoscaling未対応)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="401" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                        cacheNodeTypeDisableAutoScale...
                    </text>
                </switch>
            </g>
        </a>
        <a xlink:href="cache.t3.small">
            <rect x="9" y="171" width="201" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 199px; height: 1px; padding-top: 201px; margin-left: 10px;">
                            <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                                <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                    cacheNodeTypeEnableAutoScale
                                    <br/>
                                    (Autoscaling対応)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="110" y="205" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                        cacheNodeTypeEnableAutoScale...
                    </text>
                </switch>
            </g>
        </a>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>