<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="481px" height="178px" viewBox="-0.5 -0.5 481 178" content="&lt;mxfile&gt;&lt;diagram name=&quot;240828&quot; id=&quot;8lhTI8-DoGhHDNCJjHgO&quot;&gt;7VhRb9owEP41kbYHqjgOhD4mKbSbOqlapTHtpTKJCVZNHDmm0P36nYkDCU5RWUu3aquosL+7OOf7fHc+HBwv1peSFPMvIqXc8dx07eALx/MQ8vrwpZHHCgmC8wrIJEuN0g64ZT+pAV2DLllKy5aiEoIrVrTBROQ5TVQLI1KKVVttJnj7rQXJqAXcJoTb6ISlal6hQy/Y4VeUZfP6zWhg9rcgtbLZSTknqVg1IDxycCyFUNVosY4p186r/TIZfe6lN5ffbuTk648r5X73xne9arHxMY9styBprl536WG19APhS+OvFZn1CJ/2SkWSe7Nx9Vh7czVnit4WJNHzFZwYB0dzteAwQzCcMc5jwYWEeS5yUIo4mVJ+I0qmmMgBTmAPFOTRA5WKAU/XewpK6EUJZ1mnemgEU6GUWIDgmc4xTtTL0HXjaBhnXVKxoEo+goqRYtfwbg4+Gpr5aneMvBqbN4/QwIDEHN1su/aOHhgYho5gy+tkCyKnVHIJsfNytqRY5ilNjcL75c5rc7eN4SZ3fhd37qm4wxZ3zgg7Q+yEF5uB54Qjg0TYIJFrUUpTyG5majhrMHqcP0uxlAk9oGdSvyIyo4fWG1R62raD7EjKiWIP7cz8Ek/73U4FX9aDaGycOsT1AP2WU/c2d1ov4zfy3yF7Gk6dhGPHG3AwLppKGGV69GFCp6AWxtcfLYeW91QlOs5cUCwEy9XGyH4EHzjTcfXfB9VYI2f6kmGBXVhgg8hWgy/U9YZ9sAsLbBDZanpWW90Gu7Cgb1u8/zTqeBrtPQ0fHIml4iyn8fa+pH08E7mqU7kD6Q97Y814lEmSMtqSjft+4J83ZBdMwkJVAs+F1PmxVRrgmThAGMGZiqDQiHvakMw2fyBJSTnfFA73QH3YJv79ivBE4WgWK9ihuV0ir56bE6dfScqicseMrbUdEVzXCi1crDN9sz0jq9I/k7QKx0+JtieCaTVqa0FVPWVxGuwVJ98uTl21yT9VaRpYAX8LCciNlgmEcVfgk9m1yCrx/9h/T7E/cEOMg+NiHzo+aIr+mdgv8QlD3x/+udBHVpTTpOyRonhvrZ7l7ucy9SQt6BmtHvI6eHmVVs/u6nT//e67uhPQ9IyurrMhf42mzu7fwuvocPF7UbY21O0lY//CC8LQSsZG+a/Lw5vyT+XogVa3APRUboY0xMEWbeIdFyS9mxJO8mRjwMmOk/9myRimu98JN7LGr6149As=&lt;/diagram&gt;&lt;diagram name=&quot;240827&quot; id=&quot;jIyY93TUZ7x5-tAiMjUD&quot;&gt;7VhZb+M2EP41BNqHBJKoy4+SfPRIgGC9bXb7UtASLRGRRJeiYzu/focSZetw07pI0l1sAgXmfDMckXNxRISjYr8QZJPd8oTmyDKSPcJTZFmmaTnwo5BDg3jepAFSwRItdAKW7Ilq0NDoliW06glKznPJNn0w5mVJY9nDiBB81xdb87z/1g1J6QhYxiQfo/cskVmD+pZ3wn+iLM3aN5uu3l9BWmG9kyojCd91IDxDOBKcy2ZU7COaK+O1dim2v/+1lrvb8Ddj8UeKP38MVh+uGmXzS6YctyBoKV9WtdWofiT5VttrR9ZX4ItKii14o9m4PLTW3GVM0uWGxIreQcQgHGayyIEyYbhmeR7xnAugS16CUCj4tkxoogVysqL5Ha+YZLwELIYdUZAOH6mQDLx2MxCQXL2C5Cw9Kx5oxopLyQtg/EtTaZMqNXTfCRRtugXlBZXiACKai9so0GlwjIrdKagsV2NZJ6COgkQHcnrUfXIWDLS/LvAdHvkOzTDyMQqm9cBCwUwjIdZIaIxcShPIF01qn3U8epk9K74VMX1GThcTSURKn9PnNnJqbc96R9CcSPbYz/UXt7Q9snRIKhajmY+CAPnTeuCjMKqtbKLAQzMHhXPkOzXLVaBmBchyc9h6uBIwStWodZvbeitqvdVFHDQx6ncBYrQKMZqYrfCk1TPv64EHWLZaz2Sq9EB8TPxn44ALmfGUlySfndBuWEA0iMMnIIyW+KyIa6clp/suc3roUndUMHCMSuQa/J9izHvpGKunBkKQQ0dgw1kpq47mOwWcCottDgqLOyjj/yCPfWMQ380KTtF+3Mp/TwBnlAD3EGWjQP7hnq5ALIhufhyFV/VAZZxpj2ujAMMJ4YEdRM2/A6KRQq5V3zECz2HeGDTHYvBjnnvDEDyHeWPQHIspql11HzyHec54xcPZ5pnZ5mA2PDjkW5mzkkbHFkrZeM1L2Z7FCM4vbM2Vx8NUkITRHm/u2J496fCmTICi5gQuVTEYnO0wJ/JMbELQhdAp8Afa4azrP+AkpMrqk9945oA/ntzDI/1vTv5utwE71A2nabW0jjj1SlJtGnOs2V6tI4QObqOYxT5Vze412VX2taBNRfk5VusJgWxGfSloi16zu3AHRcAedxf2mebCfq3ewh0l/BLaDSPcxpDG5xKfrG942rDfc/9byn3XCDD2Lst9+AiE76TvJvcr/Iqpb/tfWep749Rf3p5J+TsiSNPFWcZSckHf8/4t8/7dnF9VGZ1j2/aty8poGJnY+Y7K6KGStKj+LEhJ0noh/buht6qwx2brDSpsVd1+LO4Xyadfow9P6S+PTwvb1fd0o0u3nG+TtQAHXFWSxA8vcPf27d62GX2Xmf7YZUeZ3m2bf7nTgDxd4jZfzKercDz7Ag==&lt;/diagram&gt;&lt;diagram id=&quot;sn1t80STWCxECPuZ5oly&quot; name=&quot;240816&quot;&gt;7Vjrj6M2EP9rLLUfbgWYVz4CSa6V9qSV8mHbT5UXHLAOcGScTdK/vmMw4WF2e4/cXavbiCie34zH43l4HBBOqvN7QQ7FB57REjlWdkZ4jRzHth0PfhRy6RDPdjogFyzTQgOwY39TDVoaPbKMNhNByXkp2WEKpryuaSonGBGCn6Zie15OVz2QnBrALiWliT6yTBYdGjrBgP9GWV70K9v+quNUpBfWO2kKkvHTCMIbhBPBuexG1TmhpXJe75du3vYF7tUwQWv5KRN0IJ5JedR7O5H9O/BbI8URPNcZKS/9zk8Fk3R3IKmiTxBdhONCViVQNgz3rCwTXnIBdM1rEIoFP9YZzbRASZ5o+cAbJhmvAUvBTgrS8TMVkoGH72cCkqslSMnyRfFIM564lLwChukA7RM1g55HkHbIe8orKsUFRDQX98HR2XkN1mmIteNrrBjF+SpIdH7lV91DCGCgo7AcEdeICNpgFGIUrduBg6KNRmKskdgyAkUzyFhN6kiM4vSilxp+FKlWoWtSEpFTLYU7SCl/1ZOClkSy52m5fI1XAsMrMWlYijYhiiIUrttBiOKk9YiNogBtPBRvUei1LF+BmhUhxy9hQ/GTgFGuRr2L/d6zSe/ZMeKhldWuBYjVK8RoZffCq17PdqoHHmC5yp7VWumBWK7CV2PGhSx4zmtSbgZ0HEKInLj8AYTVE38q4s7ryfV5zFxfxtQDFQxioEqpBb80H/xb50M7NRKCXEYCB85q2Yw0PyhgKFjXnhWsPzv0/kUeh9YsFzsLhsy8buWTktUxkvURMsJIul8e6ROIRcn9r0YqNB+pTAsdHb1/YHgxPGBs0n09EE0UcqcOcgNcwgITtE0x+LGXVpiDS1hggrYppqje6im4hAWeafF8tr0w257NhgfH/ChLVtPkejlQPt7zWvadC0ELwM5WBTfOBckYnfC2nhu4qxFvzQQo6vpVrQp31glhThLY2Ib8iqGv8o90xNm3H+BkpCnaPmm90g6vfW7eAF/ok+PeDDvUVym4bmlaZ5xakjSHzh17dlZ2xHA3OShmdc7VNe6OnBr3TtDuSPg9VfbEQHajqRRcIm7Ui/1ZabtmL3YXWrF7g06MjTLeAWTFxxSKc6mcyf6e5x37raL/TxXtWxHGwedVtBMEcK//aSq6wbcpaDf8cQXtmwW9+7BQyA9EkO525Fg7yQV9q+bvWc1v7vxPHY5b7Lqh83mHY5zY2PuJDsdLI2nV/FWRmuStIdO3Ht/g3LxejG5/bgI5vIDq/v4Mr/Hw5h8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="200" y="17" width="280" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 14px; margin-left: 201px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                waf-alb-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    waf-alb-stack
                </text>
            </switch>
        </g>
        <rect x="220" y="57" width="240" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 54px; margin-left: 221px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                waf-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    waf-construct
                </text>
            </switch>
        </g>
        <path d="M 300 97 L 373.63 97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 378.88 97 L 371.88 100.5 L 373.63 97 L 371.88 93.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 97px; margin-left: 340px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ロギング
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="100" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ロギング
                </text>
            </switch>
        </g>
        <path d="M 260 97 L 86.37 97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 81.12 97 L 88.12 93.5 L 86.37 97 L 88.12 100.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 97px; margin-left: 170px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アタッチ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="170" y="100" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アタッチ
                </text>
            </switch>
        </g>
        <path d="M 260 77 L 300 77 L 300 117 L 260 117 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 265.37 97.57 L 264 97.57 L 264 96.43 L 265.37 96.43 C 265.49 93.54 266.42 90.79 268.12 88.44 L 269.05 89.11 C 267.49 91.26 266.63 93.78 266.51 96.43 L 268 96.43 L 268 97.57 L 266.51 97.57 C 266.62 100.24 267.48 102.77 269.05 104.93 L 268.12 105.6 C 266.41 103.24 265.48 100.48 265.37 97.57 Z M 288.58 108.9 C 286.23 110.6 283.47 111.54 280.57 111.65 L 280.57 113 L 279.43 113 L 279.43 111.65 C 276.53 111.54 273.77 110.6 271.42 108.9 L 272.09 107.98 C 274.25 109.54 276.77 110.4 279.43 110.51 L 279.43 109 L 280.57 109 L 280.57 110.51 C 283.23 110.4 285.75 109.53 287.91 107.98 Z M 271.42 85.14 C 273.77 83.44 276.53 82.5 279.43 82.39 L 279.43 81 L 280.57 81 L 280.57 82.39 C 283.47 82.5 286.23 83.44 288.58 85.14 L 287.91 86.07 C 285.75 84.51 283.23 83.64 280.57 83.53 L 280.57 85 L 279.43 85 L 279.43 83.53 C 276.77 83.64 274.25 84.51 272.09 86.07 Z M 296 96.43 L 296 97.57 L 294.63 97.57 C 294.52 100.48 293.59 103.24 291.88 105.6 L 290.95 104.93 C 292.52 102.77 293.38 100.24 293.49 97.57 L 292 97.57 L 292 96.43 L 293.49 96.43 C 293.37 93.78 292.51 91.26 290.95 89.11 L 291.88 88.44 C 293.58 90.79 294.51 93.54 294.63 96.43 Z M 288.56 87.66 L 293.04 83.18 L 293.84 83.99 L 289.36 88.47 Z M 271.44 106.39 L 266.96 110.87 L 266.16 110.06 L 270.64 105.58 Z M 272.72 90.52 L 264.24 82.04 L 265.04 81.24 L 273.52 89.72 Z M 287.26 103.45 L 295.76 111.96 L 294.96 112.76 L 286.45 104.26 Z M 275.24 97.42 C 275.28 97.34 275.33 97.27 275.37 97.19 C 276.33 95.68 276.06 93.62 275.75 92.38 C 276.58 92.92 277.32 94.04 277.57 94.51 C 277.68 94.71 277.89 94.83 278.12 94.81 C 278.35 94.79 278.54 94.64 278.62 94.43 C 279.47 92.01 279.03 90.19 278.43 89.01 C 279.16 89.44 279.73 90.05 280.1 90.82 C 280.91 92.51 280.73 94.81 279.63 96.83 C 278.13 99.59 278.43 102.49 278.77 104.05 C 277.91 103.68 277.15 103.26 276.49 102.8 C 274.76 101.6 274.21 99.23 275.24 97.42 Z M 282.91 97.23 C 282.87 97.46 282.98 97.69 283.19 97.81 C 283.39 97.93 283.65 97.91 283.83 97.76 C 283.88 97.73 284.82 96.95 285.27 95.08 C 285.82 95.88 286.39 97.48 285.63 100.43 C 284.88 103.35 281.3 104.17 280.03 104.37 C 279.72 103.27 279.1 100.2 280.63 97.37 C 281.72 95.37 282.01 93.1 281.47 91.23 C 282.5 92.44 283.37 94.36 282.91 97.23 Z M 274.24 96.86 C 272.93 99.18 273.63 102.2 275.83 103.74 C 276.83 104.44 278.05 105.04 279.43 105.54 C 279.5 105.56 279.56 105.57 279.63 105.57 C 279.64 105.57 279.65 105.57 279.66 105.56 L 279.66 105.57 C 279.9 105.55 285.61 105.05 286.73 100.72 C 288.18 95.14 285.29 93.48 285.17 93.41 C 285 93.32 284.8 93.32 284.63 93.41 C 284.46 93.5 284.34 93.66 284.32 93.85 C 284.28 94.24 284.22 94.58 284.14 94.89 C 283.75 90.51 280.27 88.72 279.57 88.41 C 278.85 87.85 277.98 87.47 276.96 87.29 C 276.71 87.25 276.45 87.38 276.35 87.62 C 276.24 87.85 276.3 88.13 276.5 88.3 C 276.58 88.37 278.36 89.91 277.85 92.85 C 277.19 91.98 276.13 90.93 274.89 90.93 C 274.7 90.93 274.53 91.02 274.42 91.18 C 274.31 91.34 274.29 91.53 274.36 91.71 C 274.37 91.74 275.54 94.81 274.41 96.58 C 274.35 96.67 274.29 96.77 274.24 96.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                WAF
                                <br/>
                                (Web ACL)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    WAF...
                </text>
            </switch>
        </g>
        <path d="M 380 77 L 420 77 L 420 117 L 380 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 411.94 98.65 L 412.16 97.11 C 414.18 98.32 414.21 98.82 414.21 98.83 C 414.2 98.84 413.86 99.13 411.94 98.65 Z M 410.83 98.34 C 407.33 97.29 402.46 95.05 400.49 94.12 C 400.49 94.11 400.49 94.11 400.49 94.1 C 400.49 93.34 399.88 92.72 399.12 92.72 C 398.36 92.72 397.75 93.34 397.75 94.1 C 397.75 94.85 398.36 95.47 399.12 95.47 C 399.45 95.47 399.75 95.35 399.99 95.15 C 402.31 96.25 407.14 98.45 410.67 99.49 L 409.27 109.32 C 409.27 109.35 409.27 109.37 409.27 109.4 C 409.27 110.27 405.43 111.86 399.17 111.86 C 392.84 111.86 388.97 110.27 388.97 109.4 C 388.97 109.37 388.97 109.35 388.97 109.32 L 386.05 88.06 C 388.57 89.8 393.99 90.71 399.18 90.71 C 404.35 90.71 409.76 89.8 412.29 88.07 Z M 385.75 85.84 C 385.79 85.09 390.11 82.14 399.18 82.14 C 408.24 82.14 412.56 85.09 412.6 85.84 L 412.6 86.1 C 412.11 87.79 406.51 89.57 399.18 89.57 C 391.83 89.57 386.23 87.78 385.75 86.09 Z M 413.75 85.86 C 413.75 83.88 408.07 81 399.18 81 C 390.28 81 384.6 83.88 384.6 85.86 L 384.66 86.29 L 387.83 109.44 C 387.9 112.03 394.81 113 399.17 113 C 404.59 113 410.34 111.76 410.41 109.45 L 411.78 99.79 C 412.54 99.97 413.17 100.07 413.67 100.07 C 414.35 100.07 414.8 99.9 415.08 99.57 C 415.31 99.3 415.4 98.97 415.33 98.62 C 415.18 97.83 414.24 96.98 412.33 95.89 L 413.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 400px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (WafLogBucket)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <rect x="0" y="17" width="120" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ecs-app-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecs-app-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="57" width="80" height="100" rx="12" ry="12" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 54px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                alb-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    alb-construct
                </text>
            </switch>
        </g>
        <rect x="40" y="77" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 60 77 C 48.97 77 40 85.97 40 97 C 40 108.03 48.97 117 60 117 C 71.03 117 80 108.03 80 97 C 80 85.97 71.03 77 60 77 Z M 60 115.18 C 49.97 115.18 41.82 107.03 41.82 97 C 41.82 86.97 49.97 78.82 60 78.82 C 70.03 78.82 78.18 86.97 78.18 97 C 78.18 107.03 70.03 115.18 60 115.18 Z M 71.85 102.45 L 70.45 102.45 L 70.45 99.39 C 70.45 98.88 70.05 98.48 69.55 98.48 L 67.27 98.48 L 67.27 95.41 C 67.27 94.91 66.87 94.5 66.36 94.5 L 60.91 94.5 L 60.91 92.34 L 66.36 92.34 C 66.87 92.34 67.27 91.93 67.27 91.43 L 67.27 84.27 C 67.27 83.77 66.87 83.36 66.36 83.36 L 53.64 83.36 C 53.13 83.36 52.73 83.77 52.73 84.27 L 52.73 91.43 C 52.73 91.93 53.13 92.34 53.64 92.34 L 59.09 92.34 L 59.09 94.5 L 53.64 94.5 C 53.13 94.5 52.73 94.91 52.73 95.41 L 52.73 98.48 L 50.46 98.48 C 49.95 98.48 49.55 98.88 49.55 99.39 L 49.55 102.45 L 48.15 102.45 C 47.65 102.45 47.24 102.86 47.24 103.36 L 47.24 107.34 C 47.24 107.84 47.65 108.25 48.15 108.25 L 52.05 108.25 C 52.55 108.25 52.96 107.84 52.96 107.34 L 52.96 103.36 C 52.96 102.86 52.55 102.45 52.05 102.45 L 51.36 102.45 L 51.36 100.3 L 55.11 100.3 L 55.11 102.45 L 54.43 102.45 C 53.93 102.45 53.52 102.86 53.52 103.36 L 53.52 107.34 C 53.52 107.84 53.93 108.25 54.43 108.25 L 58.41 108.25 C 58.91 108.25 59.32 107.84 59.32 107.34 L 59.32 103.36 C 59.32 102.86 58.91 102.45 58.41 102.45 L 56.93 102.45 L 56.93 99.39 C 56.93 98.88 56.53 98.48 56.02 98.48 L 54.55 98.48 L 54.55 96.32 L 65.45 96.32 L 65.45 98.48 L 63.98 98.48 C 63.47 98.48 63.07 98.88 63.07 99.39 L 63.07 102.45 L 61.59 102.45 C 61.09 102.45 60.68 102.86 60.68 103.36 L 60.68 107.34 C 60.68 107.84 61.09 108.25 61.59 108.25 L 65.57 108.25 C 66.07 108.25 66.48 107.84 66.48 107.34 L 66.48 103.36 C 66.48 102.86 66.07 102.45 65.57 102.45 L 64.89 102.45 L 64.89 100.3 L 68.64 100.3 L 68.64 102.45 L 67.9 102.45 C 67.4 102.45 66.99 102.86 66.99 103.36 L 66.99 107.34 C 66.99 107.84 67.4 108.25 67.9 108.25 L 71.85 108.25 C 72.35 108.25 72.76 107.84 72.76 107.34 L 72.76 103.36 C 72.76 102.86 72.35 102.45 71.85 102.45 Z M 54.55 90.52 L 54.55 85.18 L 65.45 85.18 L 65.45 90.52 Z M 49.06 106.43 L 49.06 104.27 L 51.14 104.27 L 51.14 106.43 Z M 55.34 106.43 L 55.34 104.27 L 57.5 104.27 L 57.5 106.43 Z M 62.5 106.43 L 62.5 104.27 L 64.66 104.27 L 64.66 106.43 Z M 68.81 106.43 L 68.81 104.27 L 70.94 104.27 L 70.94 106.43 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 60px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ALB
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ALB
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>