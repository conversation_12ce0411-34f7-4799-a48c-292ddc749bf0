<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="361px" height="341px" viewBox="-0.5 -0.5 361 341" content="&lt;mxfile&gt;&lt;diagram name=&quot;240904&quot; id=&quot;b2An3zXtUOspAL43v1Ef&quot;&gt;7VlbT+M4FP41eQTl3vaxKS2zYthFoGV2npBp3MTCiSPHvc2v3+PEaRI77dKZrYYHoELx5+PjY3/nlmJ5s2x3y1GR3rMYU8u1453l3ViuG/oe/JXAvgZ8P6iBhJO4hpwWeCI/sAJtha5JjMueoGCMClL0wSXLc7wUPQxxzrZ9sRWj/V0LlGADeFoiaqLfSCzSGh27oxb/gkmSNjs74aSeyVAjrE5Spihm2w7kzS1vxhkT9VO2m2Eq7665l9XjP38vJo+7Kb7it8nt/bOTOFe1ssU5Sw5H4DgXP636z+/3633yLXr+kX1P71Z/BbfbWC2xN4iu1X3BKTkoxSVb8yUur0qBlm/qAsS+udVUZBSeHMuLSsHZG54xyjggMV6hNQUrI87WeYzl5jaMVoTSRiZnOaiJKHrF9IGVRBCWA7yE02GYjzaYCwIMftUEBCtgFlGSDIpP1cQrE4JlMPHOa1PXK9XgXcdp1DXeYpZhwfcgombdsbo2FRJh4yHb1sGcBku7zmUrECmnTg66W+LgQXF3Bo+OweMr0LYurgqK8iMcblMi8FOBlnK8heCHK/vktcOrH/52Xr1jvG4kG5/Evo9Y/+MFrG8QG1XEAvZc8aGTWr5hsUwVOWwtKMmBw6ZmVoyxXDSMWa4HvwtpUJRwFBPczik2uwSDuLcYh65vuIcSjlGZHlzjGN0HHnWCj/hBxyel7ap3cNxmrA4vt0RlUR90RXbSjqhgRGqZb0BZ2Th2igq5INslspW5RtvSv67j5WVjOLlzQYcLXM3hfNPh/AF/8y/lboHhbjcq7l37ESeSP93hqhuu7Agi+IBlM9sKYGYmR9duoAH6eNQHHHMkdfQBfTzqA46u3tH2d3QDO4Ax6qm3tf3tjoHwORJyR0Kr49hDufi/nB1aYYFgL6506L5ey1CKipK8HlZxvFzzkmzwIy5r5faxmEggCorK/D+WVeSasy+89go9H0CmcPzRPJoOl4jTkU/xSsZgCddB8uRrNbrxBnLXYYtD3nGMAnWxLiDUugDbjF0vHAhe72LRGx4vFg/Q333Wiv+3VsieWeldoIxQ6QhfMN1gaeig553Mue/2PM3xxr+5ZjRv8B23s+ahNZlak5E1D6zxworqh5k1CQ0nxDG8h6sh4yJlCcsRnbdol9Pzgrl+Nz1levNNA+IJPsWRasOkrScZ4pgiAbm1IzR849XSKedo3xFQpbTV/CCBTn860dqFUHuVP08eHmoLWu4PR/kFdzDfMe/unwC4w/vTGWiwkxjsJoY6isGuwuwsemJVrR/YQQeHsJEJOqZY0x6Y4BA21Avpq52B1Y62+ngn8t6EDnOLwB/5k87cDYHeQeXlXIaqmfFnI8dzFkPdwKr6+YhpfzDFN19s1Y0PNE3lUAv0hvcvGcpRgjOw5KXEfENkwrlY0+F9tBcGuGIz+XvW2KuSf/UwnVQPrjX1m6l5MzWVlQLqwrgqENO5rBofqUCE78z7ip4riNZxoL6MOa8WGMk78PtUBxONw7poqVUtjecWFWMf+0SRaFc36tlqVWJh+NHJQgLD9uvwWrz9n4I3/xc=&lt;/diagram&gt;&lt;diagram id=&quot;t9WJrTuWiwzIOLG_Me5O&quot; name=&quot;240821&quot;&gt;7Vpdb5swFP01PKaKgQTyWNKkm7Zp0yptj5UDDlg1GIHztV+/62ASwKZK1Sjp1lapgs+9/rrn+thGsZxpur0vcJ584xFhlj2MtpZzZ9k2QvYIviSyqxDPm1RAXNBIOR2BB/qHKHCo0BWNSNlyFJwzQfM2GPIsI6FoYbgo+KbttuSs3WuOY6IBDyFmOvqbRiKpUN/2jvgnQuOk7hmN1fxSXDurmZQJjvimATkzy5kWnIvqKd1OCZPBq+NS1Zv3WA8DK0gmTqngVhXWmK3U3BaM4EG8IqUYkLDEeT4ocZrDzKvxil0dhE1CBXnIcSjLGyDacoJEpAxKCB5LUfAnMuWMF4BEZIlXDIYUFHyVRSRSXkvKWO2T8QyaChheEPaDl1RQngEcwlQI2IM1KQQFEr52HASXXWNGY6P7rTIsuBA8BYMeIxU2WYNsG5CK2T3hKRHFDlyUtaZP5a9TlzeNbHAVljQywR4qEKsMjA9NH0mCB8WTmbN69TRIu/sJZWvmWb5r3SJr5lg+sibeSzkzsBHhMjmw1ebuXyBq7HeYGulMobGBKTQ6B1PDPqbG1mRq+ZM9U7bl334whU5gyjWtqbMwhWyNKY2SGIKanz7Tw/aDF3ULw2cjcBCHOgK+IVdtUwTOoSpIz9UFDp9W+WAtlXtQCij915uA3UOkTthlCEEaIcGeEMB+7ePYJaN8IiKU4xrCBPlKMJpB7OsjkASXPBN1pC3Ythx7LjsP4gJHlBxtioUmMeDuzP0xnBi6tHb1Z/gMTYf4d4np4a8peTB2dRQEqlRZTV52icu8muiSbuU4gpxT2cpsDY2VdUImOJcV0m0sj6Y3eFO6N1WeP66NyfmqRFFWt72wDacFk7C550gi/YB3cV1z3Gvqmt8naznD2TtQNbeHxiup2qRf1H4AIR+a1tK0l0qYzGmTFL4qL64vYvqF5/tPLVNgGqKPR40Dw+Js0NIlMqVRJLsJTMJwXPXDM91ZOrdLF+mhNl1ZnHOEWl+fF98v3O5N4KL7hZ5rX749APCF7J4Xp/1a3Xc8CuADQ5lW/yNwnUrkRjaugSbM00Gku8EXMvXQBU2Yp4NId5OletRt0IR5I33E3drIUBt1asPnlVoPtvnI9dxJw3ZHC2iokuyMFzKTupvB1EMOmmsiApbl/u8N7AiatBu3g4KUfFWE5HMoxxNAsXpqez2R3WOKMxyTFEbyWJJiTaWymURs0rO63+J24b3zI9/pXF1EUW39InvxPcW76ruVesLv9t3KCw5BlyHE0Qj5eLfyFt6tvPy0fL19xh5fX9eu+mrl3avauIfGK6nah6i9UVE7PU8uLWpQPP7YYG9r/GTDmf0F&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="20" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 217px; margin-left: 80px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                share-resources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    share-resources-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="60" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 57px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                backup-plan-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="57" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backup-plan-stack
                </text>
            </switch>
        </g>
        <rect x="220" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 217px; margin-left: 221px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                backup-vault-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backup-vault-stack
                </text>
            </switch>
        </g>
        <rect x="260" y="240" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 267.54 259.05 L 265.73 259.06 C 265.76 263.62 267.87 267.79 271.52 270.51 C 274.09 272.43 277.11 273.36 280.1 273.36 C 284.49 273.36 288.83 271.36 291.65 267.57 C 296.4 261.21 295.08 252.18 288.72 247.44 C 282.56 242.85 273.91 243.95 269.06 249.8 L 269.09 247.29 L 267.27 247.26 L 267.21 251.81 C 267.21 252.05 267.3 252.28 267.47 252.45 C 267.64 252.63 267.87 252.72 268.11 252.73 L 272.71 252.79 L 272.74 250.97 L 270.47 250.94 C 274.71 245.85 282.25 244.89 287.63 248.89 C 293.19 253.04 294.34 260.93 290.2 266.49 C 286.05 272.05 278.16 273.2 272.6 269.05 C 269.42 266.68 267.57 263.03 267.54 259.05 Z M 280 255.45 C 277.99 255.45 276.36 257.09 276.36 259.09 C 276.36 261.1 277.99 262.73 280 262.73 C 282.01 262.73 283.64 261.1 283.64 259.09 C 283.64 257.09 282.01 255.45 280 255.45 Z M 273.59 264.35 L 275.61 262.32 C 274.95 261.41 274.55 260.3 274.55 259.09 C 274.55 257.84 274.98 256.68 275.69 255.76 L 273.59 253.81 L 274.83 252.48 L 277.03 254.52 C 277.88 253.97 278.9 253.64 280 253.64 C 281.22 253.64 282.34 254.04 283.25 254.72 L 285.44 252.48 L 286.74 253.75 L 284.51 256.03 C 285.11 256.91 285.45 257.96 285.45 259.09 C 285.45 260.23 285.1 261.29 284.5 262.16 L 286.74 264.32 L 285.48 265.63 L 283.24 263.47 C 282.33 264.14 281.21 264.55 280 264.55 C 278.86 264.55 277.79 264.19 276.92 263.59 L 274.87 265.63 Z M 290 278.18 L 293.64 278.18 L 293.64 277.27 L 290 277.27 Z M 266.36 278.18 L 270 278.18 L 270 277.27 L 266.36 277.27 Z M 300 240.91 L 300 276.36 C 300 276.87 299.59 277.27 299.09 277.27 L 295.45 277.27 L 295.45 279.09 C 295.45 279.59 295.05 280 294.55 280 L 289.09 280 C 288.59 280 288.18 279.59 288.18 279.09 L 288.18 277.27 L 271.82 277.27 L 271.82 279.09 C 271.82 279.59 271.41 280 270.91 280 L 265.45 280 C 264.95 280 264.55 279.59 264.55 279.09 L 264.55 277.27 L 260.91 277.27 C 260.41 277.27 260 276.87 260 276.36 L 260 268.18 L 261.82 268.18 L 261.82 275.45 L 298.18 275.45 L 298.18 241.82 L 261.82 241.82 L 261.82 248.18 L 260 248.18 L 260 240.91 C 260 240.41 260.41 240 260.91 240 L 299.09 240 C 299.59 240 300 240.41 300 240.91 Z M 260 262.73 L 261.82 262.73 L 261.82 253.64 L 260 253.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 0 0 L 360 0 L 360 340 L 0 340 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 8.06 3.47 C 7.47 3.47 7 3.95 7 4.53 C 7 4.99 7.3 5.39 7.71 5.53 L 7.71 20.82 L 5.95 20.82 L 5.95 21.57 L 10.16 21.57 L 10.16 20.82 L 8.46 20.82 L 8.46 12.75 L 19.84 12.75 L 17.19 9.59 L 19.83 6.36 L 8.46 6.36 L 8.46 5.51 C 8.85 5.35 9.12 4.97 9.12 4.53 C 9.12 3.95 8.64 3.47 8.06 3.47 Z M 8.06 4.22 C 8.23 4.22 8.37 4.35 8.37 4.53 C 8.37 4.71 8.23 4.84 8.06 4.84 C 7.88 4.84 7.75 4.71 7.75 4.53 C 7.75 4.35 7.88 4.22 8.06 4.22 Z M 8.46 7.11 L 18.25 7.11 L 16.22 9.6 L 18.23 12 L 8.46 12 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Default Region
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Default Region
                </text>
            </switch>
        </g>
        <rect x="60" y="80" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 93.64 118.18 L 93.64 89.09 L 87.27 89.09 C 86.77 89.09 86.36 88.68 86.36 88.18 L 86.36 81.82 L 66.36 81.82 L 66.36 118.18 Z M 88.18 87.27 L 92.35 87.27 L 88.18 83.1 Z M 95.45 88.18 L 95.45 119.09 C 95.45 119.59 95.05 120 94.55 120 L 65.45 120 C 64.95 120 64.55 119.59 64.55 119.09 L 64.55 80.91 C 64.55 80.41 64.95 80 65.45 80 L 87.27 80 L 87.27 80.01 C 87.51 80.01 87.74 80.09 87.92 80.27 L 95.19 87.54 C 95.36 87.71 95.45 87.95 95.45 88.18 Z M 89.62 111.29 L 88.1 112.81 L 86.59 111.29 L 85.3 112.58 L 86.82 114.09 L 85.3 115.61 L 86.59 116.89 L 88.1 115.38 L 89.62 116.89 L 90.9 115.61 L 89.39 114.09 L 90.9 112.58 Z M 72.27 89.09 C 73.02 89.09 73.64 88.48 73.64 87.73 C 73.64 86.98 73.02 86.36 72.27 86.36 C 71.52 86.36 70.91 86.98 70.91 87.73 C 70.91 88.48 71.52 89.09 72.27 89.09 Z M 72.27 90.91 C 70.52 90.91 69.09 89.48 69.09 87.73 C 69.09 85.97 70.52 84.55 72.27 84.55 C 74.03 84.55 75.45 85.97 75.45 87.73 C 75.45 89.48 74.03 90.91 72.27 90.91 Z M 81.39 111.71 C 80.9 111.78 80.42 111.81 79.94 111.81 C 77.81 111.81 75.76 111.13 74.03 109.84 C 71.51 107.97 70.06 105.09 70.03 101.95 L 71.85 101.94 C 71.87 104.5 73.06 106.85 75.11 108.38 C 76.85 109.68 78.98 110.22 81.13 109.91 C 83.27 109.59 85.16 108.47 86.45 106.73 C 89.12 103.15 88.38 98.06 84.8 95.39 C 81.46 92.9 76.81 93.38 74.03 96.36 L 75.45 96.36 L 75.45 98.18 L 71.82 98.18 C 71.32 98.18 70.91 97.77 70.91 97.27 L 70.91 93.64 L 72.73 93.64 L 72.73 95.1 C 76.13 91.47 81.81 90.89 85.89 93.93 C 90.27 97.2 91.18 103.43 87.91 107.82 C 86.33 109.94 84.01 111.32 81.39 111.71 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Plan
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="139" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 100 260.03 L 220.03 260.03 Q 230.03 260.03 240.03 260.03 L 253.63 260.03" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 258.88 260.03 L 251.88 263.53 L 253.63 260.03 L 251.88 256.53 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                暗号化
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="263" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    暗号化
                </text>
            </switch>
        </g>
        <path d="M 60 240 L 100 240 L 100 280 L 60 280 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 87.38 267.56 L 91.69 267.56 L 91.69 266.33 L 87.38 266.33 Z M 81.85 267.56 L 86.15 267.56 L 86.15 266.33 L 81.85 266.33 Z M 76.31 267.56 L 80.61 267.56 L 80.61 266.33 L 76.31 266.33 Z M 83.08 262.63 C 83.08 261.96 83.63 261.4 84.31 261.4 C 84.99 261.4 85.54 261.96 85.54 262.63 C 85.54 263.31 84.99 263.86 84.31 263.86 C 83.63 263.86 83.08 263.31 83.08 262.63 Z M 86.77 262.63 C 86.77 261.28 85.67 260.17 84.31 260.17 C 82.95 260.17 81.85 261.28 81.85 262.63 C 81.85 263.99 82.95 265.1 84.31 265.1 C 85.67 265.1 86.77 263.99 86.77 262.63 Z M 78.15 261.4 C 78.83 261.4 79.38 261.96 79.38 262.63 C 79.38 263.31 78.83 263.86 78.15 263.86 C 77.47 263.86 76.92 263.31 76.92 262.63 C 76.92 261.96 77.47 261.4 78.15 261.4 Z M 78.15 265.1 C 79.51 265.1 80.61 263.99 80.61 262.63 C 80.61 261.28 79.51 260.17 78.15 260.17 C 76.79 260.17 75.69 261.28 75.69 262.63 C 75.69 263.99 76.79 265.1 78.15 265.1 Z M 96 255.86 L 96 270.02 C 96 270.36 95.72 270.63 95.38 270.63 L 76.31 270.63 L 76.31 269.4 L 94.77 269.4 L 94.77 256.48 L 81.23 256.48 L 81.23 255.25 L 95.38 255.25 C 95.72 255.25 96 255.52 96 255.86 Z M 74.27 258.97 C 74.02 259.05 73.84 259.29 73.84 259.56 L 73.84 272.51 L 72.37 273.77 L 70.77 271.94 L 70.77 270.33 C 70.77 270.16 70.7 270.01 70.59 269.89 L 69.18 268.48 L 70.59 267.07 C 70.7 266.95 70.77 266.8 70.77 266.63 L 70.77 265.4 C 70.77 265.24 70.7 265.08 70.59 264.97 L 69.18 263.56 L 70.59 262.15 C 70.7 262.03 70.77 261.87 70.77 261.71 L 70.77 259.56 C 70.77 259.29 70.59 259.06 70.34 258.98 C 67.17 257.97 65.3 254.72 65.99 251.4 C 66.48 249.02 68.32 247.1 70.67 246.53 C 72.66 246.04 74.72 246.46 76.29 247.7 C 77.87 248.94 78.77 250.79 78.77 252.79 C 78.77 255.59 76.92 258.13 74.27 258.97 Z M 80 252.79 C 80 250.41 78.92 248.2 77.05 246.73 C 75.18 245.26 72.74 244.75 70.37 245.33 C 67.57 246.02 65.37 248.3 64.78 251.16 L 64.78 251.16 C 64 254.94 66.03 258.66 69.54 260 L 69.54 261.46 L 67.87 263.12 C 67.63 263.36 67.63 263.75 67.87 263.99 L 69.54 265.66 L 69.54 266.38 L 67.87 268.05 C 67.63 268.29 67.63 268.68 67.87 268.92 L 69.54 270.58 L 69.54 272.17 C 69.54 272.32 69.59 272.47 69.69 272.58 L 71.84 275.04 C 71.96 275.18 72.13 275.25 72.31 275.25 C 72.45 275.25 72.59 275.2 72.71 275.1 L 74.86 273.26 C 75 273.14 75.08 272.97 75.08 272.79 L 75.08 259.99 C 78 258.87 80 255.97 80 252.79 Z M 72.31 254.25 C 71.46 254.25 70.77 253.56 70.77 252.71 C 70.77 251.86 71.46 251.17 72.31 251.17 C 73.15 251.17 73.84 251.86 73.84 252.71 C 73.84 253.56 73.15 254.25 72.31 254.25 Z M 72.31 249.94 C 70.78 249.94 69.54 251.18 69.54 252.71 C 69.54 254.23 70.78 255.48 72.31 255.48 C 73.83 255.48 75.08 254.23 75.08 252.71 C 75.08 251.18 73.83 249.94 72.31 249.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
        <path d="M 95.45 100.03 L 270.03 100.03 Q 280.03 100.03 280.03 110.03 L 280 183.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 280 188.88 L 276.5 181.88 L 280 183.63 L 283.5 181.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 100px; margin-left: 194px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                プライマリ指定
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="194" y="103" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    プライマリ指定
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>