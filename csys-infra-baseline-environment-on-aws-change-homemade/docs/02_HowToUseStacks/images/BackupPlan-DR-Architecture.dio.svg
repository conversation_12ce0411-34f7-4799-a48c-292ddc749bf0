<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="761px" height="341px" viewBox="-0.5 -0.5 761 341" content="&lt;mxfile&gt;&lt;diagram name=&quot;240904&quot; id=&quot;b2An3zXtUOspAL43v1Ef&quot;&gt;7VpZc+I4EP41eiTlCx+PNoHM7ExqU6Q2M28pBYTxRFheWVz761fCMtiSYGAm1245oQqp1WrJ3V+3umWAO1hsbigs5rdkijBwrOkGuNfAcWwrsPmXoGwrSuA5FSGl2VQyHQj32T+onimpy2yKyhYjIwSzrGgTJyTP0YS1aJBSsm6zzQhur1rAFGmE+wnEOvVbNmXziho6wYH+CWXpvF7Z9qNqZAFrZvkk5RxOybpBcofAHVBCWNVabAYIC+XVepmNv/81isabGPXoTXpz+2Cndq8SNrpkyv4RKMrZL4vePvztfx78uPsjLv/8Nu6XP1D/odevRK8gXkp98aekXCgqyZJOUNkrGZw8SwWwba3VOVtg3rKBm5SMkmc0IJhQTpmiGVxivsuEkmU+RWJxi/dmGcY1T05yLibB8AnhO1JmLCM5J0/40yE+nqwQZRm34FeFgZGCj0KcpUb2WA48EcbIgg+cqTapXiEGbRqgkWq8QWSBGN1yFjnqSUBIj/BrgKwP+LJr2ryJLUsSocR0uhd9sBtvSNNdghBfs+MTN9uy6BUY5kdsuJ5nDN0XcCL6a+79XGWdXQ929fy3tGsdLxsmBEMXhA5IEtmIq4YLElc2Qks24hgMfRAOQBiAYR/EQxDFmsHRlEdG2SWUzUlKcoiHB2rT/JcpuYoWp/hkvBV7OMcWPesqcNywmkQRhixbtSO6SdlS3B3J+Kb3sgKrbdh+pNiLQZoiJmcdTBZTCrcNtkIwlOev40WWgoBKojK7Fk9msxIxDTN73fxGeIiOhYeVcOouPpwXHz5g4Hf0qJHsLMtpDzuDqFYtnxGbzKV1yJLhLOdGrHOvnclIzmqTAcfl/yOxoySlcJqhw5g0Z9PCnN0dhb7jafiQzFNYzvfYOGbvvSFVCx8BQgOUYu8yB7Wdui8fXiwJy6J60Fm2EftIdj6N6HCFhGtLZM9hISYsNqnIia/guvSuKod5XGkovzxYXoC4UEGcpyPOMwDOewG8GfNFW4PbtXR8xxqjVNhPBZyMmrzdT/iH72xggT4fGYjeldNXCGo/aBNsvSdktAlqP2gTbFW8raxvqxtsELReS7ylrG81Nsg/R1zuiGs1gG0Kxj8DOy+pGORrUSlDxXrFgzEsyuxpP4uiyZKW/LQdo7ISbh3ziZR7QbHb/ufJznP10UdaoUKNBzxS2F4wTGLzGXHa8zGaCR8suTqyPP266127hti1X2Ifd2zthHo131WSAUt3Xdc3+K77Es57MgczHRZ3vE7ozoqXPStE7SXljuAiwwIJnxBeIbFRI/JOxtyzkee3kRe+85nh6GfGmPfBMACRB+KwUcJU1c2gLnz2Q8Gh8OmOl+546Y4X7q4f73w5egnVVZmXRO+PV2WeyBu6IvM/X2T671hk6iFD3GNGMYh295jhCCRVYwAiX4PZG11oGrMar+Krrg5PyKs99acXn5ddcl56N+n0FSP7ytuiy/jbd5m/cE95SqcNNHy5veeEL2h7OsQYk0BjImhKBo0JoZ4Utth2aZphBZVoogU60dbZ6sxOJ5popjRWnW0bZtvK7ONJ5LkRm4+N+l7gRY2x64ynfTLw5sJT9ZA+CGzXHpkSudnu7yPGdWMMr1+dVjkrz3dLU/b6jLaPC5jDFC34Th5LRFeZiDevFuXfMci7hiC/K+eiuq6Lo/oVl1cPDf/Hb7acsC918ptvttw3erOlrWO//ZstPQl9XnQ/TzBVhu9aQOivGj9gRheembyd69pnu/G5Wgy7TKjLhLpM6HXi49ulQrx7+H1gdeYdfmXpDv8F&lt;/diagram&gt;&lt;diagram id=&quot;t9WJrTuWiwzIOLG_Me5O&quot; name=&quot;240821&quot;&gt;7Vpdb5swFP01PKaKgQTyWNKkm7Zp0yptj5UDDlg1GIHztV+/62ASwKZK1Sjp1lapgs+9/rrn+thGsZxpur0vcJ584xFhlj2MtpZzZ9k2QvYIviSyq5CRN6yAuKCRcjoCD/QPUWDttqIRKVuOgnMmaN4GQ55lJBQtDBcF37Tdlpy1e81xTDTgIcRMR3/TSCQV6tveEf9EaJzUPaPxpLKkuHZWMykTHPFNA3JmljMtOBfVU7qdEiaDV8elqjfvsR4GVpBMnFLBrSqsMVupuS0YwYN4RUoxIGGJ83xQ4jSHmVfjFbs6CJuECvKQ41CWN0C05QSJSBmUEDyWouBPZMoZLwCJyBKvGAwpKPgqi0ikvJaUsdon4xk0FTC8IOwHL6mgPAM4hKkQsAdrUggKJHztOAguu8aMxkb3W2VYcCF4CgY9RipssgbZNiAVs3vCUyKKHbgoa02fyl+nLm8a2eAqLGlkgj1UIFYZGB+aPpIED4onM2f16mmQdvcTytbMs3zXukXWzLF8ZE28l3JmYCPCZXJgq83dv0DU2O8wNdKZQmMDU2h0DqaGfUyNrcnU8id7pmzLv/1gCp3AlGtaU2dhCtkaUxolMQQ1P32mh+0HL+oWhs9G4CAOdQR8Q67apgicQ1WQnqsLHD6t8sFaKvegFFD6rzcBu4dInbDLEII0QoI9IYD92sexS0b5REQoxzWECfKVYDSD2NdHIAkueSbqSFuwbTn2XHYexAWOKDnaFAtNYsDdmftjODF0ae3qz/AZmg7x7xLTw19T8mDs6igIVKmymrzsEpd5NdEl3cpxBDmnspXZGhor64RMcC4rpNtYHk1v8KZ0b6o8f1wbk/NViaKsbnthG04LJmFzz5FE+gHv4rrmuNfUNb9P1nKGs3egam4PjVdStUm/qP0AQj40raVpL5UwmdMmKXxVXlxfxPQLz/efWqbANEQfjxoHhsXZoKVLZEqjSHYTmIThuOqHZ7qzdG6XLtJDbbqyOOcItb4+L75fuN2bwEX3Cz3Xvnx7AOAL2T0vTvu1uu94FMAHhjKt/kfgOpXIjWxcA02Yp4NId4MvZOqhC5owTweR7iZL9ajboAnzRvqIu7WRoTbq1IbPK7UebPOR67mThu2OFtBQJdkZL2QmdTeDqYccNNdEBCzL/d8b2BE0aTduBwUp+aoIyedQjieAYvXU9noiu8cUZzgmKYzksSTFmkplM4nYpGd1v8XtwnvnR77TubqIotr6Rfbie4p31Xcr9YTf7buVFxyCLkOIoxHy8W7lLbxbeflp+Xr7jD2+vq5d9dXKu1e1cQ+NV1K1D1F7o6J2ep5cWtSgePyxwd7W+MmGM/sL&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="20" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 217px; margin-left: 80px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                share-resources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    share-resources-stack
                </text>
            </switch>
        </g>
        <rect x="20" y="60" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 57px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                backup-plan-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="57" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backup-plan-stack
                </text>
            </switch>
        </g>
        <path d="M 95.45 90 L 670.05 90 Q 680.05 90 680.04 100 L 680 183.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 680 188.88 L 676.5 181.88 L 680 183.63 L 683.5 181.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 90px; margin-left: 190px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                セカンダリ指定
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="93" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    セカンダリ指定
                </text>
            </switch>
        </g>
        <rect x="220" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 217px; margin-left: 221px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                backup-vault-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backup-vault-stack
                </text>
            </switch>
        </g>
        <rect x="260" y="240" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 267.54 259.05 L 265.73 259.06 C 265.76 263.62 267.87 267.79 271.52 270.51 C 274.09 272.43 277.11 273.36 280.1 273.36 C 284.49 273.36 288.83 271.36 291.65 267.57 C 296.4 261.21 295.08 252.18 288.72 247.44 C 282.56 242.85 273.91 243.95 269.06 249.8 L 269.09 247.29 L 267.27 247.26 L 267.21 251.81 C 267.21 252.05 267.3 252.28 267.47 252.45 C 267.64 252.63 267.87 252.72 268.11 252.73 L 272.71 252.79 L 272.74 250.97 L 270.47 250.94 C 274.71 245.85 282.25 244.89 287.63 248.89 C 293.19 253.04 294.34 260.93 290.2 266.49 C 286.05 272.05 278.16 273.2 272.6 269.05 C 269.42 266.68 267.57 263.03 267.54 259.05 Z M 280 255.45 C 277.99 255.45 276.36 257.09 276.36 259.09 C 276.36 261.1 277.99 262.73 280 262.73 C 282.01 262.73 283.64 261.1 283.64 259.09 C 283.64 257.09 282.01 255.45 280 255.45 Z M 273.59 264.35 L 275.61 262.32 C 274.95 261.41 274.55 260.3 274.55 259.09 C 274.55 257.84 274.98 256.68 275.69 255.76 L 273.59 253.81 L 274.83 252.48 L 277.03 254.52 C 277.88 253.97 278.9 253.64 280 253.64 C 281.22 253.64 282.34 254.04 283.25 254.72 L 285.44 252.48 L 286.74 253.75 L 284.51 256.03 C 285.11 256.91 285.45 257.96 285.45 259.09 C 285.45 260.23 285.1 261.29 284.5 262.16 L 286.74 264.32 L 285.48 265.63 L 283.24 263.47 C 282.33 264.14 281.21 264.55 280 264.55 C 278.86 264.55 277.79 264.19 276.92 263.59 L 274.87 265.63 Z M 290 278.18 L 293.64 278.18 L 293.64 277.27 L 290 277.27 Z M 266.36 278.18 L 270 278.18 L 270 277.27 L 266.36 277.27 Z M 300 240.91 L 300 276.36 C 300 276.87 299.59 277.27 299.09 277.27 L 295.45 277.27 L 295.45 279.09 C 295.45 279.59 295.05 280 294.55 280 L 289.09 280 C 288.59 280 288.18 279.59 288.18 279.09 L 288.18 277.27 L 271.82 277.27 L 271.82 279.09 C 271.82 279.59 271.41 280 270.91 280 L 265.45 280 C 264.95 280 264.55 279.59 264.55 279.09 L 264.55 277.27 L 260.91 277.27 C 260.41 277.27 260 276.87 260 276.36 L 260 268.18 L 261.82 268.18 L 261.82 275.45 L 298.18 275.45 L 298.18 241.82 L 261.82 241.82 L 261.82 248.18 L 260 248.18 L 260 240.91 C 260 240.41 260.41 240 260.91 240 L 299.09 240 C 299.59 240 300 240.41 300 240.91 Z M 260 262.73 L 261.82 262.73 L 261.82 253.64 L 260 253.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 0 0 L 360 0 L 360 340 L 0 340 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 8.06 3.47 C 7.47 3.47 7 3.95 7 4.53 C 7 4.99 7.3 5.39 7.71 5.53 L 7.71 20.82 L 5.95 20.82 L 5.95 21.57 L 10.16 21.57 L 10.16 20.82 L 8.46 20.82 L 8.46 12.75 L 19.84 12.75 L 17.19 9.59 L 19.83 6.36 L 8.46 6.36 L 8.46 5.51 C 8.85 5.35 9.12 4.97 9.12 4.53 C 9.12 3.95 8.64 3.47 8.06 3.47 Z M 8.06 4.22 C 8.23 4.22 8.37 4.35 8.37 4.53 C 8.37 4.71 8.23 4.84 8.06 4.84 C 7.88 4.84 7.75 4.71 7.75 4.53 C 7.75 4.35 7.88 4.22 8.06 4.22 Z M 8.46 7.11 L 18.25 7.11 L 16.22 9.6 L 18.23 12 L 8.46 12 Z M 0 25 L 0 0 L 25 0 L 25 25 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 7px; margin-left: 32px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Default Region
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="32" y="19" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Default Region
                </text>
            </switch>
        </g>
        <rect x="60" y="80" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 93.64 118.18 L 93.64 89.09 L 87.27 89.09 C 86.77 89.09 86.36 88.68 86.36 88.18 L 86.36 81.82 L 66.36 81.82 L 66.36 118.18 Z M 88.18 87.27 L 92.35 87.27 L 88.18 83.1 Z M 95.45 88.18 L 95.45 119.09 C 95.45 119.59 95.05 120 94.55 120 L 65.45 120 C 64.95 120 64.55 119.59 64.55 119.09 L 64.55 80.91 C 64.55 80.41 64.95 80 65.45 80 L 87.27 80 L 87.27 80.01 C 87.51 80.01 87.74 80.09 87.92 80.27 L 95.19 87.54 C 95.36 87.71 95.45 87.95 95.45 88.18 Z M 89.62 111.29 L 88.1 112.81 L 86.59 111.29 L 85.3 112.58 L 86.82 114.09 L 85.3 115.61 L 86.59 116.89 L 88.1 115.38 L 89.62 116.89 L 90.9 115.61 L 89.39 114.09 L 90.9 112.58 Z M 72.27 89.09 C 73.02 89.09 73.64 88.48 73.64 87.73 C 73.64 86.98 73.02 86.36 72.27 86.36 C 71.52 86.36 70.91 86.98 70.91 87.73 C 70.91 88.48 71.52 89.09 72.27 89.09 Z M 72.27 90.91 C 70.52 90.91 69.09 89.48 69.09 87.73 C 69.09 85.97 70.52 84.55 72.27 84.55 C 74.03 84.55 75.45 85.97 75.45 87.73 C 75.45 89.48 74.03 90.91 72.27 90.91 Z M 81.39 111.71 C 80.9 111.78 80.42 111.81 79.94 111.81 C 77.81 111.81 75.76 111.13 74.03 109.84 C 71.51 107.97 70.06 105.09 70.03 101.95 L 71.85 101.94 C 71.87 104.5 73.06 106.85 75.11 108.38 C 76.85 109.68 78.98 110.22 81.13 109.91 C 83.27 109.59 85.16 108.47 86.45 106.73 C 89.12 103.15 88.38 98.06 84.8 95.39 C 81.46 92.9 76.81 93.38 74.03 96.36 L 75.45 96.36 L 75.45 98.18 L 71.82 98.18 C 71.32 98.18 70.91 97.77 70.91 97.27 L 70.91 93.64 L 72.73 93.64 L 72.73 95.1 C 76.13 91.47 81.81 90.89 85.89 93.93 C 90.27 97.2 91.18 103.43 87.91 107.82 C 86.33 109.94 84.01 111.32 81.39 111.71 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 127px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Plan
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="139" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 400 0 L 760 0 L 760 340 L 400 340 Z" fill="none" stroke="#147eba" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 408.06 3.47 C 407.47 3.47 407 3.95 407 4.53 C 407 4.99 407.3 5.39 407.71 5.53 L 407.71 20.82 L 405.95 20.82 L 405.95 21.57 L 410.16 21.57 L 410.16 20.82 L 408.46 20.82 L 408.46 12.75 L 419.84 12.75 L 417.19 9.59 L 419.83 6.36 L 408.46 6.36 L 408.46 5.51 C 408.85 5.35 409.12 4.97 409.12 4.53 C 409.12 3.95 408.64 3.47 408.06 3.47 Z M 408.06 4.22 C 408.23 4.22 408.37 4.35 408.37 4.53 C 408.37 4.71 408.23 4.84 408.06 4.84 C 407.88 4.84 407.75 4.71 407.75 4.53 C 407.75 4.35 407.88 4.22 408.06 4.22 Z M 408.46 7.11 L 418.25 7.11 L 416.22 9.6 L 418.23 12 L 408.46 12 Z M 400 25 L 400 0 L 425 0 L 425 25 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 328px; height: 1px; padding-top: 7px; margin-left: 432px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                DR 用リージョン
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="432" y="19" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    DR 用リージョン
                </text>
            </switch>
        </g>
        <rect x="620" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 217px; margin-left: 621px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                backup-vault-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="680" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    backup-vault-stack
                </text>
            </switch>
        </g>
        <rect x="660" y="240" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 667.54 259.05 L 665.73 259.06 C 665.76 263.62 667.87 267.79 671.52 270.51 C 674.09 272.43 677.11 273.36 680.1 273.36 C 684.49 273.36 688.83 271.36 691.65 267.57 C 696.4 261.21 695.08 252.18 688.72 247.44 C 682.56 242.85 673.91 243.95 669.06 249.8 L 669.09 247.29 L 667.27 247.26 L 667.21 251.81 C 667.21 252.05 667.3 252.28 667.47 252.45 C 667.64 252.63 667.87 252.72 668.11 252.73 L 672.71 252.79 L 672.74 250.97 L 670.47 250.94 C 674.71 245.85 682.25 244.89 687.63 248.89 C 693.19 253.04 694.34 260.93 690.2 266.49 C 686.05 272.05 678.16 273.2 672.6 269.05 C 669.42 266.68 667.57 263.03 667.54 259.05 Z M 680 255.45 C 677.99 255.45 676.36 257.09 676.36 259.09 C 676.36 261.1 677.99 262.73 680 262.73 C 682.01 262.73 683.64 261.1 683.64 259.09 C 683.64 257.09 682.01 255.45 680 255.45 Z M 673.59 264.35 L 675.61 262.32 C 674.95 261.41 674.55 260.3 674.55 259.09 C 674.55 257.84 674.98 256.68 675.69 255.76 L 673.59 253.81 L 674.83 252.48 L 677.03 254.52 C 677.88 253.97 678.9 253.64 680 253.64 C 681.22 253.64 682.34 254.04 683.25 254.72 L 685.44 252.48 L 686.74 253.75 L 684.51 256.03 C 685.11 256.91 685.45 257.96 685.45 259.09 C 685.45 260.23 685.1 261.29 684.5 262.16 L 686.74 264.32 L 685.48 265.63 L 683.24 263.47 C 682.33 264.14 681.21 264.55 680 264.55 C 678.86 264.55 677.79 264.19 676.92 263.59 L 674.87 265.63 Z M 690 278.18 L 693.64 278.18 L 693.64 277.27 L 690 277.27 Z M 666.36 278.18 L 670 278.18 L 670 277.27 L 666.36 277.27 Z M 700 240.91 L 700 276.36 C 700 276.87 699.59 277.27 699.09 277.27 L 695.45 277.27 L 695.45 279.09 C 695.45 279.59 695.05 280 694.55 280 L 689.09 280 C 688.59 280 688.18 279.59 688.18 279.09 L 688.18 277.27 L 671.82 277.27 L 671.82 279.09 C 671.82 279.59 671.41 280 670.91 280 L 665.45 280 C 664.95 280 664.55 279.59 664.55 279.09 L 664.55 277.27 L 660.91 277.27 C 660.41 277.27 660 276.87 660 276.36 L 660 268.18 L 661.82 268.18 L 661.82 275.45 L 698.18 275.45 L 698.18 241.82 L 661.82 241.82 L 661.82 248.18 L 660 248.18 L 660 240.91 C 660 240.41 660.41 240 660.91 240 L 699.09 240 C 699.59 240 700 240.41 700 240.91 Z M 660 262.73 L 661.82 262.73 L 661.82 253.64 L 660 253.64 Z" fill="#3f8624" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 680px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Backup Vault
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="680" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Backup...
                </text>
            </switch>
        </g>
        <path d="M 100 260 L 220.05 260 Q 230.05 260 240.05 260 L 253.63 260" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 258.88 260 L 251.88 263.5 L 253.63 260 L 251.88 256.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 180px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                暗号化
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="263" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    暗号化
                </text>
            </switch>
        </g>
        <path d="M 60 240 L 100 240 L 100 280 L 60 280 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 87.38 267.56 L 91.69 267.56 L 91.69 266.33 L 87.38 266.33 Z M 81.85 267.56 L 86.15 267.56 L 86.15 266.33 L 81.85 266.33 Z M 76.31 267.56 L 80.61 267.56 L 80.61 266.33 L 76.31 266.33 Z M 83.08 262.63 C 83.08 261.96 83.63 261.4 84.31 261.4 C 84.99 261.4 85.54 261.96 85.54 262.63 C 85.54 263.31 84.99 263.86 84.31 263.86 C 83.63 263.86 83.08 263.31 83.08 262.63 Z M 86.77 262.63 C 86.77 261.28 85.67 260.17 84.31 260.17 C 82.95 260.17 81.85 261.28 81.85 262.63 C 81.85 263.99 82.95 265.1 84.31 265.1 C 85.67 265.1 86.77 263.99 86.77 262.63 Z M 78.15 261.4 C 78.83 261.4 79.38 261.96 79.38 262.63 C 79.38 263.31 78.83 263.86 78.15 263.86 C 77.47 263.86 76.92 263.31 76.92 262.63 C 76.92 261.96 77.47 261.4 78.15 261.4 Z M 78.15 265.1 C 79.51 265.1 80.61 263.99 80.61 262.63 C 80.61 261.28 79.51 260.17 78.15 260.17 C 76.79 260.17 75.69 261.28 75.69 262.63 C 75.69 263.99 76.79 265.1 78.15 265.1 Z M 96 255.86 L 96 270.02 C 96 270.36 95.72 270.63 95.38 270.63 L 76.31 270.63 L 76.31 269.4 L 94.77 269.4 L 94.77 256.48 L 81.23 256.48 L 81.23 255.25 L 95.38 255.25 C 95.72 255.25 96 255.52 96 255.86 Z M 74.27 258.97 C 74.02 259.05 73.84 259.29 73.84 259.56 L 73.84 272.51 L 72.37 273.77 L 70.77 271.94 L 70.77 270.33 C 70.77 270.16 70.7 270.01 70.59 269.89 L 69.18 268.48 L 70.59 267.07 C 70.7 266.95 70.77 266.8 70.77 266.63 L 70.77 265.4 C 70.77 265.24 70.7 265.08 70.59 264.97 L 69.18 263.56 L 70.59 262.15 C 70.7 262.03 70.77 261.87 70.77 261.71 L 70.77 259.56 C 70.77 259.29 70.59 259.06 70.34 258.98 C 67.17 257.97 65.3 254.72 65.99 251.4 C 66.48 249.02 68.32 247.1 70.67 246.53 C 72.66 246.04 74.72 246.46 76.29 247.7 C 77.87 248.94 78.77 250.79 78.77 252.79 C 78.77 255.59 76.92 258.13 74.27 258.97 Z M 80 252.79 C 80 250.41 78.92 248.2 77.05 246.73 C 75.18 245.26 72.74 244.75 70.37 245.33 C 67.57 246.02 65.37 248.3 64.78 251.16 L 64.78 251.16 C 64 254.94 66.03 258.66 69.54 260 L 69.54 261.46 L 67.87 263.12 C 67.63 263.36 67.63 263.75 67.87 263.99 L 69.54 265.66 L 69.54 266.38 L 67.87 268.05 C 67.63 268.29 67.63 268.68 67.87 268.92 L 69.54 270.58 L 69.54 272.17 C 69.54 272.32 69.59 272.47 69.69 272.58 L 71.84 275.04 C 71.96 275.18 72.13 275.25 72.31 275.25 C 72.45 275.25 72.59 275.2 72.71 275.1 L 74.86 273.26 C 75 273.14 75.08 272.97 75.08 272.79 L 75.08 259.99 C 78 258.87 80 255.97 80 252.79 Z M 72.31 254.25 C 71.46 254.25 70.77 253.56 70.77 252.71 C 70.77 251.86 71.46 251.17 72.31 251.17 C 73.15 251.17 73.84 251.86 73.84 252.71 C 73.84 253.56 73.15 254.25 72.31 254.25 Z M 72.31 249.94 C 70.78 249.94 69.54 251.18 69.54 252.71 C 69.54 254.23 70.78 255.48 72.31 255.48 C 73.83 255.48 75.08 254.23 75.08 252.71 C 75.08 251.18 73.83 249.94 72.31 249.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 80px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="80" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
        <path d="M 95.45 110 L 270.05 110 Q 280.05 110 280.04 120 L 280 183.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 280 188.88 L 276.5 181.88 L 280 183.63 L 283.5 181.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 110px; margin-left: 190px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                プライマリ指定
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="113" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    プライマリ指定
                </text>
            </switch>
        </g>
        <rect x="420" y="220" width="120" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 217px; margin-left: 480px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                kms-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="217" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    kms-stack
                </text>
            </switch>
        </g>
        <path d="M 500 260 L 653.63 260" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 658.88 260 L 651.88 263.5 L 653.63 260 L 651.88 256.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 580px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                暗号化
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="580" y="263" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    暗号化
                </text>
            </switch>
        </g>
        <path d="M 460 240 L 500 240 L 500 280 L 460 280 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 487.38 267.56 L 491.69 267.56 L 491.69 266.33 L 487.38 266.33 Z M 481.85 267.56 L 486.15 267.56 L 486.15 266.33 L 481.85 266.33 Z M 476.31 267.56 L 480.61 267.56 L 480.61 266.33 L 476.31 266.33 Z M 483.08 262.63 C 483.08 261.96 483.63 261.4 484.31 261.4 C 484.99 261.4 485.54 261.96 485.54 262.63 C 485.54 263.31 484.99 263.86 484.31 263.86 C 483.63 263.86 483.08 263.31 483.08 262.63 Z M 486.77 262.63 C 486.77 261.28 485.67 260.17 484.31 260.17 C 482.95 260.17 481.85 261.28 481.85 262.63 C 481.85 263.99 482.95 265.1 484.31 265.1 C 485.67 265.1 486.77 263.99 486.77 262.63 Z M 478.15 261.4 C 478.83 261.4 479.38 261.96 479.38 262.63 C 479.38 263.31 478.83 263.86 478.15 263.86 C 477.47 263.86 476.92 263.31 476.92 262.63 C 476.92 261.96 477.47 261.4 478.15 261.4 Z M 478.15 265.1 C 479.51 265.1 480.61 263.99 480.61 262.63 C 480.61 261.28 479.51 260.17 478.15 260.17 C 476.79 260.17 475.69 261.28 475.69 262.63 C 475.69 263.99 476.79 265.1 478.15 265.1 Z M 496 255.86 L 496 270.02 C 496 270.36 495.72 270.63 495.38 270.63 L 476.31 270.63 L 476.31 269.4 L 494.77 269.4 L 494.77 256.48 L 481.23 256.48 L 481.23 255.25 L 495.38 255.25 C 495.72 255.25 496 255.52 496 255.86 Z M 474.27 258.97 C 474.02 259.05 473.84 259.29 473.84 259.56 L 473.84 272.51 L 472.37 273.77 L 470.77 271.94 L 470.77 270.33 C 470.77 270.16 470.7 270.01 470.59 269.89 L 469.18 268.48 L 470.59 267.07 C 470.7 266.95 470.77 266.8 470.77 266.63 L 470.77 265.4 C 470.77 265.24 470.7 265.08 470.59 264.97 L 469.18 263.56 L 470.59 262.15 C 470.7 262.03 470.77 261.87 470.77 261.71 L 470.77 259.56 C 470.77 259.29 470.59 259.06 470.34 258.98 C 467.17 257.97 465.3 254.72 465.99 251.4 C 466.48 249.02 468.32 247.1 470.67 246.53 C 472.66 246.04 474.72 246.46 476.29 247.7 C 477.87 248.94 478.77 250.79 478.77 252.79 C 478.77 255.59 476.92 258.13 474.27 258.97 Z M 480 252.79 C 480 250.41 478.92 248.2 477.05 246.73 C 475.18 245.26 472.74 244.75 470.37 245.33 C 467.57 246.02 465.37 248.3 464.78 251.16 L 464.78 251.16 C 464 254.94 466.03 258.66 469.54 260 L 469.54 261.46 L 467.87 263.12 C 467.63 263.36 467.63 263.75 467.87 263.99 L 469.54 265.66 L 469.54 266.38 L 467.87 268.05 C 467.63 268.29 467.63 268.68 467.87 268.92 L 469.54 270.58 L 469.54 272.17 C 469.54 272.32 469.59 272.47 469.69 272.58 L 471.84 275.04 C 471.96 275.18 472.13 275.25 472.31 275.25 C 472.45 275.25 472.59 275.2 472.71 275.1 L 474.86 273.26 C 475 273.14 475.08 272.97 475.08 272.79 L 475.08 259.99 C 478 258.87 480 255.97 480 252.79 Z M 472.31 254.25 C 471.46 254.25 470.77 253.56 470.77 252.71 C 470.77 251.86 471.46 251.17 472.31 251.17 C 473.15 251.17 473.84 251.86 473.84 252.71 C 473.84 253.56 473.15 254.25 472.31 254.25 Z M 472.31 249.94 C 470.78 249.94 469.54 251.18 469.54 252.71 C 469.54 254.23 470.78 255.48 472.31 255.48 C 473.83 255.48 475.08 254.23 475.08 252.71 C 475.08 251.18 473.83 249.94 472.31 249.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 287px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="299" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>