<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="562px" height="178px" viewBox="-0.5 -0.5 562 178" content="&lt;mxfile&gt;&lt;diagram id=&quot;XiMurMHVUEbgKz0XYkYH&quot; name=&quot;240822&quot;&gt;7VlRb5swEP41kbaHVoAJZI+FtN2kTaqWhz1ODhiw6mBknCbdr98ZDAFMu3ZljaKFpi3+7ozt8913ZzJD4WZ/K3CRfeMxYTPHivcztJw5ju978FcBjzXg2p9qIBU0riH7AKzoL6JBS6NbGpOypyg5Z5IWfTDieU4i2cOwEHzXV0s4649a4JQYwCrCzER/0FhmNbpw/AP+mdA0a0a2Pb2+DW6U9UrKDMd814HQ9QyFgnNZ3232IWHKdo1d6n43T0jbiQmSy5d0cNy6xwNmW724ghaE0Zxc0DwR8KCSb0VEyotS4uhez1o+NqbYZVSSVYEj1d7Bbs9QkMkNg5YNtwyvCQu4iIkIOeMC4JznoBuUUvB70oAzB1nV1Uoaw6qnJJSxQXfBt3lM4u4wd7ykkvIcsAiWT0A7eCBCUti4rwMFydVEMaPpqPqVFqy5lHwDAtOu2tSqB9l3IG3nW8I3RIpHUGmknt5z7fS2q9u7gwvNG52s6z4NiLXbpu2zDzsLN3pzxzfaM/Y5zLAMwM+GO1reExmp+ai9KDjNZTXsPIAPTCSsf+egGirk0pmPgGOYb4K2qQb/7LERhuAY5pugbaqpVjPrPjiG+XNzxsPe9khve9AbPmdzTmxOvpWKqcKW5pXLJjyXHVqBnxsVEkEqcExJT3aDXHfhdGRLKuBBNUXkXKiw7JEP9AlCG829MQJLqgskMS6zipqsZxiopZYh5zxBTR1SVSvUSdF2mrYOYDUkLovaHAndq3kEkGUKJdzsU5WPL/GudC8bZv8SqfkE0Kzv+loRsMRaZaM/UnmXsCegS8/t06VjmXTpjrClOwFZOgZZrhC0g20E3DhzPKbssRZwl6q7D6vKkLX445lQT4gBPOsKIf91DAD1K1R0/w0DlGiagG4D+AgB3ZwvOhF9/QBLCeB4AaW+Y33fgu2eDdw3eZomymEqsayFuzQcSSsf24cqpiKislOp+4y7EdjuJ3plhjA8dAIPmx8xZSCzvoZT750+S51zwgnlBHfpg/B1OQEuN/j09pywoXGsfMSI51bQhDQjiTxiWQjO3bwoGDl/izq4Jkkbi8Gx2TOD2hkJameCoDZfjvzD9x+t273o/UfrTROV2y8y85id7SkM7f9VwX0FIZLg6FxynxS9nkvudyu5kXO8gmg+WhAFW8ric7ieULgesxo6kXBVxdC6cux3f0vmvl+EQ/PwvVQl63y5h65/Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="17" width="560" height="160" rx="24" ry="24" fill="none" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 558px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                pipeline-infraresources-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    pipeline-infraresources-stack
                </text>
            </switch>
        </g>
        <path d="M 480 77 L 520 77 L 520 117 L 480 117 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 498.6 87.18 L 500.84 87.18 L 500.84 86.05 L 498.6 86.05 Z M 494.67 87.18 L 496.91 87.18 L 496.91 86.05 L 494.67 86.05 Z M 490.74 87.18 L 492.98 87.18 L 492.98 86.05 L 490.74 86.05 Z M 514.15 103.09 C 513.98 103.49 513.62 103.79 513.19 103.93 L 513.19 100.76 C 513.53 100.88 513.83 101.1 514.04 101.41 C 514.36 101.9 514.4 102.51 514.15 103.09 Z M 485.85 103.09 C 485.6 102.51 485.64 101.9 485.96 101.41 C 486.17 101.1 486.47 100.88 486.81 100.76 L 486.81 103.93 C 486.38 103.79 486.02 103.49 485.85 103.09 Z M 514.98 100.79 C 514.56 100.16 513.92 99.74 513.19 99.59 L 513.19 98.59 C 513.19 96.63 511.68 95.04 509.82 95.04 L 499.16 95.04 L 499.16 96.16 L 509.82 96.16 C 511.06 96.16 512.07 97.25 512.07 98.59 L 512.07 106.08 C 512.07 107.42 511.06 108.51 509.82 108.51 L 490.18 108.51 C 488.94 108.51 487.93 107.42 487.93 106.08 L 487.93 98.59 C 487.93 97.25 488.94 96.16 490.18 96.16 L 492.98 96.16 L 492.98 95.04 L 490.18 95.04 C 488.32 95.04 486.81 96.63 486.81 98.59 L 486.81 99.59 C 486.08 99.74 485.44 100.16 485.02 100.79 C 484.49 101.6 484.41 102.6 484.82 103.54 C 485.17 104.35 485.92 104.92 486.81 105.09 L 486.81 106.08 C 486.81 108.04 488.32 109.63 490.18 109.63 L 496.91 109.63 L 496.91 113 L 498.04 113 L 498.04 109.63 L 501.4 109.63 L 501.4 113 L 502.53 113 L 502.53 109.63 L 509.82 109.63 C 511.68 109.63 513.19 108.04 513.19 106.08 L 513.19 105.09 C 514.08 104.92 514.83 104.35 515.18 103.54 C 515.59 102.6 515.51 101.6 514.98 100.79 Z M 495.23 102.33 C 495.23 103.26 494.47 104.02 493.54 104.02 C 492.62 104.02 491.86 103.26 491.86 102.33 C 491.86 101.4 492.62 100.65 493.54 100.65 C 494.47 100.65 495.23 101.4 495.23 102.33 Z M 490.74 102.33 C 490.74 103.88 492 105.14 493.54 105.14 C 495.09 105.14 496.35 103.88 496.35 102.33 C 496.35 100.79 495.09 99.53 493.54 99.53 C 492 99.53 490.74 100.79 490.74 102.33 Z M 504.77 102.33 C 504.77 101.4 505.53 100.65 506.46 100.65 C 507.38 100.65 508.14 101.4 508.14 102.33 C 508.14 103.26 507.38 104.02 506.46 104.02 C 505.53 104.02 504.77 103.26 504.77 102.33 Z M 509.26 102.33 C 509.26 100.79 508 99.53 506.46 99.53 C 504.91 99.53 503.65 100.79 503.65 102.33 C 503.65 103.88 504.91 105.14 506.46 105.14 C 508 105.14 509.26 103.88 509.26 102.33 Z M 488.49 82.6 C 488.49 82.34 488.71 82.12 488.97 82.12 L 503.09 82.12 L 503.09 91.17 C 503.09 91.44 502.86 91.67 502.59 91.67 L 496.91 91.67 C 496.6 91.67 496.35 91.92 496.35 92.23 L 496.35 95.14 L 493.87 91.89 C 493.76 91.75 493.6 91.67 493.42 91.67 L 488.97 91.67 C 488.71 91.67 488.49 91.45 488.49 91.19 Z M 488.97 92.79 L 493.15 92.79 L 496.47 97.14 C 496.57 97.28 496.74 97.36 496.91 97.36 C 496.97 97.36 497.03 97.35 497.09 97.33 C 497.32 97.26 497.47 97.04 497.47 96.8 L 497.47 92.79 L 502.59 92.79 C 503.48 92.79 504.21 92.06 504.21 91.17 L 504.21 81.86 C 504.21 81.38 503.83 81 503.35 81 L 488.97 81 C 488.09 81 487.37 81.72 487.37 82.6 L 487.37 91.19 C 487.37 92.07 488.09 92.79 488.97 92.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 500px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ChatBot
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="500" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ChatBot
                </text>
            </switch>
        </g>
        <path d="M 40 77 L 80 77 L 80 117 L 40 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 71.94 98.65 L 72.16 97.11 C 74.18 98.32 74.21 98.82 74.21 98.83 C 74.2 98.84 73.86 99.13 71.94 98.65 Z M 70.83 98.34 C 67.33 97.29 62.46 95.05 60.49 94.12 C 60.49 94.11 60.49 94.11 60.49 94.1 C 60.49 93.34 59.88 92.72 59.12 92.72 C 58.36 92.72 57.75 93.34 57.75 94.1 C 57.75 94.85 58.36 95.47 59.12 95.47 C 59.45 95.47 59.75 95.35 59.99 95.15 C 62.31 96.25 67.14 98.45 70.67 99.49 L 69.27 109.32 C 69.27 109.35 69.27 109.37 69.27 109.4 C 69.27 110.27 65.43 111.86 59.17 111.86 C 52.84 111.86 48.97 110.27 48.97 109.4 C 48.97 109.37 48.97 109.35 48.97 109.32 L 46.05 88.06 C 48.57 89.8 53.99 90.71 59.18 90.71 C 64.35 90.71 69.76 89.8 72.29 88.07 Z M 45.75 85.84 C 45.79 85.09 50.11 82.14 59.18 82.14 C 68.24 82.14 72.56 85.09 72.6 85.84 L 72.6 86.1 C 72.11 87.79 66.51 89.57 59.18 89.57 C 51.83 89.57 46.23 87.78 45.75 86.09 Z M 73.75 85.86 C 73.75 83.88 68.07 81 59.18 81 C 50.28 81 44.6 83.88 44.6 85.86 L 44.66 86.29 L 47.83 109.44 C 47.9 112.03 54.81 113 59.17 113 C 64.59 113 70.34 111.76 70.41 109.45 L 71.78 99.79 C 72.54 99.97 73.17 100.07 73.67 100.07 C 74.35 100.07 74.8 99.9 75.08 99.57 C 75.31 99.3 75.4 98.97 75.33 98.62 C 75.18 97.83 74.24 96.98 72.33 95.89 L 73.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 60px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (SourceBucket)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="60" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <rect x="380" y="77" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 397.04 97.82 L 397.04 96 L 416.8 96 L 412.95 92.15 L 414.24 90.86 L 419.64 96.27 C 420 96.62 420 97.2 419.64 97.55 L 419.64 97.55 L 414.24 102.96 L 412.95 101.67 L 416.8 97.82 Z M 405.19 81.36 L 403.52 80.65 L 401.28 85.89 L 402.96 86.6 Z M 412.07 87.22 L 410.98 85.76 L 406.39 89.21 L 407.49 90.66 Z M 403.93 106.83 L 407.4 111.35 L 408.85 110.24 L 405.38 105.72 Z M 398.09 108.68 L 398.8 114.31 L 400.61 114.08 L 399.9 108.45 Z M 389.87 112.64 L 391.54 113.35 L 393.78 108.11 L 392.11 107.4 Z M 382.99 106.78 L 384.08 108.24 L 388.67 104.79 L 387.57 103.34 Z M 385.71 97.58 L 380 98.3 L 380.23 100.11 L 385.93 99.39 Z M 387.01 91.66 L 381.71 89.47 L 381.01 91.15 L 386.31 93.34 Z M 391.13 87.17 L 387.66 82.65 L 386.22 83.76 L 389.69 88.28 Z M 396.97 85.32 L 396.26 79.69 L 394.45 79.92 L 395.16 85.55 Z M 404.47 101.19 L 402.39 104.8 C 402.22 105.08 401.92 105.26 401.6 105.26 L 393.55 105.26 C 393.22 105.26 392.92 105.08 392.76 104.8 L 388.73 97.83 C 388.57 97.55 388.57 97.2 388.73 96.92 L 392.76 89.95 C 392.92 89.66 393.22 89.49 393.55 89.49 L 401.6 89.49 C 401.92 89.49 402.22 89.66 402.39 89.95 L 403.76 92.33 L 402.18 93.24 L 401.07 91.31 L 394.07 91.31 L 390.57 97.37 L 394.07 103.44 L 401.07 103.44 L 402.89 100.28 Z" fill="#b0084d" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 400px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                EventBridge Rule
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    EventB...
                </text>
            </switch>
        </g>
        <path d="M 120 37 L 140 37 L 140 57 L 120 57 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 136.41 39 L 123.59 39 C 122.71 39 122 39.71 122 40.59 L 122 44.39 C 122 45.27 122.71 45.98 123.59 45.98 L 124.04 45.98 L 124.04 54.71 C 124.04 54.87 124.17 55 124.33 55 L 135.38 55 C 135.54 55 135.67 54.87 135.67 54.71 L 135.67 45.98 L 136.41 45.98 C 137.29 45.98 138 45.27 138 44.39 L 138 40.59 C 138 39.71 137.29 39 136.41 39 Z M 124.62 54.42 L 124.62 45.98 L 135.09 45.98 L 135.09 54.42 Z M 136.41 45.4 L 123.59 45.4 C 123.03 45.4 122.58 44.95 122.58 44.39 L 122.58 44.24 L 126.07 44.24 L 126.07 43.65 L 122.58 43.65 L 122.58 40.59 C 122.58 40.03 123.03 39.58 123.59 39.58 L 136.41 39.58 C 136.97 39.58 137.42 40.03 137.42 40.59 L 137.42 43.65 L 129.27 43.65 L 129.27 44.24 L 137.42 44.24 L 137.42 44.39 C 137.42 44.95 136.97 45.4 136.41 45.4 Z M 125.64 50.07 C 125.64 49.98 125.67 49.9 125.74 49.85 L 127.58 48.22 L 127.97 48.66 L 126.37 50.06 L 127.96 51.43 L 127.58 51.87 L 125.74 50.29 C 125.68 50.23 125.64 50.15 125.64 50.07 Z M 131.48 51.45 L 133.07 50.04 L 131.48 48.66 L 131.86 48.22 L 133.71 49.82 C 133.77 49.88 133.81 49.96 133.81 50.04 C 133.81 50.12 133.77 50.2 133.71 50.26 L 131.86 51.88 Z M 128.88 53.01 L 128.35 52.79 L 130.56 47.4 L 131.09 47.62 Z M 126.95 44.24 L 128.4 44.24 L 128.4 43.65 L 126.95 43.65 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 47px; margin-left: 142px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodePipeline
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="142" y="51" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Code...
                </text>
            </switch>
        </g>
        <rect x="120" y="37" width="220" height="120" fill="none" stroke="#4d72f3" stroke-dasharray="3 3" pointer-events="all"/>
        <path d="M 160 77 L 200 77 L 200 117 L 160 117 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 191.94 98.65 L 192.16 97.11 C 194.18 98.32 194.21 98.82 194.21 98.83 C 194.2 98.84 193.86 99.13 191.94 98.65 Z M 190.83 98.34 C 187.33 97.29 182.46 95.05 180.49 94.12 C 180.49 94.11 180.49 94.11 180.49 94.1 C 180.49 93.34 179.88 92.72 179.12 92.72 C 178.36 92.72 177.75 93.34 177.75 94.1 C 177.75 94.85 178.36 95.47 179.12 95.47 C 179.45 95.47 179.75 95.35 179.99 95.15 C 182.31 96.25 187.14 98.45 190.67 99.49 L 189.27 109.32 C 189.27 109.35 189.27 109.37 189.27 109.4 C 189.27 110.27 185.43 111.86 179.17 111.86 C 172.84 111.86 168.97 110.27 168.97 109.4 C 168.97 109.37 168.97 109.35 168.97 109.32 L 166.05 88.06 C 168.57 89.8 173.99 90.71 179.18 90.71 C 184.35 90.71 189.76 89.8 192.29 88.07 Z M 165.75 85.84 C 165.79 85.09 170.11 82.14 179.18 82.14 C 188.24 82.14 192.56 85.09 192.6 85.84 L 192.6 86.1 C 192.11 87.79 186.51 89.57 179.18 89.57 C 171.83 89.57 166.23 87.78 165.75 86.09 Z M 193.75 85.86 C 193.75 83.88 188.07 81 179.18 81 C 170.28 81 164.6 83.88 164.6 85.86 L 164.66 86.29 L 167.83 109.44 C 167.9 112.03 174.81 113 179.17 113 C 184.59 113 190.34 111.76 190.41 109.45 L 191.78 99.79 C 192.54 99.97 193.17 100.07 193.67 100.07 C 194.35 100.07 194.8 99.9 195.08 99.57 C 195.31 99.3 195.4 98.97 195.33 98.62 C 195.18 97.83 194.24 96.98 192.33 95.89 L 193.69 86.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 180px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (SourceArtifact)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="180" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <path d="M 260 77 L 300 77 L 300 117 L 260 117 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 293.99 87.08 L 290.71 84.08 L 287.43 87.08 Z M 289.46 83.74 L 282.8 83.75 L 286.35 86.58 Z M 286.45 93.42 L 281.22 95.35 L 291.29 95.35 Z M 278.76 106.37 L 293.53 106.37 L 293.53 96.45 L 278.76 96.45 Z M 285.22 87.08 L 281.6 84.2 L 277.99 87.08 Z M 276.84 86.6 L 280.39 83.76 L 273.32 83.77 Z M 275.69 87.08 L 272.07 84.18 L 268.45 87.08 L 273.84 87.08 Z M 273.3 88.18 L 268.87 88.18 L 273.3 92.57 Z M 273.3 94.29 L 268.1 99.02 L 273.3 104.1 Z M 273.3 105.89 L 267.83 111.24 L 267.83 111.32 L 273.3 111.32 Z M 267.83 100.29 L 267.83 109.7 L 272.64 105 Z M 267.83 97.78 L 272.61 93.43 L 267.83 88.7 Z M 267.28 86.61 L 270.82 83.77 L 267.28 83.78 Z M 266.19 82.68 L 265.09 82.68 L 265.09 88.18 L 266.19 88.18 L 266.19 87.63 L 266.19 83.23 Z M 295.92 87.83 C 295.84 88.04 295.63 88.18 295.41 88.18 L 286.97 88.18 L 286.97 92.59 L 294.28 95.39 L 294.28 95.39 C 294.48 95.47 294.62 95.67 294.62 95.9 L 294.62 106.92 C 294.62 107.22 294.38 107.47 294.08 107.47 L 278.22 107.47 C 277.92 107.47 277.67 107.22 277.67 106.92 L 277.67 95.9 C 277.67 95.66 277.82 95.46 278.03 95.39 L 278.03 95.38 L 285.87 92.59 L 285.87 88.18 L 274.39 88.18 L 274.39 111.88 C 274.39 112.18 274.15 112.43 273.84 112.43 L 267.28 112.43 C 266.98 112.43 266.73 112.18 266.73 111.88 L 266.73 89.29 L 264.55 89.29 C 264.25 89.29 264 89.04 264 88.74 L 264 82.12 C 264 81.82 264.25 81.57 264.55 81.57 L 266.73 81.57 C 267.04 81.57 267.28 81.82 267.28 82.12 L 267.28 82.67 L 290.79 82.68 L 295.78 87.23 C 295.94 87.38 296 87.62 295.92 87.83 Z M 284.57 105.43 L 287.75 98.15 L 286.75 97.7 L 283.57 104.99 Z M 287.56 103 L 288.27 103.84 L 290.63 101.82 C 290.75 101.73 290.82 101.58 290.82 101.43 C 290.83 101.28 290.77 101.13 290.67 101.02 L 288.69 99 L 287.92 99.78 L 289.47 101.37 Z M 280.46 101.4 C 280.34 101.29 280.27 101.13 280.28 100.96 C 280.29 100.8 280.37 100.65 280.5 100.55 L 283.19 98.55 L 283.84 99.43 L 281.69 101.04 L 283.49 102.7 L 282.75 103.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 280px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodeBuild
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CodeBu...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>