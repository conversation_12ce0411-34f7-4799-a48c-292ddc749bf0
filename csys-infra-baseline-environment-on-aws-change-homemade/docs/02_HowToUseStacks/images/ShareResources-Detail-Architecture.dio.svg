<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="891px" height="878px" viewBox="-0.5 -0.5 891 878" content="&lt;mxfile&gt;&lt;diagram id=&quot;KbvtEL4ZbZiMRcHTK1LA&quot; name=&quot;240830&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;bB2WDEhCmBBRvsyurUcO&quot; name=&quot;240828&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;2Y8jC7U9ZkPlZ7FMHuWo&quot; name=&quot;240816&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f54749-1-c7131f-1-s-0">
            <stop offset="0%" style="stop-color: rgb(199, 19, 31); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(245, 71, 73); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="20" y="57" width="640" height="600" rx="90" ry="90" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 638px; height: 1px; padding-top: 54px; margin-left: 21px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                vpc-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    vpc-construct
                </text>
            </switch>
        </g>
        <rect x="0" y="17" width="890" height="660" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 888px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                share-resources
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="445" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    share-resources
                </text>
            </switch>
        </g>
        <rect x="400" y="717" width="160" height="160" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 158px; height: 1px; padding-top: 714px; margin-left: 401px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ecs-app-stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="714" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecs-app-stack
                </text>
            </switch>
        </g>
        <rect x="420" y="757" width="120" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 754px; margin-left: 421px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ecs-app-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="754" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecs-app-construct
                </text>
            </switch>
        </g>
        <rect x="680" y="217" width="200" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 214px; margin-left: 681px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                cognito-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="214" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    cognito-construct
                </text>
            </switch>
        </g>
        <path d="M 710 237 L 750 237 L 750 277 L 710 277 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 716.83 254.61 L 724.18 254.61 L 724.18 253.48 L 716.83 253.48 Z M 742.12 262.83 L 742.92 263.62 L 738.72 267.82 C 738.61 267.93 738.46 267.98 738.32 267.98 C 738.17 267.98 738.03 267.93 737.92 267.82 L 735.81 265.71 L 736.61 264.91 L 738.32 266.62 Z M 744.72 265.94 C 744.59 267 744.16 267.99 743.47 268.79 C 742.98 269.37 742.36 269.84 741.68 270.16 C 740.77 270.6 739.75 270.76 738.74 270.64 C 737.76 270.52 736.83 270.13 736.06 269.52 C 734.58 268.34 733.83 266.52 734.06 264.64 C 734.27 262.86 735.35 261.31 736.94 260.49 C 737.7 260.1 738.53 259.9 739.38 259.9 C 739.6 259.9 739.82 259.91 740.04 259.94 C 741.81 260.15 743.36 261.24 744.17 262.83 C 744.66 263.79 744.85 264.87 744.72 265.94 Z M 745.18 262.32 C 744.19 260.39 742.32 259.08 740.17 258.81 C 738.88 258.66 737.58 258.89 736.42 259.49 C 734.5 260.48 733.2 262.35 732.94 264.5 C 732.66 266.77 733.57 268.98 735.36 270.4 C 736.29 271.14 737.41 271.61 738.6 271.76 C 738.87 271.79 739.13 271.81 739.39 271.81 C 740.35 271.81 741.29 271.6 742.16 271.18 C 742.99 270.79 743.74 270.22 744.33 269.53 C 745.16 268.55 745.69 267.36 745.84 266.07 C 746 264.78 745.77 263.48 745.18 262.32 Z M 722.48 258 L 724.74 258 L 724.74 256.87 L 722.48 256.87 Z M 716.83 258 L 721.35 258 L 721.35 256.87 L 716.83 256.87 Z M 716.34 243.32 L 741.62 243.32 C 742.29 243.32 742.83 243.98 742.83 244.79 L 742.83 248.97 L 741.14 248.97 L 741.14 246.15 C 741.14 245.83 740.88 245.58 740.57 245.58 L 727.57 245.58 C 727.25 245.58 727 245.83 727 246.15 L 727 248.97 L 715.13 248.97 L 715.13 244.79 C 715.13 243.99 715.68 243.32 716.34 243.32 Z M 734.08 248.8 C 735.39 248.8 736.46 249.86 736.46 251.16 C 736.46 252.01 735.99 252.8 735.23 253.21 C 734.5 253.6 733.64 253.6 732.92 253.21 C 732.17 252.8 731.7 252.01 731.7 251.16 C 731.7 249.86 732.76 248.8 734.08 248.8 Z M 715.13 261.05 L 715.13 250.1 L 727 250.1 L 727 259.69 C 727 260.01 727.25 260.26 727.57 260.26 L 733.48 260.26 L 733.48 259.13 L 728.76 259.13 C 728.8 256.87 730.31 254.92 732.47 254.29 C 733.47 254.76 734.66 254.77 735.68 254.29 C 736.91 254.65 737.99 255.47 738.65 256.58 L 739.62 255.99 C 738.94 254.86 737.9 253.98 736.69 253.47 C 737.26 252.84 737.59 252.02 737.59 251.16 C 737.59 249.24 736.01 247.67 734.08 247.67 C 732.14 247.67 730.57 249.24 730.57 251.16 C 730.57 252.02 730.89 252.83 731.46 253.46 C 729.95 254.1 728.77 255.29 728.13 256.76 L 728.13 246.71 L 740 246.71 L 740 255.74 L 741.14 255.74 L 741.14 250.1 L 742.83 250.1 L 742.83 258.56 L 743.96 258.56 L 743.96 244.79 C 743.96 243.36 742.91 242.19 741.62 242.19 L 716.34 242.19 C 715.05 242.19 714 243.36 714 244.79 L 714 261.05 C 714 262.48 715.05 263.64 716.34 263.64 L 731.52 263.64 L 731.52 262.52 L 716.34 262.52 C 715.68 262.52 715.13 261.84 715.13 261.05 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 284px; margin-left: 730px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Cognito
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="296" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Cognito
                </text>
            </switch>
        </g>
        <image x="809.5" y="236.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA2UExURd00TOZneeh0hN9BV////+yNm/bM0uqAj+FNYuNabvKzvPnZ3f3y9Pvm6fS/x+6apvCmsAAAAKTEzdYAAAASdFJOU///////////////////////AOK/vxIAAAAJcEhZcwAAFxEAABcRAcom8z8AAAU7SURBVGhD7ZqNkqsqEIQjRNdVY3z/pz3diAo4GCDsrbpVfucnxBiaYYZh1Dxubm5ubm5ubm4sTdPYVk2Uff2Afj6f2rbr0T4727rmT9R/0GmS/N/Y3iXI6/5XtTivVU1vD9XiWl7/tsOIMw6m+V0z/C7km2FVPDFEYrXANxH530N6msYZL103re/JIjhhmGwjB0m+mVelqVvULw+gzRfdvHk66UJLMdyphvXW7qnbZ9iLeWWnZfa0ePA0ohQC+cXY3bX2LaG6bRradWoW+xZQfCwR9+WbF/vt/MjW8+xb+miM/ridZsRtOxtHnv1Mrt0xjP6wtr+wnDjyU5I2aZ82yr+ynBzyfXo6sad+aTkJQi+Dry0npfIVLCdl8lUsJyXylSwnUXnVtq0cjEsly4ko/9422fFtj7gsFSxvbNI8y7P+OYilAr2kr9Mz4+a8UJ7vYfU82P1VnuYeW7BtFvBGv3bwvjyT/tTazZz11ZbefHp8YJNuPtzC9jmlvPWDZlUxOH6NLi8Ov7Tqg6BjUrcLsE/f0zwiGgm/FMZ+gy7dUm3ZrEWXzhZuoPVSkHPuEy9NAjC/4iqHn89exojEwMf8FRnPYYvrBaM6r3CsQFGFvZSsfPhSNJ1mnicTESrXrshJJWEf9ZhoDW20TR8V++CS+LfwwXkVhQXmgThVn4B3IzMGnQzb4x68AkOO5OhM2zGJckRccBGronrc9gs7omBRx0JVHFfcdmai1HJ4A24P89lGru0wZLbNVC4iNdf2gjUnSjz0u+u42Y14CcBR/C8ama0emUiUax+QggWH88IO+5uUtrmxXyMZj2Sbl2+waUgpAuqzUj+qxb/zHxXJUPBKetBr/dCI00644YLur/oRoht9cckNyXdvWFVYwr7y1R1fpfk+Q71X3sr/T9UbXk+MTkDVUKfjsbTg95OvPHVmEWLk9fP1nJCiQnV2gm+hJkx1PEDMn/oBbspmdoMkYL9MEET6VlbME9j1sk0X13Y4aGqMKg9t6pEVl7fe0Zm0KbvqcDKl4CNTY/b9b4+pkWzHBOWpRzYNVx01C9MhDNs3QyHqCPo6b4qX4BtCjLrqnOtFMfPvhsnqOFHy4hVypHiFAoZiOFKyrI6juRcUmFehI9f2h15vH6w96x5EvxQrVGLIQe+pG6ue7bqOt5gX1LODDqCnc6iEJRpOsq24Oj/JyDQr4nwFtnNl2I7j6nZt5CFWY3F1gxh1mHjpttI17PnkrkD9sSz7RT2B+inX0YrM1U7g41N5E/o9QLIdAxYi8SNStIS2Bwjq4hSmAIeFxkN9Uaoxf33MMSS+UB2zlR9zRPAY1D8QqPNJRZHpphIOxv25ng9WKRL2K3uxr7DE8geuu/Ga9bGReVZHmAwTC6oznOiCkastXrRUaqWDecsfutrDVaNVsNY3mi8fI+rlaoEmktvHIV+BDxlOoKI8l1luxqgmD8sL0lUleSNeEHtV5MssJxXkjc+N5eeLujjrqV/LH5ajgE3dLZqtHv1S3vE5M3aS+3k5te01X8kf0749f/PqKDDMQSWlzS742tLDF/J+wK39Pud9/yI44I6nQf0KuiO3F8u7lhu4e/DY9hwOI8Lb/YzePhp1b2YUy7Pn0NHtqg8PrCPgOeaDXm1PZV/22manWF5Y54qdrbzmxQTY0Flh4P0exlIqH1ixottDzKd7y3t5se8j/CzBCF7j7N8786gtD7RSbwbmsrRNL87SwR/IE/RqW9dQvkKV4+OtuEu6givJT6Srp/5KNYu/+T3tzc3Nzc3Nzc3/kcfjH5iiLM2nDfYKAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="789" y="285" width="84" height="15" stroke-width="0"/>
            <text x="829.5" y="294.5">
                SecretManager
            </text>
        </g>
        <rect x="680" y="57" width="200" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 54px; margin-left: 681px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                chatbot-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="54" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    chatbot-construct
                </text>
            </switch>
        </g>
        <path d="M 460 777 L 500 777 L 500 817 L 460 817 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 479.04 795.38 C 478.76 795.54 478.59 795.84 478.59 796.16 L 478.59 811.72 L 466.82 805.14 L 466.82 789.85 L 480.03 782.19 L 491.77 788.01 Z M 493.13 787.99 C 493.13 787.67 492.96 787.37 492.65 787.19 L 480.47 781.16 C 480.2 781 479.85 781 479.57 781.16 L 466.15 788.94 C 465.87 789.1 465.7 789.4 465.7 789.72 L 465.7 805.27 C 465.7 805.59 465.87 805.89 466.16 806.06 L 478.36 812.88 C 478.5 812.96 478.65 813 478.81 813 C 478.97 813 479.12 812.96 479.26 812.88 C 479.54 812.72 479.71 812.42 479.71 812.1 L 479.71 796.29 L 492.68 788.78 C 492.96 788.62 493.13 788.32 493.13 787.99 Z M 493.16 805.18 L 482.51 811.69 L 482.51 805.86 L 488.06 802.28 L 488.1 802.02 C 488.11 801.96 488.12 801.96 488.12 801.66 L 488.13 794.7 L 493.18 791.78 Z M 493.85 790.61 C 493.57 790.45 493.22 790.45 492.95 790.61 L 487.58 793.72 C 487.41 793.81 487.01 794.03 487.01 794.47 L 487 801.63 L 481.88 804.93 C 481.57 805.11 481.39 805.4 481.39 805.7 L 481.39 812.05 C 481.39 812.37 481.56 812.66 481.85 812.82 C 482 812.91 482.17 812.95 482.33 812.95 C 482.49 812.95 482.66 812.91 482.8 812.83 L 493.83 806.08 C 494.11 805.92 494.28 805.62 494.28 805.3 L 494.3 791.39 C 494.3 791.07 494.13 790.77 493.85 790.61 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 824px; margin-left: 480px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECR Repository
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="480" y="836" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ECR Re...
                </text>
            </switch>
        </g>
        <path d="M 640 777 L 680 777 L 680 817 L 640 817 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 663.91 805.73 L 671.19 805.73 L 671.19 806.85 L 663.91 806.85 L 663.91 807.97 L 662.8 807.97 L 662.8 806.85 L 660.56 806.85 L 660.56 805.73 L 662.8 805.73 L 662.8 805.17 L 663.91 805.17 Z M 666.15 802.37 L 671.19 802.37 L 671.19 803.49 L 666.15 803.49 L 666.15 804.61 L 665.03 804.61 L 665.03 803.49 L 660.56 803.49 L 660.56 802.37 L 665.03 802.37 L 665.03 801.81 L 666.15 801.81 Z M 668.95 799.02 L 671.19 799.02 L 671.19 800.13 L 668.95 800.13 L 668.95 801.25 L 667.83 801.25 L 667.83 800.13 L 660.56 800.13 L 660.56 799.02 L 667.83 799.02 L 667.83 798.46 L 668.95 798.46 Z M 649.37 801.25 L 654.41 801.25 L 654.41 802.37 L 649.37 802.37 C 648.67 802.37 647.92 802.16 647.37 801.81 C 646.24 801.09 644.34 799.42 644.34 796.25 C 644.34 792.4 646.95 790.98 648.47 790.49 L 648.44 789.98 C 648.44 786.81 650.53 783.56 653.31 782.38 C 656.57 781 660.02 781.69 662.54 784.22 C 663.32 785 663.97 785.96 664.46 787.07 C 665.45 786.24 666.8 785.96 668.05 786.37 C 669.65 786.89 670.64 788.37 670.75 790.35 C 672.34 790.73 675.66 792.03 675.66 796.3 C 675.66 796.46 675.65 796.61 675.65 796.76 L 674.53 796.73 C 674.53 796.58 674.54 796.44 674.54 796.3 C 674.54 792.53 671.43 791.58 670.1 791.35 C 669.95 791.33 669.81 791.24 669.73 791.11 C 669.64 790.99 669.61 790.83 669.64 790.68 C 669.63 789.02 668.92 787.83 667.7 787.43 C 666.61 787.07 665.41 787.47 664.72 788.42 C 664.59 788.58 664.39 788.67 664.18 788.64 C 663.98 788.61 663.81 788.47 663.74 788.27 C 663.27 786.96 662.6 785.86 661.75 785.01 C 659.56 782.81 656.57 782.22 653.75 783.41 C 651.4 784.41 649.56 787.28 649.56 789.94 L 649.61 790.87 C 649.63 791.14 649.45 791.38 649.19 791.44 C 647.8 791.8 645.46 792.89 645.46 796.25 C 645.46 798.75 646.82 800.14 647.97 800.86 C 648.35 801.1 648.88 801.25 649.37 801.25 Z M 674.54 804.23 L 673.55 804.17 C 673.29 804.16 673.04 804.34 672.98 804.61 C 672.79 805.45 672.46 806.24 672 806.97 C 671.85 807.2 671.9 807.5 672.1 807.69 L 672.84 808.34 L 671 810.18 L 670.35 809.44 C 670.17 809.24 669.86 809.2 669.63 809.34 C 668.9 809.8 668.11 810.13 667.27 810.32 C 667 810.38 666.82 810.63 666.83 810.9 L 666.89 811.88 L 664.3 811.88 L 664.35 810.9 C 664.37 810.63 664.18 810.38 663.92 810.32 C 663.08 810.13 662.28 809.8 661.55 809.34 C 661.32 809.19 661.02 809.24 660.84 809.44 L 660.18 810.18 L 658.35 808.34 L 659.08 807.69 C 659.29 807.5 659.33 807.2 659.18 806.97 C 658.72 806.24 658.4 805.45 658.21 804.61 C 658.15 804.34 657.88 804.16 657.63 804.17 L 656.64 804.23 L 656.64 801.63 L 657.63 801.69 C 657.89 801.71 658.15 801.52 658.21 801.26 C 658.4 800.42 658.73 799.63 659.19 798.9 C 659.34 798.67 659.29 798.36 659.09 798.18 L 658.35 797.52 L 660.18 795.69 L 660.84 796.43 C 661.03 796.63 661.33 796.67 661.56 796.53 C 662.29 796.07 663.08 795.74 663.92 795.55 C 664.18 795.49 664.37 795.25 664.35 794.97 L 664.3 793.98 L 666.89 793.98 L 666.83 794.97 C 666.82 795.25 667 795.49 667.27 795.55 C 668.1 795.74 668.9 796.07 669.63 796.53 C 669.86 796.67 670.16 796.63 670.34 796.43 L 671 795.69 L 672.84 797.52 L 672.1 798.18 C 671.89 798.36 671.85 798.67 672 798.9 C 672.46 799.62 672.79 800.42 672.98 801.26 C 673.04 801.52 673.29 801.71 673.55 801.69 L 674.54 801.63 Z M 675.07 800.48 L 673.95 800.55 C 673.77 799.91 673.51 799.29 673.19 798.71 L 674.02 797.96 C 674.14 797.86 674.21 797.71 674.21 797.56 C 674.22 797.41 674.16 797.26 674.05 797.15 L 671.37 794.48 C 671.27 794.37 671.11 794.3 670.96 794.31 C 670.81 794.32 670.66 794.38 670.56 794.5 L 669.81 795.34 C 669.23 795.01 668.62 794.76 667.98 794.58 L 668.04 793.45 C 668.05 793.3 667.99 793.15 667.89 793.04 C 667.78 792.93 667.64 792.86 667.48 792.86 L 663.7 792.86 C 663.55 792.86 663.4 792.93 663.3 793.04 C 663.19 793.15 663.14 793.3 663.14 793.45 L 663.21 794.58 C 662.57 794.76 661.95 795.01 661.37 795.34 L 660.62 794.5 C 660.52 794.38 660.38 794.32 660.22 794.31 C 660.06 794.31 659.92 794.37 659.81 794.48 L 657.14 797.15 C 657.03 797.26 656.97 797.41 656.97 797.56 C 656.98 797.71 657.05 797.86 657.16 797.96 L 658 798.71 C 657.68 799.29 657.42 799.91 657.24 800.55 L 656.12 800.48 C 655.97 800.47 655.81 800.53 655.7 800.63 C 655.59 800.74 655.53 800.89 655.53 801.04 L 655.53 804.82 C 655.53 804.98 655.59 805.12 655.7 805.23 C 655.81 805.33 655.97 805.39 656.12 805.38 L 657.23 805.32 C 657.42 805.96 657.67 806.57 658 807.16 L 657.16 807.9 C 657.05 808 656.98 808.15 656.97 808.3 C 656.97 808.46 657.03 808.61 657.14 808.71 L 659.81 811.39 C 659.92 811.5 660.08 811.56 660.22 811.55 C 660.38 811.55 660.52 811.48 660.62 811.36 L 661.37 810.53 C 661.95 810.86 662.57 811.11 663.21 811.29 L 663.14 812.41 C 663.14 812.56 663.19 812.71 663.3 812.82 C 663.4 812.94 663.55 813 663.7 813 L 667.48 813 C 667.64 813 667.78 812.94 667.89 812.82 C 667.99 812.71 668.05 812.56 668.04 812.41 L 667.98 811.3 C 668.62 811.11 669.24 810.86 669.82 810.53 L 670.56 811.36 C 670.66 811.48 670.81 811.55 670.96 811.55 C 671.11 811.56 671.27 811.5 671.37 811.39 L 674.05 808.71 C 674.16 808.61 674.22 808.46 674.21 808.3 C 674.21 808.15 674.14 808 674.02 807.9 L 673.19 807.16 C 673.51 806.57 673.77 805.96 673.95 805.32 L 675.07 805.38 C 675.22 805.39 675.37 805.33 675.48 805.23 C 675.6 805.12 675.66 804.98 675.66 804.82 L 675.66 801.04 C 675.66 800.89 675.6 800.74 675.48 800.63 C 675.37 800.53 675.22 800.47 675.07 800.48 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 824px; margin-left: 660px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                SSM
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="660" y="836" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SSM
                </text>
            </switch>
        </g>
        <path d="M 320 517 L 350 517 Q 360 517 360 527 L 360 787 Q 360 797 370 797 L 453.63 797" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 458.88 797 L 451.88 800.5 L 453.63 797 L 451.88 793.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 320 517 L 590 517 Q 600 517 600 527 L 600 787 Q 600 797 610 797 L 633.63 797" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 638.88 797 L 631.88 800.5 L 633.63 797 L 631.88 793.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 710 77 L 750 77 L 750 117 L 710 117 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 728.6 87.18 L 730.84 87.18 L 730.84 86.05 L 728.6 86.05 Z M 724.67 87.18 L 726.91 87.18 L 726.91 86.05 L 724.67 86.05 Z M 720.74 87.18 L 722.98 87.18 L 722.98 86.05 L 720.74 86.05 Z M 744.15 103.09 C 743.98 103.49 743.62 103.79 743.19 103.93 L 743.19 100.76 C 743.53 100.88 743.83 101.1 744.04 101.41 C 744.36 101.9 744.4 102.51 744.15 103.09 Z M 715.85 103.09 C 715.6 102.51 715.64 101.9 715.96 101.41 C 716.17 101.1 716.47 100.88 716.81 100.76 L 716.81 103.93 C 716.38 103.79 716.02 103.49 715.85 103.09 Z M 744.98 100.79 C 744.56 100.16 743.92 99.74 743.19 99.59 L 743.19 98.59 C 743.19 96.63 741.68 95.04 739.82 95.04 L 729.16 95.04 L 729.16 96.16 L 739.82 96.16 C 741.06 96.16 742.07 97.25 742.07 98.59 L 742.07 106.08 C 742.07 107.42 741.06 108.51 739.82 108.51 L 720.18 108.51 C 718.94 108.51 717.93 107.42 717.93 106.08 L 717.93 98.59 C 717.93 97.25 718.94 96.16 720.18 96.16 L 722.98 96.16 L 722.98 95.04 L 720.18 95.04 C 718.32 95.04 716.81 96.63 716.81 98.59 L 716.81 99.59 C 716.08 99.74 715.44 100.16 715.02 100.79 C 714.49 101.6 714.41 102.6 714.82 103.54 C 715.17 104.35 715.92 104.92 716.81 105.09 L 716.81 106.08 C 716.81 108.04 718.32 109.63 720.18 109.63 L 726.91 109.63 L 726.91 113 L 728.04 113 L 728.04 109.63 L 731.4 109.63 L 731.4 113 L 732.53 113 L 732.53 109.63 L 739.82 109.63 C 741.68 109.63 743.19 108.04 743.19 106.08 L 743.19 105.09 C 744.08 104.92 744.83 104.35 745.18 103.54 C 745.59 102.6 745.51 101.6 744.98 100.79 Z M 725.23 102.33 C 725.23 103.26 724.47 104.02 723.54 104.02 C 722.62 104.02 721.86 103.26 721.86 102.33 C 721.86 101.4 722.62 100.65 723.54 100.65 C 724.47 100.65 725.23 101.4 725.23 102.33 Z M 720.74 102.33 C 720.74 103.88 722 105.14 723.54 105.14 C 725.09 105.14 726.35 103.88 726.35 102.33 C 726.35 100.79 725.09 99.53 723.54 99.53 C 722 99.53 720.74 100.79 720.74 102.33 Z M 734.77 102.33 C 734.77 101.4 735.53 100.65 736.46 100.65 C 737.38 100.65 738.14 101.4 738.14 102.33 C 738.14 103.26 737.38 104.02 736.46 104.02 C 735.53 104.02 734.77 103.26 734.77 102.33 Z M 739.26 102.33 C 739.26 100.79 738 99.53 736.46 99.53 C 734.91 99.53 733.65 100.79 733.65 102.33 C 733.65 103.88 734.91 105.14 736.46 105.14 C 738 105.14 739.26 103.88 739.26 102.33 Z M 718.49 82.6 C 718.49 82.34 718.71 82.12 718.97 82.12 L 733.09 82.12 L 733.09 91.17 C 733.09 91.44 732.86 91.67 732.59 91.67 L 726.91 91.67 C 726.6 91.67 726.35 91.92 726.35 92.23 L 726.35 95.14 L 723.87 91.89 C 723.76 91.75 723.6 91.67 723.42 91.67 L 718.97 91.67 C 718.71 91.67 718.49 91.45 718.49 91.19 Z M 718.97 92.79 L 723.15 92.79 L 726.47 97.14 C 726.57 97.28 726.74 97.36 726.91 97.36 C 726.97 97.36 727.03 97.35 727.09 97.33 C 727.32 97.26 727.47 97.04 727.47 96.8 L 727.47 92.79 L 732.59 92.79 C 733.48 92.79 734.21 92.06 734.21 91.17 L 734.21 81.86 C 734.21 81.38 733.83 81 733.35 81 L 718.97 81 C 718.09 81 717.37 81.72 717.37 82.6 L 717.37 91.19 C 717.37 92.07 718.09 92.79 718.97 92.79 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 124px; margin-left: 730px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ChatBot
                                <br/>
                                Slack Client
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="136" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ChatBo...
                </text>
            </switch>
        </g>
        <path d="M 849.1 100.63 C 848.97 100.52 848.82 100.43 848.66 100.37 C 847.73 92.1 840.68 85.72 832.28 85.72 C 828.49 85.72 824.79 87.04 821.86 89.43 C 817.99 92.58 815.78 97.24 815.78 102.22 C 815.78 102.26 815.78 102.29 815.78 102.33 C 815.19 102.63 814.56 102.91 813.94 103.19 C 812.04 104.01 810.67 104.61 810.47 105.69 C 810.42 105.97 810.41 106.51 810.93 107.03 C 811.85 107.95 813.62 108.28 815.41 108.28 C 816.16 108.28 816.91 108.22 817.61 108.13 C 818.8 107.97 821.02 107.53 822.53 106.43 C 824.9 104.7 830.28 104.06 833.08 104.28 C 834.75 104.4 836.77 105.28 838.4 105.99 C 839.64 106.52 840.62 106.95 841.39 107.06 C 842.44 107.2 844.17 107.03 846 106.84 L 846.15 106.82 C 846.63 106.77 847.1 106.73 847.57 106.69 C 847.76 106.67 847.95 106.66 848.2 106.64 C 848.98 106.58 849.59 105.93 849.59 105.15 L 849.59 101.75 C 849.59 101.32 849.41 100.92 849.1 100.63 Z M 832.28 87.5 C 839.71 87.5 845.95 93.09 846.87 100.37 C 846.61 100.39 846.34 100.42 846.06 100.44 L 845.9 100.46 C 845.87 99.64 845.21 98.98 844.39 98.98 L 843.75 98.98 C 841.8 93.23 836.14 89.27 829.73 89.27 C 827.35 89.27 825.03 89.81 822.94 90.84 C 822.96 90.83 822.97 90.82 822.98 90.8 C 825.59 88.67 828.89 87.5 832.28 87.5 Z M 841.41 101.97 L 841.41 100.76 L 844.12 100.76 L 844.12 101.97 Z M 817.37 106.37 C 815.02 106.69 813.16 106.4 812.39 105.93 C 812.86 105.6 813.91 105.14 814.65 104.81 C 815.29 104.53 815.94 104.24 816.57 103.92 C 817.59 104.7 818.86 105.29 820.27 105.64 C 819.45 105.96 818.45 106.22 817.37 106.37 Z M 847.82 104.89 C 847.68 104.9 847.54 104.91 847.41 104.92 C 846.94 104.96 846.46 105 845.97 105.06 L 845.82 105.07 C 844.25 105.23 842.48 105.42 841.63 105.3 C 841.11 105.22 840.13 104.8 839.1 104.36 C 837.36 103.6 835.18 102.66 833.22 102.5 C 830.51 102.3 825.84 102.81 822.82 104.22 C 821.08 104.14 819.47 103.67 818.21 102.9 C 818.2 102.66 818.2 102.42 818.2 102.32 C 818.2 98.65 819.92 95.2 822.92 92.86 C 824.96 91.67 827.31 91.04 829.73 91.04 C 835.16 91.04 839.99 94.25 841.87 98.98 L 841.15 98.98 C 840.42 98.98 839.82 99.49 839.67 100.17 C 839.55 100.13 839.44 100.08 839.3 100.03 C 837.5 99.34 835.03 98.39 832.17 98.56 C 827.99 98.8 825.94 99.05 824.32 99.52 L 824.81 101.23 C 826.29 100.8 828.24 100.57 832.28 100.33 C 834.75 100.19 837.02 101.06 838.66 101.69 C 839.02 101.83 839.33 101.95 839.63 102.05 L 839.63 102.23 C 839.63 103.07 840.31 103.75 841.15 103.75 L 844.39 103.75 C 845.22 103.75 845.9 103.07 845.9 102.24 L 846.21 102.21 C 846.74 102.16 847.22 102.12 847.63 102.08 L 847.82 102.06 Z" fill="#bf0816" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 124px; margin-left: 811px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Role
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="830" y="136" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Role
                </text>
            </switch>
        </g>
        <rect x="680" y="377" width="200" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 374px; margin-left: 681px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                sns-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="374" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    sns-construct
                </text>
            </switch>
        </g>
        <image x="709.5" y="396.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAN8QgOcYeOQVeucUfOYWeecVeuYUe+UUeugVe+cVe+gUfOcVe+gVe+cVe+gVe+cVe6mUJCoAAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAABqElEQVRYR+1Yy5KEIBATxBcozP9/7SYZwLF2LytzsEpzIQZIWd1N++gePPgLtm+FzU7d9GrHnL1ivm5B3L1SE45eLtNzcI/Xv/B4/Q/38wpLC/zBqxnFK+TrFqzZy8z5XhtQm/RV4YaBgx0A3SxJT9KTGRBDojksfi+vc1hea2pB7KihTF6viQozu5HMlOhqSRZKK0giGSnJFaPnCHBnq9dHfW306jdgpBJAAslIiV6GRA/BBUQV4CjJCwbFa/ZewTkN671u+cJwzim1p2GcK0FC6C7Zv9YY27z6GFWNNwFir1OI1Dr37gWAsmzJWDD7XE92mONL6kcev3a2N/BWr5JHF2OkF1Ibo/oEqiSqF0yU6GVI1Cc8iHYOlN59Im+7CSa8oLfGvsTrmzXxTa8tJUmdBWjaGbJKJJHsEsm+fEipeN0BMSWG9zxcSuU8ImuXfHbg012pPQ98vWd2VfgQ2m6xD+HXuxy/6XXgUSVJfXWmxBNjSdRXVxBFeqSkpKHP1PNYvHhYG882N7R6aYpAUWisf2b0g4fEkKhgSCTVuX15ER48OKDrfgCPPT8Qh4k7owAAAABJRU5ErkJggg==" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="716" y="445" width="30" height="15" stroke-width="0"/>
            <text x="729.5" y="454.5">
                Topic
            </text>
        </g>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" text-anchor="middle" font-size="12px">
            <text x="829.5" y="414.5">
                Mail
            </text>
        </g>
        <path d="M 810 403.66 L 850 403.66 L 850 430.33 L 810 430.33 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 810 403.66 L 830 417 L 850 403.66" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 444px; margin-left: 811px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Mail
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="830" y="456" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Mail
                </text>
            </switch>
        </g>
        <path d="M 750 417 L 803.63 417" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 808.88 417 L 801.88 420.5 L 803.63 417 L 801.88 413.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 417px; margin-left: 780px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                通知
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="420" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    通知
                </text>
            </switch>
        </g>
        <rect x="680" y="537" width="200" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 534px; margin-left: 681px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                kms-key-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="534" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    kms-key-construct
                </text>
            </switch>
        </g>
        <path d="M 760 557 L 800 557 L 800 597 L 760 597 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="none"/>
        <path d="M 787.38 584.56 L 791.69 584.56 L 791.69 583.33 L 787.38 583.33 Z M 781.85 584.56 L 786.15 584.56 L 786.15 583.33 L 781.85 583.33 Z M 776.31 584.56 L 780.61 584.56 L 780.61 583.33 L 776.31 583.33 Z M 783.08 579.63 C 783.08 578.96 783.63 578.4 784.31 578.4 C 784.99 578.4 785.54 578.96 785.54 579.63 C 785.54 580.31 784.99 580.86 784.31 580.86 C 783.63 580.86 783.08 580.31 783.08 579.63 Z M 786.77 579.63 C 786.77 578.28 785.67 577.17 784.31 577.17 C 782.95 577.17 781.85 578.28 781.85 579.63 C 781.85 580.99 782.95 582.1 784.31 582.1 C 785.67 582.1 786.77 580.99 786.77 579.63 Z M 778.15 578.4 C 778.83 578.4 779.38 578.96 779.38 579.63 C 779.38 580.31 778.83 580.86 778.15 580.86 C 777.47 580.86 776.92 580.31 776.92 579.63 C 776.92 578.96 777.47 578.4 778.15 578.4 Z M 778.15 582.1 C 779.51 582.1 780.61 580.99 780.61 579.63 C 780.61 578.28 779.51 577.17 778.15 577.17 C 776.79 577.17 775.69 578.28 775.69 579.63 C 775.69 580.99 776.79 582.1 778.15 582.1 Z M 796 572.86 L 796 587.02 C 796 587.36 795.72 587.63 795.38 587.63 L 776.31 587.63 L 776.31 586.4 L 794.77 586.4 L 794.77 573.48 L 781.23 573.48 L 781.23 572.25 L 795.38 572.25 C 795.72 572.25 796 572.52 796 572.86 Z M 774.27 575.97 C 774.02 576.05 773.84 576.29 773.84 576.56 L 773.84 589.51 L 772.37 590.77 L 770.77 588.94 L 770.77 587.33 C 770.77 587.16 770.7 587.01 770.59 586.89 L 769.18 585.48 L 770.59 584.07 C 770.7 583.95 770.77 583.8 770.77 583.63 L 770.77 582.4 C 770.77 582.24 770.7 582.08 770.59 581.97 L 769.18 580.56 L 770.59 579.15 C 770.7 579.03 770.77 578.87 770.77 578.71 L 770.77 576.56 C 770.77 576.29 770.59 576.06 770.34 575.98 C 767.17 574.97 765.3 571.72 765.99 568.4 C 766.48 566.02 768.32 564.1 770.67 563.53 C 772.66 563.04 774.72 563.46 776.29 564.7 C 777.87 565.94 778.77 567.79 778.77 569.79 C 778.77 572.59 776.92 575.13 774.27 575.97 Z M 780 569.79 C 780 567.41 778.92 565.2 777.05 563.73 C 775.18 562.26 772.74 561.75 770.37 562.33 C 767.57 563.02 765.37 565.3 764.78 568.16 L 764.78 568.16 C 764 571.94 766.03 575.66 769.54 577 L 769.54 578.46 L 767.87 580.12 C 767.63 580.36 767.63 580.75 767.87 580.99 L 769.54 582.66 L 769.54 583.38 L 767.87 585.05 C 767.63 585.29 767.63 585.68 767.87 585.92 L 769.54 587.58 L 769.54 589.17 C 769.54 589.32 769.59 589.47 769.69 589.58 L 771.84 592.04 C 771.96 592.18 772.13 592.25 772.31 592.25 C 772.45 592.25 772.59 592.2 772.71 592.1 L 774.86 590.26 C 775 590.14 775.08 589.97 775.08 589.79 L 775.08 576.99 C 778 575.87 780 572.97 780 569.79 Z M 772.31 571.25 C 771.46 571.25 770.77 570.56 770.77 569.71 C 770.77 568.86 771.46 568.17 772.31 568.17 C 773.15 568.17 773.84 568.86 773.84 569.71 C 773.84 570.56 773.15 571.25 772.31 571.25 Z M 772.31 566.94 C 770.78 566.94 769.54 568.18 769.54 569.71 C 769.54 571.23 770.78 572.48 772.31 572.48 C 773.83 572.48 775.08 571.23 775.08 569.71 C 775.08 568.18 773.83 566.94 772.31 566.94 Z" fill="#ffffff" stroke="none" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 604px; margin-left: 780px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="780" y="616" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    KMS Key
                </text>
            </switch>
        </g>
        <path d="M 60 97 L 470 97 L 470 617 L 60 617 Z" fill="none" stroke="#879196" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 70.59 103.65 C 70.53 103.65 70.48 103.65 70.42 103.65 L 70.42 103.65 C 69.11 103.68 68.03 104.24 67.14 105.25 C 67.13 105.25 67.13 105.25 67.13 105.25 C 66.2 106.36 65.87 107.52 65.96 108.73 C 64.81 109.06 64.12 109.92 63.76 110.74 C 63.75 110.75 63.75 110.76 63.74 110.78 C 63.33 112.05 63.68 113.36 64.24 114.16 C 64.25 114.17 64.25 114.17 64.26 114.18 C 64.94 115.05 65.97 115.53 67.02 115.53 L 78.17 115.53 C 79.19 115.53 80.07 115.16 80.8 114.37 C 81.25 113.94 81.49 113.29 81.58 112.59 C 81.67 111.9 81.61 111.16 81.32 110.55 C 81.31 110.54 81.31 110.53 81.31 110.52 C 80.8 109.62 79.95 108.81 78.76 108.64 C 78.74 107.79 78.28 106.99 77.68 106.56 C 77.67 106.55 77.66 106.55 77.65 106.54 C 77.01 106.18 76.4 106.14 75.91 106.3 C 75.6 106.4 75.36 106.56 75.14 106.74 C 74.51 105.36 73.43 104.18 71.81 103.79 C 71.81 103.79 71.81 103.79 71.81 103.79 C 71.38 103.7 70.97 103.65 70.59 103.65 Z M 70.43 104.38 C 70.8 104.38 71.2 104.43 71.64 104.53 C 73.16 104.89 74.15 106.07 74.66 107.48 C 74.71 107.6 74.81 107.69 74.94 107.72 C 75.07 107.74 75.2 107.7 75.29 107.61 C 75.54 107.34 75.83 107.11 76.14 107.01 C 76.44 106.91 76.78 106.92 77.26 107.18 C 77.67 107.49 78.11 108.31 78.03 108.9 C 78.01 109.01 78.05 109.12 78.12 109.2 C 78.19 109.28 78.29 109.33 78.39 109.33 C 79.46 109.34 80.16 110.02 80.64 110.88 C 80.85 111.3 80.91 111.92 80.84 112.5 C 80.76 113.07 80.53 113.59 80.28 113.83 C 80.27 113.84 80.27 113.85 80.26 113.85 C 79.65 114.53 79.03 114.78 78.17 114.78 L 67.02 114.78 C 66.2 114.78 65.39 114.41 64.85 113.73 C 64.44 113.13 64.14 112.02 64.46 111.02 C 64.79 110.27 65.36 109.55 66.41 109.36 C 66.6 109.32 66.74 109.14 66.71 108.94 C 66.56 107.79 66.8 106.81 67.7 105.74 C 68.49 104.85 69.33 104.39 70.43 104.38 Z M 72.2 107.7 C 71.77 107.7 71.4 107.93 71.13 108.21 C 70.85 108.5 70.64 108.85 70.64 109.25 L 70.64 109.71 L 70.14 109.71 C 70.04 109.71 69.94 109.75 69.87 109.82 C 69.8 109.89 69.76 109.98 69.76 110.08 L 69.76 112.7 C 69.76 112.8 69.8 112.89 69.87 112.96 C 69.94 113.03 70.04 113.07 70.14 113.07 L 74.16 113.07 C 74.26 113.07 74.35 113.03 74.42 112.96 C 74.49 112.89 74.53 112.8 74.53 112.7 L 74.53 110.08 C 74.53 109.98 74.49 109.89 74.42 109.82 C 74.35 109.75 74.26 109.71 74.16 109.71 L 73.68 109.71 L 73.68 109.25 C 73.68 108.84 73.47 108.47 73.21 108.2 C 72.94 107.92 72.61 107.7 72.2 107.7 Z M 72.2 108.45 C 72.29 108.45 72.5 108.54 72.67 108.72 C 72.83 108.89 72.93 109.11 72.93 109.25 L 72.93 109.71 L 71.39 109.71 L 71.39 109.25 C 71.39 109.15 71.49 108.91 71.66 108.74 C 71.83 108.56 72.06 108.45 72.2 108.45 Z M 70.51 110.46 L 73.78 110.46 L 73.78 112.32 L 70.51 112.32 Z M 60 122 L 60 97 L 85 97 L 85 122 Z" fill="#879196" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 378px; height: 1px; padding-top: 104px; margin-left: 92px;">
                        <div data-drawio-colors="color: #879196; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(135, 145, 150); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="92" y="116" fill="#879196" font-family="Helvetica" font-size="12px">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 100 297 L 300 297 L 300 417 L 100 417 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 100 297 L 125 297 L 125 322 L 100 322 Z M 112.52 300.21 C 111.4 300.21 110.31 300.63 109.48 301.39 C 108.67 302.11 108.2 303.15 108.2 304.24 L 108.2 306.78 L 105.89 306.78 C 105.8 306.78 105.7 306.82 105.64 306.89 C 105.57 306.95 105.54 307.04 105.54 307.13 L 105.54 318.43 C 105.54 318.63 105.7 318.79 105.89 318.79 L 119.11 318.79 C 119.3 318.79 119.46 318.63 119.46 318.43 L 119.46 307.15 C 119.47 307.06 119.43 306.97 119.36 306.9 C 119.3 306.83 119.21 306.79 119.11 306.79 L 116.81 306.79 L 116.81 304.29 C 116.8 303.21 116.35 302.18 115.56 301.44 C 114.74 300.65 113.65 300.22 112.52 300.21 Z M 112.51 300.93 C 113.46 300.92 114.37 301.28 115.06 301.93 C 115.72 302.54 116.1 303.4 116.1 304.29 L 116.1 306.79 L 108.88 306.79 L 108.89 304.26 C 108.9 303.36 109.28 302.51 109.95 301.91 C 110.65 301.27 111.57 300.92 112.51 300.93 Z M 106.24 307.5 L 118.76 307.5 L 118.75 318.07 L 106.24 318.07 Z M 112.51 309.74 C 111.48 309.73 110.61 310.51 110.51 311.53 C 110.42 312.56 111.13 313.48 112.14 313.66 L 112.14 316.44 L 112.86 316.44 L 112.86 313.66 C 113.79 313.49 114.47 312.67 114.48 311.72 C 114.48 310.63 113.6 309.75 112.51 309.74 Z M 112.39 310.45 C 112.43 310.45 112.47 310.45 112.51 310.46 C 112.84 310.46 113.16 310.59 113.4 310.83 C 113.64 311.07 113.77 311.39 113.76 311.72 C 113.77 312.06 113.64 312.38 113.4 312.61 C 113.16 312.85 112.84 312.98 112.51 312.98 C 112.04 313.02 111.6 312.8 111.34 312.42 C 111.08 312.03 111.06 311.53 111.28 311.12 C 111.5 310.71 111.93 310.46 112.39 310.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 304px; margin-left: 132px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Private subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="132" y="316" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Private subnet
                </text>
            </switch>
        </g>
        <rect x="180" y="337" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 207.84 367.34 L 209.66 367.34 L 209.66 346.66 L 207.84 346.66 Z M 190.34 367.34 L 192.16 367.34 L 192.16 346.66 L 190.34 346.66 Z M 214.2 358.35 L 212.58 357 L 214.2 355.65 Z M 215.5 352.89 C 215.18 352.74 214.8 352.78 214.53 353.01 L 210.58 356.3 C 210.37 356.48 210.25 356.73 210.25 357 C 210.25 357.27 210.37 357.53 210.58 357.7 L 214.53 360.99 C 214.7 361.13 214.91 361.2 215.11 361.2 C 215.25 361.2 215.38 361.17 215.5 361.11 C 215.82 360.96 216.02 360.64 216.02 360.29 L 216.02 353.71 C 216.02 353.36 215.82 353.04 215.5 352.89 Z M 185.8 358.35 L 185.8 355.65 L 187.42 357 Z M 185.47 353.01 C 185.2 352.79 184.82 352.74 184.5 352.89 C 184.18 353.04 183.98 353.36 183.98 353.71 L 183.98 360.29 C 183.98 360.64 184.18 360.96 184.5 361.11 C 184.62 361.17 184.76 361.2 184.89 361.2 C 185.1 361.2 185.3 361.13 185.47 360.99 L 189.42 357.7 C 189.63 357.53 189.75 357.27 189.75 357 C 189.75 356.73 189.63 356.48 189.42 356.3 Z M 200 375.18 C 189.97 375.18 181.82 367.03 181.82 357 C 181.82 346.98 189.97 338.82 200 338.82 C 210.03 338.82 218.18 346.98 218.18 357 C 218.18 367.03 210.03 375.18 200 375.18 Z M 200 337 C 188.97 337 180 345.97 180 357 C 180 368.03 188.97 377 200 377 C 211.03 377 220 368.03 220 357 C 220 345.97 211.03 337 200 337 Z M 203.86 361.66 L 196.14 361.65 L 196.14 355.52 L 203.86 355.53 Z M 198.53 352.5 C 198.53 352 198.7 351.53 199 351.19 C 199.28 350.88 199.63 350.71 200.01 350.71 L 200.01 350.71 C 200.82 350.71 201.47 351.52 201.47 352.52 L 201.47 353.71 L 198.52 353.71 Z M 204.77 353.71 L 203.29 353.71 L 203.29 352.53 C 203.3 350.53 201.83 348.9 200.02 348.89 L 200.01 348.89 C 199.11 348.89 198.27 349.28 197.64 349.98 C 197.04 350.66 196.71 351.55 196.71 352.49 L 196.7 353.71 L 195.23 353.71 C 194.99 353.71 194.76 353.8 194.59 353.97 C 194.42 354.14 194.32 354.37 194.32 354.61 L 194.32 362.56 C 194.32 363.07 194.72 363.47 195.23 363.47 L 204.77 363.48 C 205.01 363.48 205.24 363.38 205.41 363.21 C 205.58 363.04 205.68 362.81 205.68 362.57 L 205.68 354.62 C 205.68 354.38 205.59 354.15 205.42 353.98 C 205.25 353.8 205.01 353.71 204.77 353.71 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 384px; margin-left: 200px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="396" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NACL
                </text>
            </switch>
        </g>
        <path d="M 100 457 L 300 457 L 300 577 L 100 577 Z" fill="#e6f2f8" stroke="none" pointer-events="none"/>
        <path d="M 100 457 L 125 457 L 125 482 L 100 482 Z M 112.52 460.21 C 111.4 460.21 110.31 460.63 109.48 461.39 C 108.67 462.11 108.2 463.15 108.2 464.24 L 108.2 466.78 L 105.89 466.78 C 105.8 466.78 105.7 466.82 105.64 466.89 C 105.57 466.95 105.54 467.04 105.54 467.13 L 105.54 478.43 C 105.54 478.63 105.7 478.79 105.89 478.79 L 119.11 478.79 C 119.3 478.79 119.46 478.63 119.46 478.43 L 119.46 467.15 C 119.47 467.06 119.43 466.97 119.36 466.9 C 119.3 466.83 119.21 466.79 119.11 466.79 L 116.81 466.79 L 116.81 464.29 C 116.8 463.21 116.35 462.18 115.56 461.44 C 114.74 460.65 113.65 460.22 112.52 460.21 Z M 112.51 460.93 C 113.46 460.92 114.37 461.28 115.06 461.93 C 115.72 462.54 116.1 463.4 116.1 464.29 L 116.1 466.79 L 108.88 466.79 L 108.89 464.26 C 108.9 463.36 109.28 462.51 109.95 461.91 C 110.65 461.27 111.57 460.92 112.51 460.93 Z M 106.24 467.5 L 118.76 467.5 L 118.75 478.07 L 106.24 478.07 Z M 112.51 469.74 C 111.48 469.73 110.61 470.51 110.51 471.53 C 110.42 472.56 111.13 473.48 112.14 473.66 L 112.14 476.44 L 112.86 476.44 L 112.86 473.66 C 113.79 473.49 114.47 472.67 114.48 471.72 C 114.48 470.63 113.6 469.75 112.51 469.74 Z M 112.39 470.45 C 112.43 470.45 112.47 470.45 112.51 470.46 C 112.84 470.46 113.16 470.59 113.4 470.83 C 113.64 471.07 113.77 471.39 113.76 471.72 C 113.77 472.06 113.64 472.38 113.4 472.61 C 113.16 472.85 112.84 472.98 112.51 472.98 C 112.04 473.02 111.6 472.8 111.34 472.42 C 111.08 472.03 111.06 471.53 111.28 471.12 C 111.5 470.71 111.93 470.46 112.39 470.45 Z" fill="#147eba" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 464px; margin-left: 132px;">
                        <div data-drawio-colors="color: #147EBA; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(20, 126, 186); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Protected subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="132" y="476" fill="#147EBA" font-family="Helvetica" font-size="12px">
                    Protected subnet
                </text>
            </switch>
        </g>
        <rect x="180" y="497" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 207.84 527.34 L 209.66 527.34 L 209.66 506.66 L 207.84 506.66 Z M 190.34 527.34 L 192.16 527.34 L 192.16 506.66 L 190.34 506.66 Z M 214.2 518.35 L 212.58 517 L 214.2 515.65 Z M 215.5 512.89 C 215.18 512.74 214.8 512.78 214.53 513.01 L 210.58 516.3 C 210.37 516.48 210.25 516.73 210.25 517 C 210.25 517.27 210.37 517.53 210.58 517.7 L 214.53 520.99 C 214.7 521.13 214.91 521.2 215.11 521.2 C 215.25 521.2 215.38 521.17 215.5 521.11 C 215.82 520.96 216.02 520.64 216.02 520.29 L 216.02 513.71 C 216.02 513.36 215.82 513.04 215.5 512.89 Z M 185.8 518.35 L 185.8 515.65 L 187.42 517 Z M 185.47 513.01 C 185.2 512.79 184.82 512.74 184.5 512.89 C 184.18 513.04 183.98 513.36 183.98 513.71 L 183.98 520.29 C 183.98 520.64 184.18 520.96 184.5 521.11 C 184.62 521.17 184.76 521.2 184.89 521.2 C 185.1 521.2 185.3 521.13 185.47 520.99 L 189.42 517.7 C 189.63 517.53 189.75 517.27 189.75 517 C 189.75 516.73 189.63 516.48 189.42 516.3 Z M 200 535.18 C 189.97 535.18 181.82 527.03 181.82 517 C 181.82 506.98 189.97 498.82 200 498.82 C 210.03 498.82 218.18 506.98 218.18 517 C 218.18 527.03 210.03 535.18 200 535.18 Z M 200 497 C 188.97 497 180 505.97 180 517 C 180 528.03 188.97 537 200 537 C 211.03 537 220 528.03 220 517 C 220 505.97 211.03 497 200 497 Z M 203.86 521.66 L 196.14 521.65 L 196.14 515.52 L 203.86 515.53 Z M 198.53 512.5 C 198.53 512 198.7 511.53 199 511.19 C 199.28 510.88 199.63 510.71 200.01 510.71 L 200.01 510.71 C 200.82 510.71 201.47 511.52 201.47 512.52 L 201.47 513.71 L 198.52 513.71 Z M 204.77 513.71 L 203.29 513.71 L 203.29 512.53 C 203.3 510.53 201.83 508.9 200.02 508.89 L 200.01 508.89 C 199.11 508.89 198.27 509.28 197.64 509.98 C 197.04 510.66 196.71 511.55 196.71 512.49 L 196.7 513.71 L 195.23 513.71 C 194.99 513.71 194.76 513.8 194.59 513.97 C 194.42 514.14 194.32 514.37 194.32 514.61 L 194.32 522.56 C 194.32 523.07 194.72 523.47 195.23 523.47 L 204.77 523.48 C 205.01 523.48 205.24 523.38 205.41 523.21 C 205.58 523.04 205.68 522.81 205.68 522.57 L 205.68 514.62 C 205.68 514.38 205.59 514.15 205.42 513.98 C 205.25 513.8 205.01 513.71 204.77 513.71 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 544px; margin-left: 200px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="556" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NACL
                </text>
            </switch>
        </g>
        <path d="M 100 137 L 300 137 L 300 257 L 100 257 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 100 137 L 125 137 L 125 162 L 100 162 Z M 112.52 140.21 C 111.4 140.21 110.31 140.63 109.48 141.39 C 108.67 142.11 108.2 143.15 108.2 144.24 L 108.2 146.78 L 105.89 146.78 C 105.8 146.78 105.7 146.82 105.64 146.89 C 105.57 146.95 105.54 147.04 105.54 147.13 L 105.54 158.43 C 105.54 158.63 105.7 158.79 105.89 158.79 L 119.11 158.79 C 119.3 158.79 119.46 158.63 119.46 158.43 L 119.46 147.15 C 119.47 147.06 119.43 146.97 119.36 146.9 C 119.3 146.83 119.21 146.79 119.11 146.79 L 116.81 146.79 L 116.81 144.29 C 116.8 143.21 116.35 142.18 115.56 141.44 C 114.74 140.65 113.65 140.22 112.52 140.21 Z M 112.51 140.93 C 113.46 140.92 114.37 141.28 115.06 141.93 C 115.72 142.54 116.1 143.4 116.1 144.29 L 116.1 146.79 L 108.88 146.79 L 108.89 144.26 C 108.9 143.36 109.28 142.51 109.95 141.91 C 110.65 141.27 111.57 140.92 112.51 140.93 Z M 106.24 147.5 L 118.76 147.5 L 118.75 158.07 L 106.24 158.07 Z M 112.51 149.74 C 111.48 149.73 110.61 150.51 110.51 151.53 C 110.42 152.56 111.13 153.48 112.14 153.66 L 112.14 156.44 L 112.86 156.44 L 112.86 153.66 C 113.79 153.49 114.47 152.67 114.48 151.72 C 114.48 150.63 113.6 149.75 112.51 149.74 Z M 112.39 150.45 C 112.43 150.45 112.47 150.45 112.51 150.46 C 112.84 150.46 113.16 150.59 113.4 150.83 C 113.64 151.07 113.77 151.39 113.76 151.72 C 113.77 152.06 113.64 152.38 113.4 152.61 C 113.16 152.85 112.84 152.98 112.51 152.98 C 112.04 153.02 111.6 152.8 111.34 152.42 C 111.08 152.03 111.06 151.53 111.28 151.12 C 111.5 150.71 111.93 150.46 112.39 150.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 144px; margin-left: 132px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="132" y="156" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="140" y="177" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 150.57 207.84 L 150.57 205.26 L 152.18 206.55 Z M 150.23 202.65 C 149.95 202.43 149.58 202.39 149.26 202.54 C 148.95 202.7 148.75 203.01 148.75 203.36 L 148.75 209.73 C 148.75 210.08 148.95 210.39 149.26 210.55 C 149.39 210.61 149.53 210.64 149.66 210.64 C 149.86 210.64 150.06 210.57 150.23 210.44 L 154.2 207.26 C 154.42 207.08 154.55 206.82 154.55 206.55 C 154.55 206.27 154.42 206.01 154.2 205.84 Z M 172.05 199.58 L 172.05 196.01 L 173.83 197.79 Z M 175.76 197.15 L 171.78 193.18 C 171.52 192.92 171.13 192.84 170.79 192.98 C 170.45 193.12 170.23 193.45 170.23 193.82 L 170.23 196.89 L 163.29 196.89 L 163.29 188.25 C 163.29 187.75 162.89 187.34 162.39 187.34 L 155.39 187.34 L 155.39 189.16 L 161.48 189.16 L 161.48 196.89 L 155.23 196.89 L 155.23 198.7 L 161.48 198.7 L 161.48 206.43 L 155.39 206.43 L 155.39 208.25 L 162.39 208.25 C 162.89 208.25 163.29 207.84 163.29 207.34 L 163.29 198.7 L 170.23 198.7 L 170.23 201.77 C 170.23 202.14 170.45 202.47 170.79 202.61 C 170.9 202.66 171.02 202.68 171.14 202.68 C 171.37 202.68 171.61 202.59 171.78 202.42 L 175.76 198.44 C 176.11 198.08 176.11 197.51 175.76 197.15 Z M 150.57 199.09 L 150.57 196.51 L 152.18 197.79 Z M 150.23 193.9 C 149.95 193.68 149.58 193.64 149.26 193.79 C 148.95 193.95 148.75 194.26 148.75 194.61 L 148.75 200.98 C 148.75 201.33 148.95 201.64 149.26 201.8 C 149.39 201.86 149.53 201.89 149.66 201.89 C 149.86 201.89 150.06 201.82 150.23 201.69 L 154.2 198.5 C 154.42 198.33 154.55 198.07 154.55 197.79 C 154.55 197.52 154.42 197.26 154.2 197.08 Z M 150.57 189.54 L 150.57 186.96 L 152.18 188.25 Z M 150.23 184.36 C 149.95 184.14 149.58 184.1 149.26 184.25 C 148.95 184.4 148.75 184.72 148.75 185.07 L 148.75 191.43 C 148.75 191.78 148.95 192.1 149.26 192.25 C 149.39 192.31 149.53 192.34 149.66 192.34 C 149.86 192.34 150.06 192.27 150.23 192.14 L 154.2 188.96 C 154.42 188.79 154.55 188.53 154.55 188.25 C 154.55 187.97 154.42 187.71 154.2 187.54 Z M 160 215.18 C 149.97 215.18 141.82 207.03 141.82 197 C 141.82 186.97 149.97 178.82 160 178.82 C 170.03 178.82 178.18 186.97 178.18 197 C 178.18 207.03 170.03 215.18 160 215.18 Z M 160 177 C 148.97 177 140 185.97 140 197 C 140 208.03 148.97 217 160 217 C 171.03 217 180 208.03 180 197 C 180 185.97 171.03 177 160 177 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 224px; margin-left: 160px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="160" y="236" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Ga...
                </text>
            </switch>
        </g>
        <path d="M 520 257 L 560 257 L 560 297 L 520 297 Z" fill="url(#mx-gradient-f54749-1-c7131f-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 547.38 284.56 L 551.69 284.56 L 551.69 283.33 L 547.38 283.33 Z M 541.85 284.56 L 546.15 284.56 L 546.15 283.33 L 541.85 283.33 Z M 536.31 284.56 L 540.61 284.56 L 540.61 283.33 L 536.31 283.33 Z M 543.08 279.63 C 543.08 278.96 543.63 278.4 544.31 278.4 C 544.99 278.4 545.54 278.96 545.54 279.63 C 545.54 280.31 544.99 280.86 544.31 280.86 C 543.63 280.86 543.08 280.31 543.08 279.63 Z M 546.77 279.63 C 546.77 278.28 545.67 277.17 544.31 277.17 C 542.95 277.17 541.85 278.28 541.85 279.63 C 541.85 280.99 542.95 282.1 544.31 282.1 C 545.67 282.1 546.77 280.99 546.77 279.63 Z M 538.15 278.4 C 538.83 278.4 539.38 278.96 539.38 279.63 C 539.38 280.31 538.83 280.86 538.15 280.86 C 537.47 280.86 536.92 280.31 536.92 279.63 C 536.92 278.96 537.47 278.4 538.15 278.4 Z M 538.15 282.1 C 539.51 282.1 540.61 280.99 540.61 279.63 C 540.61 278.28 539.51 277.17 538.15 277.17 C 536.79 277.17 535.69 278.28 535.69 279.63 C 535.69 280.99 536.79 282.1 538.15 282.1 Z M 556 272.86 L 556 287.02 C 556 287.36 555.72 287.63 555.38 287.63 L 536.31 287.63 L 536.31 286.4 L 554.77 286.4 L 554.77 273.48 L 541.23 273.48 L 541.23 272.25 L 555.38 272.25 C 555.72 272.25 556 272.52 556 272.86 Z M 534.27 275.97 C 534.02 276.05 533.84 276.29 533.84 276.56 L 533.84 289.51 L 532.37 290.77 L 530.77 288.94 L 530.77 287.33 C 530.77 287.16 530.7 287.01 530.59 286.89 L 529.18 285.48 L 530.59 284.07 C 530.7 283.95 530.77 283.8 530.77 283.63 L 530.77 282.4 C 530.77 282.24 530.7 282.08 530.59 281.97 L 529.18 280.56 L 530.59 279.15 C 530.7 279.03 530.77 278.87 530.77 278.71 L 530.77 276.56 C 530.77 276.29 530.59 276.06 530.34 275.98 C 527.17 274.97 525.3 271.72 525.99 268.4 C 526.48 266.02 528.32 264.1 530.67 263.53 C 532.66 263.04 534.72 263.46 536.29 264.7 C 537.87 265.94 538.77 267.79 538.77 269.79 C 538.77 272.59 536.92 275.13 534.27 275.97 Z M 540 269.79 C 540 267.41 538.92 265.2 537.05 263.73 C 535.18 262.26 532.74 261.75 530.37 262.33 C 527.57 263.02 525.37 265.3 524.78 268.16 L 524.78 268.16 C 524 271.94 526.03 275.66 529.54 277 L 529.54 278.46 L 527.87 280.12 C 527.63 280.36 527.63 280.75 527.87 280.99 L 529.54 282.66 L 529.54 283.38 L 527.87 285.05 C 527.63 285.29 527.63 285.68 527.87 285.92 L 529.54 287.58 L 529.54 289.17 C 529.54 289.32 529.59 289.47 529.69 289.58 L 531.84 292.04 C 531.96 292.18 532.13 292.25 532.31 292.25 C 532.45 292.25 532.59 292.2 532.71 292.1 L 534.86 290.26 C 535 290.14 535.08 289.97 535.08 289.79 L 535.08 276.99 C 538 275.87 540 272.97 540 269.79 Z M 532.31 271.25 C 531.46 271.25 530.77 270.56 530.77 269.71 C 530.77 268.86 531.46 268.17 532.31 268.17 C 533.15 268.17 533.84 268.86 533.84 269.71 C 533.84 270.56 533.15 271.25 532.31 271.25 Z M 532.31 266.94 C 530.78 266.94 529.54 268.18 529.54 269.71 C 529.54 271.23 530.78 272.48 532.31 272.48 C 533.83 272.48 535.08 271.23 535.08 269.71 C 535.08 268.18 533.83 266.94 532.31 266.94 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 277px; margin-left: 562px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                KMS Key
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="562" y="281" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    KMS Key
                </text>
            </switch>
        </g>
        <path d="M 520 177 L 560 177 L 560 217 L 520 217 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 551.94 198.65 L 552.16 197.11 C 554.18 198.32 554.21 198.82 554.21 198.83 C 554.2 198.84 553.86 199.13 551.94 198.65 Z M 550.83 198.34 C 547.33 197.29 542.46 195.05 540.49 194.12 C 540.49 194.11 540.49 194.11 540.49 194.1 C 540.49 193.34 539.88 192.72 539.12 192.72 C 538.36 192.72 537.75 193.34 537.75 194.1 C 537.75 194.85 538.36 195.47 539.12 195.47 C 539.45 195.47 539.75 195.35 539.99 195.15 C 542.31 196.25 547.14 198.45 550.67 199.49 L 549.27 209.32 C 549.27 209.35 549.27 209.37 549.27 209.4 C 549.27 210.27 545.43 211.86 539.17 211.86 C 532.84 211.86 528.97 210.27 528.97 209.4 C 528.97 209.37 528.97 209.35 528.97 209.32 L 526.05 188.06 C 528.57 189.8 533.99 190.71 539.18 190.71 C 544.35 190.71 549.76 189.8 552.29 188.07 Z M 525.75 185.84 C 525.79 185.09 530.11 182.14 539.18 182.14 C 548.24 182.14 552.56 185.09 552.6 185.84 L 552.6 186.1 C 552.11 187.79 546.51 189.57 539.18 189.57 C 531.83 189.57 526.23 187.78 525.75 186.09 Z M 553.75 185.86 C 553.75 183.88 548.07 181 539.18 181 C 530.28 181 524.6 183.88 524.6 185.86 L 524.66 186.29 L 527.83 209.44 C 527.9 212.03 534.81 213 539.17 213 C 544.59 213 550.34 211.76 550.41 209.45 L 551.78 199.79 C 552.54 199.97 553.17 200.07 553.67 200.07 C 554.35 200.07 554.8 199.9 555.08 199.57 C 555.31 199.3 555.4 198.97 555.33 198.62 C 555.18 197.83 554.24 196.98 552.33 195.89 L 553.69 186.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 197px; margin-left: 562px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                                <br/>
                                (FlowLogBucket)
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="562" y="201" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    S3 Buck...
                </text>
            </switch>
        </g>
        <path d="M 540 257 L 540 223.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 540 218.12 L 543.5 225.12 L 540 223.37 L 536.5 225.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 237px; margin-left: 540px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                暗号化
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="540" y="240" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    暗号化
                </text>
            </switch>
        </g>
        <path d="M 380 197 L 513.63 197" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 518.88 197 L 511.88 200.5 L 513.63 197 L 511.88 193.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 197px; margin-left: 450px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ロギング
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="450" y="200" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ロギング
                </text>
            </switch>
        </g>
        <rect x="340" y="177" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 376.55 197.64 L 372.57 201.62 L 371.29 200.33 L 373.71 197.91 L 344.89 197.91 L 344.89 196.09 L 373.71 196.09 L 371.29 193.67 L 372.57 192.38 L 376.55 196.36 C 376.91 196.71 376.91 197.29 376.55 197.64 Z M 353.64 205.86 L 365.57 205.86 L 365.57 204.05 L 353.64 204.05 Z M 353.64 201.89 L 365.57 201.89 L 365.57 200.07 L 353.64 200.07 Z M 353.64 193.93 L 365.57 193.93 L 365.57 192.11 L 353.64 192.11 Z M 353.64 189.95 L 365.57 189.95 L 365.57 188.14 L 353.64 188.14 Z M 367.05 199.39 L 368.86 199.39 L 368.86 208.93 C 368.86 209.43 368.46 209.84 367.95 209.84 L 351.25 209.84 C 350.75 209.84 350.34 209.43 350.34 208.93 L 350.34 199.39 L 352.16 199.39 L 352.16 208.02 L 367.05 208.02 Z M 352.16 194.61 L 350.34 194.61 L 350.34 185.07 C 350.34 184.57 350.75 184.16 351.25 184.16 L 367.95 184.16 C 368.46 184.16 368.86 184.57 368.86 185.07 L 368.86 194.61 L 367.05 194.61 L 367.05 185.98 L 352.16 185.98 Z M 360 215.18 C 349.97 215.18 341.82 207.03 341.82 197 C 341.82 186.98 349.97 178.82 360 178.82 C 370.03 178.82 378.18 186.98 378.18 197 C 378.18 207.03 370.03 215.18 360 215.18 Z M 360 177 C 348.97 177 340 185.97 340 197 C 340 208.03 348.97 217 360 217 C 371.03 217 380 208.03 380 197 C 380 185.97 371.03 177 360 177 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 224px; margin-left: 360px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Flow Logs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="360" y="236" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Flow L...
                </text>
            </switch>
        </g>
        <rect x="220" y="177" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 247.84 207.34 L 249.66 207.34 L 249.66 186.66 L 247.84 186.66 Z M 230.34 207.34 L 232.16 207.34 L 232.16 186.66 L 230.34 186.66 Z M 254.2 198.35 L 252.58 197 L 254.2 195.65 Z M 255.5 192.89 C 255.18 192.74 254.8 192.78 254.53 193.01 L 250.58 196.3 C 250.37 196.48 250.25 196.73 250.25 197 C 250.25 197.27 250.37 197.53 250.58 197.7 L 254.53 200.99 C 254.7 201.13 254.91 201.2 255.11 201.2 C 255.25 201.2 255.38 201.17 255.5 201.11 C 255.82 200.96 256.02 200.64 256.02 200.29 L 256.02 193.71 C 256.02 193.36 255.82 193.04 255.5 192.89 Z M 225.8 198.35 L 225.8 195.65 L 227.42 197 Z M 225.47 193.01 C 225.2 192.79 224.82 192.74 224.5 192.89 C 224.18 193.04 223.98 193.36 223.98 193.71 L 223.98 200.29 C 223.98 200.64 224.18 200.96 224.5 201.11 C 224.62 201.17 224.76 201.2 224.89 201.2 C 225.1 201.2 225.3 201.13 225.47 200.99 L 229.42 197.7 C 229.63 197.53 229.75 197.27 229.75 197 C 229.75 196.73 229.63 196.48 229.42 196.3 Z M 240 215.18 C 229.97 215.18 221.82 207.03 221.82 197 C 221.82 186.98 229.97 178.82 240 178.82 C 250.03 178.82 258.18 186.98 258.18 197 C 258.18 207.03 250.03 215.18 240 215.18 Z M 240 177 C 228.97 177 220 185.97 220 197 C 220 208.03 228.97 217 240 217 C 251.03 217 260 208.03 260 197 C 260 185.97 251.03 177 240 177 Z M 243.86 201.66 L 236.14 201.65 L 236.14 195.52 L 243.86 195.53 Z M 238.53 192.5 C 238.53 192 238.7 191.53 239 191.19 C 239.28 190.88 239.63 190.71 240.01 190.71 L 240.01 190.71 C 240.82 190.71 241.47 191.52 241.47 192.52 L 241.47 193.71 L 238.52 193.71 Z M 244.77 193.71 L 243.29 193.71 L 243.29 192.53 C 243.3 190.53 241.83 188.9 240.02 188.89 L 240.01 188.89 C 239.11 188.89 238.27 189.28 237.64 189.98 C 237.04 190.66 236.71 191.55 236.71 192.49 L 236.7 193.71 L 235.23 193.71 C 234.99 193.71 234.76 193.8 234.59 193.97 C 234.42 194.14 234.32 194.37 234.32 194.61 L 234.32 202.56 C 234.32 203.07 234.72 203.47 235.23 203.47 L 244.77 203.48 C 245.01 203.48 245.24 203.38 245.41 203.21 C 245.58 203.04 245.68 202.81 245.68 202.57 L 245.68 194.62 C 245.68 194.38 245.59 194.15 245.42 193.98 C 245.25 193.8 245.01 193.71 244.77 193.71 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 224px; margin-left: 240px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NACL
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="240" y="236" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NACL
                </text>
            </switch>
        </g>
        <rect x="280" y="497" width="40" height="40" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <image x="279.5" y="496.5" width="40" height="40" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAABLCAMAAAAPkIrYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAzUExURQAAAI9Q/4dQ/4pQ/4tQ/4xQ/4pQ/4tQ/4tO/41O/4xP/4xP/4xP/4xP/4xP/4xP/4xP/03YYr4AAAAQdFJOUwAQIDBAUGBwgI+fr7/P3+8jGoKKAAAACXBIWXMAABcRAAAXEQHKJvM/AAAEd0lEQVRYR+1Y26KrKgws3tCqtf//tWcmBEkQu9rnfeZJEYbJhQA+/kc/xmXbgW19zkPQ1t8Rxufx9jiek378BWFYayLFOmiXLxHiriP3dYnTOI5TnLdXbvtF3JBGHUvlIVgtH75n61d2P7a+6Wqle3b6/hGRfjqWXl+v6BPb39KCdNw+z9pv7LTo2x06+vwY9e0ekWS7vrTR0+nbxU/hmgYy6f4heXu6aqk7DAual4vVdMbrlqyDqqNyaZhzpm21s2c03pkZMKqiEkkFlTiSbfpcgWllqc7k38YQJXK1uAUtT312YGhMmLOkY06ZplnlxXH6RtA7DF31+RGmLGky3j11FnF0y9X/MCJHJVSSHiEvgiJOGzo8nwoyRjRmtZKI7y0qdQfq12mZissZN+G5Wm4B6XDyIz5F0rBrKNds2UBxp5mgrhIDUo6THlwanW7ONYt4zaoU/ji5enzw7gd7iaFynZJgbVTOVFQtF2PphMFbRwl14prS8GNNes8yCDbHRWE2h8FtopG4RNMZAEDFHRXXA60mYQPGGZsTF5mqCD1GTHrhQijRloG3lz4SJ5dffgQMunAF9CxFCbPZIvkjF4fP+igW27BWXNbQJpc1q46E55rtuCYXjcwhQka4DHFcXFBlYJOLIc/i0d2tT8fFCllGtrnQJeojstDtT/00IS7TlOqNI2tzgSA7H3FwnypYsjYX6isMEeATw9jdQXa6NLjNBadkJ2FecnH6D5CsaXMZh3/HJb3bXEiwvB99xZUOBt/ocp9qcDvXjfOWy/g+p0cLhure9zmpENKyNoE+RnSNMU1gqW64TG0wIRVgTcGV8BEXhqPiJyxjcIn3MkxtYIXWR0F6hxcpDL4wh4w0bVVXuB5zbeA6t3UirXtYLis+Giqy4K3qnwxXpB4FmKeXLVlGmPoFxWjDWMcF6eW4Y9aTIAUWM7hWAE6GiUhNV6NcXa0dBu40xGx0xABBcBTKgp2EO0+p93SQNZLcwZ8LBNBLYyrXw0S782Amd8BLDoSwehAFJKcVIOA2PSnefoaRnIortKgnFW3DxFZuTV37mTGEItqe5wzglxMaD31WLQz3nqA5lhyhoTDqxdlrGMYZDOkoBFn2KE5ZRbsAwqzHmELMejnxZ+ycjWNNnFjAvawz3CcwuyzHdD8iDjl9kdxOikV1VLLEKptNTIh0uuvitr/2NZ3jWPrLoS9Ruz1MQD/bbKaVfmUBA4bamxctbN1j6AhrufjdXTnloGpXeqAxFwsJ9rT1Vcje68zOIQzpYmSpZISrogXIFEfWy2jijKY73ZHq6qwE2aDdPHpmzbAnzhTh+xukkPlsGWaqBY4tH/kT6KuPl1Ehk5S0CF3f1YPkt8P1BmwRRIV1WhPpxnS5AdfgEn7vzpwLBso//pwRHSVoekNoQZjeu11xt9AleLlfC/T+ffxpX4YcuIA1+tAhX1OmXS4RnzBqJoDvOc9xivylljP2Jyai978ATrx8mn2LMedpRp2vP2Kc5ue6buu6zGP7J9a/hMfjPzVzWbbaBL2DAAAAAElFTkSuQmCC" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="262" y="545" width="77" height="15" stroke-width="0"/>
            <text x="299.5" y="554.5">
                VPC Endpoint
            </text>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>