<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="201px" height="118px" viewBox="-0.5 -0.5 201 118" content="&lt;mxfile&gt;&lt;diagram id=&quot;WGHeBbRYfrETVr63uyxh&quot; name=&quot;240821&quot;&gt;7VZdr5swDP01PN6qCaXtHge93SZt0qQ+3McphRSihhiFcKH79XNKoHxV2qT7uJYKfGzHxnZO6vlR3nzRrMh+QMKlR9dJ4/kHj1JCaIA3i9xaJNhvWiDVInFGD+AkfnMHrh1aiYSXI0MDII0oxmAMSvHYjDCmNdRjswvIcdSCpXwGnGIm5+ibSEzWonu6e+BfuUizLjLZfmo1OeuM3ZuUGUugHkD+q+dHGsC0T3kTcWmL19Wl9Ts+0faJaa7M3zjQ1uGdycq9WyShSt6YiTPED6zMzsB04rI1t64E5ZVbE/+w9vywAKHMvaZBiBeGjNpfgKaRRVa25TNwCdvNQTI3wxtZijAFl7DdHCRzMyt1WY/BJWwXzDOeepMFbzLxxut/OT+4nFAZKRSPejKwI3sBZSKQoO/j7OP3aHdImGqWCD7SHf3NZk8HuoPQuJAAhXoF2m7p8CKkHPiEEfGDLeKl0XDlA83l/kFNgnuLJy6dd66NQIL5zs5c/oRSuOXPYAzkA4PPUqRWYaBAlDkpxqw4Bggzk0uUiXtDR52EdrLbwDYkK4u2HBfR2DxC5KLCKvMmtbS9YnW5WWleQqVj/i22+YQotk9jq9iSRm1J45cN5UgFU+bNU2IiPd3hOcEh50bf0MQ5+FvHkO6IoBsn1w/C7aBswLUdxhzFp/3KDxbEB0eEy6Toz0gxByUM6JfSsPjqWU492uOrY8cXLAk2usJyTnmyzoThp4LFVq6xYpMmDcZGgeK2xFCp5D4Y1kBOBqLv9LOJeTIY0/npB+sDekX3k17Rea/oeqFZZP3v3ULxcTzedYM/Gf7rHw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f34482-1-bc1356-1-s-0">
            <stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(243, 68, 130); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 80 37 L 120 37 L 120 77 L 80 77 Z" fill="url(#mx-gradient-f34482-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 108.43 61.18 C 108.43 58.96 106.62 57.16 104.38 57.16 C 102.15 57.16 100.34 58.96 100.34 61.18 C 100.34 63.4 102.15 65.21 104.38 65.21 C 106.62 65.21 108.43 63.4 108.43 61.18 M 109.58 61.18 C 109.58 64.03 107.25 66.34 104.38 66.34 C 101.52 66.34 99.19 64.03 99.19 61.18 C 99.19 58.34 101.52 56.03 104.38 56.03 C 107.25 56.03 109.58 58.34 109.58 61.18 M 114.37 68.95 L 110.39 65.4 C 110.07 65.84 109.7 66.26 109.28 66.62 L 113.25 70.18 C 113.59 70.48 114.12 70.46 114.43 70.12 C 114.73 69.78 114.7 69.26 114.37 68.95 M 104.38 67.36 C 107.81 67.36 110.6 64.59 110.6 61.18 C 110.6 57.78 107.81 55.01 104.38 55.01 C 100.96 55.01 98.17 57.78 98.17 61.18 C 98.17 64.59 100.96 67.36 104.38 67.36 M 115.27 70.88 C 114.89 71.31 114.35 71.52 113.81 71.52 C 113.34 71.52 112.87 71.36 112.49 71.02 L 108.36 67.32 C 107.21 68.06 105.85 68.49 104.38 68.49 C 100.33 68.49 97.03 65.21 97.03 61.18 C 97.03 57.15 100.33 53.87 104.38 53.87 C 108.44 53.87 111.74 57.15 111.74 61.18 C 111.74 62.34 111.47 63.43 110.98 64.4 L 115.13 68.11 C 115.93 68.84 116 70.08 115.27 70.88 M 89.27 51.32 C 89.27 51.61 89.28 51.9 89.32 52.19 C 89.34 52.35 89.29 52.51 89.18 52.63 C 89.09 52.73 88.98 52.79 88.85 52.82 C 87.45 53.18 85.14 54.27 85.14 57.53 C 85.14 60 86.51 61.36 87.66 62.07 C 88.05 62.32 88.52 62.45 89.01 62.45 L 95.89 62.46 L 95.88 63.59 L 89 63.58 C 88.29 63.58 87.62 63.39 87.06 63.03 C 85.92 62.33 84 60.68 84 57.53 C 84 53.74 86.61 52.34 88.14 51.84 C 88.13 51.67 88.13 51.49 88.13 51.32 C 88.13 48.22 90.24 45 93.05 43.84 C 96.33 42.48 99.8 43.15 102.34 45.65 C 103.13 46.43 103.78 47.37 104.28 48.45 C 104.94 47.9 105.77 47.6 106.63 47.6 C 108.34 47.6 110.25 48.89 110.6 51.7 C 112.19 52.06 115.56 53.34 115.56 57.58 C 115.56 59.27 115.03 60.67 113.98 61.73 L 113.16 60.93 C 114 60.09 114.42 58.96 114.42 57.58 C 114.42 53.87 111.3 52.95 109.95 52.72 C 109.8 52.7 109.67 52.61 109.58 52.48 C 109.49 52.36 109.46 52.21 109.49 52.07 C 109.3 49.77 107.93 48.73 106.63 48.73 C 105.82 48.73 105.05 49.13 104.54 49.82 C 104.41 49.98 104.2 50.07 103.99 50.04 C 103.79 50.01 103.61 49.87 103.54 49.67 C 103.08 48.38 102.4 47.3 101.54 46.46 C 99.34 44.29 96.33 43.71 93.49 44.89 C 91.12 45.87 89.27 48.69 89.27 51.32" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 84px; margin-left: 100px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CloudWatch Dashboard
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="96" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CloudW...
                </text>
            </switch>
        </g>
        <rect x="0" y="17" width="200" height="100" rx="15" ry="15" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 14px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                monitor-stack / dashboard-construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="100" y="14" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    monitor-stack / dashboard-constru...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>