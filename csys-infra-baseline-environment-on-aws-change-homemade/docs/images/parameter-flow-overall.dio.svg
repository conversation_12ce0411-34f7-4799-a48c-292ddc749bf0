<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="711px" height="191px" viewBox="-0.5 -0.5 711 191" content="&lt;mxfile&gt;&lt;diagram name=&quot;241001&quot; id=&quot;UVLhrP0xN7gqCva98EpB&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;XJDUTHY_1lf7rPM-2Na6&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="0" width="710" height="190" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <image x="-0.5" y="-0.5" width="50" height="50" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH0AAAB9CAMAAAC4XpwXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAA2UExURckl0dNO2tpp3+GF5eef6+ut7uSS6N134tdc3cwz1Pzx/P///+678dBA1/XW9vHI8/jk+QAAAEqT1yoAAAASdFJOU///////////////////////AOK/vxIAAAAJcEhZcwAAFxEAABcRAcom8z8AAAVbSURBVGhD7VqJtqMoEI2JWXBL/v9r+9YCQhBEgz0zZ7znvGcR8rgUtWL35cSJEydOnDhx4sT/E8310d7v9/b50g/+Gszr3vUzhod+/jdgWp9acDc6eTAsdzcN7fP2aN886vtW5w/F+CGqbvCs/Xrwfj6Njo/Di7lvOnJ4Mv9TR0eBWe46CHAj+mNPn8i7qw6+MNLG3jo4AkSQIgemQ7U3cLgul1vI/Q+zfZvVnEDajypXxhVLR87+BTKNipWBc32vZTQKyEPOnlRfLyh3KE9bbIeu+wy3amaA6oOKOcAzW68QVIqBEUuVaPKE8pyMFV0V9R9lqrPyRHp7jdcnyzXocZb5aLNAXM6eR/m3QhDQwReWcI9cguB328Ock4praDs/5ihF/dx54Ah3tk+w/VqOWsWwm52CQMXdQLTvzGGm3GOSAHuZy8cojpY09utOyfc3r2+ofd/rO8hTyFR7934Z4XJA7u9NJqHB7QhBHJZDri1drmVrptzit0GKzrS95Tas+Dt37A2cghbP+FYD4wObg5bIc+vasyEMucjilnuxGU+CNc/t2Mg95l2g3Ehd3ybtacO5P7hyDX3T2Yxy/rlv0zc2hL6BYpnDgrMB3U0PXLaSa3vp+yoWAMc5JW0pgRjc6di2/ZQMP8q65WkDX04FkuEkEnW5wp+8zdO0iqvAdz+pddjK71hLTU06ioCp0qyDhZJOxCZfmDVymdZRhOL2kBmSLkQUOJpvRcQR0zUVt4LSao9Vkg4kreuXi0nWI+X1gwjkdyquIPtNUIzcvvZ3y2943N2z/QR2ndQoQIOTVTEGVmkuI0ISYPObVjIP1sYjx16WcLLsYCAd1M7Pi9wcOOtl2fH9slIH9rSHsO4EoWV8NOtB5OcS3N+tIWt3zI2qoGQYx7168mV2J89K7tPXgd1tmBfNsJNGheyIn2RWDnUI+TLsiPfSOxFlWhUjgME7l4hdpQjIdcUdBpZJhcc+3engywIOQDinlMcyOd0T7FtqHG81YflId5UISfbMekugIr74qojeHKZ0T0aqgRsnS/YCDKWyBXpJcXOB9diNpJ+lKk6lv9jqBNIjOixbRvvJckBW9nnum6ehXeWazgXQK5N+8tXXnkoKnBZYSPyUEvuRuaDrkgK4kVzb8H6yadT2c2AViXs4PPHQOZRc4eofll/7rQ0e56ArsaylTByucTP4bbx9BXMENuB7/VXnEngl+lMx6lxOjPwrTU/XVN3XzCDtvfZen/n0tqN5wMBq1HCVm/ALvuf4Qz6Mpy2IeyFruT7KQQssEGsnxtjsawtgJb0M48BGBZa0Q4eSmNkIOeJID80uhMil7VwFdl4Htg2ziMsuhK8M4+bq6C4m9t6A2Fc6+NG52S1sjOOnju6julErHJJTPhjhYWxeECo3R15RR3eQahZpyagscebBEwxzhvHmKrHbVdSaN8nAkl0g8dyLvWzSOfaCqroTJIsBLrtAVslFv52rp7tzKeFo3aoYqGQzjLrGAboTsKZXNAIGKspzWBC7ir8g1AF78fJeyIDRvM+jdA/ZPYbj2aO9qEQ4gj1cBaN/Vvec3We+Q7wOo/Dk/026V2fHKt4dAQPHzp26d0/AwLFz7q3AziXEVfD5JOL7A2Tlk7kq/xNF6pZe/iGJ7tq68W/tpiGxoC/t93TxS3DV1dldPiHlhOlBtHjikXiP/AO0guOI8bvRgrp0txiNFCI9jVrQCk5F9ikGn71N5nr6HxpXr8JXhZjfIuxxiXhGLYOHEBMT4oN1e/vUuEMsQk0cXKwt9N2xOsMxIBOnjIogP5SbsKS3RV1HP3HixIkTJ06c+E/hcvkDowxTCrQZn7IAAAAASUVORK5CYII=" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="24.5" y="67.5">
                CDK
            </text>
        </g>
        <path d="M 247 141 L 280.63 141" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 285.88 141 L 278.88 144.5 L 280.63 141 L 278.88 137.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="67" y="111" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 141px; margin-left: 68px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                bin/
                                <br/>
                                blea-guest-ecsapp-sample.ts
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="157" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    bin/...
                </text>
            </switch>
        </g>
        <rect x="287" y="111" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 141px; margin-left: 288px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                lib/stack/
                                <br/>
                                スタックファイル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="377" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    lib/stack/...
                </text>
            </switch>
        </g>
        <rect x="507" y="111" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 141px; margin-left: 508px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                lib/construct/
                                <br/>
                                コンストラクトファイル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="597" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    lib/construct/...
                </text>
            </switch>
        </g>
        <path d="M 157 87.37 L 157 111" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 157 82.12 L 160.5 89.12 L 157 87.37 L 153.5 89.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="67" y="21" width="180" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 178px; height: 1px; padding-top: 51px; margin-left: 68px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                params/
                                <br/>
                                環境ファイル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="157" y="55" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    params/...
                </text>
            </switch>
        </g>
        <rect x="159" y="87" width="80" height="20" fill="rgb(255, 255, 255)" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 97px; margin-left: 160px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                ファイル読込
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="199" y="101" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ファイル読込
                </text>
            </switch>
        </g>
        <rect x="215" y="116" width="100" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 126px; margin-left: 216px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 8px;">
                                    Pass
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="265" y="130" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pass
                </text>
            </switch>
        </g>
        <path d="M 467 141 L 500.63 141" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 505.88 141 L 498.88 144.5 L 500.63 141 L 498.88 137.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="437" y="116" width="100" height="20" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 126px; margin-left: 438px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 8px;">
                                    Pass
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="487" y="130" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Pass
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>