<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1271px" height="321px" viewBox="-0.5 -0.5 1271 321" content="&lt;mxfile&gt;&lt;diagram id=&quot;yny-sU1lQc9J4URSI4Re&quot; name=&quot;ページ1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <rect x="0" y="11" width="1270" height="309" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="60" y="40" width="580" height="260" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="60" y="40" width="590" height="80" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 588px; height: 1px; padding-top: 47px; margin-left: 62px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="">
                                    <font style="">
                                        <font style="font-size: 15px;">
                                            <b>
                                                bin/blea-guest-ecsapp-sample.ts
                                            </b>
                                        </font>
                                        <br/>
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
                                    </font>
                                </div>
                                <span style="">
                                    <font style="font-size: 12px;">
                                        <br/>
                                    </font>
                                </span>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <font style="font-size: 12px;">
                                        const
                                        <span style="">
                                            config
                                        </span>
                                        :
                                        <span style="font-style: italic;">
                                            IConfig
                                        </span>
                                        =
                                        <u style="">
                                            require('../params/' + envKey)
                                        </u>
                                        ;
                                    </font>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="62" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    bin/blea-guest-ecsapp-sample.ts...
                </text>
            </switch>
        </g>
        <image x="-0.5" y="9.5" width="50" height="50" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="24.5" y="77.5">
                CDK
            </text>
        </g>
        <rect x="667" y="40" width="283" height="120" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 281px; height: 1px; padding-top: 47px; margin-left: 669px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="">
                                    <b style="">
                                        <font style="font-size: 15px;">
                                            params/stage.ts
                                        </font>
                                        <br/>
                                    </b>
                                    <br/>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="font-style: italic;">
                                                        export
                                                    </span>
                                                    const
                                                    <span style="">
                                                        VpcParam
                                                    </span>
                                                    : inf.
                                                    <span style="font-style: italic;">
                                                        <font color="#ff0000">
                                                            IVpcParam
                                                        </font>
                                                    </span>
                                                    = {
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="">
                                                        cidr
                                                    </span>
                                                    : '10.100.0.0/16',
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    maxAzs: 2,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    };
                                                </font>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="669" y="59" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/stage.ts...
                </text>
            </switch>
        </g>
        <rect x="60" y="180" width="560" height="70" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 558px; height: 1px; padding-top: 187px; margin-left: 62px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
                                    </div>
                                    <div style="">
                                        myVpcCidr:
                                        <span style="">
                                            config
                                        </span>
                                        .
                                        <span style="">
                                            VpcParam
                                        </span>
                                        .
                                        <span style="">
                                            cidr
                                        </span>
                                        ,
                                    </div>
                                    <div style="line-height: 19px;">
                                        <div>
                                            myVpcMaxAzs: config.VpcParam.maxAzs,
                                        </div>
                                    </div>
                                    <div style="">
                                        });
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="62" y="199" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {...
                </text>
            </switch>
        </g>
        <rect x="970" y="40" width="280" height="260" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 278px; height: 1px; padding-top: 47px; margin-left: 972px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="font-size: 12px;">
                                    <b style="font-size: 15px;">
                                        params/interface.ts
                                        <br/>
                                    </b>
                                    <font style="font-size: 12px;">
                                        <br/>
                                    </font>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <div style="line-height: 19px;">
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <span style="font-style: italic;">
                                                                export
                                                            </span>
                                                            interface
                                                            <span style="font-style: italic;">
                                                                <font color="#ff0000" style="font-size: 12px;">
                                                                    IVpcParam
                                                                </font>
                                                            </span>
                                                            {
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            cidr: string;
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            maxAzs: number;
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            }
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <br/>
                                                        </font>
                                                    </div>
                                                    <div style="">
                                                        <font style="font-size: 12px;">
                                                            <div style="line-height: 19px;">
                                                                <div style="">
                                                                    <span style="font-style: italic;">
                                                                        export
                                                                    </span>
                                                                    interface
                                                                    <span style="font-style: italic;">
                                                                        IConfig
                                                                    </span>
                                                                    {
                                                                </div>
                                                                <div style="">
                                                                    VpcParam:
                                                                    <span style="font-style: italic;">
                                                                        IVpcParam
                                                                    </span>
                                                                    ;
                                                                </div>
                                                                <div style="">
                                                                    }
                                                                </div>
                                                            </div>
                                                        </font>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="972" y="59" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/interface.ts...
                </text>
            </switch>
        </g>
        <path d="M 667 240 L 667 245 L 650 245 L 650 170 L 355 170 L 355 136.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 355 131.12 L 358.5 138.12 L 355 136.37 L 351.5 138.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="667" y="180" width="283" height="120" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 281px; height: 1px; padding-top: 187px; margin-left: 669px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font style="">
                                    <b style="">
                                        <font style="font-size: 15px;">
                                            params/prod.ts
                                        </font>
                                        <br/>
                                    </b>
                                    <br/>
                                </font>
                                <div style="font-family: Consolas, &quot;Courier New&quot;, monospace; line-height: 19px;">
                                    <div style="">
                                        <div style="line-height: 19px;">
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="font-style: italic;">
                                                        export
                                                    </span>
                                                    const
                                                    <span style="">
                                                        VpcParam
                                                    </span>
                                                    : inf.
                                                    <span style="font-style: italic;">
                                                        <font color="#ff0000">
                                                            IVpcParam
                                                        </font>
                                                    </span>
                                                    = {
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    <span style="">
                                                        cidr
                                                    </span>
                                                    : '10.200.0.0/16',
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    maxAzs: 3,
                                                </font>
                                            </div>
                                            <div style="">
                                                <font style="font-size: 12px;">
                                                    };
                                                </font>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="669" y="199" fill="#000000" font-family="Helvetica" font-size="12px">
                    params/prod.ts...
                </text>
            </switch>
        </g>
        <path d="M 667 100 L 650 100 L 650 170 L 355 170 L 355 136.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 355 131.12 L 358.5 138.12 L 355 136.37 L 351.5 138.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 170px; margin-left: 523px;">
                        <div data-drawio-colors="color: #000000; background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                環境ファイル読込
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="523" y="173" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    環境ファイル読込
                </text>
            </switch>
        </g>
        <path d="M 730 0 L 940 0 L 940 50 L 840.8 50 L 820.3 100 L 830.8 50 L 730 50 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 25px; margin-left: 731px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                設定値を定義
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="835" y="29" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    設定値を定義
                </text>
            </switch>
        </g>
        <path d="M 978 0 L 1188 0 L 1188 50 L 1088.8 50 L 1068.3 100 L 1078.8 50 L 978 50 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 25px; margin-left: 979px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                型を定義
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1083" y="29" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    型を定義
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>