<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="680px" height="330px" viewBox="-0.5 -0.5 680 330" content="&lt;mxfile&gt;&lt;diagram name=&quot;240822&quot; id=&quot;xNOoMu4lXysAkjPaJcP_&quot;&gt;7VlNc5swEP01PjYjIcTHsXW+Lpm28SHJqUNANjSAGFkOJr++spEMijyJHdswIfXF2tVKYt8+PaFhhMbZ8ooFRXxDI5KOLBAtR+h8ZFkQAFf8rTxV7XE96ZixJJJBjWOSvBA1UnoXSUTmWiCnNOVJoTtDmuck5JovYIyWetiUpvqqRTAjhmMSBqnpvUsiHtdez3Ib/zVJZrFaGTp+3ZMFKlhmMo+DiJYtF7oYoTGjlNetbDkm6Qo8hUv4lN3+zcC9g8lD8fP+Gvm/p9/qyS73GbJJgZGcf3jqSwTj8u5m8ccqk6p64dS6vZVDwHOQLiReMldeKQBJJPCUZk5z8fcj5lkqLCiaZJnwe9EGZ1haD62e8xWNgDIqaeyYjkx7ThcsJG/koFgVsBnhb8ThOm6VT4saEqwrQjPCWSUCGEkDnjzr/AkkDWebuAZq0ZBo74E8NJD/XhQG+GWccDIpgjUApdihOvrTJE3HNKVsHY3CkODpVPgZXeQRiWTUnDP6RFScrOF+RXgmjJPlm7DJXqS2S6XrQNlsPqhC4tbG88DhQPsdkFfAxarWoJX50O5rhq0tnfSvyHfEXYDNXWB3xG9V4v+4Syr3qSvY0JUJD8KnA5UlwsSL7HeURcR51iNynBPKi+vr6oL97uQFoq/Bc2BSGnZ1VkL762JsdYSx+dLXo0QYW39XPHd/A+lUIwxs9+Tzqc+rLW/LYDucO/NODv1FE/GETRk8Wy8DQGcA+c3P0Wesn1xO0r7HvJ4X2Gc+dKDtYg9Y2HNcbRnRr89bZ2rMuy7kJtudaosOre0n1apdeQT9jogEPsYcR62tqGKdjCq2QZUxzYUULkJ+oMxOvZCE4fsy++hhG4MTyqytq6zlmSqrXG2RxUcQWW/46ELcH7zQPMQGh+/msO8DX2v4+CK/R3zdz3cut89b6JtnqzqqPlqW41/UvAGCfCD3jw+ybyjFoK5q2OnxqrblHjw4GcZWfzK85S48OHwd1Bm+wmy+NdZXkuaLLbr4Bw==&lt;/diagram&gt;&lt;diagram id=&quot;fd1B5gNecEqGeb0Rrr6i&quot; name=&quot;240815&quot;&gt;3ZfPc6MgFMf/Gu4KieJRU5M9dE859Ewjq8ygZAjRZP/6JfHhj6W705lOa9uTX748xPfh8SZBZFNfdpodq5+q4BLhoLgg8oAwDkO8to+bc+2dKIh7o9SigKDR2IvfHMwA3LMo+GkWaJSSRhzn5kE1DT+Ymce0Vt087JeS812PrOSesT8w6btPojBV71Icj/4PLsrK7RxGST9TMxcMmZwqVqhuYpEckY1WyvSqvmy4vMFzXPp123/MDh+meWNesyDqF7RMniE3lBNEMUozEFnixPYuCEpyF7NyUylkY64OES8sMRg2qrGPrDK1tKPQSv8z4ctP6qwP8AoMJ8t0ySEK6ub28skySG3HVc2NvtoAzSUzop0fF4NTL4e4EYwVwOZlTtjjlD3maUu8vLtKGL4/snsSnS3/Vybecm345b9JuVlXO3B58ArG3ViKQ0w1KUMavJ0D/cT1svbrJV6qXlyn+iqgksVA+TfrU4MKw6VIrV8AFaEsRNTSiFGyQikFPjQCkRFHLAaRbtyUE5Qu0MRItGATiz2Sj6IVTYlwJO022bO2qrypL8MzChbkmXg8d7xlTSO+EdChYD8CqGsyf931xF73AOUUpRGiWw/cg+uF1HXH2IntBOW3OZIB/zsciR2OP8Lvc5O/MiT/Aw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 340 80 L 164.8 159.24" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 160.02 161.4 L 164.95 155.33 L 164.8 159.24 L 167.84 161.71 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="280" y="0" width="120" height="80" rx="12" ry="12" fill="#cce5ff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 40px; margin-left: 281px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                App
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    App
                </text>
            </switch>
        </g>
        <path d="M 99 229 L 49.19 274.7" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 45.32 278.24 L 48.12 270.93 L 49.19 274.7 L 52.85 276.09 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 99 229 L 149.77 274.74" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 153.67 278.25 L 146.13 276.17 L 149.77 274.74 L 150.81 270.97 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="39" y="149" width="120" height="80" rx="12" ry="12" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 189px; margin-left: 40px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="99" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Stack
                </text>
            </switch>
        </g>
        <path d="M 340 229 L 289.23 274.74" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 285.33 278.25 L 288.19 270.97 L 289.23 274.74 L 292.87 276.17 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 340 229 L 389.81 274.7" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 393.68 278.24 L 386.15 276.09 L 389.81 274.7 L 390.88 270.93 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="280" y="149" width="120" height="80" rx="12" ry="12" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 189px; margin-left: 281px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="340" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Stack
                </text>
            </switch>
        </g>
        <path d="M 340 80 L 340 142.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 340 147.88 L 336.5 140.88 L 340 142.63 L 343.5 140.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 340 80 L 573.88 147.24" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 578.93 148.69 L 571.23 150.12 L 573.88 147.24 L 573.17 143.39 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="0" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="45" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
        <rect x="110" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 111px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="155" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
        <rect x="240" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 241px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="285" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
        <rect x="350" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 351px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="395" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
        <path d="M 580 229 L 529.23 274.74" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 525.33 278.25 L 528.19 270.97 L 529.23 274.74 L 532.87 276.17 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 580 229 L 629.81 274.7" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 633.68 278.24 L 626.15 276.09 L 629.81 274.7 L 630.88 270.93 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="520" y="149" width="120" height="80" rx="12" ry="12" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 189px; margin-left: 521px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Stack
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="580" y="193" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Stack
                </text>
            </switch>
        </g>
        <rect x="480" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 481px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="525" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
        <rect x="590" y="279" width="89" height="50" rx="7.5" ry="7.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 304px; margin-left: 591px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Construct
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="635" y="308" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Construct
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
