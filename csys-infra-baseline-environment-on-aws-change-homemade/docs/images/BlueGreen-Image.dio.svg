<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="721px" height="241px" viewBox="-0.5 -0.5 721 241" content="&lt;mxfile&gt;&lt;diagram id=&quot;6bH6avleOhPzAbBBx2qF&quot; name=&quot;ページ1&quot;&gt;7Vptb6M4EP41fNwKAwHyMSHJ3kl7UnX9sHefKgoOserEyDhv++tvDIYAdtp0S7YbXdOo9Yzfx8/MPLZqudH68JXH+eovlmJqOXZ6sNyZ5Tgo8G34IzXHSuONwkqRcZKqRifFA/mBlVL1y7YkxUWnoWCMCpJ3lQnbbHAiOrqYc7bvNlsy2p01jzOsKR6SmOra7yQVq0obOsFJ/wcm2aqeGfnjqmYd143VTopVnLJ9S+XOLTfijImqtD5EmErj1Xap+i3O1DYL43gjLungVB12Md2qvU2+P4AiomybqiWKY73vnJGNKG03msIXho5sawQ1kZTunFFP0ZeDrgLpkhyjq+jLQVeB+sOj3vyov8CWQpM6w9u9+e3WAuHrTtlWULLBUYMyG5QZj1MC1o8YZRx0G7YB601XYk1BQlDcr4jAD3mcSKvuwUNAt2QboXCOnFpWhpejApJFDHNxJZcngfl8h6sDqdpQGucFeWp6cZxseUF2+G9cVINLLWAul+X1IZPueRfvC+8u42ybl8v/E+Yy1j5C8TGRwHiMqZADCc6ecb1Ry3HhZyFxNl0SSnsG2GEuCLjQhJJMji+YnC5WEsXLckSwCtlk30pp5trKEqYp0rhY4VRtSaEYpsCHs56AGv+CwITZGgt+hCZ1B1u5pIpJqHbR/cnDR6HSrVre7XhKGauokjVjnxwPCsr3zjhuqDniVJacxVeO8caau1YI30AVxnVhMisLjjXxNG/FKQQtJeooBLPw4z/KfqXwrxQA70qcHdqVs2PH1HLslw0NS2FbnqhWanUi5hlWrYIXj+OLfYfgU3XjmMYCYNwN5AZzq+HupXe0mrDlsoBp++fRzHrREfnaCWkWN3l2y+QGr9BcaOZBoAmGgbQX9iDt65BGJkg3yvdAOriGvRqvR9c23sjpGs+xDcZzTMZzBjCeHg7mUZWXtwXEfc2SxTMWyaqdHPpp2piqTenamLL1tN1pViZSwwx9pUkX6EqkN6tzr6406UxEo98bGXqjXu/zaf5cWuqnf6hbBOHc9lp1MwKJWZAyzW4Yl2jqYF0i2R5FKDBhfFl++gmwzq7f4idM71lB1PBrkqbUlH6bil4GbrvfK5wkLvLKHktykAsxEwuOqzxQ0QqgJIWJYOAE8DqlvdXzyqt+VSx0Dd7sDuDMY82Zo4bOvejI70Keiph9YNUhsgusfnh9CVNPTAi2fpXSJVgy1LdBqk9r0TlUNXz4EZmQOET893vx39MB4xkAMwgbtDXEPGC+I0mKbxEw1wxCFyMGIsxjURoRmxBzxdBzCZO4VuipV9tmYerC4NSXCvfMpcK1plHdZvyLbheldI85gZ02l92fu3LUVm/fOZD/2qXDCUfBb3TpcPRI8IPk2mG0LN8lfzNTQCbr8n1L8y2ln5F1Buui5Al+xz+2HMstZBiibQxrWywIxXfFLhvGO754HxdnPUNiTvEM55QdPyn2DVFsbxZA5dsoNny86fj9FPuKdGhIhp0AstMK2YP4rWt/ID8yZLU3vi1oh95AyPDqMIC50AUkwGivQZ4T0MhAA0JrEpa5fmRN5tZ4UtOAUc0Q0OltUREDsIRfPv0+wc3Fz0qCJMeZ1JwBWdOFHDBcWGNfdQ8nt0cekK+TB898wD/LE875kd8772oJqte7yATSnzAfXJCn2+RZMZXPhHcbCc+3J64bvC3hOUGAAAL/l4RXuANF7g8kqEh/RJcM9Z7kWMLn02VvyGU/kqPeyjOwJKl5De6rPspcwscGepQB8fQPHVXaPv1bjDv/Dw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <path d="M 140 0 L 720 0 L 720 240 L 140 240 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 146.09 7.18 C 146.01 7.18 145.93 7.19 145.85 7.19 C 145.5 7.19 145.15 7.23 144.81 7.32 C 144.53 7.39 144.25 7.49 143.98 7.62 C 143.9 7.65 143.84 7.7 143.79 7.76 C 143.75 7.83 143.74 7.91 143.74 7.99 L 143.74 8.32 C 143.74 8.46 143.78 8.53 143.88 8.53 L 143.99 8.53 L 144.22 8.44 C 144.45 8.35 144.69 8.27 144.94 8.21 C 145.17 8.16 145.41 8.13 145.65 8.13 C 146.04 8.09 146.43 8.2 146.74 8.44 C 146.97 8.74 147.09 9.12 147.05 9.5 L 147.05 9.99 C 146.78 9.93 146.54 9.88 146.29 9.84 C 146.05 9.81 145.81 9.79 145.57 9.79 C 144.98 9.76 144.4 9.94 143.94 10.31 C 143.54 10.65 143.32 11.15 143.34 11.68 C 143.31 12.15 143.49 12.62 143.82 12.96 C 144.18 13.29 144.66 13.46 145.15 13.44 C 145.91 13.45 146.63 13.11 147.11 12.51 C 147.18 12.66 147.24 12.79 147.31 12.91 C 147.38 13.02 147.46 13.12 147.55 13.21 C 147.6 13.27 147.67 13.31 147.75 13.31 C 147.81 13.31 147.87 13.29 147.92 13.25 L 148.34 12.97 C 148.41 12.93 148.46 12.86 148.47 12.77 C 148.47 12.72 148.45 12.67 148.42 12.62 C 148.34 12.47 148.26 12.31 148.21 12.14 C 148.15 11.95 148.12 11.75 148.13 11.55 L 148.14 9.37 C 148.2 8.77 148 8.18 147.59 7.74 C 147.17 7.39 146.64 7.19 146.09 7.18 Z M 159.89 7.19 C 159.78 7.19 159.68 7.19 159.57 7.2 C 159.29 7.2 159 7.24 158.73 7.31 C 158.47 7.38 158.23 7.5 158.01 7.66 C 157.82 7.81 157.66 7.99 157.54 8.21 C 157.42 8.43 157.35 8.67 157.36 8.92 C 157.36 9.27 157.48 9.61 157.69 9.89 C 157.97 10.22 158.34 10.46 158.76 10.56 L 159.72 10.87 C 159.97 10.93 160.2 11.05 160.39 11.22 C 160.51 11.35 160.58 11.51 160.57 11.69 C 160.58 11.94 160.45 12.18 160.23 12.31 C 159.93 12.48 159.6 12.56 159.26 12.54 C 158.99 12.54 158.72 12.51 158.46 12.45 C 158.22 12.4 157.98 12.32 157.75 12.22 L 157.59 12.15 C 157.54 12.14 157.5 12.14 157.46 12.15 C 157.36 12.15 157.31 12.22 157.31 12.36 L 157.31 12.69 C 157.31 12.76 157.32 12.82 157.35 12.89 C 157.4 12.97 157.47 13.03 157.56 13.07 C 157.8 13.19 158.06 13.28 158.32 13.34 C 158.66 13.41 159 13.45 159.35 13.45 L 159.33 13.46 C 159.66 13.45 159.98 13.4 160.29 13.3 C 160.55 13.22 160.8 13.09 161.01 12.92 C 161.21 12.77 161.38 12.57 161.49 12.34 C 161.61 12.1 161.67 11.83 161.66 11.56 C 161.67 11.23 161.56 10.9 161.36 10.63 C 161.09 10.32 160.73 10.09 160.33 9.99 L 159.39 9.69 C 159.13 9.61 158.88 9.49 158.67 9.32 C 158.54 9.2 158.47 9.03 158.47 8.85 C 158.46 8.61 158.58 8.38 158.79 8.25 C 159.06 8.11 159.36 8.05 159.67 8.06 C 160.11 8.06 160.55 8.14 160.96 8.32 C 161.04 8.37 161.12 8.4 161.21 8.41 C 161.31 8.41 161.36 8.34 161.36 8.19 L 161.36 7.88 C 161.37 7.8 161.35 7.72 161.31 7.66 C 161.25 7.59 161.18 7.54 161.11 7.49 L 160.83 7.38 L 160.45 7.27 L 160.01 7.2 C 159.97 7.2 159.93 7.19 159.89 7.19 Z M 156.02 7.36 C 155.94 7.35 155.86 7.38 155.79 7.42 C 155.72 7.5 155.68 7.59 155.66 7.69 L 154.51 12.14 L 153.47 7.71 C 153.45 7.61 153.41 7.52 153.34 7.44 C 153.26 7.39 153.17 7.37 153.07 7.38 L 152.54 7.38 C 152.44 7.37 152.35 7.39 152.27 7.44 C 152.2 7.51 152.15 7.61 152.14 7.71 L 151.09 12.14 L 149.97 7.7 C 149.95 7.6 149.91 7.51 149.84 7.44 C 149.76 7.39 149.67 7.36 149.58 7.37 L 148.92 7.37 C 148.81 7.37 148.76 7.43 148.76 7.54 C 148.77 7.63 148.79 7.72 148.82 7.81 L 150.38 12.95 C 150.4 13.05 150.45 13.14 150.52 13.21 C 150.6 13.26 150.69 13.29 150.78 13.28 L 151.36 13.26 C 151.46 13.27 151.55 13.25 151.63 13.19 C 151.7 13.12 151.74 13.03 151.76 12.93 L 152.79 8.64 L 153.82 12.93 C 153.83 13.03 153.88 13.12 153.95 13.19 C 154.03 13.25 154.12 13.27 154.21 13.26 L 154.78 13.26 C 154.88 13.27 154.97 13.25 155.04 13.2 C 155.11 13.13 155.16 13.03 155.18 12.94 L 156.79 7.79 C 156.84 7.72 156.84 7.63 156.84 7.63 C 156.84 7.59 156.84 7.56 156.84 7.52 C 156.84 7.48 156.82 7.43 156.79 7.4 C 156.76 7.37 156.72 7.35 156.67 7.36 L 156.05 7.36 C 156.04 7.36 156.03 7.36 156.02 7.36 Z M 145.65 10.62 C 145.7 10.62 145.75 10.62 145.8 10.62 L 146.43 10.62 C 146.64 10.64 146.85 10.67 147.06 10.71 L 147.06 11.01 C 147.07 11.21 147.05 11.4 147 11.59 C 146.96 11.75 146.88 11.9 146.77 12.01 C 146.61 12.21 146.39 12.36 146.14 12.44 C 145.91 12.52 145.67 12.56 145.43 12.56 C 145.18 12.6 144.93 12.53 144.73 12.37 C 144.55 12.18 144.46 11.92 144.49 11.66 C 144.47 11.36 144.59 11.08 144.81 10.89 C 145.06 10.72 145.35 10.62 145.65 10.62 Z M 161.04 14.72 C 160.34 14.73 159.51 14.89 158.88 15.33 C 158.69 15.46 158.72 15.63 158.94 15.63 C 159.64 15.54 161.21 15.35 161.5 15.71 C 161.78 16.06 161.19 17.54 160.94 18.21 C 160.86 18.41 161.03 18.49 161.21 18.34 C 162.39 17.36 162.72 15.3 162.46 15 C 162.32 14.85 161.74 14.71 161.04 14.72 Z M 142.65 15.1 C 142.5 15.12 142.42 15.3 142.58 15.44 C 145.29 17.89 148.82 19.23 152.48 19.21 C 155.37 19.22 158.2 18.36 160.59 16.74 C 160.95 16.47 160.63 16.07 160.26 16.23 C 157.87 17.24 155.3 17.76 152.71 17.77 C 149.23 17.78 145.82 16.87 142.81 15.14 C 142.75 15.11 142.69 15.1 142.65 15.1 Z M 140 0 L 165 0 L 165 25 L 140 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 548px; height: 1px; padding-top: 7px; margin-left: 172px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS Cloud
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="172" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS Cloud
                </text>
            </switch>
        </g>
        <path d="M 380 140 L 553.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 558.88 140 L 551.88 143.5 L 553.63 140 L 551.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 460px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Blue/Greenデプロイ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="460" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Blue/Greenデプロイ
                </text>
            </switch>
        </g>
        <rect x="520" y="40" width="180" height="180" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="560" y="80" width="120" height="120" fill="none" stroke="#d45b07" stroke-dasharray="3 3" pointer-events="all"/>
        <path d="M 520 40 L 550 40 L 550 70 L 520 70 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 544.89 58.26 L 541.52 56.24 L 541.52 51.43 C 541.52 51.28 541.44 51.15 541.31 51.07 L 536.47 48.25 L 536.47 44.18 L 544.89 49.15 Z M 545.52 48.55 L 536.27 43.08 C 536.14 43 535.98 43 535.84 43.07 C 535.71 43.15 535.63 43.29 535.63 43.44 L 535.63 48.49 C 535.63 48.64 535.71 48.78 535.84 48.85 L 540.68 51.68 L 540.68 56.48 C 540.68 56.63 540.76 56.77 540.88 56.84 L 545.09 59.37 C 545.16 59.41 545.23 59.43 545.31 59.43 C 545.38 59.43 545.45 59.41 545.51 59.37 C 545.65 59.3 545.73 59.16 545.73 59.01 L 545.73 48.91 C 545.73 48.76 545.65 48.62 545.52 48.55 Z M 534.98 66.1 L 525.11 60.86 L 525.11 49.15 L 533.53 44.18 L 533.53 48.26 L 529.09 51.08 C 528.97 51.16 528.9 51.29 528.9 51.43 L 528.9 58.59 C 528.9 58.74 528.99 58.89 529.13 58.96 L 534.79 61.9 C 534.91 61.97 535.05 61.97 535.17 61.9 L 540.66 59.07 L 544.04 61.09 Z M 545.1 60.75 L 540.9 58.22 C 540.77 58.15 540.62 58.14 540.49 58.21 L 534.98 61.06 L 529.74 58.33 L 529.74 51.66 L 534.17 48.84 C 534.3 48.77 534.37 48.63 534.37 48.49 L 534.37 43.44 C 534.37 43.29 534.29 43.15 534.16 43.07 C 534.03 43 533.86 43 533.73 43.08 L 524.48 48.55 C 524.35 48.62 524.27 48.76 524.27 48.91 L 524.27 61.11 C 524.27 61.27 524.36 61.41 524.49 61.48 L 534.78 66.95 C 534.84 66.98 534.91 67 534.98 67 C 535.05 67 535.12 66.98 535.18 66.95 L 545.09 61.48 C 545.22 61.41 545.3 61.27 545.31 61.12 C 545.31 60.97 545.23 60.83 545.1 60.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 55px; margin-left: 552px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Cluster
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="552" y="59" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS C...
                </text>
            </switch>
        </g>
        <rect x="600" y="120" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 639.09 127.16 L 600.91 127.16 C 600.41 127.16 600 127.57 600 128.07 L 600 151.93 C 600 152.43 600.41 152.84 600.91 152.84 L 639.09 152.84 C 639.59 152.84 640 152.43 640 151.93 L 640 128.07 C 640 127.57 639.59 127.16 639.09 127.16 Z M 601.82 151.02 L 601.82 128.98 L 638.18 128.98 L 638.18 151.02 Z M 604.77 148.75 L 606.59 148.75 L 606.59 131.25 L 604.77 131.25 Z M 609.55 148.75 L 611.36 148.75 L 611.36 131.25 L 609.55 131.25 Z M 614.32 148.75 L 616.14 148.75 L 616.14 131.25 L 614.32 131.25 Z M 619.09 148.75 L 620.91 148.75 L 620.91 131.25 L 619.09 131.25 Z M 623.86 148.75 L 625.68 148.75 L 625.68 131.25 L 623.86 131.25 Z M 628.64 148.75 L 630.45 148.75 L 630.45 131.25 L 628.64 131.25 Z M 633.41 148.75 L 635.23 148.75 L 635.23 131.25 L 633.41 131.25 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 620px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="620" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Contai...
                </text>
            </switch>
        </g>
        <rect x="560" y="80" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 575.8 104.69 L 584.49 104.69 L 584.49 103.33 L 575.8 103.33 Z M 575.8 97.93 L 584.49 97.93 L 584.49 96.57 L 575.8 96.57 Z M 575.8 91.18 L 584.49 91.18 L 584.49 89.81 L 575.8 89.81 Z M 572.14 104.78 L 572.14 103.24 L 573.67 103.24 L 573.67 104.78 Z M 571.46 106.14 L 574.36 106.14 C 574.73 106.14 575.04 105.83 575.04 105.46 L 575.04 102.56 C 575.04 102.18 574.73 101.88 574.36 101.88 L 571.46 101.88 C 571.08 101.88 570.78 102.18 570.78 102.56 L 570.78 105.46 C 570.78 105.83 571.08 106.14 571.46 106.14 Z M 572.14 98.02 L 572.14 96.49 L 573.67 96.49 L 573.67 98.02 Z M 571.46 99.38 L 574.36 99.38 C 574.73 99.38 575.04 99.08 575.04 98.7 L 575.04 95.8 C 575.04 95.43 574.73 95.12 574.36 95.12 L 571.46 95.12 C 571.08 95.12 570.78 95.43 570.78 95.8 L 570.78 98.7 C 570.78 99.08 571.08 99.38 571.46 99.38 Z M 572.14 91.26 L 572.14 89.73 L 573.67 89.73 L 573.67 91.26 Z M 571.46 92.63 L 574.36 92.63 C 574.73 92.63 575.04 92.32 575.04 91.94 L 575.04 89.05 C 575.04 88.67 574.73 88.37 574.36 88.37 L 571.46 88.37 C 571.08 88.37 570.78 88.67 570.78 89.05 L 570.78 91.94 C 570.78 92.32 571.08 92.63 571.46 92.63 Z M 569.73 108.64 L 569.73 86.83 L 585.74 86.83 L 585.74 108.64 Z M 568.37 86.15 L 568.37 105.74 L 566.83 105.74 L 566.83 83.94 L 582.85 83.94 L 582.85 85.47 L 569.05 85.47 C 568.67 85.47 568.37 85.77 568.37 86.15 Z M 565.47 83.26 L 565.47 103.17 L 564.26 103.17 L 564.26 81.36 L 580.27 81.36 L 580.27 82.57 L 566.15 82.57 C 565.77 82.57 565.47 82.88 565.47 83.26 Z M 586.42 85.47 L 584.21 85.47 L 584.21 83.26 C 584.21 82.88 583.9 82.57 583.53 82.57 L 581.63 82.57 L 581.63 80.68 C 581.63 80.31 581.33 80 580.95 80 L 563.58 80 C 563.2 80 562.9 80.31 562.9 80.68 L 562.9 103.85 C 562.9 104.23 563.2 104.53 563.58 104.53 L 565.47 104.53 L 565.47 106.42 C 565.47 106.8 565.77 107.1 566.15 107.1 L 568.37 107.1 L 568.37 109.32 C 568.37 109.7 568.67 110 569.05 110 L 586.42 110 C 586.8 110 587.1 109.7 587.1 109.32 L 587.1 86.15 C 587.1 85.77 586.8 85.47 586.42 85.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 95px; margin-left: 592px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Servicde
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="592" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Servi...
                </text>
            </switch>
        </g>
        <path d="M 40 140 L 173.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 178.88 140 L 171.88 143.5 L 173.63 140 L 171.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 90px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アップロード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="90" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アップロード
                </text>
            </switch>
        </g>
        <image x="-0.5" y="119.5" width="40" height="40" xlink:href="data:image/svg+xml;base64,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"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 20px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                zip
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="20" y="179" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    zip
                </text>
            </switch>
        </g>
        <path d="M 340 120 L 380 120 L 380 160 L 340 160 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 360.02 124 C 351.22 124 344.35 130.71 344.01 139.6 C 344 139.81 344.12 140 344.29 140.11 L 350.04 143.89 L 350.65 142.94 L 345.22 139.37 C 345.5 138.16 346.39 137.24 347.66 136.78 C 348.26 136.56 348.95 136.44 349.71 136.44 C 350.95 136.44 352.11 136.91 352.97 137.76 L 353.07 137.85 C 353.85 138.61 354.31 139.99 354.31 139.99 C 354.31 140.03 355.07 142.88 355.07 142.88 L 356.16 142.59 L 355.43 139.87 L 355.43 139.81 C 355.7 137.33 357.97 136.44 360.01 136.44 C 361.24 136.44 362.4 136.91 363.27 137.76 C 363.27 137.76 364.49 138.7 364.58 139.8 L 364.59 139.88 L 363.85 142.59 L 364.93 142.89 L 365.7 140.06 C 365.7 140.04 365.71 140.02 365.71 139.99 C 365.86 137.83 367.66 136.44 370.31 136.44 C 371.54 136.44 372.69 136.91 373.56 137.76 C 374.23 138.41 374.71 138.91 374.84 139.4 L 369.99 142.8 L 370.63 143.73 L 375.76 140.13 C 375.9 140.02 376 139.62 376 139.6 C 375.84 130.71 368.98 124.01 360.02 124 Z M 354.33 137.51 C 354.18 137.36 354.02 137.2 353.85 137.04 L 353.75 136.94 C 352.67 135.89 351.24 135.31 349.71 135.31 C 347.97 135.31 346.51 135.84 345.5 136.76 C 346.83 130.74 351.55 126.27 357.6 125.33 C 355.79 127.45 354.54 131.94 354.33 137.51 Z M 364.25 137.14 L 364.05 136.94 C 362.97 135.89 361.54 135.31 360.01 135.31 C 358.06 135.31 356.48 135.97 355.47 137.08 C 355.84 129.95 358.03 125.14 360.01 125.14 C 360.02 125.14 360.02 125.14 360.02 125.14 C 362.04 125.15 364.26 130.12 364.57 137.46 C 364.47 137.35 364.36 137.25 364.25 137.14 Z M 374.34 136.94 C 373.26 135.89 371.83 135.31 370.31 135.31 C 368.32 135.31 366.69 136.02 365.69 137.2 C 365.44 131.77 364.2 127.41 362.43 125.33 C 368.77 126.29 373.59 130.98 374.66 137.25 C 374.55 137.15 374.45 137.04 374.34 136.94 Z M 354.89 149.91 C 354.77 149.8 354.71 149.63 354.72 149.47 C 354.73 149.3 354.81 149.15 354.95 149.05 L 357.61 147.04 L 358.28 147.95 L 356.14 149.56 L 358.06 151.44 L 357.27 152.25 Z M 364.51 150.11 L 362.6 148.23 L 363.38 147.42 L 365.77 149.76 C 365.89 149.87 365.95 150.04 365.94 150.2 C 365.93 150.37 365.84 150.52 365.71 150.62 L 363.05 152.63 L 362.38 151.72 Z M 358.09 153.86 L 361.49 146.08 L 362.52 146.53 L 359.12 154.32 Z M 368.14 144.11 L 352.43 144.11 C 352.12 144.11 351.87 144.36 351.87 144.67 L 351.87 155.43 C 351.87 155.74 352.12 156 352.43 156 L 368.14 156 C 368.45 156 368.7 155.74 368.7 155.43 L 368.7 144.67 C 368.7 144.36 368.45 144.11 368.14 144.11 Z M 352.99 154.86 L 352.99 145.24 L 367.58 145.24 L 367.58 154.86 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 360px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodeDeploy
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="360" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CodeDe...
                </text>
            </switch>
        </g>
        <rect x="160" y="80" width="240" height="120" fill="none" stroke="#4d72f3" pointer-events="all"/>
        <path d="M 220 140 L 333.63 140" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 338.88 140 L 331.88 143.5 L 333.63 140 L 331.88 136.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 140px; margin-left: 280px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                設定ファイル
                                <br/>
                                読み取り
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="280" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    設定ファイル
読み取り
                </text>
            </switch>
        </g>
        <path d="M 180 120 L 220 120 L 220 160 L 180 160 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 211.94 141.65 L 212.16 140.11 C 214.18 141.32 214.21 141.82 214.21 141.83 C 214.2 141.84 213.86 142.13 211.94 141.65 Z M 210.83 141.34 C 207.33 140.29 202.46 138.05 200.49 137.12 C 200.49 137.11 200.49 137.11 200.49 137.1 C 200.49 136.34 199.88 135.72 199.12 135.72 C 198.36 135.72 197.75 136.34 197.75 137.1 C 197.75 137.85 198.36 138.47 199.12 138.47 C 199.45 138.47 199.75 138.35 199.99 138.15 C 202.31 139.25 207.14 141.45 210.67 142.49 L 209.27 152.32 C 209.27 152.35 209.27 152.37 209.27 152.4 C 209.27 153.27 205.43 154.86 199.17 154.86 C 192.84 154.86 188.97 153.27 188.97 152.4 C 188.97 152.37 188.97 152.35 188.97 152.32 L 186.05 131.06 C 188.57 132.8 193.99 133.71 199.18 133.71 C 204.35 133.71 209.76 132.8 212.29 131.07 Z M 185.75 128.84 C 185.79 128.09 190.11 125.14 199.18 125.14 C 208.24 125.14 212.56 128.09 212.6 128.84 L 212.6 129.1 C 212.11 130.79 206.51 132.57 199.18 132.57 C 191.83 132.57 186.23 130.78 185.75 129.09 Z M 213.75 128.86 C 213.75 126.88 208.07 124 199.18 124 C 190.28 124 184.6 126.88 184.6 128.86 L 184.66 129.29 L 187.83 152.44 C 187.9 155.03 194.81 156 199.17 156 C 204.59 156 210.34 154.76 210.41 152.45 L 211.78 142.79 C 212.54 142.97 213.17 143.07 213.67 143.07 C 214.35 143.07 214.8 142.9 215.08 142.57 C 215.31 142.3 215.4 141.97 215.33 141.62 C 215.18 140.83 214.24 139.98 212.33 138.89 L 213.69 129.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 167px; margin-left: 200px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="200" y="179" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <path d="M 160 80 L 190 80 L 190 110 L 160 110 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 184.62 83 L 165.38 83 C 164.07 83 163 84.07 163 85.38 L 163 91.09 C 163 92.4 164.07 93.47 165.38 93.47 L 166.05 93.47 L 166.05 106.56 C 166.05 106.8 166.25 107 166.49 107 L 183.07 107 C 183.31 107 183.51 106.8 183.51 106.56 L 183.51 93.47 L 184.62 93.47 C 185.93 93.47 187 92.4 187 91.09 L 187 85.38 C 187 84.07 185.93 83 184.62 83 Z M 166.93 106.13 L 166.93 93.47 L 182.64 93.47 L 182.64 106.13 Z M 184.62 92.6 L 165.38 92.6 C 164.55 92.6 163.87 91.92 163.87 91.09 L 163.87 90.85 L 169.11 90.85 L 169.11 89.98 L 163.87 89.98 L 163.87 85.38 C 163.87 84.55 164.55 83.87 165.38 83.87 L 184.62 83.87 C 185.45 83.87 186.13 84.55 186.13 85.38 L 186.13 89.98 L 173.91 89.98 L 173.91 90.85 L 186.13 90.85 L 186.13 91.09 C 186.13 91.92 185.45 92.6 184.62 92.6 Z M 168.46 99.6 C 168.46 99.48 168.51 99.36 168.61 99.27 L 171.37 96.83 L 171.95 97.48 L 169.56 99.6 L 171.94 101.65 L 171.37 102.31 L 168.61 99.93 C 168.51 99.85 168.46 99.73 168.46 99.6 Z M 177.21 101.67 L 179.61 99.56 L 177.21 97.49 L 177.79 96.83 L 180.56 99.23 C 180.66 99.31 180.71 99.43 180.71 99.56 C 180.71 99.69 180.66 99.81 180.56 99.89 L 177.79 102.32 Z M 173.33 104.02 L 172.52 103.69 L 175.83 95.6 L 176.64 95.94 Z M 170.42 90.85 L 172.6 90.85 L 172.6 89.98 L 170.42 89.98 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 95px; margin-left: 192px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodePipeline
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="192" y="99" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    CodeP...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>