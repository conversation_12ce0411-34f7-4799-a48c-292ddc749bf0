<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="932px" height="221px" viewBox="-0.5 -0.5 932 221" content="&lt;mxfile&gt;&lt;diagram id=&quot;w6xJ1pXGt0ISZXmTK1yF&quot; name=&quot;240823&quot;&gt;7XxXt6NIsu6vqcc7C4R/TLwXXqCXWXgQVoCEpF9/M7fp7qpdPWfO6e6ZO+selWqLNKSJjC/ii1Sib4QwPJQlnRtrKsr+2wErHt8I8dvhgLMcCz9QzvM9hyEP7xn10hYflX7N8NtX+ZGJfeTe2qJcv6u4TVO/tfP3mfk0jmW+fZeXLsu0f1+tmvrve53TuvyS4edp/zX31BZb857LHphf89WyrZvPnnGaey8Z0s/KHzNZm7SY9t9kEdI3QlimaXu/Gh5C2SPhfcrl/T75d0p/GdhSjts/dQPxfsc97W8fk/sY2Pb8nO3etFvpz2mO0jtc0W8E32xDD1M4vFy3ZepKYeqn5a0+QYrMQSZgSdX2/Wf+OI3wdv6jt3LZysfvDhn/RRBQg8ppKLflCat83PB/PmX3oTwH7CO9/7oUBPuR1/x2GT5vTD+Wv/6l7V8lBC8+hPQ7AsP+sMB+IpYvMhRJiseYP0dgJPu9wHD6q8DwnwqM/RMExv0V8irStSmL31PAP1N41D+hbb8o1p+ubQfqq/Qk6huQvnHyN4n9BvBvrPBFnmUBrdVH8kNgvxEnnPjyjGEC+0wkKPE36jMpPn5bKD4/Uu/doLb/sSjhUKbbkn/U+pjAli51+VmL+bnEl7JPt/b+ffN/RHpfhSdAV8TfWmjuf5TZ2pVb3nxMdZ7acXvrmOLhGy6k8P6fglUFlPM31PiXzJ/lMV8z8a/V4Af+sx5+zPxZHvM1E/9aDaU+R/195s/yGOrriH+8G//J3fgPd8M3wU+3rW9HiNBPb4xkXE3j9hvMwn8yWkW+XtKiLb8r+8WhfJaJ7QIbaqfxTcEXhMPvrAS8B75InvuZdajeXr81Img4yEi00MObaVb2zrS2H81n07ZNw28qgL6tUcE2ITOVfqRyOKpy+cFuwRl+cBf88Jn+0DjUZbrO7+Ko2gcaBw/JwIwKh0eNeNPf0n0l/7aU73DScjQeHibfr76vlUPFzt4Um+D7H6bwy9j+BHP4i+/4NIfkV3NI/sQakn+CMaS/wFkSfJgh9LcVTfB/If2fA2mZYSWM/O9BWsQoAWf+OKSHtiiQjnyB9C8Fn6juy2r792G6zNefoHl5B9W/ihgSPwEz8SeAmfmJbx63FOrUfwHkP6R5H2zoR8X65IvfK9aPXPPf5CbeLFe5SPfy3YDhv6dV+acA/47/TBP/DDL8b7T+7BeF8cvl3uZF+Z+oL3+lDfqnFQYamL+vb0Isf6Ywf6Hl+Weiqr/K8vxEk75JxDf28A28X7y93y845uMCiB8XvPBZh/sXxV1vKadcWjhTZDv+QDCGH34SjXH/omjss/PfyB0q4Ayd3zq979VV/4lI/n/B8iMgb+na/UVm///8grt/g93Hv9L+38UkAJ85xAekIff9gCvzO5CGsCf/VUh+tFv82Qa8/s1dMPXrTSjxx3ZdcOYr0P9lOP9K7X6D8x8B/qHO7fC25f5fo+zNKfFp3tXLdBuLHxD7Oxj8ERNvvYHPXOwzB14X6ZZ+I8B78iDPYw3Dnjbij96OGUo9Afiy/bCRwhpeuSgpAQEk8JPP82szoBwQ276HaWBZyZyGdRRg654kh6XMbPEJfx6Y6dgXr5VkkuVWDrCfIxETuX6LNhnXsRchDRXFPMP5tsLu5ays7ahQenlbSCAJgnN+zXYydZJIyqIkBPxt1VQBCHzSKFN7wCilJqVaAjKYgA80RZBcwMv2i0rFCY5GBjzJg91t4UCXWyW6haABWNUGkwIkVKF40alYe3CmgNdksINWAPVlrQI3h1XFCfDvVV1LKS50IgJYtQBAk7UdCIZYv7oqqF3wUXVWAPDc/Fhe6BBK6bdVzcsLi10oSQOItcDXjQSrBvm3A3+qaXjNu6AEriVpqAoz7piKRq295l1E6vj2dixYvVW9V0449NHLXqW4H2H+iF/DV5q9wjvLqDSq+A1tIh14mivvK/yE76GHvqRC02/EaTuI0tkS+ZNYZsHphgWFw0bwZR3Ft9rybvqLMD312qFv1YFbbvcYWg5Zhn/4idSasJYoUzLop3uvd3DaJVIhJuK4vQ6XbGTvsCp/1Cb/ZLl8UgvnLQWuQWIQT/yjC8uLXT/Yu0eq9Xi5Pp3j+XlUGzJVOdXjCl6bxlpTPaQqsP71OohQ+6ckbe1nKRj3umLUTTJgkUROFyCa5Ot0fvmE85yHvmYcjd6WVLH0iNR7A8wMOL3uDrE+S5BNAWwUW4YsJsbGPXQ6lHtjaUapu5I0QbjyKbWxE7mSmkJOT7hETHZqU+o+PthKLTt5v3nWIAB14o/k62n2nhNITVpWrU7nGlbJWOCKgb+jVRq5uJgDBsOCyirGVe2zsVCBKt59xm76e6rgyakSBI0WXb3dlPzB88GzJCxfyiWiluWIumDAuj8IhqO9luMU9i7xoNuhfjTT6ZUHw/El74541sITv+91GLgBHJqgiK/NbiTBA6Ed8I44Y3R+LyKoDbJvuODKJ5oETKJhnaZ8dqOCYa4rVtcOjlm+iC/awoAmPMLd7BoHUJA/8tX9Fdk1EbHCYXKUatKcI364kSy1U0YIzCwzE4Juj8y4KCqsjqwnXFAeSYG6TL0vJQCJljhUhEjBwmhTNZ/Qiu5aB7WQMI+ite4dbmGrHwon8LpfYKUnxhLyEk+9Yt1nizTmxGNLQn0GEyONsSqE6bQElymlnsxqtUDOQ7N0a2+CvIovEGJ0JjA7PQLtRWLQsvcP474Ms55ESuDeykCinznLeMbSSP7o8eliNcl96s2+J7QWBKHwtEWSZN1w1kwt6u0oOrM1vVyHRdok4dJTErQmN+veJLpkgKAmqqamsb3O2EsGzYwlJmvTFqGQA/51Y+pV19OFq0YkT4RQzemww928pvHLirLLmsbEiSGSINQEd3V1UtyKbe0yG9YPrx2rjLi9n7FFNAMyCHwFZpfSJaUvWKlJE2guFa0ph9Ar16oT+i4v6UHkE+KSDZDo1d5+rJwRLUI2X1Xb6RN4uRSquGSVr1ZO30IRdgueCu1SMkTK3OAIu82HOOYVaP0S4b5HZb6/7qdF9wewubhX4A+WW+qrJmmCJwacgzRK7CT2FNpWnE2NNoozBL/rHg2qep2XzRjsJ+5wPAQtlVyhbVbdAjpTGVdPaqkxRila9HXRCcLPpLGo8GW4Jbegk8oYs9US4LmIbEI1S69qe22qXRyT9QEt+RjB7Iszn8zA4GMwcCBbkk2M2z7obsEln4rHYD1O1PHclgACF9VGZo7vouFutZLuS2JADGvdpbNV13xIxLD0jkW5bobKmdqbrrwH7LhZCyuR4JAWa90rd5cV62r2cLFaXva6KjrohNC+0wek+HQuKGZ+1/ndav2N9+srdQxX3gBFDVxmQasa36KIGVMmOh9Oesff16CqR1d4Plfad1XFuNomXwf+seovnZjVA9CuFjXThyG0Xn5LiYfnZl+qOL8HYikk4unEsoeYDm7A9u4CKQmEwxK1xTagyiVMiMOXoHHdkwhYH/ZuT6490wN/1F35BLqGfO4c8iTGsAxHzPBfGayEiXbgCyAGV3Aw96sAcX3xxTyuz94Iysy43lX2wPvnYQerDlQeH4D9CvSTdzhn1Wv07tksDYLF8w4PeP1xPZvuqjh0d1mG5EoKEfRut9tqholuIEuvTlpzQZK/PymbD6e61m3VdVblCDj/dVwFx8ROdX6ZDL+EXIEfIZjkrdLGauR2AwnX9oDO3G6H2zHSNEcyLkMD662X6V5gZ8uQcRqyMp4d3BlpLVEVUJECzsaQveneHd/K6uM5jJ4OL4xr3JyEjmJj+uLlUJGF8LL2/vOWUpKZlSCNRtCqVnNiUKOEmpqAjev8cZ+yjngt0Om7JhlGp+jhrnl/zyQjwCB0pbkxkR7XjHLfkTLOTOZ0c7lLnQoIjUc+viO3ed6ZgjNUXL5nsAM5vM9pfhHxiIZuPz0UxIk809y+9jpZitAYnAEkDresjkxiJ7vc1wWXKULGrxCK5TYRGW5WQbDQu9UEKYd2t/iOK4G48oqNmRbATkRaHcMOArDzIkiO5XiB87EIJvSlWJKw/GIqvPFiiE5KHjjN3bKBWnzJ6wscjiZHi5H2Cw1ti9ygEfPQ6WGIMDyv8I/f2DcRXWjn7ihowpjgkiGZrQZ4wlCyW7CuW8bK0VJh0Nxb3hFontCeyn2qOMg5Bf5UlLJRwl51Z/Z5BXB0BFLRck9aDHrjSPbP2X0OZvioZUkwhLpP+MifjJjFIDHEh62pD94oa1Ktn4CjhtnizXxtOeTVvdo+RkgCpIggE6BDlxO0LIlD+iEn+zzlBTuQJEGne44duO4VCfcEyyDpjd0FuLUkD2xAuWDvx3kxPFO4uYcK0lCz3LM7SCCjy6FxlORWYZrdkgU4M1dukA/gQbhqI3/YSc+zDhpVPsQnuChcD6+qXg9VsFjInVqPeR1BHWoktJpakhOitGI8EIEyQ4EGrG5KO+jPtVpLQj2YdJMoluZbyOdfz/1t3oGxN1sAyCYwaoMhQmrVbPy2UZpe3Hyh5jE+kYr6Gp29YLnLFLL7hfw8nBPcvqk70C+vBhyjs5t2N6p456J47gXHtcVNoNVir0wnKguywuXdtN12wiGEIzUaUN8xXHCFrpYBNJsN22hmPAXlPpKgIyZzimu3s2EHPJK4hv77+i5KIK4xMT0lngRWINQwnOObY7qpUwMZgwaoJozBRa6BGB+qd3prWDq0qjKt0P3yCAVJmF0XEovWALvqSrqdIfUMXoNkvkI44Ae8JX9h2XoMXA0K1VOJmHXwmUtV0ggNcGofSncjpgbUFpD4tgzh/eQbKs40ZhDTasB442hJyoXV6oU3W+gz5JcP7f8VcSm5bD00UubiZ3vN1LWIjBWSm8m4nN2uvXmBGiMmvH1OXMSP5EeF/l4u2BnNwyUh+4PmwHcwM9sXlZ055XBDcshui7l0ExhAEmaX4Wwj0rUhsgXvc8WNWWPbHYCwq+dBk/iguHTV7V4h614lTm4t0XOSamEA7ikLAsqen6hZREyuiJjcx511eGyJhgIrrIh+BTuWo5EZkEXxIvdIJwnjBe6Z90jsNRPsviZaBtK1K7Pg9BnqpHy7okaFTJwJdnDmqpXbTgM2lOVz4jV3FsijgXlnNEds8o+EG/GWFts3rNdclLdPXu5ONZAeS7LOTw0EwDJCkcSsoBOmR3nSWwnmHX1jT6QnUgDeLwajg5R9rlUqffLpAGj+mq1R95DAq067cGfCUcyFkDxUodQoQK8VyjhIqYyl/DErQ0oIgQ4eR+3gT/Z5lc4tFwTdDE4g7QysnQbVEKwdqj+f2KQPRQw720O5D0IZPwjac+MibslVDeKpdMeAbt1k0hYRLQ8lqaCWj8IitEac+NdagxFjy+Exas0ewzq/vgaK53gbMOCVG8+RFTEXGUsH0AAty/UknKGptAB9PsgUuaiJ0u8ZZd70roam5obRhRN/rmEv0zztcO4RhuvtfqsYbrV2F5g7jFh3V5F8UUSG4yESz2C3Qgvg4qV/PqSOlfwzxIMNThRRYfUeortI3i4giJPdxUTErEFCToAEIYn3V+3epDy+xRUy+BVxh9HKdHmKRsO9YPR7d424CvNuB/dSRbRIZc7LkVrKO81AnNwhLHBv4s5eX94oNPBIol68zVMAofoSWbOpkDFkMcDntfSIhRnSca1z8dvcHRljtO9TolCWBASubqulha5bu+h0pzGH7jhQt2wjqwtbHSiWCibk3i/yUeQxmWWGs2GzyjEZBBR3yxciye68BxLdFfjjbMUplPYRsQyQ9YY3TlbP7ND0wvjpmaHBDbpON4EA6Y2IdolO3bJa/lL6iqsL17uYSjq4NU9wfyy0P/VMAqcEzVlqHoR5GEoVxpMMixu3SNjP0JBgMfVGfRS8B62WrZuNBTwOJrFB69QMuDVYrniWJsj1fRTcy4uWTOYlFJipB40r0IzJYbgNQ818mG23ws9kDHzOwogXgTZF9Em+HzXWrm6kdTUOOJ8wukweo/2ovkLCQnxf7roHmATmDteINamAlx82FKs+8Xc89Vm0o6AeRqV+Pk4ev5PuYZdqXqm8THZ9ldIUsVQOoAHWC2c5OdkkVwKZM4pyEgCJubV+bQkr5oCzHdR9KNXWyrGc7W9SHYLY6la9Bp11ziJZdkuT1FojIOJ6ibXaAG+MVUYxD9u7R3HOxUtenAZEsy3h3ATYfJ4Mcd1H4CMjJUMm6JWeOdnpNa8t0cyWipFETrX8uzyqiNh4osdD36ppqoq2vlb1dYhaflCA0gTksz86d9B0HbvvWEgC55rLHeguHde9MeTpGrWSBi2uLF1f1GM+7f3x6p8sodvr2s1QkLKNt7A1+LoMEu+kPpis1VrfTGgLrt/h6izxMT1yLjL6QL+frgIRZUue4eX6GDzN3c8b+Ti3loqiPwcscZH10ZMz2AdCH5DIWsqNm8JcTBnhgXudDO+IbODE8Nfdr8/Oc82fIPfm60LeO/b1UrRY0kWfZrvDCyyCUT8YqrguTorpHhvk28F/VA6yHil+YutzwrP+Y+sJDEX3DDIrgkcsskIONSlfYuSgTtzNUYmkOxBB4bi4fLlOj8RRIJrIKkIOKe6O9aSjlRuGayzHF4Y9ZQ9HL331bQ3OFOPugr8s3GliwDOxZgBjc095DqrSSqwe8WdlfIhANng0MgbO7bnVbb7zT8TXX3QWGmnEe9sjc1oLXAsJ5hZh0ZSF4ITQXfORbUZHrgzD9jLoD3Y5CtCRc2blh74AZ3pGseerW45bpPrrJZCdEO1P1YGbq2iG2Msj+tHo690ly1dhn6LUdmgINmC0awKdT8yjefQW6thIJ90gy5GEodOpXJUXgq1+latuSUJpF8cbcrD+fB7mtF3z+xm78jOdXSaRjNWWNo62I4xtmlzXwnTlnX8JrOXE9is/xNBeIGM45vPYtVot3C/iq3kwWBqt1+EEuVlRcUrNp3m7N07tycfO1CtFpcR6E25zGsK4QMwI1L01zVN9jf0+1FQBWjx3pV9rLjYsS2ROzOzeYGJ8d2ow2EtMZ0/i6rD59WYUPNqmXJT1kXuD7nrPi44Yg3Y0H0fY7xu9bzQhHdMNp+TjK/DGiKo0I+voopugjdd2izplt4rFJl0/P6yLLgjY4Tz65G2DjSNk751zfKYGDhotel4vMIjzHNaF3q+4Dh0FkTG4Vos/IrKVpFradfP5JO5A72tlwsqwY5RzltwkSAuJ/iIlT57UdGDWJ6Bq+50Ytc6+8aFOTTu0rnNeLDEhAOxxeCxkI4DALTzaqkjtdCBoiQhqcmBvHVL7x6ZDrby7wbS7Bwaf+zz3+GMTyuf2wdcFJcV+oO7SVoXdeOuXuHZqYQM7HU6Gkheg0c1zuXZvuwkHHFJ71wW5Gwqvl3g/ldHLcmCUce8LcTfcmwXpBAxvyKhWE+p5TmkYGVvR0k4z9eDt1j6bL4P1y6SeoKmL08Ml5E9xiga51BJ/EdSOGbcDjHRMyEuxjsG3DHhu5J2YXLkhfof4NU/h2WRUcaNEnWQqlEY+5k7XzM0DfN4/nklMrkV4gfXA4alMjL88TaD6waXWt8ucEF0nCMQwKrG0jhNWb/Vj4sUzLiqgdtegs6ia3f3bgRDzk3DsXfFqXSpmRbSSbtIRgGl9cdosk4e8PEm+MDy0eS+eN0EFJRF4ncIMZjJmblIsHOLaadU4m0NlibRbjzC75gJw750wBqKCEP7OKY7JltTBVlZl5yBfHLAecAjwjM7bHsccIuNqQ57XLConvb0JNX0mX2jDYQrS6clzkTX6ICSCw8jzZQaYRiNEo2cbFnnFKIqL4ZLq1uS+xkcEtanuES2A8VgjQOnLhzMPKWeqK6c4mhCFRzTjwHkRCoi9LkNChzjEKm4VOjQ8Qa355BTXyLNXs2u/dE/332L65YZ6vNIL3W0uxl4Z9poyy/nJ0doBhrG9M6/MCgOAJ2Ckuoa9OKJNXQZoz7tQU6BjN7GkOgn1Ic8qxZ9gW2wmYqIrHOpMY/HhyvkOeQrdvd3dmJk5jWVLwtlMszweubXCfYHgHvEpL12+We73GxFsRit0Eaca1DluT+mYw8jATDIxuw902hiuTQmVf98stN96MG7XcpzJUoUEcBIFY1aNap4IHjoPcAwNOPxOejxzAecJtRgZFPGp5H1aL7xkuNr2pLQrRTmXM2a0uYl2KFlRu43PhrlixSU5SpOIRQJkeg4FWcuTUDe8SB7yi5gIfTqvz64owIgB/xrsmjW8aB7YaHMFOaMjCvcc87zY1PMkSyOnO8IVOVeDzIbI6VpwBjWh30Rz4vNFzHevHJSxleU38o7CmaxDnjZ8lF7FUcNIWn2oKspbJMwXojiyd8QH7geK418q63bT8kRU2befOmLGPjMfehdI0X5SKFFhIAGYp1JDurk306Va+J1btqJhIcXdDCD42i4OPAI3/kLf3/TiStlu/yIFI6WPOLUQj0N7lGrALUfEG3m7281CjMsiSESoWuJkz6bT2UUFOnBF39EZFdgNQVCEU1o32nLrqxRzr+SzPOuNDai9Z1NqGa74FbFLo9NCEnIVvXDP7hw7bILPlerokyjpkhiFhJ9Cmh8cXJMYsN6AFHjJMm8X48eVxDkJgGEEBF5YI4vhxvLsn3Ns20vAlnR3fGmb4nAtGlUf5epy69a7pvtmPLc3/Ii20mLssXjoOwCjNjRfA37bnntsb+rOkqWJvXDWaVPQt1vyZs6Otwhi3dl6MkhbER7P+10MzLcAnELrI/jcJj9tD2/9hz+pjX0ukqoaRBHtDHKhBONFY0vc55oq25kD2KtyLmjPyuEearUPqiRNPVICE+Xpp/xV9qysACkdeb0IFZvm8xLZEkrBD+cHN/VGKYbb2Qn1yYlMy3cFgZfF2xzdmlfJGkglV1k8aUq7G1vTGGU7eg170XS9fwuhgnKXJAnTWyOc16SNwFHK5QC3lXCimcukalo/alRVIsuHpvDKbizL6I6X7GBUJ1F+3XBEjA1b40RH0869zAtHq7db3KcCaSWqnB1GWjHDC7EdewDBmUOgzsVMLQaxNyNtHYwasX2/H+nQeYaE4NiHRnhxqsbuG4NhUTxBUnERi9rXaETw+MfpAiLEyqDZ99IiaxhG5fV7KZx0S9MDSyP9TvMlDSOCRlfyYZi4o1gSJs0q0E/1VpQR4KgZ5olQKdsQ+jKWhrgXXFPbVrSn8GpeAfXEdZp8nWEE0T2do9oFDnexNC6zVC9MC6OJzyfd9itE3WwjiiK0xQ8EKXXJ+NwYeSTccxjBoplFs6ufLHA0ydq0e5dEAnM9QpEEUeQIRLJ3PyS7hwMAL3UO3feBk9fBZYJOT8Vfng1MBcSmFaDzeHwXog1WP32hSKJ++4K0th4MCLWjNd4ukJUxvSJZdfSCcaOsrM9B8IVSHV2C3UxIiNxeH5yhSzHykN0pph7r/XZFmzn8oT1E16Ajk+ZxCOLQJ9la2ow+H8vczw8kTlmKgwOZmYbapMZaGYTy6fRZLRyrhK2HGuNjAYiL4O0DFWxspxs3uXIOhIVrOe0DcG75Gz6v7O7MyuV6SU+0ZV3vtEasQin67KARsdvyD1476jTuSq/jvbnddTqEDuaCPQvp5vG2qF4SQ5d8tcsvaeEGB19+yAr3XNQsu+jYGWfscy3XQdQJSnWmMy+Pi6rTkyDIHRCexalWt2Hmp9fNurQI815Y7uBZk4f9wMmzJG5tbgNcVZwJG22/BJD8uQFR25Go7ydrIoMXd3KNsLNuIG8aMogZYeavQ7tLvKGeHYfajpQUJbp2TyYJUbFherfp/FJPYdhr4VHrQXpuI+Y2C3VQT8Yq5LkPg4aYUE7Lqdcewlzmz8IAR+hqBSBfGk+Rwc6jEwI8WlQ3mmsXLKAFIEKFvLjDMEHb5eKtcKp3bQKwchQ0rgJZSwOAuePIM7ly3aKvdIVEC2GhxMc7bER94TLtyuCKiviEDL3GtUCM2kfbjJicJhIwUKGMCmuX1UIgWHxcwSLNEgwAq8ohGfK1x2ouEFg7qjo+0Vb0/f8gyBIsAhGLWmc5r+x413grOh1lKQlh0PXWNoot+dMAUYQK+bdCyRFgCArhTsCiVgRtDbSaj44ySKRKgKb/LkYxBU6CCVpW5XbuxDNhKHL42/kRXvdCSlo6va5rdCgGvb871laUVXrr/6STlwf2y1Guv2EY+cuL+Jcd7PrJEfC3R9rYt5NZ7w+5fZ7n4qjPs1r4r4e2Po5xQanQb9LJFnhVv8kJtQM+T3jh33gZNcjK3zj643YW/DfPfP0Pz2FxX89hUf+qc1hfn7z04epi/C3v4Fj+92mZ/5ynZWgMEATz33ta5sAwOE7/f/MA3Er8Qfv4bzjaevj68D16QNVp5xKpy/9C9D8Iov/OZ1T/Ux5oQw+pzp/K/Zc+X/JP/UbEX/V8yVe/C07vz6lOt+ILqH+K4y8Y/hG/X7D7PW6/YOJHPHzBwveo/ALcH9H9xQR8byW+APVHNH+B/D/E4e88zPEbNf7ZT0j8V6r9y/NyH238+LjFe52+T+e1zX65C6L6tqyQqHnl+t449nuoQEfU57fh/wwNb6V/h5d/z5Fi/D19I7FfTMEvRucnP4Pxj13yB9hXKJV2rM23lEj8I7v2nbX5M2BIfg9D/CfOlfnZj2cc/gc/ngGTv/5uzlvZb359iJD+Lw==&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-4d72f3-1-3334b9-1-s-0">
            <stop offset="0%" style="stop-color: rgb(51, 52, 185); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(77, 114, 243); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-f78e04-1-d05c17-1-s-0">
            <stop offset="0%" style="stop-color: rgb(208, 92, 23); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(247, 142, 4); stop-opacity: 1;"/>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-60a337-1-277116-1-s-0">
            <stop offset="0%" style="stop-color: rgb(39, 113, 22); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(96, 163, 55); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="230" y="60" width="380" height="120" fill="none" stroke="#4d72f3" pointer-events="all"/>
        <rect x="730" y="20" width="180" height="180" fill="none" stroke="#d45b07" pointer-events="all"/>
        <rect x="770" y="60" width="120" height="120" fill="none" stroke="#d45b07" stroke-dasharray="3 3" pointer-events="all"/>
        <path d="M 450 120 L 523.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 528.88 120 L 521.88 123.5 L 523.63 120 L 521.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 490px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                実行
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="490" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    実行
                </text>
            </switch>
        </g>
        <path d="M 410 100 L 450 100 L 450 140 L 410 140 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 443.99 110.08 L 440.71 107.08 L 437.43 110.08 Z M 439.46 106.74 L 432.8 106.75 L 436.35 109.58 Z M 436.45 116.42 L 431.22 118.35 L 441.29 118.35 Z M 428.76 129.37 L 443.53 129.37 L 443.53 119.45 L 428.76 119.45 Z M 435.22 110.08 L 431.6 107.2 L 427.99 110.08 Z M 426.84 109.6 L 430.39 106.76 L 423.32 106.77 Z M 425.69 110.08 L 422.07 107.18 L 418.45 110.08 L 423.84 110.08 Z M 423.3 111.18 L 418.87 111.18 L 423.3 115.57 Z M 423.3 117.29 L 418.1 122.02 L 423.3 127.1 Z M 423.3 128.89 L 417.83 134.24 L 417.83 134.32 L 423.3 134.32 Z M 417.83 123.29 L 417.83 132.7 L 422.64 128 Z M 417.83 120.78 L 422.61 116.43 L 417.83 111.7 Z M 417.28 109.61 L 420.82 106.77 L 417.28 106.78 Z M 416.19 105.68 L 415.09 105.68 L 415.09 111.18 L 416.19 111.18 L 416.19 110.63 L 416.19 106.23 Z M 445.92 110.83 C 445.84 111.04 445.63 111.18 445.41 111.18 L 436.97 111.18 L 436.97 115.59 L 444.28 118.39 L 444.28 118.39 C 444.48 118.47 444.62 118.67 444.62 118.9 L 444.62 129.92 C 444.62 130.22 444.38 130.47 444.08 130.47 L 428.22 130.47 C 427.92 130.47 427.67 130.22 427.67 129.92 L 427.67 118.9 C 427.67 118.66 427.82 118.46 428.03 118.39 L 428.03 118.38 L 435.87 115.59 L 435.87 111.18 L 424.39 111.18 L 424.39 134.88 C 424.39 135.18 424.15 135.43 423.84 135.43 L 417.28 135.43 C 416.98 135.43 416.73 135.18 416.73 134.88 L 416.73 112.29 L 414.55 112.29 C 414.25 112.29 414 112.04 414 111.74 L 414 105.12 C 414 104.82 414.25 104.57 414.55 104.57 L 416.73 104.57 C 417.04 104.57 417.28 104.82 417.28 105.12 L 417.28 105.67 L 440.79 105.68 L 445.78 110.23 C 445.94 110.38 446 110.62 445.92 110.83 Z M 434.57 128.43 L 437.75 121.15 L 436.75 120.7 L 433.57 127.99 Z M 437.56 126 L 438.27 126.84 L 440.63 124.82 C 440.75 124.73 440.82 124.58 440.82 124.43 C 440.83 124.28 440.77 124.13 440.67 124.02 L 438.69 122 L 437.92 122.78 L 439.47 124.37 Z M 430.46 124.4 C 430.34 124.29 430.27 124.13 430.28 123.96 C 430.29 123.8 430.37 123.65 430.5 123.55 L 433.19 121.55 L 433.84 122.43 L 431.69 124.04 L 433.49 125.7 L 432.75 126.52 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 430px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodeBuild
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    CodeBu...
                </text>
            </switch>
        </g>
        <path d="M 730 20 L 760 20 L 760 50 L 730 50 Z" fill="url(#mx-gradient-f78e04-1-d05c17-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 754.89 38.26 L 751.52 36.24 L 751.52 31.43 C 751.52 31.28 751.44 31.15 751.31 31.07 L 746.47 28.25 L 746.47 24.18 L 754.89 29.15 Z M 755.52 28.55 L 746.27 23.08 C 746.14 23 745.98 23 745.84 23.07 C 745.71 23.15 745.63 23.29 745.63 23.44 L 745.63 28.49 C 745.63 28.64 745.71 28.78 745.84 28.85 L 750.68 31.68 L 750.68 36.48 C 750.68 36.63 750.76 36.77 750.88 36.84 L 755.09 39.37 C 755.16 39.41 755.23 39.43 755.31 39.43 C 755.38 39.43 755.45 39.41 755.51 39.37 C 755.65 39.3 755.73 39.16 755.73 39.01 L 755.73 28.91 C 755.73 28.76 755.65 28.62 755.52 28.55 Z M 744.98 46.1 L 735.11 40.86 L 735.11 29.15 L 743.53 24.18 L 743.53 28.26 L 739.09 31.08 C 738.97 31.16 738.9 31.29 738.9 31.43 L 738.9 38.59 C 738.9 38.74 738.99 38.89 739.13 38.96 L 744.79 41.9 C 744.91 41.97 745.05 41.97 745.17 41.9 L 750.66 39.07 L 754.04 41.09 Z M 755.1 40.75 L 750.9 38.22 C 750.77 38.15 750.62 38.14 750.49 38.21 L 744.98 41.06 L 739.74 38.33 L 739.74 31.66 L 744.17 28.84 C 744.3 28.77 744.37 28.63 744.37 28.49 L 744.37 23.44 C 744.37 23.29 744.29 23.15 744.16 23.07 C 744.03 23 743.86 23 743.73 23.08 L 734.48 28.55 C 734.35 28.62 734.27 28.76 734.27 28.91 L 734.27 41.11 C 734.27 41.27 734.36 41.41 734.49 41.48 L 744.78 46.95 C 744.84 46.98 744.91 47 744.98 47 C 745.05 47 745.12 46.98 745.18 46.95 L 755.09 41.48 C 755.22 41.41 755.3 41.27 755.31 41.12 C 755.31 40.97 755.23 40.83 755.1 40.75 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 35px; margin-left: 762px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ECS Cluster
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="762" y="39" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    ECS C...
                </text>
            </switch>
        </g>
        <rect x="810" y="100" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 849.09 107.16 L 810.91 107.16 C 810.41 107.16 810 107.57 810 108.07 L 810 131.93 C 810 132.43 810.41 132.84 810.91 132.84 L 849.09 132.84 C 849.59 132.84 850 132.43 850 131.93 L 850 108.07 C 850 107.57 849.59 107.16 849.09 107.16 Z M 811.82 131.02 L 811.82 108.98 L 848.18 108.98 L 848.18 131.02 Z M 814.77 128.75 L 816.59 128.75 L 816.59 111.25 L 814.77 111.25 Z M 819.55 128.75 L 821.36 128.75 L 821.36 111.25 L 819.55 111.25 Z M 824.32 128.75 L 826.14 128.75 L 826.14 111.25 L 824.32 111.25 Z M 829.09 128.75 L 830.91 128.75 L 830.91 111.25 L 829.09 111.25 Z M 833.86 128.75 L 835.68 128.75 L 835.68 111.25 L 833.86 111.25 Z M 838.64 128.75 L 840.45 128.75 L 840.45 111.25 L 838.64 111.25 Z M 843.41 128.75 L 845.23 128.75 L 845.23 111.25 L 843.41 111.25 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 830px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Container
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="830" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Contai...
                </text>
            </switch>
        </g>
        <rect x="770" y="60" width="30" height="30" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 785.8 84.69 L 794.49 84.69 L 794.49 83.33 L 785.8 83.33 Z M 785.8 77.93 L 794.49 77.93 L 794.49 76.57 L 785.8 76.57 Z M 785.8 71.18 L 794.49 71.18 L 794.49 69.81 L 785.8 69.81 Z M 782.14 84.78 L 782.14 83.24 L 783.67 83.24 L 783.67 84.78 Z M 781.46 86.14 L 784.36 86.14 C 784.73 86.14 785.04 85.83 785.04 85.46 L 785.04 82.56 C 785.04 82.18 784.73 81.88 784.36 81.88 L 781.46 81.88 C 781.08 81.88 780.78 82.18 780.78 82.56 L 780.78 85.46 C 780.78 85.83 781.08 86.14 781.46 86.14 Z M 782.14 78.02 L 782.14 76.49 L 783.67 76.49 L 783.67 78.02 Z M 781.46 79.38 L 784.36 79.38 C 784.73 79.38 785.04 79.08 785.04 78.7 L 785.04 75.8 C 785.04 75.43 784.73 75.12 784.36 75.12 L 781.46 75.12 C 781.08 75.12 780.78 75.43 780.78 75.8 L 780.78 78.7 C 780.78 79.08 781.08 79.38 781.46 79.38 Z M 782.14 71.26 L 782.14 69.73 L 783.67 69.73 L 783.67 71.26 Z M 781.46 72.63 L 784.36 72.63 C 784.73 72.63 785.04 72.32 785.04 71.94 L 785.04 69.05 C 785.04 68.67 784.73 68.37 784.36 68.37 L 781.46 68.37 C 781.08 68.37 780.78 68.67 780.78 69.05 L 780.78 71.94 C 780.78 72.32 781.08 72.63 781.46 72.63 Z M 779.73 88.64 L 779.73 66.83 L 795.74 66.83 L 795.74 88.64 Z M 778.37 66.15 L 778.37 85.74 L 776.83 85.74 L 776.83 63.94 L 792.85 63.94 L 792.85 65.47 L 779.05 65.47 C 778.67 65.47 778.37 65.77 778.37 66.15 Z M 775.47 63.26 L 775.47 83.17 L 774.26 83.17 L 774.26 61.36 L 790.27 61.36 L 790.27 62.57 L 776.15 62.57 C 775.77 62.57 775.47 62.88 775.47 63.26 Z M 796.42 65.47 L 794.21 65.47 L 794.21 63.26 C 794.21 62.88 793.9 62.57 793.53 62.57 L 791.63 62.57 L 791.63 60.68 C 791.63 60.31 791.33 60 790.95 60 L 773.58 60 C 773.2 60 772.9 60.31 772.9 60.68 L 772.9 83.85 C 772.9 84.23 773.2 84.53 773.58 84.53 L 775.47 84.53 L 775.47 86.42 C 775.47 86.8 775.77 87.1 776.15 87.1 L 778.37 87.1 L 778.37 89.32 C 778.37 89.7 778.67 90 779.05 90 L 796.42 90 C 796.8 90 797.1 89.7 797.1 89.32 L 797.1 66.15 C 797.1 65.77 796.8 65.47 796.42 65.47 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 75px; margin-left: 802px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Servicde
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="802" y="79" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    Servi...
                </text>
            </switch>
        </g>
        <path d="M 55.23 120 L 243.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 248.88 120 L 241.88 123.5 L 243.63 120 L 241.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 152px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                アップロード
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="152" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    アップロード
                </text>
            </switch>
        </g>
        <rect x="20" y="100" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 36.82 131.25 L 51.14 131.25 L 51.14 129.43 L 36.82 129.43 Z M 36.82 120.11 L 51.14 120.11 L 51.14 118.3 L 36.82 118.3 Z M 36.82 108.98 L 51.14 108.98 L 51.14 107.16 L 36.82 107.16 Z M 30.57 131.82 L 30.57 128.86 L 33.52 128.86 L 33.52 131.82 Z M 29.66 133.64 L 34.43 133.64 C 34.93 133.64 35.34 133.23 35.34 132.73 L 35.34 127.95 C 35.34 127.45 34.93 127.05 34.43 127.05 L 29.66 127.05 C 29.16 127.05 28.75 127.45 28.75 127.95 L 28.75 132.73 C 28.75 133.23 29.16 133.64 29.66 133.64 Z M 30.57 120.68 L 30.57 117.73 L 33.52 117.73 L 33.52 120.68 Z M 29.66 122.5 L 34.43 122.5 C 34.93 122.5 35.34 122.09 35.34 121.59 L 35.34 116.82 C 35.34 116.32 34.93 115.91 34.43 115.91 L 29.66 115.91 C 29.16 115.91 28.75 116.32 28.75 116.82 L 28.75 121.59 C 28.75 122.09 29.16 122.5 29.66 122.5 Z M 30.57 109.55 L 30.57 106.59 L 33.52 106.59 L 33.52 109.55 Z M 29.66 111.36 L 34.43 111.36 C 34.93 111.36 35.34 110.96 35.34 110.45 L 35.34 105.68 C 35.34 105.18 34.93 104.77 34.43 104.77 L 29.66 104.77 C 29.16 104.77 28.75 105.18 28.75 105.68 L 28.75 110.45 C 28.75 110.96 29.16 111.36 29.66 111.36 Z M 26.59 138.18 L 26.59 101.82 L 53.41 101.82 L 53.41 138.18 Z M 54.32 100 L 25.68 100 C 25.18 100 24.77 100.41 24.77 100.91 L 24.77 139.09 C 24.77 139.59 25.18 140 25.68 140 L 54.32 140 C 54.82 140 55.23 139.59 55.23 139.09 L 55.23 100.91 C 55.23 100.41 54.82 100 54.32 100 Z" fill="#d45b07" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 40px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                ecspresso conf
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="40" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    ecspre...
                </text>
            </switch>
        </g>
        <path d="M 570 120 L 763.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 768.88 120 L 761.88 123.5 L 763.63 120 L 761.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 670px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                ローリングデプロイ
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="670" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    ローリングデプロイ
                </text>
            </switch>
        </g>
        <image x="529.5" y="99.5" width="40" height="40" xlink:href="data:image/png;base64,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" preserveAspectRatio="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" text-anchor="middle" font-size="12px">
            <text x="549.5" y="157.5">
                ecspresso
            </text>
        </g>
        <path d="M 290 120 L 403.63 120" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 408.88 120 L 401.88 123.5 L 403.63 120 L 401.88 116.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 120px; margin-left: 350px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                設定ファイル
                                <br/>
                                読み取り
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="350" y="123" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    設定ファイル
読み取り
                </text>
            </switch>
        </g>
        <path d="M 250 100 L 290 100 L 290 140 L 250 140 Z" fill="url(#mx-gradient-60a337-1-277116-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 281.94 121.65 L 282.16 120.11 C 284.18 121.32 284.21 121.82 284.21 121.83 C 284.2 121.84 283.86 122.13 281.94 121.65 Z M 280.83 121.34 C 277.33 120.29 272.46 118.05 270.49 117.12 C 270.49 117.11 270.49 117.11 270.49 117.1 C 270.49 116.34 269.88 115.72 269.12 115.72 C 268.36 115.72 267.75 116.34 267.75 117.1 C 267.75 117.85 268.36 118.47 269.12 118.47 C 269.45 118.47 269.75 118.35 269.99 118.15 C 272.31 119.25 277.14 121.45 280.67 122.49 L 279.27 132.32 C 279.27 132.35 279.27 132.37 279.27 132.4 C 279.27 133.27 275.43 134.86 269.17 134.86 C 262.84 134.86 258.97 133.27 258.97 132.4 C 258.97 132.37 258.97 132.35 258.97 132.32 L 256.05 111.06 C 258.57 112.8 263.99 113.71 269.18 113.71 C 274.35 113.71 279.76 112.8 282.29 111.07 Z M 255.75 108.84 C 255.79 108.09 260.11 105.14 269.18 105.14 C 278.24 105.14 282.56 108.09 282.6 108.84 L 282.6 109.1 C 282.11 110.79 276.51 112.57 269.18 112.57 C 261.83 112.57 256.23 110.78 255.75 109.09 Z M 283.75 108.86 C 283.75 106.88 278.07 104 269.18 104 C 260.28 104 254.6 106.88 254.6 108.86 L 254.66 109.29 L 257.83 132.44 C 257.9 135.03 264.81 136 269.17 136 C 274.59 136 280.34 134.76 280.41 132.45 L 281.78 122.79 C 282.54 122.97 283.17 123.07 283.67 123.07 C 284.35 123.07 284.8 122.9 285.08 122.57 C 285.31 122.3 285.4 121.97 285.33 121.62 C 285.18 120.83 284.24 119.98 282.33 118.89 L 283.69 109.31 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 147px; margin-left: 270px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                S3 Bucket
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="270" y="159" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    S3 Buc...
                </text>
            </switch>
        </g>
        <path d="M 230 60 L 260 60 L 260 90 L 230 90 Z" fill="url(#mx-gradient-4d72f3-1-3334b9-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 254.62 63 L 235.38 63 C 234.07 63 233 64.07 233 65.38 L 233 71.09 C 233 72.4 234.07 73.47 235.38 73.47 L 236.05 73.47 L 236.05 86.56 C 236.05 86.8 236.25 87 236.49 87 L 253.07 87 C 253.31 87 253.51 86.8 253.51 86.56 L 253.51 73.47 L 254.62 73.47 C 255.93 73.47 257 72.4 257 71.09 L 257 65.38 C 257 64.07 255.93 63 254.62 63 Z M 236.93 86.13 L 236.93 73.47 L 252.64 73.47 L 252.64 86.13 Z M 254.62 72.6 L 235.38 72.6 C 234.55 72.6 233.87 71.92 233.87 71.09 L 233.87 70.85 L 239.11 70.85 L 239.11 69.98 L 233.87 69.98 L 233.87 65.38 C 233.87 64.55 234.55 63.87 235.38 63.87 L 254.62 63.87 C 255.45 63.87 256.13 64.55 256.13 65.38 L 256.13 69.98 L 243.91 69.98 L 243.91 70.85 L 256.13 70.85 L 256.13 71.09 C 256.13 71.92 255.45 72.6 254.62 72.6 Z M 238.46 79.6 C 238.46 79.48 238.51 79.36 238.61 79.27 L 241.37 76.83 L 241.95 77.48 L 239.56 79.6 L 241.94 81.65 L 241.37 82.31 L 238.61 79.93 C 238.51 79.85 238.46 79.73 238.46 79.6 Z M 247.21 81.67 L 249.61 79.56 L 247.21 77.49 L 247.79 76.83 L 250.56 79.23 C 250.66 79.31 250.71 79.43 250.71 79.56 C 250.71 79.69 250.66 79.81 250.56 79.89 L 247.79 82.32 Z M 243.33 84.02 L 242.52 83.69 L 245.83 75.6 L 246.64 75.94 Z M 240.42 70.85 L 242.6 70.85 L 242.6 69.98 L 240.42 69.98 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 75px; margin-left: 262px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                CodePipeline
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="262" y="79" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    CodeP...
                </text>
            </switch>
        </g>
        <path d="M 210 0 L 930 0 L 930 220 L 210 220 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 216.09 7.18 C 216.01 7.18 215.93 7.19 215.85 7.19 C 215.5 7.19 215.15 7.23 214.81 7.32 C 214.53 7.39 214.25 7.49 213.98 7.62 C 213.9 7.65 213.84 7.7 213.79 7.76 C 213.75 7.83 213.74 7.91 213.74 7.99 L 213.74 8.32 C 213.74 8.46 213.78 8.53 213.88 8.53 L 213.99 8.53 L 214.22 8.44 C 214.45 8.35 214.69 8.27 214.94 8.21 C 215.17 8.16 215.41 8.13 215.65 8.13 C 216.04 8.09 216.43 8.2 216.74 8.44 C 216.97 8.74 217.09 9.12 217.05 9.5 L 217.05 9.99 C 216.78 9.93 216.54 9.88 216.29 9.84 C 216.05 9.81 215.81 9.79 215.57 9.79 C 214.98 9.76 214.4 9.94 213.94 10.31 C 213.54 10.65 213.32 11.15 213.34 11.68 C 213.31 12.15 213.49 12.62 213.82 12.96 C 214.18 13.29 214.66 13.46 215.15 13.44 C 215.91 13.45 216.63 13.11 217.11 12.51 C 217.18 12.66 217.24 12.79 217.31 12.91 C 217.38 13.02 217.46 13.12 217.55 13.21 C 217.6 13.27 217.67 13.31 217.75 13.31 C 217.81 13.31 217.87 13.29 217.92 13.25 L 218.34 12.97 C 218.41 12.93 218.46 12.86 218.47 12.77 C 218.47 12.72 218.45 12.67 218.42 12.62 C 218.34 12.47 218.26 12.31 218.21 12.14 C 218.15 11.95 218.12 11.75 218.13 11.55 L 218.14 9.37 C 218.2 8.77 218 8.18 217.59 7.74 C 217.17 7.39 216.64 7.19 216.09 7.18 Z M 229.89 7.19 C 229.78 7.19 229.68 7.19 229.57 7.2 C 229.29 7.2 229 7.24 228.73 7.31 C 228.47 7.38 228.23 7.5 228.01 7.66 C 227.82 7.81 227.66 7.99 227.54 8.21 C 227.42 8.43 227.35 8.67 227.36 8.92 C 227.36 9.27 227.48 9.61 227.69 9.89 C 227.97 10.22 228.34 10.46 228.76 10.56 L 229.72 10.87 C 229.97 10.93 230.2 11.05 230.39 11.22 C 230.51 11.35 230.58 11.51 230.57 11.69 C 230.58 11.94 230.45 12.18 230.23 12.31 C 229.93 12.48 229.6 12.56 229.26 12.54 C 228.99 12.54 228.72 12.51 228.46 12.45 C 228.22 12.4 227.98 12.32 227.75 12.22 L 227.59 12.15 C 227.54 12.14 227.5 12.14 227.46 12.15 C 227.36 12.15 227.31 12.22 227.31 12.36 L 227.31 12.69 C 227.31 12.76 227.32 12.82 227.35 12.89 C 227.4 12.97 227.47 13.03 227.56 13.07 C 227.8 13.19 228.06 13.28 228.32 13.34 C 228.66 13.41 229 13.45 229.35 13.45 L 229.33 13.46 C 229.66 13.45 229.98 13.4 230.29 13.3 C 230.55 13.22 230.8 13.09 231.01 12.92 C 231.21 12.77 231.38 12.57 231.49 12.34 C 231.61 12.1 231.67 11.83 231.66 11.56 C 231.67 11.23 231.56 10.9 231.36 10.63 C 231.09 10.32 230.73 10.09 230.33 9.99 L 229.39 9.69 C 229.13 9.61 228.88 9.49 228.67 9.32 C 228.54 9.2 228.47 9.03 228.47 8.85 C 228.46 8.61 228.58 8.38 228.79 8.25 C 229.06 8.11 229.36 8.05 229.67 8.06 C 230.11 8.06 230.55 8.14 230.96 8.32 C 231.04 8.37 231.12 8.4 231.21 8.41 C 231.31 8.41 231.36 8.34 231.36 8.19 L 231.36 7.88 C 231.37 7.8 231.35 7.72 231.31 7.66 C 231.25 7.59 231.18 7.54 231.11 7.49 L 230.83 7.38 L 230.45 7.27 L 230.01 7.2 C 229.97 7.2 229.93 7.19 229.89 7.19 Z M 226.02 7.36 C 225.94 7.35 225.86 7.38 225.79 7.42 C 225.72 7.5 225.68 7.59 225.66 7.69 L 224.51 12.14 L 223.47 7.71 C 223.45 7.61 223.41 7.52 223.34 7.44 C 223.26 7.39 223.17 7.37 223.07 7.38 L 222.54 7.38 C 222.44 7.37 222.35 7.39 222.27 7.44 C 222.2 7.51 222.15 7.61 222.14 7.71 L 221.09 12.14 L 219.97 7.7 C 219.95 7.6 219.91 7.51 219.84 7.44 C 219.76 7.39 219.67 7.36 219.58 7.37 L 218.92 7.37 C 218.81 7.37 218.76 7.43 218.76 7.54 C 218.77 7.63 218.79 7.72 218.82 7.81 L 220.38 12.95 C 220.4 13.05 220.45 13.14 220.52 13.21 C 220.6 13.26 220.69 13.29 220.78 13.28 L 221.36 13.26 C 221.46 13.27 221.55 13.25 221.63 13.19 C 221.7 13.12 221.74 13.03 221.76 12.93 L 222.79 8.64 L 223.82 12.93 C 223.83 13.03 223.88 13.12 223.95 13.19 C 224.03 13.25 224.12 13.27 224.21 13.26 L 224.78 13.26 C 224.88 13.27 224.97 13.25 225.04 13.2 C 225.11 13.13 225.16 13.03 225.18 12.94 L 226.79 7.79 C 226.84 7.72 226.84 7.63 226.84 7.63 C 226.84 7.59 226.84 7.56 226.84 7.52 C 226.84 7.48 226.82 7.43 226.79 7.4 C 226.76 7.37 226.72 7.35 226.67 7.36 L 226.05 7.36 C 226.04 7.36 226.03 7.36 226.02 7.36 Z M 215.65 10.62 C 215.7 10.62 215.75 10.62 215.8 10.62 L 216.43 10.62 C 216.64 10.64 216.85 10.67 217.06 10.71 L 217.06 11.01 C 217.07 11.21 217.05 11.4 217 11.59 C 216.96 11.75 216.88 11.9 216.77 12.01 C 216.61 12.21 216.39 12.36 216.14 12.44 C 215.91 12.52 215.67 12.56 215.43 12.56 C 215.18 12.6 214.93 12.53 214.73 12.37 C 214.55 12.18 214.46 11.92 214.49 11.66 C 214.47 11.36 214.59 11.08 214.81 10.89 C 215.06 10.72 215.35 10.62 215.65 10.62 Z M 231.04 14.72 C 230.34 14.73 229.51 14.89 228.88 15.33 C 228.69 15.46 228.72 15.63 228.94 15.63 C 229.64 15.54 231.21 15.35 231.5 15.71 C 231.78 16.06 231.19 17.54 230.94 18.21 C 230.86 18.41 231.03 18.49 231.21 18.34 C 232.39 17.36 232.72 15.3 232.46 15 C 232.32 14.85 231.74 14.71 231.04 14.72 Z M 212.65 15.1 C 212.5 15.12 212.42 15.3 212.58 15.44 C 215.29 17.89 218.82 19.23 222.48 19.21 C 225.37 19.22 228.2 18.36 230.59 16.74 C 230.95 16.47 230.63 16.07 230.26 16.23 C 227.87 17.24 225.3 17.76 222.71 17.77 C 219.23 17.78 215.82 16.87 212.81 15.14 C 212.75 15.11 212.69 15.1 212.65 15.1 Z M 210 0 L 235 0 L 235 25 L 210 25 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 688px; height: 1px; padding-top: 7px; margin-left: 242px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                AWS Cloud
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="242" y="19" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    AWS Cloud
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>