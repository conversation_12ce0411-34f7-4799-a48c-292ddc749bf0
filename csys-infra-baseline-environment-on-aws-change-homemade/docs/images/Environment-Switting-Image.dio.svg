<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="971px" height="431px" viewBox="-0.5 -0.5 971 431" content="&lt;mxfile&gt;&lt;diagram id=&quot;Aiv_-87A5-uPUERjsNWz&quot; name=&quot;240822&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0">
            <stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/>
            <stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/>
        </linearGradient>
    </defs>
    <g>
        <rect x="0" y="0" width="970" height="430" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="10" y="10" width="470" height="410" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <rect x="598" y="40" width="330" height="160" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <path d="M 568 220 L 948 220 L 948 420 L 568 420 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 574.09 227.18 C 574.01 227.18 573.93 227.19 573.85 227.19 C 573.5 227.19 573.15 227.23 572.81 227.32 C 572.53 227.39 572.25 227.49 571.98 227.62 C 571.9 227.65 571.84 227.7 571.79 227.76 C 571.75 227.83 571.74 227.91 571.74 227.99 L 571.74 228.32 C 571.74 228.46 571.78 228.53 571.88 228.53 L 571.99 228.53 L 572.22 228.44 C 572.45 228.35 572.69 228.27 572.94 228.21 C 573.17 228.16 573.41 228.13 573.65 228.13 C 574.04 228.09 574.43 228.2 574.74 228.44 C 574.97 228.74 575.09 229.12 575.05 229.5 L 575.05 229.99 C 574.78 229.93 574.54 229.88 574.29 229.84 C 574.05 229.81 573.81 229.79 573.57 229.79 C 572.98 229.76 572.4 229.94 571.94 230.31 C 571.54 230.65 571.32 231.15 571.34 231.68 C 571.31 232.15 571.49 232.62 571.82 232.96 C 572.18 233.29 572.66 233.46 573.15 233.44 C 573.91 233.45 574.63 233.11 575.11 232.51 C 575.18 232.66 575.24 232.79 575.31 232.91 C 575.38 233.02 575.46 233.12 575.55 233.21 C 575.6 233.27 575.67 233.31 575.75 233.31 C 575.81 233.31 575.87 233.29 575.92 233.25 L 576.34 232.97 C 576.41 232.93 576.46 232.86 576.47 232.77 C 576.47 232.72 576.45 232.67 576.42 232.62 C 576.34 232.47 576.26 232.31 576.21 232.14 C 576.15 231.95 576.12 231.75 576.13 231.55 L 576.14 229.37 C 576.2 228.77 576 228.18 575.59 227.74 C 575.17 227.39 574.64 227.19 574.09 227.18 Z M 587.89 227.19 C 587.78 227.19 587.68 227.19 587.57 227.2 C 587.29 227.2 587 227.24 586.73 227.31 C 586.47 227.38 586.23 227.5 586.01 227.66 C 585.82 227.81 585.66 227.99 585.54 228.21 C 585.42 228.43 585.35 228.67 585.36 228.92 C 585.36 229.27 585.48 229.61 585.69 229.89 C 585.97 230.22 586.34 230.46 586.76 230.56 L 587.72 230.87 C 587.97 230.93 588.2 231.05 588.39 231.22 C 588.51 231.35 588.58 231.51 588.57 231.69 C 588.58 231.94 588.45 232.18 588.23 232.31 C 587.93 232.48 587.6 232.56 587.26 232.54 C 586.99 232.54 586.72 232.51 586.46 232.45 C 586.22 232.4 585.98 232.32 585.75 232.22 L 585.59 232.15 C 585.54 232.14 585.5 232.14 585.46 232.15 C 585.36 232.15 585.31 232.22 585.31 232.36 L 585.31 232.69 C 585.31 232.76 585.32 232.82 585.35 232.88 C 585.4 232.97 585.47 233.03 585.56 233.07 C 585.8 233.19 586.06 233.28 586.32 233.34 C 586.66 233.41 587 233.45 587.35 233.45 L 587.33 233.46 C 587.66 233.45 587.98 233.4 588.29 233.3 C 588.55 233.22 588.8 233.09 589.01 232.92 C 589.21 232.77 589.38 232.57 589.49 232.34 C 589.61 232.1 589.67 231.83 589.66 231.56 C 589.67 231.23 589.56 230.9 589.36 230.63 C 589.09 230.32 588.73 230.09 588.33 229.99 L 587.39 229.69 C 587.13 229.61 586.88 229.49 586.67 229.32 C 586.54 229.2 586.47 229.03 586.47 228.85 C 586.46 228.61 586.58 228.38 586.79 228.25 C 587.06 228.11 587.36 228.05 587.67 228.06 C 588.11 228.06 588.55 228.14 588.96 228.32 C 589.04 228.37 589.12 228.4 589.21 228.41 C 589.31 228.41 589.36 228.34 589.36 228.19 L 589.36 227.88 C 589.37 227.8 589.35 227.72 589.31 227.66 C 589.25 227.59 589.18 227.54 589.11 227.49 L 588.83 227.38 L 588.45 227.27 L 588.01 227.2 C 587.97 227.2 587.93 227.19 587.89 227.19 Z M 584.02 227.35 C 583.94 227.35 583.86 227.38 583.79 227.42 C 583.72 227.5 583.68 227.59 583.66 227.69 L 582.51 232.14 L 581.47 227.71 C 581.45 227.61 581.41 227.52 581.34 227.44 C 581.26 227.39 581.17 227.37 581.07 227.38 L 580.54 227.38 C 580.44 227.37 580.35 227.39 580.27 227.44 C 580.2 227.51 580.15 227.61 580.14 227.71 L 579.09 232.14 L 577.97 227.7 C 577.95 227.6 577.91 227.51 577.84 227.44 C 577.76 227.39 577.67 227.36 577.58 227.37 L 576.92 227.37 C 576.81 227.37 576.76 227.43 576.76 227.54 C 576.77 227.63 576.79 227.72 576.82 227.81 L 578.38 232.95 C 578.4 233.05 578.45 233.14 578.52 233.21 C 578.6 233.26 578.69 233.29 578.78 233.28 L 579.36 233.26 C 579.46 233.27 579.55 233.25 579.63 233.19 C 579.7 233.12 579.74 233.03 579.76 232.93 L 580.79 228.64 L 581.82 232.93 C 581.83 233.03 581.88 233.12 581.95 233.19 C 582.03 233.25 582.12 233.27 582.21 233.26 L 582.78 233.26 C 582.88 233.27 582.97 233.25 583.04 233.2 C 583.11 233.13 583.16 233.03 583.18 232.94 L 584.79 227.79 C 584.84 227.72 584.84 227.63 584.84 227.63 C 584.84 227.59 584.84 227.56 584.84 227.52 C 584.84 227.48 584.82 227.43 584.79 227.4 C 584.76 227.37 584.72 227.35 584.67 227.36 L 584.05 227.36 C 584.04 227.36 584.03 227.36 584.02 227.35 Z M 573.65 230.62 C 573.7 230.62 573.75 230.62 573.8 230.62 L 574.43 230.62 C 574.64 230.64 574.85 230.67 575.07 230.71 L 575.07 231.01 C 575.07 231.21 575.05 231.4 575 231.59 C 574.96 231.75 574.88 231.9 574.77 232.01 C 574.61 232.21 574.39 232.36 574.14 232.44 C 573.91 232.52 573.67 232.56 573.43 232.56 C 573.18 232.6 572.93 232.53 572.73 232.37 C 572.55 232.18 572.46 231.92 572.49 231.66 C 572.47 231.36 572.59 231.08 572.82 230.89 C 573.06 230.72 573.35 230.62 573.65 230.62 Z M 589.04 234.72 C 588.34 234.73 587.51 234.89 586.88 235.33 C 586.69 235.47 586.72 235.63 586.94 235.63 C 587.64 235.54 589.21 235.35 589.5 235.71 C 589.78 236.06 589.19 237.54 588.94 238.21 C 588.86 238.41 589.03 238.49 589.21 238.34 C 590.39 237.36 590.72 235.3 590.46 235 C 590.32 234.85 589.74 234.71 589.04 234.72 Z M 570.65 235.1 C 570.5 235.12 570.42 235.3 570.58 235.44 C 573.29 237.89 576.82 239.23 580.48 239.21 C 583.37 239.22 586.2 238.36 588.59 236.74 C 588.95 236.47 588.63 236.07 588.26 236.23 C 585.87 237.24 583.3 237.76 580.71 237.77 C 577.23 237.78 573.82 236.87 570.81 235.14 C 570.75 235.11 570.7 235.1 570.65 235.1 Z M 568 220 L 593 220 L 593 245 L 568 245 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 227px; margin-left: 600px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font color="#0000ff">
                                    prodアカウント
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="239" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    prodアカウント
                </text>
            </switch>
        </g>
        <rect x="598" y="247" width="330" height="160" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/>
        <path d="M 568 10 L 948 10 L 948 210 L 568 210 Z" fill="none" stroke="#232f3e" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 574.09 17.18 C 574.01 17.18 573.93 17.19 573.85 17.19 C 573.5 17.19 573.15 17.23 572.81 17.32 C 572.53 17.39 572.25 17.49 571.98 17.62 C 571.9 17.65 571.84 17.7 571.79 17.76 C 571.75 17.83 571.74 17.91 571.74 17.99 L 571.74 18.32 C 571.74 18.46 571.78 18.53 571.88 18.53 L 571.99 18.53 L 572.22 18.44 C 572.45 18.35 572.69 18.27 572.94 18.21 C 573.17 18.16 573.41 18.13 573.65 18.13 C 574.04 18.09 574.43 18.2 574.74 18.44 C 574.97 18.74 575.09 19.12 575.05 19.5 L 575.05 19.99 C 574.78 19.93 574.54 19.88 574.29 19.84 C 574.05 19.81 573.81 19.79 573.57 19.79 C 572.98 19.76 572.4 19.94 571.94 20.31 C 571.54 20.65 571.32 21.15 571.34 21.68 C 571.31 22.15 571.49 22.62 571.82 22.96 C 572.18 23.29 572.66 23.46 573.15 23.44 C 573.91 23.45 574.63 23.11 575.11 22.51 C 575.18 22.66 575.24 22.79 575.31 22.91 C 575.38 23.02 575.46 23.12 575.55 23.21 C 575.6 23.27 575.67 23.31 575.75 23.31 C 575.81 23.31 575.87 23.29 575.92 23.25 L 576.34 22.97 C 576.41 22.93 576.46 22.86 576.47 22.77 C 576.47 22.72 576.45 22.67 576.42 22.62 C 576.34 22.47 576.26 22.31 576.21 22.14 C 576.15 21.95 576.12 21.75 576.13 21.55 L 576.14 19.37 C 576.2 18.77 576 18.18 575.59 17.74 C 575.17 17.39 574.64 17.19 574.09 17.18 Z M 587.89 17.19 C 587.78 17.19 587.68 17.19 587.57 17.2 C 587.29 17.2 587 17.24 586.73 17.31 C 586.47 17.38 586.23 17.5 586.01 17.66 C 585.82 17.81 585.66 17.99 585.54 18.21 C 585.42 18.43 585.35 18.67 585.36 18.92 C 585.36 19.27 585.48 19.61 585.69 19.89 C 585.97 20.22 586.34 20.46 586.76 20.56 L 587.72 20.87 C 587.97 20.93 588.2 21.05 588.39 21.22 C 588.51 21.35 588.58 21.51 588.57 21.69 C 588.58 21.94 588.45 22.18 588.23 22.31 C 587.93 22.48 587.6 22.56 587.26 22.54 C 586.99 22.54 586.72 22.51 586.46 22.45 C 586.22 22.4 585.98 22.32 585.75 22.22 L 585.59 22.15 C 585.54 22.14 585.5 22.14 585.46 22.15 C 585.36 22.15 585.31 22.22 585.31 22.36 L 585.31 22.69 C 585.31 22.76 585.32 22.82 585.35 22.89 C 585.4 22.97 585.47 23.03 585.56 23.07 C 585.8 23.19 586.06 23.28 586.32 23.34 C 586.66 23.41 587 23.45 587.35 23.45 L 587.33 23.46 C 587.66 23.45 587.98 23.4 588.29 23.3 C 588.55 23.22 588.8 23.09 589.01 22.92 C 589.21 22.77 589.38 22.57 589.49 22.34 C 589.61 22.1 589.67 21.83 589.66 21.56 C 589.67 21.23 589.56 20.9 589.36 20.63 C 589.09 20.32 588.73 20.09 588.33 19.99 L 587.39 19.69 C 587.13 19.61 586.88 19.49 586.67 19.32 C 586.54 19.2 586.47 19.03 586.47 18.85 C 586.46 18.61 586.58 18.38 586.79 18.25 C 587.06 18.11 587.36 18.05 587.67 18.06 C 588.11 18.06 588.55 18.14 588.96 18.32 C 589.04 18.37 589.12 18.4 589.21 18.41 C 589.31 18.41 589.36 18.34 589.36 18.19 L 589.36 17.88 C 589.37 17.8 589.35 17.72 589.31 17.66 C 589.25 17.59 589.18 17.54 589.11 17.49 L 588.83 17.38 L 588.45 17.27 L 588.01 17.2 C 587.97 17.2 587.93 17.19 587.89 17.19 Z M 584.02 17.36 C 583.94 17.35 583.86 17.38 583.79 17.42 C 583.72 17.5 583.68 17.59 583.66 17.69 L 582.51 22.14 L 581.47 17.71 C 581.45 17.61 581.41 17.52 581.34 17.44 C 581.26 17.39 581.17 17.37 581.07 17.38 L 580.54 17.38 C 580.44 17.37 580.35 17.39 580.27 17.44 C 580.2 17.51 580.15 17.61 580.14 17.71 L 579.09 22.14 L 577.97 17.7 C 577.95 17.6 577.91 17.51 577.84 17.44 C 577.76 17.39 577.67 17.36 577.58 17.37 L 576.92 17.37 C 576.81 17.37 576.76 17.43 576.76 17.54 C 576.77 17.63 576.79 17.72 576.82 17.81 L 578.38 22.95 C 578.4 23.05 578.45 23.14 578.52 23.21 C 578.6 23.26 578.69 23.29 578.78 23.28 L 579.36 23.26 C 579.46 23.27 579.55 23.25 579.63 23.19 C 579.7 23.12 579.74 23.03 579.76 22.93 L 580.79 18.64 L 581.82 22.93 C 581.83 23.03 581.88 23.12 581.95 23.19 C 582.03 23.25 582.12 23.27 582.21 23.26 L 582.78 23.26 C 582.88 23.27 582.97 23.25 583.04 23.2 C 583.11 23.13 583.16 23.03 583.18 22.94 L 584.79 17.79 C 584.84 17.72 584.84 17.63 584.84 17.63 C 584.84 17.59 584.84 17.56 584.84 17.52 C 584.84 17.48 584.82 17.43 584.79 17.4 C 584.76 17.37 584.72 17.35 584.67 17.36 L 584.05 17.36 C 584.04 17.36 584.03 17.36 584.02 17.36 Z M 573.65 20.62 C 573.7 20.62 573.75 20.62 573.8 20.62 L 574.43 20.62 C 574.64 20.64 574.85 20.67 575.07 20.71 L 575.07 21.01 C 575.07 21.21 575.05 21.4 575 21.59 C 574.96 21.75 574.88 21.9 574.77 22.01 C 574.61 22.21 574.39 22.36 574.14 22.44 C 573.91 22.52 573.67 22.56 573.43 22.56 C 573.18 22.6 572.93 22.53 572.73 22.37 C 572.55 22.18 572.46 21.92 572.49 21.66 C 572.47 21.36 572.59 21.08 572.82 20.89 C 573.06 20.72 573.35 20.62 573.65 20.62 Z M 589.04 24.72 C 588.34 24.73 587.51 24.89 586.88 25.33 C 586.69 25.46 586.72 25.63 586.94 25.63 C 587.64 25.54 589.21 25.35 589.5 25.71 C 589.78 26.06 589.19 27.54 588.94 28.21 C 588.86 28.41 589.03 28.49 589.21 28.34 C 590.39 27.36 590.72 25.3 590.46 25 C 590.32 24.85 589.74 24.71 589.04 24.72 Z M 570.65 25.1 C 570.5 25.12 570.42 25.3 570.58 25.44 C 573.29 27.89 576.82 29.23 580.48 29.21 C 583.37 29.22 586.2 28.36 588.59 26.74 C 588.95 26.47 588.63 26.07 588.26 26.23 C 585.87 27.24 583.3 27.76 580.71 27.77 C 577.23 27.78 573.82 26.87 570.81 25.14 C 570.75 25.11 570.7 25.1 570.65 25.1 Z M 568 10 L 593 10 L 593 35 L 568 35 Z" fill="#232f3e" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 348px; height: 1px; padding-top: 17px; margin-left: 600px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <font color="#ff3333">
                                    devアカウント
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="600" y="29" fill="#232F3E" font-family="Helvetica" font-size="12px">
                    devアカウント
                </text>
            </switch>
        </g>
        <rect x="33" y="88" width="189" height="270" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 187px; height: 1px; padding-top: 85px; margin-left: 34px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                環境ファイル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="128" y="85" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    環境ファイル
                </text>
            </switch>
        </g>
        <rect x="53" y="258" width="149" height="80" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 137px; height: 1px; padding-top: 298px; margin-left: 65px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(0, 0, 0);">
                                    <br/>
                                    vpcCidr: '10.
                                </span>
                                <font color="#0000ff">
                                    200
                                </font>
                                <span style="color: rgb(0, 0, 0);">
                                    .0.0/16',
                                    <br/>
                                    natGateway:
                                </span>
                                <font color="#0000ff">
                                    2
                                </font>
                                <span style="color: rgb(0, 0, 0);">
                                    ,
                                    <br/>
                                    <br/>
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="65" y="302" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    vpcCidr: '10.200.0.0/16...
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 43px; height: 1px; padding-top: 248px; margin-left: 106px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font color="#0000ff">
                                    prod.ts
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="128" y="252" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    prod.ts
                </text>
            </switch>
        </g>
        <image x="9.5" y="9.5" width="25" height="25" xlink:href="data:image/png;base64,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" preserveAspectRatio="none" pointer-events="none"/>
        <g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" font-size="12px">
            <rect fill="rgb(255, 255, 255)" stroke="none" x="37" y="16" width="27" height="15" stroke-width="0"/>
            <text x="36.5" y="27">
                CDK
            </text>
        </g>
        <rect x="53" y="118" width="149" height="80" fill="#f8cecc" stroke="#b85450" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 158px; margin-left: 65px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="">
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        vpcCidr: '10.
                                    </span>
                                    <span style="background-color: initial;">
                                        100
                                    </span>
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        .0.0/16',
                                    </span>
                                    <br/>
                                </div>
                                <div style="">
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        natGateway:
                                    </span>
                                    <span style="background-color: initial;">
                                        1
                                    </span>
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        ,
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="65" y="162" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    vpcCidr: '10.100.0.0/16...
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 43px; height: 1px; padding-top: 108px; margin-left: 106px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                <font color="#ff3333">
                                    dev.ts
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="128" y="112" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    dev.ts
                </text>
            </switch>
        </g>
        <path d="M 708 50 L 914 50 L 914 190 L 708 190 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 708 50 L 733 50 L 733 75 L 708 75 Z M 720.52 53.21 C 719.4 53.21 718.31 53.63 717.49 54.39 C 716.67 55.11 716.2 56.15 716.2 57.24 L 716.2 59.78 L 713.89 59.78 C 713.8 59.78 713.7 59.82 713.64 59.89 C 713.57 59.95 713.54 60.04 713.54 60.13 L 713.54 71.43 C 713.54 71.63 713.7 71.79 713.89 71.79 L 727.11 71.79 C 727.3 71.79 727.46 71.63 727.46 71.43 L 727.46 60.15 C 727.47 60.06 727.43 59.97 727.36 59.9 C 727.3 59.83 727.21 59.79 727.11 59.79 L 724.81 59.79 L 724.81 57.29 C 724.8 56.21 724.35 55.18 723.56 54.44 C 722.74 53.65 721.65 53.22 720.52 53.21 Z M 720.51 53.93 C 721.46 53.92 722.37 54.28 723.06 54.93 C 723.72 55.54 724.1 56.4 724.1 57.29 L 724.1 59.79 L 716.88 59.79 L 716.89 57.26 C 716.9 56.36 717.28 55.51 717.95 54.91 C 718.65 54.27 719.57 53.92 720.51 53.93 Z M 714.24 60.5 L 726.76 60.5 L 726.75 71.07 L 714.24 71.07 Z M 720.51 62.74 C 719.48 62.73 718.61 63.51 718.51 64.53 C 718.42 65.56 719.13 66.48 720.14 66.66 L 720.14 69.44 L 720.86 69.44 L 720.86 66.66 C 721.79 66.49 722.47 65.67 722.48 64.72 C 722.48 63.63 721.6 62.75 720.51 62.74 Z M 720.39 63.45 C 720.43 63.45 720.47 63.45 720.51 63.46 C 720.84 63.46 721.16 63.59 721.4 63.83 C 721.64 64.07 721.77 64.39 721.76 64.72 C 721.77 65.06 721.64 65.38 721.4 65.61 C 721.16 65.85 720.84 65.98 720.51 65.98 C 720.04 66.02 719.6 65.8 719.34 65.42 C 719.08 65.03 719.06 64.53 719.28 64.12 C 719.5 63.71 719.93 63.46 720.39 63.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 174px; height: 1px; padding-top: 57px; margin-left: 740px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="740" y="69" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="786" y="98" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 796.57 128.84 L 796.57 126.26 L 798.18 127.55 Z M 796.23 123.65 C 795.95 123.43 795.58 123.39 795.26 123.54 C 794.95 123.7 794.75 124.01 794.75 124.36 L 794.75 130.73 C 794.75 131.08 794.95 131.39 795.26 131.55 C 795.39 131.61 795.53 131.64 795.66 131.64 C 795.86 131.64 796.06 131.57 796.23 131.44 L 800.2 128.26 C 800.42 128.08 800.55 127.82 800.55 127.55 C 800.55 127.27 800.42 127.01 800.2 126.84 Z M 818.05 120.58 L 818.05 117.01 L 819.83 118.79 Z M 821.76 118.15 L 817.78 114.18 C 817.52 113.92 817.13 113.84 816.79 113.98 C 816.45 114.12 816.23 114.45 816.23 114.82 L 816.23 117.89 L 809.29 117.89 L 809.29 109.25 C 809.29 108.75 808.89 108.34 808.39 108.34 L 801.39 108.34 L 801.39 110.16 L 807.48 110.16 L 807.48 117.89 L 801.23 117.89 L 801.23 119.7 L 807.48 119.7 L 807.48 127.43 L 801.39 127.43 L 801.39 129.25 L 808.39 129.25 C 808.89 129.25 809.29 128.84 809.29 128.34 L 809.29 119.7 L 816.23 119.7 L 816.23 122.77 C 816.23 123.14 816.45 123.47 816.79 123.61 C 816.9 123.66 817.02 123.68 817.14 123.68 C 817.37 123.68 817.61 123.59 817.78 123.42 L 821.76 119.44 C 822.11 119.08 822.11 118.51 821.76 118.15 Z M 796.57 120.09 L 796.57 117.51 L 798.18 118.79 Z M 796.23 114.9 C 795.95 114.68 795.58 114.64 795.26 114.79 C 794.95 114.95 794.75 115.26 794.75 115.61 L 794.75 121.98 C 794.75 122.33 794.95 122.64 795.26 122.8 C 795.39 122.86 795.53 122.89 795.66 122.89 C 795.86 122.89 796.06 122.82 796.23 122.69 L 800.2 119.5 C 800.42 119.33 800.55 119.07 800.55 118.79 C 800.55 118.52 800.42 118.26 800.2 118.08 Z M 796.57 110.54 L 796.57 107.96 L 798.18 109.25 Z M 796.23 105.36 C 795.95 105.14 795.58 105.1 795.26 105.25 C 794.95 105.4 794.75 105.72 794.75 106.07 L 794.75 112.43 C 794.75 112.78 794.95 113.1 795.26 113.25 C 795.39 113.31 795.53 113.34 795.66 113.34 C 795.86 113.34 796.06 113.27 796.23 113.14 L 800.2 109.96 C 800.42 109.79 800.55 109.53 800.55 109.25 C 800.55 108.97 800.42 108.71 800.2 108.54 Z M 806 136.18 C 795.97 136.18 787.82 128.03 787.82 118 C 787.82 107.97 795.97 99.82 806 99.82 C 816.03 99.82 824.18 107.97 824.18 118 C 824.18 128.03 816.03 136.18 806 136.18 Z M 806 98 C 794.97 98 786 106.97 786 118 C 786 129.03 794.97 138 806 138 C 817.03 138 826 129.03 826 118 C 826 106.97 817.03 98 806 98 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 145px; margin-left: 806px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="806" y="157" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Ga...
                </text>
            </switch>
        </g>
        <path d="M 708 260 L 914 260 L 914 400 L 708 400 Z" fill="#e9f3e6" stroke="none" pointer-events="none"/>
        <path d="M 708 260 L 733 260 L 733 285 L 708 285 Z M 720.52 263.21 C 719.4 263.21 718.31 263.63 717.49 264.39 C 716.67 265.11 716.2 266.15 716.2 267.24 L 716.2 269.78 L 713.89 269.78 C 713.8 269.78 713.7 269.82 713.64 269.89 C 713.57 269.95 713.54 270.04 713.54 270.13 L 713.54 281.43 C 713.54 281.63 713.7 281.79 713.89 281.79 L 727.11 281.79 C 727.3 281.79 727.46 281.63 727.46 281.43 L 727.46 270.15 C 727.47 270.06 727.43 269.97 727.36 269.9 C 727.3 269.83 727.21 269.79 727.11 269.79 L 724.81 269.79 L 724.81 267.29 C 724.8 266.21 724.35 265.18 723.56 264.44 C 722.74 263.65 721.65 263.22 720.52 263.21 Z M 720.51 263.93 C 721.46 263.92 722.37 264.28 723.06 264.93 C 723.72 265.54 724.1 266.4 724.1 267.29 L 724.1 269.79 L 716.88 269.79 L 716.89 267.26 C 716.9 266.36 717.28 265.51 717.95 264.91 C 718.65 264.27 719.57 263.92 720.51 263.93 Z M 714.24 270.5 L 726.76 270.5 L 726.75 281.07 L 714.24 281.07 Z M 720.51 272.74 C 719.48 272.73 718.61 273.51 718.51 274.53 C 718.42 275.56 719.13 276.48 720.14 276.66 L 720.14 279.44 L 720.86 279.44 L 720.86 276.66 C 721.79 276.49 722.47 275.67 722.48 274.72 C 722.48 273.63 721.6 272.75 720.51 272.74 Z M 720.39 273.45 C 720.43 273.45 720.47 273.45 720.51 273.46 C 720.84 273.46 721.16 273.59 721.4 273.83 C 721.64 274.07 721.77 274.39 721.76 274.72 C 721.77 275.06 721.64 275.38 721.4 275.61 C 721.16 275.85 720.84 275.98 720.51 275.98 C 720.04 276.02 719.6 275.8 719.34 275.42 C 719.08 275.03 719.06 274.53 719.28 274.12 C 719.5 273.71 719.93 273.46 720.39 273.45 Z" fill="#248814" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 174px; height: 1px; padding-top: 267px; margin-left: 740px;">
                        <div data-drawio-colors="color: #248814; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(36, 136, 20); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Public subnet
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="740" y="279" fill="#248814" font-family="Helvetica" font-size="12px">
                    Public subnet
                </text>
            </switch>
        </g>
        <rect x="742" y="308" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 752.57 338.84 L 752.57 336.26 L 754.18 337.55 Z M 752.23 333.65 C 751.95 333.43 751.58 333.39 751.26 333.54 C 750.95 333.7 750.75 334.01 750.75 334.36 L 750.75 340.73 C 750.75 341.08 750.95 341.39 751.26 341.55 C 751.39 341.61 751.53 341.64 751.66 341.64 C 751.86 341.64 752.06 341.57 752.23 341.44 L 756.2 338.26 C 756.42 338.08 756.55 337.82 756.55 337.55 C 756.55 337.27 756.42 337.01 756.2 336.84 Z M 774.05 330.58 L 774.05 327.01 L 775.83 328.79 Z M 777.76 328.15 L 773.78 324.18 C 773.52 323.92 773.13 323.84 772.79 323.98 C 772.45 324.12 772.23 324.45 772.23 324.82 L 772.23 327.89 L 765.29 327.89 L 765.29 319.25 C 765.29 318.75 764.89 318.34 764.39 318.34 L 757.39 318.34 L 757.39 320.16 L 763.48 320.16 L 763.48 327.89 L 757.23 327.89 L 757.23 329.7 L 763.48 329.7 L 763.48 337.43 L 757.39 337.43 L 757.39 339.25 L 764.39 339.25 C 764.89 339.25 765.29 338.84 765.29 338.34 L 765.29 329.7 L 772.23 329.7 L 772.23 332.77 C 772.23 333.14 772.45 333.47 772.79 333.61 C 772.9 333.66 773.02 333.68 773.14 333.68 C 773.37 333.68 773.61 333.59 773.78 333.42 L 777.76 329.44 C 778.11 329.08 778.11 328.51 777.76 328.15 Z M 752.57 330.09 L 752.57 327.51 L 754.18 328.79 Z M 752.23 324.9 C 751.95 324.68 751.58 324.64 751.26 324.79 C 750.95 324.95 750.75 325.26 750.75 325.61 L 750.75 331.98 C 750.75 332.33 750.95 332.64 751.26 332.8 C 751.39 332.86 751.53 332.89 751.66 332.89 C 751.86 332.89 752.06 332.82 752.23 332.69 L 756.2 329.5 C 756.42 329.33 756.55 329.07 756.55 328.79 C 756.55 328.52 756.42 328.26 756.2 328.08 Z M 752.57 320.54 L 752.57 317.96 L 754.18 319.25 Z M 752.23 315.36 C 751.95 315.14 751.58 315.1 751.26 315.25 C 750.95 315.4 750.75 315.72 750.75 316.07 L 750.75 322.43 C 750.75 322.78 750.95 323.1 751.26 323.25 C 751.39 323.31 751.53 323.34 751.66 323.34 C 751.86 323.34 752.06 323.27 752.23 323.14 L 756.2 319.96 C 756.42 319.79 756.55 319.53 756.55 319.25 C 756.55 318.97 756.42 318.71 756.2 318.54 Z M 762 346.18 C 751.97 346.18 743.82 338.03 743.82 328 C 743.82 317.97 751.97 309.82 762 309.82 C 772.03 309.82 780.18 317.97 780.18 328 C 780.18 338.03 772.03 346.18 762 346.18 Z M 762 308 C 750.97 308 742 316.97 742 328 C 742 339.03 750.97 348 762 348 C 773.03 348 782 339.03 782 328 C 782 316.97 773.03 308 762 308 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 355px; margin-left: 762px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway 1
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="762" y="367" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Ga...
                </text>
            </switch>
        </g>
        <rect x="842" y="308" width="40" height="40" fill="none" stroke="none" pointer-events="all"/>
        <path d="M 852.57 338.84 L 852.57 336.26 L 854.18 337.55 Z M 852.23 333.65 C 851.95 333.43 851.58 333.39 851.26 333.54 C 850.95 333.7 850.75 334.01 850.75 334.36 L 850.75 340.73 C 850.75 341.08 850.95 341.39 851.26 341.55 C 851.39 341.61 851.53 341.64 851.66 341.64 C 851.86 341.64 852.06 341.57 852.23 341.44 L 856.2 338.26 C 856.42 338.08 856.55 337.82 856.55 337.55 C 856.55 337.27 856.42 337.01 856.2 336.84 Z M 874.05 330.58 L 874.05 327.01 L 875.83 328.79 Z M 877.76 328.15 L 873.78 324.18 C 873.52 323.92 873.13 323.84 872.79 323.98 C 872.45 324.12 872.23 324.45 872.23 324.82 L 872.23 327.89 L 865.29 327.89 L 865.29 319.25 C 865.29 318.75 864.89 318.34 864.39 318.34 L 857.39 318.34 L 857.39 320.16 L 863.48 320.16 L 863.48 327.89 L 857.23 327.89 L 857.23 329.7 L 863.48 329.7 L 863.48 337.43 L 857.39 337.43 L 857.39 339.25 L 864.39 339.25 C 864.89 339.25 865.29 338.84 865.29 338.34 L 865.29 329.7 L 872.23 329.7 L 872.23 332.77 C 872.23 333.14 872.45 333.47 872.79 333.61 C 872.9 333.66 873.02 333.68 873.14 333.68 C 873.37 333.68 873.61 333.59 873.78 333.42 L 877.76 329.44 C 878.11 329.08 878.11 328.51 877.76 328.15 Z M 852.57 330.09 L 852.57 327.51 L 854.18 328.79 Z M 852.23 324.9 C 851.95 324.68 851.58 324.64 851.26 324.79 C 850.95 324.95 850.75 325.26 850.75 325.61 L 850.75 331.98 C 850.75 332.33 850.95 332.64 851.26 332.8 C 851.39 332.86 851.53 332.89 851.66 332.89 C 851.86 332.89 852.06 332.82 852.23 332.69 L 856.2 329.5 C 856.42 329.33 856.55 329.07 856.55 328.79 C 856.55 328.52 856.42 328.26 856.2 328.08 Z M 852.57 320.54 L 852.57 317.96 L 854.18 319.25 Z M 852.23 315.36 C 851.95 315.14 851.58 315.1 851.26 315.25 C 850.95 315.4 850.75 315.72 850.75 316.07 L 850.75 322.43 C 850.75 322.78 850.95 323.1 851.26 323.25 C 851.39 323.31 851.53 323.34 851.66 323.34 C 851.86 323.34 852.06 323.27 852.23 323.14 L 856.2 319.96 C 856.42 319.79 856.55 319.53 856.55 319.25 C 856.55 318.97 856.42 318.71 856.2 318.54 Z M 862 346.18 C 851.97 346.18 843.82 338.03 843.82 328 C 843.82 317.97 851.97 309.82 862 309.82 C 872.03 309.82 880.18 317.97 880.18 328 C 880.18 338.03 872.03 346.18 862 346.18 Z M 862 308 C 850.97 308 842 316.97 842 328 C 842 339.03 850.97 348 862 348 C 873.03 348 882 339.03 882 328 C 882 316.97 873.03 308 862 308 Z" fill="#4d27aa" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 355px; margin-left: 862px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                NAT Gateway 2
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="862" y="367" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    NAT Ga...
                </text>
            </switch>
        </g>
        <path d="M 598 40 L 623 40 L 623 65 L 598 65 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 617.94 53.35 L 615.98 52.56 L 615.98 59.67 C 616.56 59.61 617.02 59.42 617.34 59.09 C 617.95 58.47 617.94 57.57 617.94 57.56 Z M 615.27 59.67 L 615.27 52.56 L 613.31 53.35 L 613.31 57.56 C 613.32 57.63 613.4 59.46 615.27 59.67 Z M 618.65 57.56 C 618.65 57.6 618.67 58.75 617.86 59.58 C 617.33 60.13 616.57 60.4 615.63 60.4 C 613.29 60.4 612.63 58.55 612.6 57.56 L 612.6 53.11 C 612.6 52.96 612.69 52.83 612.83 52.78 L 615.5 51.71 C 615.58 51.67 615.67 51.67 615.76 51.71 L 618.43 52.78 C 618.56 52.83 618.65 52.96 618.65 53.11 Z M 619.72 56.86 L 619.72 52.1 L 615.63 50.46 L 611.53 52.1 L 611.53 56.84 C 611.53 56.88 611.49 59.06 612.79 60.4 C 613.48 61.11 614.43 61.47 615.63 61.47 C 616.83 61.47 617.79 61.11 618.48 60.4 C 619.78 59.05 619.72 56.88 619.72 56.86 Z M 618.99 60.89 C 618.16 61.75 617.03 62.18 615.63 62.18 C 614.23 62.18 613.1 61.75 612.28 60.9 C 610.76 59.33 610.82 56.93 610.82 56.83 L 610.82 51.86 C 610.82 51.71 610.91 51.58 611.05 51.53 L 615.5 49.75 C 615.58 49.72 615.67 49.72 615.76 49.75 L 620.21 51.53 C 620.34 51.58 620.43 51.71 620.43 51.86 L 620.43 56.84 C 620.44 56.93 620.5 59.33 618.99 60.89 Z M 604.43 55.42 L 609.75 55.42 L 609.75 56.13 L 604.43 56.13 C 602.27 56.13 600.62 54.69 600.51 52.7 C 600.5 52.62 600.5 52.53 600.5 52.43 C 600.5 49.99 602.21 49.09 603.21 48.77 C 603.2 48.66 603.2 48.54 603.2 48.42 C 603.2 46.48 604.58 44.45 606.42 43.7 C 608.57 42.82 610.84 43.24 612.49 44.84 C 613.05 45.38 613.47 45.96 613.78 46.6 C 614.21 46.23 614.73 46.03 615.28 46.03 C 616.46 46.03 617.7 46.95 617.92 48.72 C 618.69 48.92 619.67 49.33 620.36 50.22 L 619.8 50.66 C 619.16 49.84 618.2 49.5 617.51 49.36 C 617.36 49.33 617.24 49.19 617.23 49.03 C 617.14 47.46 616.16 46.74 615.28 46.74 C 614.76 46.74 614.3 46.99 613.95 47.45 C 613.87 47.56 613.74 47.61 613.61 47.59 C 613.48 47.57 613.37 47.48 613.33 47.36 C 613.06 46.62 612.62 45.96 612 45.35 C 610.56 43.96 608.57 43.59 606.69 44.36 C 605.1 45.01 603.91 46.75 603.91 48.42 C 603.91 48.62 603.92 48.81 603.94 48.99 C 603.96 49.17 603.85 49.33 603.67 49.38 C 602.75 49.6 601.21 50.3 601.21 52.43 C 601.21 52.5 601.21 52.58 601.22 52.65 C 601.31 54.26 602.66 55.42 604.43 55.42 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 72px; margin-left: 611px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="611" y="84" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 598 247.5 L 623 247.5 L 623 272.5 L 598 272.5 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/>
        <path d="M 617.94 260.85 L 615.98 260.06 L 615.98 267.17 C 616.56 267.11 617.02 266.92 617.34 266.59 C 617.95 265.97 617.94 265.07 617.94 265.06 Z M 615.27 267.17 L 615.27 260.06 L 613.31 260.85 L 613.31 265.06 C 613.32 265.13 613.4 266.96 615.27 267.17 Z M 618.65 265.06 C 618.65 265.1 618.67 266.25 617.86 267.08 C 617.33 267.63 616.57 267.9 615.63 267.9 C 613.29 267.9 612.63 266.05 612.6 265.06 L 612.6 260.61 C 612.6 260.46 612.69 260.33 612.83 260.28 L 615.5 259.21 C 615.58 259.17 615.67 259.17 615.76 259.21 L 618.43 260.28 C 618.56 260.33 618.65 260.46 618.65 260.61 Z M 619.72 264.36 L 619.72 259.6 L 615.63 257.96 L 611.53 259.6 L 611.53 264.34 C 611.53 264.38 611.49 266.56 612.79 267.9 C 613.48 268.61 614.43 268.97 615.63 268.97 C 616.83 268.97 617.79 268.61 618.48 267.9 C 619.78 266.55 619.72 264.38 619.72 264.36 Z M 618.99 268.39 C 618.16 269.25 617.03 269.68 615.63 269.68 C 614.23 269.68 613.1 269.25 612.28 268.4 C 610.76 266.83 610.82 264.43 610.82 264.33 L 610.82 259.36 C 610.82 259.21 610.91 259.08 611.05 259.03 L 615.5 257.25 C 615.58 257.22 615.67 257.22 615.76 257.25 L 620.21 259.03 C 620.34 259.08 620.43 259.21 620.43 259.36 L 620.43 264.34 C 620.44 264.43 620.5 266.83 618.99 268.39 Z M 604.43 262.92 L 609.75 262.92 L 609.75 263.63 L 604.43 263.63 C 602.27 263.63 600.62 262.19 600.51 260.2 C 600.5 260.12 600.5 260.03 600.5 259.93 C 600.5 257.49 602.21 256.59 603.21 256.27 C 603.2 256.16 603.2 256.04 603.2 255.92 C 603.2 253.98 604.58 251.95 606.42 251.2 C 608.57 250.32 610.84 250.74 612.49 252.34 C 613.05 252.88 613.47 253.46 613.78 254.1 C 614.21 253.73 614.73 253.53 615.28 253.53 C 616.46 253.53 617.7 254.45 617.92 256.22 C 618.69 256.42 619.67 256.83 620.36 257.72 L 619.8 258.16 C 619.16 257.34 618.2 257 617.51 256.86 C 617.36 256.83 617.24 256.69 617.23 256.53 C 617.14 254.96 616.16 254.24 615.28 254.24 C 614.76 254.24 614.3 254.49 613.95 254.95 C 613.87 255.06 613.74 255.11 613.61 255.09 C 613.48 255.07 613.37 254.98 613.33 254.86 C 613.06 254.12 612.62 253.46 612 252.85 C 610.56 251.46 608.57 251.09 606.69 251.86 C 605.1 252.51 603.91 254.25 603.91 255.92 C 603.91 256.12 603.92 256.31 603.94 256.49 C 603.96 256.67 603.85 256.83 603.67 256.88 C 602.75 257.1 601.21 257.8 601.21 259.93 C 601.21 260 601.21 260.08 601.22 260.15 C 601.31 261.76 602.66 262.92 604.43 262.92 Z" fill="#ffffff" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 280px; margin-left: 611px;">
                        <div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="611" y="292" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="598" y="88" width="100" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 103px; margin-left: 599px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                10.
                                <font color="#ff0000">
                                    100
                                </font>
                                .0.0/16
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="648" y="107" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    10.100.0.0/16
                </text>
            </switch>
        </g>
        <rect x="598" y="300" width="100" height="30" fill="none" stroke="none" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 315px; margin-left: 599px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                10.
                                <font color="#0000ff">
                                    200
                                </font>
                                .0.0/16
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="648" y="319" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    10.200.0.0/16
                </text>
            </switch>
        </g>
        <path d="M 453 220 L 530 220 L 530 110 L 559.76 110" fill="none" stroke="#ff0000" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 565.76 110 L 557.76 114 L 559.76 110 L 557.76 106 Z" fill="#ff0000" stroke="#ff0000" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 453 220 L 530 220 L 530 320 L 559.76 320" fill="none" stroke="#0000ff" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 565.76 320 L 557.76 324 L 559.76 320 L 557.76 316 Z" fill="#0000ff" stroke="#0000ff" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 220px; margin-left: 493px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                <font style="font-size: 14px;">
                                    デプロイ
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="493" y="223" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    デプロイ
                </text>
            </switch>
        </g>
        <rect x="264" y="159" width="189" height="122" fill="none" stroke="rgb(0, 0, 0)" stroke-dasharray="3 3" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-end; justify-content: unsafe center; width: 187px; height: 1px; padding-top: 156px; margin-left: 265px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                スタックファイル
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="359" y="156" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    スタックファイル
                </text>
            </switch>
        </g>
        <rect x="284" y="189" width="149" height="80" fill="none" stroke="rgb(0, 0, 0)" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 229px; margin-left: 296px;">
                        <div data-drawio-colors="color: #FF0000; " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(255, 0, 0); line-height: 1.2; pointer-events: none; white-space: nowrap;">
                                <div style="">
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        vpcCidr:〇〇
                                    </span>
                                    <br/>
                                </div>
                                <div style="">
                                    <span style="color: rgb(0, 0, 0); background-color: initial;">
                                        natGateway:〇〇
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="296" y="233" fill="#FF0000" font-family="Helvetica" font-size="12px">
                    vpcCidr:〇〇...
                </text>
            </switch>
        </g>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 43px; height: 1px; padding-top: 179px; margin-left: 337px;">
                        <div data-drawio-colors="color: #000000; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="359" y="183" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <path d="M 202 158 L 233 158 L 233 220 L 255.76 220" fill="none" stroke="#ff0000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 261.76 220 L 253.76 224 L 255.76 220 L 253.76 216 Z" fill="#ff0000" stroke="#ff0000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 184px; margin-left: 232px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                environment=dev
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="232" y="187" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    environment=dev
                </text>
            </switch>
        </g>
        <path d="M 202 298 L 233 298 L 233 220 L 255.76 220" fill="none" stroke="#0000ff" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 261.76 220 L 253.76 224 L 255.76 220 L 253.76 216 Z" fill="#0000ff" stroke="#0000ff" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 262px; margin-left: 236px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                environment=prod
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="236" y="265" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    environment=prod
                </text>
            </switch>
        </g>
        <path d="M 291.5 60 L 433 60 L 433 110 L 382.25 110 L 362.25 140 L 362.25 110 L 291.5 110 Z" fill="#f5f5f5" stroke="#666666" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 140px; height: 1px; padding-top: 85px; margin-left: 293px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                指定した環境ファイルで値が上書きされる
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="362" y="89" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    指定した環境ファイルで値が上書きされる
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
