# ディレクトリ構成概要

## ドキュメントのディレクトリ構成概要

- docs 配下のディレクトリ構成は以下の通り。

  ```
  docs/
  ├─ 01_Overall_Architecture.md
  ├─ 02_HowToUseStacks/
  ├─ 03_HowToRestore/
  ├─ 04_HowToDeploy.md
  ├─ 05_HowToCustomize.md
  ├─ 06_HowToDestroy.md
  ├─ 07_Deployment_Error_Reference.md
  ├─ 08_HowToSetup.md
  ├─ images/
  └─ README.md
  ```

- 汎用テンプレートの利用方法については、本ドキュメントを含めて、全部で7つのパートに分かれている。採番の順番で読むことを推奨する。

  - `01_Overall_Architecture.md`

    汎用テンプレートの目的や機能についての概要

  - `02_HowToUseStacks/`

    各スタックの具体的な利用方法

  - `03_HowToRestore/`

    AWSリソースのリストアの手順と、再度CDK管理下に置くための手順

  - `04_HowToDeploy.md`

    汎用テンプレートのデプロイ方法

  - `05_HowToCustomize.md`

    汎用テンプレートを各案件毎にカスタマイズする際の対応事項

  - `06_HowToDestroy.md`

    汎用テンプレートで作成したスタックのデストロイ方法

  - `07_Deployment_Error_Reference.md`

    デプロイ時のエラーリファレンス

  - `08_HowToSetup.md`

    初期セットアップ手順書

## 通常の開発の流れ

### 1. 必要なパッケージをインストールする

`package.json`で定義したパッケージをインストールする。

> ```sh
> # BLEAのルートディレクトリで実行
> npm install
> ```

### 2. CDK コードを編集する

任意のエディタで CDK コードを編集する。  
詳細は[04_HowToCustomize.md](./04_HowToCustomize.md)を参照

### 3. Synth/Diff を実行する

現在作成されている環境と CDK コードとの差分を確認する。

> ```sh
> npx cdk synth --all -c environment=dev
> npx cdk diff --all -c environment=dev
> ```

### 4. Deploy を実行する

スタックをデプロイする。  
詳細は[03_HowToDeploy.md](./03_HowToDeploy.md)を参照

> ```sh
> npx cdk deploy --all -c environment=dev
> ```

### 5. Destroy を実行する

検証が終わり、リソースが不要になったら、スタックをデストロイする。  
詳細は[05_HowToDestroy.md](./05_HowToDestroy.md)を参照

> ```sh
> npx cdk destroy --all -c environment=dev
> ```
