import * as cdk from 'aws-cdk-lib';
import * as opensearch from 'aws-cdk-lib/aws-opensearchservice';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as events from 'aws-cdk-lib/aws-events';
import { Duration } from 'aws-cdk-lib';
import * as inf from './interface';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as appscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import { aws_s3 as s3 } from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';

export const CognitoParam: inf.ICognitoParam = {
  urlForCallback: ['http://xxx/auth/callback'],
  urlForLogout: ['http://xxx/auth/logout'],
  secretArn: 'arn:aws:secretsmanager:xxx',
  identityProvider: cognito.UserPoolClientIdentityProvider.GOOGLE,
};

export const WafParam: inf.IWafParam = {
  basicAuthUserName: 'admin',
  basicAuthUserPass: 'pass',
  overrideAction_CommonRuleSet: { count: {} },
  overrideAction_KnownBadInputsRuleSet: { count: {} },
  overrideAction_AmazonIpReputationList: { count: {} },
  overrideAction_LinuxRuleSet: { count: {} },
  overrideAction_SQLiRuleSet: { count: {} },
  overrideAction_CSCRuleSet: { count: {} },
  ruleAction_IPsetRuleSet: { allow: {} },
  ruleAction_BasicRuleSet: {
    block: {
      customResponse: {
        responseCode: 401,
        responseHeaders: [
          {
            name: 'www-authenticate',
            value: 'Basic',
          },
        ],
      },
    },
  },
  allowIPList: ['***************/25'],
  stackTerminationProtection: false,
};

export const WafAlbParam: inf.IWafParam = {
  allowIPList: ['***************/25'],
  preSharedKey: 'pre-string-for-preSharedKey',
  stackTerminationProtection: false,
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'OrganizationName',
  RepositoryNames: {
    WafRepositoryName: 'WafRepositoryName',
    InfraRepositoryName: 'InfraRepositoryName',
    EcsDeployRepositoryName: 'EcsDeployRepositoryName',
  },
  stackTerminationProtection: false,
};

export const OpensearchParam: inf.IOpenSearchParam = {
  openSearchProvisionedParam: {
    engineVersion: opensearch.EngineVersion.OPENSEARCH_1_3,
    zoneAwareness: 3,
    ebsVolumeType: ec2.EbsDeviceVolumeType.GP3,
    ebsVolumeSize: 20,
    ebsIops: 5000,
    dataNodes: 3,
    masterNodes: 3,
    masterNodeInstanceType: 't3.medium.search',
    dataNodeInstanceType: 't3.medium.search',
  },
  openSearchServerlessParam: {
    collectionType: 'SEARCH',
  },
  stackTerminationProtection: false,
};

export const OpensearchTypeParam: inf.IOpenSearchTypeParam = {
  openSearchType: 'PROVISION',
};

export const ElastiCacheParam: inf.IElastiCacheParam = {
  ElastiCacheSelfDesignedParam: {
    engine: 'valkey',
    engineVersion: '7.2',
    numNodeGroups: 3,
    replicasPerNodeGroup: 2,
    minCapacity: 3,
    maxCapacity: 12,
    targetValueToScale: 70,
    predefinedMetricToScale: appscaling.PredefinedMetric.ELASTICACHE_PRIMARY_ENGINE_CPU_UTILIZATION,
    enableAutoScale: false,
    cacheNodeTypeEnableAutoScale: 'cache.m5.large',
    cacheNodeTypeDisableAutoScale: 'cache.t3.small',
    elastiCacheCustomParam: {
      cacheParameterGroupFamily: 'valkey7',
      description: 'CustomParameterGroupForElastiCache',
      properties: {
        'cluster-enabled': 'yes',
      },
    },
  },
  ElastiCacheServerlessParam: {
    engine: 'valkey',
    engineVersion: '7',
    dataStorageMaximum: 123, // GB
    dataStorageMinimum: 0, // GB -- When this value is set, it will be charged even if you do not use it.
    ecpuPerSecondMaximum: 1000,
    ecpuPerSecondMinimum: 0, //When this value is set, it will be charged even if you do not use it.
  },
  stackTerminationProtection: false,
};

export const AuroraParam: inf.IAuroraParam = {
  dbName: 'mydbname',
  dbUser: 'dbUser',
  dbVersion: rds.AuroraMysqlEngineVersion.VER_3_04_3,
  // dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4,
  writerInstanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
  readers: [
    {
      instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM),
      enablePerformanceInsights: false,
      autoMinorVersionUpgrade: false,
    },
  ],
  enablePerformanceInsights: false, // Change to false when cdk import
  auroraMinAcu: 2,
  auroraMaxAcu: 16,
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  //ParameterGroupforMySQL
  clusterParameters: {
    time_zone: 'Asia/Tokyo',
    character_set_client: 'utf8mb4',
    character_set_connection: 'utf8mb4',
    character_set_database: 'utf8mb4',
    character_set_results: 'utf8mb4',
    character_set_server: 'utf8mb4',
    init_connect: 'SET NAMES utf8mb4',
    binlog_format: 'ROW',
  },
  instanceParameters: {
    slow_query_log: '1',
    long_query_time: '10',
  },
  //ParameterGroupforPostgreSQL
  // clusterParameters: {
  //   timezone: 'Asia/Tokyo',
  //   client_encoding: 'UTF8',
  // },
  // instanceParameters: {
  //   //「.」があるKey値はプロパティ扱いになるため「'」で括る
  //   shared_preload_libraries: 'auto_explain,pg_stat_statements,pg_hint_plan,pgaudit',
  //   log_statement: 'ddl',
  //   log_connections: '1',
  //   log_disconnections: '1',
  //   log_lock_waits: '1',
  //   log_min_duration_statement: '5000',
  //   'auto_explain.log_min_duration': '5000',
  //   'auto_explain.log_verbose': '1',
  //   log_rotation_age: '1440',
  //   log_rotation_size: '102400',
  //   'rds.log_retention_period': '10080',
  //   random_page_cost: '1',
  //   track_activity_query_size: '16384',
  //   idle_in_transaction_session_timeout: '7200000',
  //   statement_timeout: '7200000',
  //   search_path: '"$user",public',
  // },
  backupRetentionDays: 7,
  // 17:00-18:00 UTC ~ 02:00-03:00 JST
  backupPreferredWindow: '17:00-18:00',
  // Wed:19:00-Wed:20:00 UTC ~ Thu:04:00-Thu:05:00 JST
  preferredMaintenanceWindow: 'Wed:19:00-Wed:20:00',
  autoMinorVersionUpgrade: false,
  deletionProtection: false,
  stackTerminationProtection: false,
  enableRdsProxy: false, // Set to true to enable RDS Proxy for improved performance and connection pooling
  rdsProxyConfig: {
    maxConnectionsPercent: 100,
    maxIdleConnectionsPercent: 50,
    requireTLS: true,
    idleClientTimeout: 1800, // seconds (30 minutes)
  },
};

//Used when creating front-end stacks
export const CertificateIdentifier: inf.ICertificateIdentifier = {
  identifier: '',
};

export const AlbParam: inf.IAlbParam = {
  identifier: '',
};

export const AlbBgParam: inf.IAlbParam = {
  identifier: '',
};

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// ・pathが存在しないタスクがデフォルトルールになる。1タスク必ず設定する（0でも2以上でもNG）
// ・path：デフォルトルールでないタスクの場合に必ず設定する、リクエストを振り分けるパスの文字列を設定
// 本サンプルでは2種類のECSサービスを定義し、EcsAppをデフォルトルールを設定している
export const EcsFrontTasks: inf.IEcsAlbParam = [
  {
    appName: 'EcsApp',
    portNumber: 80,
  },
  {
    appName: 'EcsApp2',
    portNumber: 80,
    path: '/path',
  },
];

// インターネットアクセス可能なECSサービス、タスクのパラメータを設定する、デプロイはBlue/Greenデプロイとなる
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// ・pathが存在しないタスクがデフォルトルールになる。1タスク必ず設定する（0でも2以上でもNG）
// ・path：デフォルトルールでないタスクの場合に必ず設定する、リクエストを振り分けるパスの文字列を設定
// 本サンプルでは2種類のECSサービスを定義し、EcsAppBgをデフォルトルールを設定している
export const EcsFrontBgTasks: inf.IEcsAlbParam = [
  {
    appName: 'EcsAppBg',
    portNumber: 80,
  },
  {
    appName: 'EcsApp2Bg',
    portNumber: 80,
    path: '/path',
  },
];

// VPC内でService Connectを経由して接続するECSサービス、タスクのパラメータを設定する
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// 本サンプルでは2種類のECSサービスを定義している
export const EcsBackTasks: inf.IEcsParam[] = [
  {
    appName: 'EcsBackend',
    portNumber: 8080,
  },
  {
    appName: 'EcsBackend2',
    portNumber: 8080,
  },
];

// VPC内でALBを経由して接続するECSサービス、タスクのパラメータを設定する、デプロイはBlue/Greenデプロイとなる
// ・appName：ECSサービスやタスク名に使用する名前
// ・portNumber：コンテナがリクエストを受け付けるポート（HTTPであれば80）
// ・pathが存在しないタスクがデフォルトルールになる。1タスク必ず設定する（0でも2以上でもNG）
// ・path：デフォルトルールでないタスクの場合に必ず設定する、リクエストを振り分けるパスの文字列を設定
// 本サンプルでは2種類のECSサービスを定義し、EcsBackendBgをデフォルトルールを設定している
export const EcsBackBgTasks: inf.IEcsAlbParam = [
  {
    appName: 'EcsBackendBg',
    portNumber: 8080,
  },
  {
    appName: 'EcsBackend2Bg',
    portNumber: 8080,
    path: '/path',
  },
];

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];

export const pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[] = [
  {
    noncurrentVersionExpiration: cdk.Duration.days(30),
    noncurrentVersionsToRetain: 20,
  },
];

export const VpcParam: inf.IVpcParam = {
  cidr: '**********/16',
  maxAzs: 3,
  natGateways: 2,
};

export const NotifierParam: inf.INotifierParam = {
  workspaceId: 'T8XXXXXXX',
  channelIdMon: 'C01YYYYYYYY',
  monitoringNotifyEmail: '<EMAIL>',
};

export const CloudFrontParam: inf.ICloudFrontParam = {
  fqdn: '',
  createClosedBucket: false,
  stackTerminationProtection: false,
};

export const Env: inf.IEnv = {
  envName: 'Stg',
  account: '************',
  region: 'ap-northeast-1',
};

export const DRRegionParam: inf.IDRRegion = {
  region: 'ap-southeast-1',
};

// バックアップスケジュールをcron形式で指定
//https://docs.aws.amazon.com/cdk/api/v2/docs/aws-cdk-lib.aws_events.CronOptions.html
//https://docs.aws.amazon.com/eventbridge/latest/userguide/eb-cron-expressions.html
export const BackupParam: inf.IBackupParam = {
  backupDisasterRecovery: false,
  retentionPeriod: Duration.days(14),
  backupSchedule: events.Schedule.cron({
    minute: '0',
    hour: '2',
    day: '*',
    month: '*',
    year: '*',
  }),
  stackTerminationProtection: false,
};

// CodeBuild完了後にSlackへのステータス通知を行う際に必要な情報
// slackChannelNameはSlackチャンネル名を入力
// slackWorkspaceIdはslackのワークスペースIDを入力
// slackChannelIdはSlackのチャンネルIDを入力
export const InfraResourcesPipelineParam: inf.IInfraResourcesPipelineParam = {
  slackChannelName: 'YOUR_CHANNEL_NAME',
  slackWorkspaceId: 'YOUR_SLACK_WORKSPACE_ID',
  slackChannelId: 'YOUR_SLACK_CHANNEL_ID',
  stackTerminationProtection: false,
};

export const BastionParam: inf.IBastionParam = {
  ecrLifecycleRules: [
    {
      description: 'Keep last 5 images',
      maxImageCount: 5,
    },
  ],
};

export const KmsKeyParam: inf.IKmsKeyParam = {
  pendingWindow: cdk.Duration.days(7),
};

export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;

export const EfsParam: inf.IEfsParam = {
  lifecyclePolicy: efs.LifecyclePolicy.AFTER_7_DAYS,
  outOfInfrequentAccessPolicy: efs.OutOfInfrequentAccessPolicy.AFTER_1_ACCESS,
  throughputMode: efs.ThroughputMode.ELASTIC,
  performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  // Please change isUseSharedTransferFamily to true after creating the EFS stack
  isUseSharedTransferFamily: false,
  // Please change the information accordingly
  sharedTransferFamilyAccountID: '************',
  hasBackup: true,
  backupParams: {
    // 17:00 UTC ~ 02:00 JST
    schedule: events.Schedule.cron({
      minute: '0',
      hour: '17',
      day: '*',
      month: '*',
      year: '*',
    }),
    retentionPeriod: Duration.days(7),
    removalPolicy: cdk.RemovalPolicy.RETAIN,
  },
  stackTerminationProtection: false,
};

export const ElastiCacheTypeParam: inf.IElastiCacheTypeParam = {
  elastiCacheType: 'SERVERLESS',
};

export const CSIRTWAFParamALB: inf.ICSIRTWAFParam = {
  isUseCSIRTManageRules: true,
  CSIRTManagerRules: {
    overrideAction: { none: {} },
    ruleGroupArn: 'arn:aws:wafv2:ap-northeast-1:************:regional/rulegroup/CSIRTManagerRules/XXXX',
  },
  CSIRTIpSetArn: 'arn:aws:wafv2:ap-northeast-1:************:regional/ipset/CSIRTIpSet/XXXX',
};
export const CSIRTWAFParamCF: inf.ICSIRTWAFParam = {
  isUseCSIRTManageRules: true,
  CSIRTManagerRules: {
    overrideAction: { none: {} },
    ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:global/rulegroup/CSIRTManagerRules/XXXX',
  },
  CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:global/ipset/CSIRTIpSet/XXXX',
};

export const SendGridLogParams: inf.ISendGridLogParam[] = [
  {
    s3BucketArn: '', // If you have an existing bucket, enter the ARN here. If you don't have one, leave it blank, and a new bucket will be created.
    suffix: 'xxxx', // 4 character string
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
];

export const SendGridLog: inf.ISendGridLog = {
  stackTerminationProtection: false,
};

export const MonitorParam: inf.IMonitorParam = {
  stackTerminationProtection: false,
};
export const ECSAppParam: inf.IECSAppParam = {
  stackTerminationProtection: false,
};

export const ShareResourcesParam: inf.IShareResourcesParam = {
  stackTerminationProtection: false,
};
