import * as cdk from 'aws-cdk-lib';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';

export interface AuroraProxyProps {
  /**
   * Project prefix for naming resources
   */
  readonly pjPrefix: string;

  /**
   * The Aurora cluster to proxy.
   */
  readonly cluster: rds.DatabaseCluster;

  /**
   * The VPC to place the proxy in.
   */
  readonly vpc: ec2.Vpc;

  /**
   * The subnets to place the proxy in.
   */
  readonly vpcSubnets: ec2.SubnetSelection;

  /**
   * RDS Proxy configuration options.
   */
  readonly proxyConfig?: {
    /**
     * The maximum number of connections that the proxy can open to the database.
     *
     * @default 100
     */
    maxConnectionsPercent?: number;
    /**
     * The maximum percentage of idle connections allowed through the proxy.
     *
     * @default 50 (%)
     */
    maxIdleConnectionsPercent?: number;
    /**
     * Whether to require TLS for connections to the proxy.
     *
     * @default true
     */
    requireTLS?: boolean;
    /**
     * The amount of time for a connection to be idle before the proxy disconnects it.
     *
     * @default 1800 seconds (30 minutes)
     */
    idleClientTimeout?: number;
  };

  /**
   * List of security groups that are allowed to connect to the proxy.
   * Can include app servers, bastion hosts, or any other services.
   *
   * @default - No security groups are added
   */
  readonly ingressSecurityGroups?: ec2.SecurityGroup[];

  /**
   * Deletion policy for Proxy-Related Resources
   */
  readonly removalPolicy: cdk.RemovalPolicy;
}

export class AuroraProxy extends Construct {
  readonly proxy: rds.DatabaseProxy;
  readonly securityGroup: ec2.SecurityGroup;

  constructor(scope: Construct, id: string, props: AuroraProxyProps) {
    super(scope, id);

    const proxyConfig = props.proxyConfig ?? {};

    this.securityGroup = new ec2.SecurityGroup(this, 'ProxySecurityGroup', {
      vpc: props.vpc,
      description: `${props.pjPrefix}-Aurora-Proxy-SecurityGroup`,
    });
    this.securityGroup.applyRemovalPolicy(props.removalPolicy);

    this.proxy = new rds.DatabaseProxy(this, `${props.pjPrefix}-AuroraProxy`, {
      proxyTarget: rds.ProxyTarget.fromCluster(props.cluster),
      secrets: props.cluster.secret ? [props.cluster.secret] : [],
      vpc: props.vpc,
      vpcSubnets: props.vpcSubnets,
      securityGroups: [this.securityGroup],
      maxConnectionsPercent: proxyConfig.maxConnectionsPercent ?? 100,
      maxIdleConnectionsPercent: proxyConfig.maxIdleConnectionsPercent ?? 50,
      requireTLS: proxyConfig.requireTLS ?? true,
      idleClientTimeout: cdk.Duration.seconds(proxyConfig.idleClientTimeout ?? 1800),
    });

    // Allow proxy to connect to cluster
    props.cluster.connections.allowFrom(this.proxy, ec2.Port.tcp(props.cluster.clusterEndpoint.port));

    // Allow ingress security groups to connect to Proxy
    if (props.ingressSecurityGroups) {
      props.ingressSecurityGroups.forEach((ingressSG, index) => {
        this.securityGroup.connections.allowFrom(
          ingressSG,
          ec2.Port.tcp(props.cluster.clusterEndpoint.port),
          `Allow connection from ingress SG ${index + 1}`,
        );
      });
    }
  }
}
