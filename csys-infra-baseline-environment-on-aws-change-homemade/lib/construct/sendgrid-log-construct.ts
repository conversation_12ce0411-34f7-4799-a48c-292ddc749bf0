import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as firehose from 'aws-cdk-lib/aws-kinesisfirehose';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as athena from 'aws-cdk-lib/aws-athena';
import * as glue from 'aws-cdk-lib/aws-glue';
import * as cw from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import { Construct } from 'constructs';
import { SNS } from './sns-construct';
import * as cognito from 'aws-cdk-lib/aws-cognito';

interface ISendGridLogConstructProps extends cdk.StackProps {
  readonly pjPrefix: string;
  readonly createAlarm: boolean;
  readonly notifyEmail: string;
  readonly s3BucketArn?: string;
  readonly logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  readonly lifecycleRules?: s3.LifecycleRule[];
  readonly suffix?: string;
}

export class SendGridLogContruct extends Construct {
  constructor(scope: Construct, id: string, props: ISendGridLogConstructProps) {
    super(scope, id);

    let s3SendGridLogBucket: s3.IBucket;
    const region = cdk.Stack.of(this).region;
    const accountId = cdk.Stack.of(this).account;

    // If s3BucketArn is provided, use it. Otherwise, create a new S3 bucket.
    if (props.s3BucketArn) {
      s3SendGridLogBucket = s3.Bucket.fromBucketArn(this, 's3SendGridLogBucketExisting', props.s3BucketArn);
    } else {
      const bucketName = `${props.pjPrefix}-${props.suffix}-sendgrid-log`.toLowerCase();
      s3SendGridLogBucket = new s3.Bucket(this, 's3SendGridLogBucket', {
        bucketName: bucketName,
        accessControl: s3.BucketAccessControl.PRIVATE,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        enforceSSL: true,
        removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
        autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
        lifecycleRules: props.lifecycleRules,
      });
    }

    // IAM Role for Firehose
    const fireHoseRole = new iam.Role(this, `${props.pjPrefix}-${props.suffix}-FireHoseRole`, {
      assumedBy: new iam.ServicePrincipal('firehose.amazonaws.com'),
      roleName: `${props.pjPrefix}-${props.suffix}-firehose-role`,
      description: 'Role for firehose',
    });
    fireHoseRole.addToPolicy(
      new iam.PolicyStatement({
        actions: [
          's3:Abort*',
          's3:DeleteObject*',
          's3:GetBucket*',
          's3:GetObject*',
          's3:List*',
          's3:PutObject',
          's3:PutObjectLegalHold',
          's3:PutObjectRetention',
          's3:PutObjectTagging',
          's3:PutObjectVersionTagging',
        ],
        resources: [s3SendGridLogBucket.bucketArn, `${s3SendGridLogBucket.bucketArn}/*`],
      }),
    );

    fireHoseRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ['glue:GetTableVersions', 'glue:GetTableVersion', 'glue:GetTable', 'glue:GetDatabase'],
        resources: [
          `arn:aws:glue:${region}:${accountId}:catalog`,
          `arn:aws:glue:${region}:${accountId}:database/*`,
          `arn:aws:glue:${region}:${accountId}:table/*`,
        ],
      }),
    );

    // Athena workgroup
    const athenaWorkgroup = new athena.CfnWorkGroup(this, 'AthenaWorkgroup', {
      name: `${props.pjPrefix}-${props.suffix}-sendgrid-log-workgroup`,
      state: 'ENABLED',
      workGroupConfiguration: {
        publishCloudWatchMetricsEnabled: false,
        resultConfiguration: {
          outputLocation: `s3://${s3SendGridLogBucket.bucketName}/athena-query-results/`,
        },
      },
      recursiveDeleteOption: true,
    });

    // Athena database
    const databaseName = `${props.pjPrefix}_${props.suffix}_sendgrid_logs`;
    const athenaDatabase = new glue.CfnDatabase(this, 'AthenaDatabase', {
      catalogId: accountId,
      databaseInput: {
        name: databaseName,
        description: 'Athena database for SendGrid log',
      },
    });

    // Athena table
    const athenaTable = new glue.CfnTable(this, 'AthenaTable', {
      catalogId: accountId,
      databaseName: databaseName,
      tableInput: {
        name: 'apigateway_sendgrid',
        description: 'Athena table for SendGrid log',
        tableType: 'EXTERNAL_TABLE',
        storageDescriptor: {
          columns: [
            { name: 'email', type: 'string' },
            { name: 'event', type: 'string' },
            { name: 'send_at', type: 'bigint' },
            { name: 'sg_event_id', type: 'string' },
            { name: 'sg_message_id', type: 'string' },
            { name: 'smtp_id', type: 'string' },
            { name: 'timestamp', type: 'bigint' },
            { name: 'ip', type: 'string' },
            { name: 'response', type: 'string' },
            { name: 'tls', type: 'int' },
          ],
          location: `s3://${s3SendGridLogBucket.bucketName}/sendgrid/`,
          inputFormat: 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat',
          outputFormat: 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat',
          serdeInfo: {
            serializationLibrary: 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe',
          },
        },
        partitionKeys: [
          { name: 'year', type: 'int' },
          { name: 'month', type: 'int' },
          { name: 'day', type: 'int' },
          { name: 'hour', type: 'int' },
        ],
        parameters: {
          'projection.enabled': 'true',
          'projection.year.type': 'integer',
          'projection.year.digits': 4,
          'projection.year.range': '2024,2100',
          'projection.month.type': 'integer',
          'projection.month.digits': 2,
          'projection.month.range': '1,12',
          'projection.day.type': 'integer',
          'projection.day.digits': 2,
          'projection.day.range': '1,31',
          'projection.hour.type': 'integer',
          'projection.hour.digits': 2,
          'projection.hour.range': '0,23',
          'storage.location.template': `s3://${s3SendGridLogBucket.bucketName}/sendgrid/year=\${year}/month=\${month}/day=\${day}/hour=\${hour}/`,
          classification: 'parquet',
          compressionType: 'snappy',
          typeOfData: 'file',
        },
      },
    });

    // Firehose Delivery Stream
    const firehoseStream = new firehose.CfnDeliveryStream(this, 'FirehoseStream', {
      deliveryStreamName: `${props.pjPrefix}-${props.suffix}-apigateway-sendgrid-stream`,
      deliveryStreamType: 'DirectPut',
      extendedS3DestinationConfiguration: {
        bucketArn: s3SendGridLogBucket.bucketArn,
        roleArn: fireHoseRole.roleArn,
        compressionFormat: 'UNCOMPRESSED',
        prefix: `sendgrid/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/`,
        errorOutputPrefix: `kinesis-error/sendgrid/!{firehose:error-output-type}/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/hour=!{timestamp:HH}/`,
        dataFormatConversionConfiguration: {
          enabled: true,
          inputFormatConfiguration: {
            deserializer: {
              openXJsonSerDe: {},
            },
          },
          outputFormatConfiguration: {
            serializer: {
              parquetSerDe: {
                compression: 'SNAPPY',
              },
            },
          },
          schemaConfiguration: {
            roleArn: fireHoseRole.roleArn,
            catalogId: accountId,
            databaseName: databaseName,
            tableName: 'apigateway_sendgrid',
            region: region,
            versionId: 'LATEST',
          },
        },
      },
    });
    firehoseStream.node.addDependency(fireHoseRole);

    // IAM Role for API Gateway
    const apiRole = new iam.Role(this, 'ApiRole', {
      roleName: `${props.pjPrefix}-${props.suffix}-apigateway-to-firehose-role`,
      assumedBy: new iam.ServicePrincipal(`apigateway.amazonaws.com`),
      description: `Role for apigateway for send logs to firehose.`,
      inlinePolicies: {
        sendLog: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['firehose:PutRecord'],
              resources: [firehoseStream.attrArn],
              effect: iam.Effect.ALLOW,
            }),
          ],
        }),
      },
    });

    const userPool = new cognito.UserPool(this, 'SendGridUserpool', {
      userPoolName: `${props.pjPrefix}-${props.suffix}-sendgrid-userpool`,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    const cognito_domain = userPool.addDomain('CognitoDomain', {
      cognitoDomain: {
        domainPrefix: `${props.pjPrefix}-${props.suffix}-sendgrid-logs`,
      },
    });

    const fullAccessScope = new cognito.ResourceServerScope({
      scopeName: '*',
      scopeDescription: 'Full access',
    });
    const logPutApiServer = userPool.addResourceServer('ResourceServer', {
      identifier: 'logPutApi',
      scopes: [fullAccessScope],
    });

    const client = userPool.addClient('AppClient', {
      generateSecret: true,
      oAuth: {
        scopes: [cognito.OAuthScope.resourceServer(logPutApiServer, fullAccessScope)],
        flows: {
          clientCredentials: true,
        },
      },
      accessTokenValidity: cdk.Duration.days(1),
      idTokenValidity: cdk.Duration.days(1),
      userPoolClientName: `${props.pjPrefix}-${props.suffix}-sendgrid-userpool-client`,
    });

    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(this, 'SendGridLogAuthorizer', {
      cognitoUserPools: [userPool],
      authorizerName: `${props.pjPrefix}-${props.suffix}-sendgrid-log-authorizer`,
    });

    // API Gateway
    const api = new apigateway.RestApi(this, 'Api', {
      restApiName: `${props.pjPrefix}-${props.suffix}-apigateway-sendgrid`,
      deployOptions: {
        metricsEnabled: true,
      },
    });

    if (props.createAlarm) {
      const alarmTopic = new SNS(this, `${props.pjPrefix}-Alarm-${props.suffix}`, {
        notifyEmail: props.notifyEmail,
      });

      api
        .metricServerError({
          period: cdk.Duration.minutes(5),
          statistic: cw.Stats.SUM,
        })
        .createAlarm(this, 'APIGateway5xxError', {
          evaluationPeriods: 3,
          datapointsToAlarm: 1,
          threshold: 10,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(alarmTopic.topic));

      api
        .metricClientError({
          period: cdk.Duration.minutes(5),
          statistic: cw.Stats.SUM,
        })
        .createAlarm(this, 'APIGateway4xxError', {
          evaluationPeriods: 3,
          datapointsToAlarm: 1,
          threshold: 10,
          comparisonOperator: cw.ComparisonOperator.GREATER_THAN_OR_EQUAL_TO_THRESHOLD,
          actionsEnabled: true,
        })
        .addAlarmAction(new cw_actions.SnsAction(alarmTopic.topic));
    }

    (api.node.findChild('CloudWatchRole').node.defaultChild as iam.CfnRole).addPropertyOverride(
      'RoleName',
      `${props.pjPrefix}-${props.suffix}-apigateway-to-cloudwatch-log-role`,
    );
    (api.node.findChild('CloudWatchRole').node.defaultChild as iam.CfnRole).addOverride('DeletionPolicy', 'Delete');

    const logPut = api.root.addMethod(
      'POST',
      new apigateway.AwsIntegration({
        service: 'firehose',
        action: 'PutRecord',
        region: region,
        options: {
          credentialsRole: apiRole,
          passthroughBehavior: apigateway.PassthroughBehavior.WHEN_NO_TEMPLATES,
          requestTemplates: {
            'application/json': `
#set($num = 0)
#set($Data = "")
#foreach($var in $input.path('$'))
#set($tmp = '$[' + $num + ']')
#set($Data = $Data + $input.json($tmp) + "
")
#set($num = $num + 1)
#end
{
"DeliveryStreamName": "${firehoseStream.deliveryStreamName}",
"Record": {
  "Data": "$util.base64Encode($Data)"
}
}`,
          },
          requestParameters: {
            'integration.request.header.Content-Type': "'application/json'",
          },
          integrationResponses: [
            {
              statusCode: '200',
              selectionPattern: '^2\\d{2}$',
              responseTemplates: {
                'application/json': '{"status": "OK"}',
              },
            },
            {
              statusCode: '400',
              selectionPattern: '^4\\d{2}$',
              responseTemplates: {
                'application/json': '{"status": "Client Error"}',
              },
            },
            {
              statusCode: '500',
              selectionPattern: '^5\\d{2}$',
              responseTemplates: {
                'application/json': '{"status": "Server Error"}',
              },
            },
          ],
        },
      }),
      {
        authorizer: authorizer,
        authorizationType: apigateway.AuthorizationType.COGNITO,
        authorizationScopes: [`${logPutApiServer.userPoolResourceServerId}/${fullAccessScope.scopeName}`],
      },
    );
    logPut.addMethodResponse({
      statusCode: '200',
    });
    logPut.addMethodResponse({
      statusCode: '400',
    });
    logPut.addMethodResponse({
      statusCode: '500',
    });

    // Output client ID and OAuth2.0 domain information
    new cdk.CfnOutput(this, 'CognitoClientId', {
      value: client.userPoolClientId,
      description: 'The ID of the Cognito user pool client',
    });

    new cdk.CfnOutput(this, 'CognitoDomainUrl', {
      value: `https://${cognito_domain.domainName}.auth.${region}.amazoncognito.com/oauth2/token`,
      description: 'The OAuth2.0 domain URL for the Cognito user pool',
    });
  }
}
