import * as cdk from 'aws-cdk-lib';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as events from 'aws-cdk-lib/aws-events';
import { Construct } from 'constructs';
import { Efs } from '../construct/efs-construct';
import { EfsAddResourcePolicy } from '../construct/efs-construct/efs-add-resource-policy';

export interface EfsStackProps extends cdk.StackProps {
  readonly pjPrefix: string;
  readonly vpc: ec2.Vpc;
  readonly appKey: kms.IKey;
  readonly appServerSecurityGroup?: ec2.SecurityGroup;
  readonly bastionSecurityGroup?: ec2.SecurityGroup;
  readonly lifecyclePolicy?: efs.LifecyclePolicy;
  readonly outOfInfrequentAccessPolicy?: efs.OutOfInfrequentAccessPolicy;
  readonly throughputMode: efs.ThroughputMode;
  readonly performanceMode: efs.PerformanceMode;
  readonly removalPolicy?: cdk.RemovalPolicy;
  readonly isUseSharedTransferFamily?: boolean;
  readonly fileSystemId?: string;
  readonly sharedTransferFamilyAccountID?: string;
  readonly hasBackup?: boolean;
  readonly backupParams?: {
    schedule: events.Schedule;
    retentionPeriod: cdk.Duration;
    removalPolicy?: cdk.RemovalPolicy;
  };
}

export class EfsStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: EfsStackProps) {
    super(scope, id, props);

    // Create EFS
    const efsConstruct = new Efs(this, 'Efs', {
      pjPrefix: props.pjPrefix,
      vpc: props.vpc,
      appKey: props.appKey,
      appServerSecurityGroup: props.appServerSecurityGroup,
      bastionSecurityGroup: props.bastionSecurityGroup,
      lifecyclePolicy: props.lifecyclePolicy,
      outOfInfrequentAccessPolicy: props.outOfInfrequentAccessPolicy,
      throughputMode: props.throughputMode,
      performanceMode: props.performanceMode,
      removalPolicy: props.removalPolicy,
      hasBackup: props.hasBackup,
      backupParams: props.backupParams,
    });

    const { fileSystem, securityGroup } = efsConstruct;

    // Create file system policy
    if (props.isUseSharedTransferFamily) {
      const efsAddResourcePolicy = new EfsAddResourcePolicy(this, 'EfsAddResourcePolicy', {
        fileSystem,
        efsSecurityGroup: securityGroup,
        sharedTransferFamilyAccountID: props.sharedTransferFamilyAccountID,
      });
      efsAddResourcePolicy.node.addDependency(efsConstruct);
    }
  }
}
