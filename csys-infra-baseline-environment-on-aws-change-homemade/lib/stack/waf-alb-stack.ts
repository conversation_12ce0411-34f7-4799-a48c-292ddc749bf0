import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { WafConstruct } from '../construct/waf-construct';
import { ICSIRTWAFParam } from '../../params/interface';

export interface WafAlbStackProps extends cdk.StackProps {
  scope: string;
  allowIPList?: string[];
  preSharedKey?: string;
  associations?: string[];
  /**
   * Lifecycle rules of WAF log bucket
   */
  wafLogBucketLifecycleRules: cdk.aws_s3.LifecycleRule[];
  /**
   * Log removal policy parameter
   */
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  /**
   * CSIRT WAF Parameter
   */
  csirtWAFParam?: ICSIRTWAFParam;
}

export class WafAlbStack extends cdk.Stack {
  public readonly webAcl: wafv2.CfnWebACL;
  public readonly preSharedKey: string;

  constructor(scope: Construct, id: string, props: WafAlbStackProps) {
    super(scope, id, props);

    const webAcl = new WafConstruct(this, 'WafAlb', {
      scope: props.scope,
      allowIPList: props.allowIPList,
      preSharedKey: props.preSharedKey,
      associations: props.associations,
      wafLogBucketLifecycleRules: props.wafLogBucketLifecycleRules,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      csirtWAFParam: props.csirtWAFParam,
    });

    this.webAcl = webAcl.webAcl;
    this.preSharedKey = webAcl.preSharedKeyValue;
  }
}
