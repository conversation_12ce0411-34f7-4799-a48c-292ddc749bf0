import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as kms from 'aws-cdk-lib/aws-kms';
import * as sns from 'aws-cdk-lib/aws-sns';
import { IEcsAlbParam, IEcsParam, IAlbParam, IBastionParam, IRemovalPolicyParam } from '../../params/interface';
import { EcsApp } from '../construct/ecs-app-construct';
import { aws_s3 as s3 } from 'aws-cdk-lib';

interface EcsAppStackProps extends cdk.StackProps {
  myVpc: ec2.Vpc;
  appKey: kms.IKey;
  alarmTopic: sns.Topic;
  prefix: string;
  albParam: IAlbParam;
  albBgParam: IAlbParam;
  ecsFrontTasks: IEcsAlbParam;
  ecsFrontBgTasks: IEcsAlbParam;
  ecsBackTasks: IEcsParam[];
  ecsBackBgTasks: IEcsAlbParam;
  albAccessLogBucketLifecycleRules: s3.LifecycleRule[];
  pipelineSourceBucketLifeCycleRules: s3.LifecycleRule[];
  ecsBastionTasks?: boolean;
  bastionParams?: IBastionParam;
  albListenerOpenFlag?: boolean;
  otherRemovalPolicyParam?: IRemovalPolicyParam;
  logRemovalPolicyParam?: IRemovalPolicyParam;
}

export class EcsAppStack extends cdk.Stack {
  public readonly app: EcsApp;

  constructor(scope: Construct, id: string, props: EcsAppStackProps) {
    super(scope, id, props);

    const ecs = new EcsApp(this, `${props.prefix}-ECSApp`, {
      myVpc: props.myVpc,
      appKey: props.appKey,
      alarmTopic: props.alarmTopic,
      prefix: props.prefix,
      albParam: props.albParam,
      albBgParam: props.albBgParam,
      ecsFrontTasks: props.ecsFrontTasks,
      ecsFrontBgTasks: props.ecsFrontBgTasks,
      ecsBackTasks: props.ecsBackTasks,
      ecsBackBgTasks: props.ecsBackBgTasks,
      ecsBastionTasks: props.ecsBastionTasks ?? true,
      albAccessLogBucketLifecycleRules: props.albAccessLogBucketLifecycleRules,
      bastionParams: props.bastionParams,
      albListenerOpenFlag: props.albListenerOpenFlag,
      ecrRemovalPolicyParam: props.otherRemovalPolicyParam,
      pipelineBucketRemovalPolicyParam: props.otherRemovalPolicyParam,
      pipelineSourceBucketLifeCycleRules: props.pipelineSourceBucketLifeCycleRules,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
    });
    this.app = ecs;
  }
}
