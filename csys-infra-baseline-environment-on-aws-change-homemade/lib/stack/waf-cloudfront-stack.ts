import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { WafConstruct } from '../construct/waf-construct';
import { ICSIRTWAFParam } from '../../params/interface';

export interface WafStackProps extends cdk.StackProps {
  basicAuthUserName?: string;
  basicAuthUserPass?: string;
  scope: string;
  overrideAction_CommonRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_KnownBadInputsRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_AmazonIpReputationList?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_LinuxRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_SQLiRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_CSCRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  ruleAction_IPsetRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  ruleAction_BasicRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  allowIPList?: string[];
  /**
   * Lifecycle rules of WAF log bucket
   */
  wafLogBucketLifecycleRules: cdk.aws_s3.LifecycleRule[];
  /**
   * Log removal policy parameter
   */
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  /**
   * CSIRT WAF Parameter
   */
  csirtWAFParam?: ICSIRTWAFParam;
}

export class WafCfStack extends cdk.Stack {
  public readonly webAcl: wafv2.CfnWebACL;

  constructor(scope: Construct, id: string, props: WafStackProps) {
    super(scope, id, props);

    // CyberSecurityCloud社のマネージドルールを適用する場合は下記コードをコメントインする。
    // 当ルールのWCUが1000のため、他ルールと組み合わせる場合はWCUの限界値を超えないようにルールの組み合わせに考慮する必要がある。
    // 例)AWSマネージドルールをすべてコメントアウトしてCyberSecurityCloud社のマネージドルールをコメントイン
    // また事前にAWSマーケットプレイスからサブスクリプション購入する必要がある。
    //  購入方法
    // ・aws-marketplace:ViewSubscriptionsとaws-marketplace:Subscribeを許可しているポリシーを持つIAMユーザーにログイン、もしくはスイッチロールする
    // ・AWSマーケットプレイス(https://aws.amazon.com/marketplace/pp/prodview-kyur2d2omnrlg?sr=0-1&ref_=beagle&applicationId=AWSMPContessa)にアクセス
    // ・「View purchse options」ボタンをクリック
    // ・「Subscribe」ボタンをクリック
    //
    // const additionalRules: wafv2.CfnWebACL.RuleProperty[] = [{
    //   priority: 10,
    //   overrideAction: props.overrideAction_CSCRuleSet,
    //   visibilityConfig: {
    //     sampledRequestsEnabled: true,
    //     cloudWatchMetricsEnabled: true,
    //     metricName: 'CyberSecurityCloud-HighSecurityOWASPSet-',
    //   },
    //   name: 'CyberSecurityCloud-HighSecurityOWASPSet-',
    //   statement: {
    //     managedRuleGroupStatement: {
    //       vendorName: 'Cyber Security Cloud Inc.',
    //       name: 'CyberSecurityCloud-HighSecurityOWASPSet-',
    //     },
    //   },
    // }]
    //

    // WebACLを作成
    const webAcl = new WafConstruct(this, cdk.Stack.of(this).stackName + 'WebAcl', {
      scope: props.scope,
      overrideAction_CommonRuleSet: props.overrideAction_CommonRuleSet,
      overrideAction_KnownBadInputsRuleSet: props.overrideAction_KnownBadInputsRuleSet,
      overrideAction_AmazonIpReputationList: props.overrideAction_AmazonIpReputationList,
      overrideAction_LinuxRuleSet: props.overrideAction_LinuxRuleSet,
      overrideAction_SQLiRuleSet: props.overrideAction_SQLiRuleSet,
      ruleAction_IPsetRuleSet: props.ruleAction_IPsetRuleSet,
      ruleAction_BasicRuleSet: props.ruleAction_BasicRuleSet,
      allowIPList: props.allowIPList,
      basicAuthUserName: props.basicAuthUserName,
      basicAuthUserPass: props.basicAuthUserPass,
      // additionalRules,
      wafLogBucketLifecycleRules: props.wafLogBucketLifecycleRules,
      logRemovalPolicyParam: props.logRemovalPolicyParam,
      csirtWAFParam: props.csirtWAFParam,
    });
    this.webAcl = webAcl.webAcl;
  }
}
