import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { DbAuroraStack } from '../lib/stack/db-aurora-stack';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import { WafAlbStack } from '../lib/stack/waf-alb-stack';
import { ElastiCacheStack } from '../lib/stack/elasticache-stack';
import { IConfig } from '../params/interface';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { EcsAppStack } from '../lib/stack/ecs-app-stack';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { InfraResourcesPipelineStack } from '../lib/stack/pipeline-infraresources-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { EfsStack } from '../lib/stack/efs-stack';
import { BackupVaultStack } from '../lib/stack/backup-vault-stack';
import { BackupPlanStack } from '../lib/stack/backup-plan-stack';
import { KMSStack } from '../lib/stack/kms-stack';
import { SendGridLogStack } from '../lib/stack/sendgrid-log-stack';

// Account and Region on test
//  cdk.process.env.* returns undefined, and cdk.Stack.of(this).* returns ${Token[Region.4]} at test time.
//  In such case, RegionInfo.get(cdk.Stack.of(this).region) returns error and test will fail.
//  So we pass 'ap-northeast-1' as region code.
const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjPrefix = 'BLEA';

const envKey = 'dev';

const config: IConfig = require('../params/' + envKey);

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

describe(`${pjPrefix} Guest Stacks`, () => {
  let app: cdk.App;
  let templates: { [key: string]: Template };

  let shareResources: ShareResourcesStack;
  let infraPipeline: InfraResourcesPipelineStack;
  let wafCloudfront: WafCfStack;
  let oidc: OidcStack;
  let ecs: EcsAppStack;
  let wafAlb: WafAlbStack;
  let cloudfront: CloudfrontStack;
  let dbCluster: DbAuroraStack;
  let monitor: MonitorStack;
  let opensearch: OpenSearchStack;
  let elasticache: ElastiCacheStack;
  let efs: EfsStack;
  let backupVault: BackupVaultStack;
  let sendgrid: SendGridLogStack;
  let appKeyDRRegion: KMSStack;
  let backupVaultDRRegion: BackupVaultStack;
  let backupPlan: BackupPlanStack;

  beforeEach(() => {
    app = new cdk.App();
    templates = {};
  });

  test('GuestAccount ECS App Stacks', () => {
    // Slack Notifier
    const workspaceId = config.NotifierParam.workspaceId;
    const channelIdMon = config.NotifierParam.channelIdMon;

    // Share resources
    shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
      pjPrefix,
      notifyEmail: config.NotifierParam.monitoringNotifyEmail,
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: workspaceId,
      channelId: channelIdMon,
      ...config.CognitoParam,
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    // Infrastructure pipeline
    infraPipeline = new InfraResourcesPipelineStack(app, `${pjPrefix}-Pipeline`, {
      ...config.InfraResourcesPipelineParam,
      pipelineBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      envKey,
      env: getProcEnv(),
      appKey: shareResources.appKey,
    });

    wafCloudfront = new WafCfStack(app, `${pjPrefix}-WafCloudfront`, {
      scope: 'CLOUDFRONT',
      env: {
        account: getProcEnv().account,
        region: 'us-east-1',
      },
      crossRegionReferences: true,
      ...config.WafParam,
      wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      csirtWAFParam: config.CSIRTWAFParamCF,
    });

    oidc = new OidcStack(app, `${pjPrefix}-OIDC`, {
      OrganizationName: config.OidcParam.OrganizationName,
      RepositoryNames: config.OidcParam.RepositoryNames,
      env: getProcEnv(),
    });

    ecs = new EcsAppStack(app, `${pjPrefix}-ECS`, {
      myVpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      prefix: pjPrefix,
      albBgParam: config.AlbBgParam,
      albParam: config.AlbParam,
      ecsFrontTasks: config.EcsFrontTasks,
      ecsFrontBgTasks: config.EcsFrontBgTasks,
      ecsBackBgTasks: config.EcsBackBgTasks,
      ecsBackTasks: config.EcsBackTasks,
      env: getProcEnv(),
      crossRegionReferences: true,
      bastionParams: config.BastionParam,
      otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      albAccessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
    });

    wafAlb = new WafAlbStack(app, `${pjPrefix}-WafAlb`, {
      scope: 'REGIONAL',
      associations: [ecs.app.frontAlb.appAlb.loadBalancerArn],
      env: getProcEnv(),
      crossRegionReferences: true,
      ...config.WafAlbParam,
      wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      csirtWAFParam: config.CSIRTWAFParamALB,
    });

    cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
      pjPrefix: pjPrefix,
      webAcl: wafCloudfront.webAcl,
      CertificateIdentifier: config.CertificateIdentifier,
      cloudFrontParam: config.CloudFrontParam,
      appAlbs: [ecs.app.frontAlb.appAlb],
      preSharedKey: wafAlb.preSharedKey,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      env: getProcEnv(),
      crossRegionReferences: true,
      accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
    });
    cloudfront.addDependency(wafAlb);

    // Aurora
    dbCluster = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
      vpc: shareResources.myVpc,
      vpcSubnets: shareResources.myVpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
      // appServerSecurityGroup: ecs.app.backEcsAppsBg[0].securityGroupForFargate,
      bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      ...config.AuroraParam,
      env: getProcEnv(),
    });

    // Monitor stack: dashboard
    monitor = new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
      pjPrefix: `${pjPrefix}`,
      dashboardName: `${pjPrefix}-ECSApp`,
      cfDistributionId: cloudfront.cfDistributionId,
      albFullName: ecs.app.frontAlb.appAlb.loadBalancerFullName,
      appTargetGroupNames: ecs.app.frontAlb.AlbTgs.map((AlbTg) => AlbTg.lbForAppTargetGroup.targetGroupName),
      albTgUnHealthyHostCountAlarms: ecs.app.frontAlb.AlbTgs.map((AlbTg) => AlbTg.albTgUnHealthyHostCountAlarm),
      ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
      ecsAlbServiceNames: ecs.app.frontEcsApps.map((ecsAlbApp) => ecsAlbApp.ecsServiceName),
      ecsInternalServiceNames: ecs.app.backEcsApps.map((ecsInternalApp) => ecsInternalApp.ecsServiceName),
      dbClusterName: dbCluster.dbClusterName,
      // AutoScaleはCDK外で管理のため、固定値を修正要で設定
      ecsScaleOnRequestCount: 50,
      ecsTargetUtilizationPercent: 10000,
      env: getProcEnv(),
    });

    opensearch = new OpenSearchStack(app, `${pjPrefix}-OpenSearch`, {
      vpc: shareResources.myVpc,
      appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
      bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      openSearchType: config.OpensearchTypeParam,
      openSearchProps: config.OpensearchParam,
      env: getProcEnv(),
    });

    elasticache = new ElastiCacheStack(app, `${pjPrefix}-ElastiCache`, {
      pjPrefix,
      elastiCacheType: config.ElastiCacheTypeParam,
      myVpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      alarmTopic: shareResources.alarmTopic,
      appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
      // appServerSecurityGroup: ecs.app.backEcsAppsBg[0].securityGroupForFargate,
      bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      ElastiCacheParam: config.ElastiCacheParam,
      logGroupRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
      env: getProcEnv(),
    });

    efs = new EfsStack(app, `${pjPrefix}-Efs`, {
      pjPrefix,
      vpc: shareResources.myVpc,
      appKey: shareResources.appKey,
      appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
      bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      ...config.EfsParam,
      env: getProcEnv(),
    });

    backupVault = new BackupVaultStack(app, `${pjPrefix}-BackupVault`, {
      env: getProcEnv(),
      appKey: shareResources.appKey,
    });

    sendgrid = new SendGridLogStack(app, `${pjPrefix}-SendGridLog`, {
      env: getProcEnv(),
      pjPrefix: pjPrefix.toLowerCase(),
      sendGridLogParams: config.SendGridLogParams,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      lifecycleRules: config.s3AuditLogLifecycleRules,
    });

    if (config.BackupParam.backupDisasterRecovery) {
      // DR用リージョンにKMSキーを作成
      appKeyDRRegion = new KMSStack(app, `${pjPrefix}-AppKeyDRRegion`, {
        pjPrefix,
        env: {
          account: getProcEnv().account,
          region: config.DRRegionParam.region,
        },
        removalPolicyParam: config.LogRemovalPolicyParam,
        kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
        crossRegionReferences: true,
      });

      // DR用リージョンにバックアップボールトを作成
      backupVaultDRRegion = new BackupVaultStack(app, `${pjPrefix}-BackupVaultDRRegion`, {
        env: {
          account: getProcEnv().account,
          region: config.DRRegionParam.region,
        },
        appKey: appKeyDRRegion.kmsKey,
        crossRegionReferences: true,
      });

      // DR用リージョンと東京リージョンに作成されたバックアップボールトを指定して、バックアッププランを作成
      backupPlan = new BackupPlanStack(app, `${pjPrefix}-BackupPlan`, {
        env: getProcEnv(),
        vault: backupVault.vault,
        secondaryVault: backupVaultDRRegion.vault,
        backupSchedule: config.BackupParam.backupSchedule,
        retentionPeriod: config.BackupParam.retentionPeriod,
        crossRegionReferences: true,
      });
    } else {
      // 東京リージョンに作成されたバックアップボールトを指定して、バックアッププランを作成
      backupPlan = new BackupPlanStack(app, `${pjPrefix}-BackupPlan`, {
        env: getProcEnv(),
        vault: backupVault.vault,
        backupSchedule: config.BackupParam.backupSchedule,
        retentionPeriod: config.BackupParam.retentionPeriod,
      });
    }

    // Tagging "Environment" tag to all resources in this app
    const envTagName = 'Environment';
    cdk.Tags.of(app).add(envTagName, envKey);

    const stacksToSnapshotTest = {
      shareResources,
      infraPipeline,
      wafCloudfront,
      oidc,
      ecs,
      wafAlb,
      cloudfront,
      dbCluster,
      monitor,
      opensearch,
      elasticache,
      efs,
      backupVault,
      sendgrid,
      ...(appKeyDRRegion && { appKeyDRRegion }),
      ...(backupVaultDRRegion && { backupVaultDRRegion }),
      ...(backupPlan && { backupPlan }),
    };

    Object.entries(stacksToSnapshotTest).forEach(([name, stack]) => {
      if (stack) {
        templates[name] = Template.fromStack(stack);
        expect(templates[name]).toMatchSnapshot(`${name} Stack`);
      }
    });
  });
});
