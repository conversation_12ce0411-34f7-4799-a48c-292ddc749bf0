{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"version": {"type": "string", "description": "Version of the schema"}, "triggers": {"type": "object", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string", "enum": ["cron", "api"], "description": "Type of trigger"}, "inputs": {"type": "object", "additionalProperties": {"type": "object", "properties": {"commands": {"type": "array", "items": {"type": "object", "properties": {"containerName": {"type": "string", "description": "Name of the container"}, "passedCommand": {"type": "array", "items": {"type": "string"}, "description": "Commands to be passed to the container"}}, "required": ["containerName", "passed<PERSON><PERSON>mand"]}}}, "required": ["commands"]}, "description": "Input commands for the trigger", "default": []}, "cron": {"type": "object", "properties": {"minute": {"type": "string", "description": "Specifies the minute field of the cron expression"}, "hour": {"type": "string", "description": "Specifies the hour field of the cron expression"}, "date": {"type": "string", "description": "Specifies the date field of the cron expression"}, "month": {"type": "string", "description": "Specifies the month field of the cron expression"}, "dayOfWeek": {"type": "string", "description": "Specifies the day of week field of the cron expression"}, "year": {"type": "string", "description": "Specifies the year field of the cron expression"}}, "additionalProperties": false, "nullable": true}, "state": {"type": "string", "enum": ["DISABLED", "ENABLED"], "description": "State of the trigger", "nullable": true}, "apiPath": {"type": "string", "description": "API endpoint path", "nullable": true}}, "required": ["type"], "additionalProperties": false, "oneOf": [{"properties": {"type": {"const": "cron"}, "cron": {"type": "object"}, "state": {"type": "string"}}, "required": ["cron", "state"]}, {"properties": {"type": {"const": "api"}, "apiPath": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>"]}]}}}, "required": ["version", "triggers"]}