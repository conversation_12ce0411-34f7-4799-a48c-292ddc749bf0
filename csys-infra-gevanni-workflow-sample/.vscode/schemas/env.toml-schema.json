{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"env": {"type": "object", "properties": {"name": {"type": "string", "title": "name"}, "service_prefix": {"type": "string", "title": "service_prefix"}}, "required": ["name", "service_prefix"], "additionalProperties": false}, "tasks": {"type": "object", "additionalProperties": {"type": "object", "properties": {"definition": {"type": "object", "properties": {"name": {"type": "string", "title": "name"}, "spec": {"type": "object", "properties": {"cpu": {"type": "number", "title": "cpu"}, "memory": {"type": "number", "title": "memory"}}, "required": ["cpu", "memory"], "additionalProperties": false, "title": "spec"}}, "required": ["name", "spec"], "additionalProperties": false, "title": "definition"}, "build": {"type": "object", "additionalProperties": {"type": "object", "properties": {"context": {"type": "string", "title": "context"}, "image_placeholder": {"type": "string", "title": "image_placeholder"}, "dockerfile": {"type": "string", "title": "dockerfile"}}, "required": ["context", "image_placeholder"], "additionalProperties": false}, "title": "build"}}, "required": ["definition", "build"], "additionalProperties": false}}}, "required": ["env", "tasks"], "additionalProperties": false}