## How To Exceute To ECS Task

### Overview

- This script is used to interact with AWS ECS Cluster via AWS SSM Parameter Store.
- The script performs the following steps:
- Retrieve the ECS Cluster name from an SSM Parameter.
- Get a list of task IDs in the cluster and display them for selection.
- After selection, choose "ecs execute-command" or "ssm start-session" to execute into the container in the selected task.

### Prerequisites

Ensure the following settings are completed

- [Installing necessary resources](HowToSetup.md#installing-necessary-resources)
- [Granting execution permissions to the execution environment](HowToSetup.md#granting-execution-permissions-to-the-execution-environment)

### Usage

1. Go to the directory containing exec_container.

   ```sh
   cd 01_Rolling/build_{app_name}/
   ```

   **example**

   ```sh
   cd 01_Rolling/build_backend/
   ```

1. Edit each setting value in the environment file.  
   **{environment name}.ini**

   ```ini
   [Env]
   ENV=Stg01
   PJ_PREFIX=GEVANNI
   SERVICE_PREFIX=sample01-dev01-ab

   [EcsApp]
   APP_NAME=EcsApp
   ...
   ```

1. Execute Command into Container

- Run the script with the SSM Parameter name that contains the ECS Cluster name:

  ```sh
  python exec_container.py <environment>
  ```

  After running the script, select ecs task to continue.

  Next you can choose:

  1. ecs execute-command
     To execute only arbitrary commands without container login.
  1. ssm start-session
     Logging into a container use SSM start session.

  **example**

  ```sh
  python exec_container.py dev
  ```

  <img width="1170" alt="ecs execute-command" src="../images/ecs-execute-command.png">
  <img width="1181" alt="ssm start-session" src="../images/ecs-ssm-start-sesstion.png">

- The script will display a list of task IDs in the ECS Cluster and prompt you to select a task to exec into the container.
