# HowToSwitchRoleAndConfigLocal

This section describes how to execute scripts on a local PC using the credentials of an IAM role.

## Reader
- External users of Mynavi

## Procedure

1. Operation with mynavicsys-vendor-bastion account
      1. Login in IAM user
      1. Start CloudShell
        ![](../images/open-cloudshell.png)
      1. Run AWS CLI and switch to IAM role for each service
          ```
          aws sts assume-role --role-arn <role-arn-to-switch> --role-session-name AWSCLI-Session
          ```
      1. Get temporary credentials
      1. Show command to set temporary credential on local PC.
1. operation on each member's local PC 1. set temporary credentials
      1. Set temporary credentials
         - Execute the following command on each member's local PC.
  
          ````
          export AWS_ACCESS_KEY_ID="XXXX”
          export AWS_SECRET_ACCESS_KEY="XXXX”
          export AWS_SESSION_TOKEN="XXXX”
          ```` 

      1. The bastion container startup scripts and container deployment scripts can be executed on the local PC.