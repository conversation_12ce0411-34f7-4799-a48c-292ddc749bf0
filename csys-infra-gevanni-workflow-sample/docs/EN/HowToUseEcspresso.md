# HowToUseEcspresso

This section describes how to use Ecspresso conf

## Contens

1. Overview
    1. [Overview](#overview)
    1. [File structure](#overview-of-ecs-deployment-related-files)
1. Steps
    1. [Flow 1：When updating the application](#flow-1updating-the-application)
    1. [Flow 2: Update container count, etc. without app update](#flow-2-update-container-count-etc-without-app-update)  
    ※Refer to HowToUseEcspressoFirstStep.md for the preliminary steps of each flow.

## Overview

- Deployment to the ECS service is done using CodePipeline.
- There are two main overall deployment patterns.

  1. Run from GitHubActions
     - Use this procedure **Flow 1：When updating the application**
  1. Run update_ecspresso_conf\.py from your PC
     - Use this procedure **Flow 2: Update container count, etc. without app update**

- **i. Run from GitHubActions** is as follows

  1. Run GitHubActions from the application team repository
  1. Run build\.py from the GHA workflow file
  1. In build\.py docker build is executed and the image is pushed to ECR
  1. In build\.py, create/update secret value to AWS Secret Manager, and update secret ARN in ecs task definition json file
  1. In build\.py, configuration files such as task definitions are compressed into image.zip and deployed to the S3 bucket
  1. The pipeline is triggered by the S3 deployment.
  1. In CodeBuild, the ecspresso command is executed to deploy to the ECS service.
     ![](../images/gevanni-ecspresso-pipeline.dio.png)

- **ii. Run update_ecspresso_conf\.py from your PC** is as follows

  1. Run `update_ecspresso_conf.py` on your local PC
  1. Download `image.zip` from S3 bucket to local PC by python script
  1. Unzip the `image.zip` and get the ecspresso conf by python script
  1. Edit ecspresso conf and environment files by manually
  1. After editing, recompress them and upload them on S3 by python script

  - ecspresso conf is the configuration file related to ecspresso (ECS services, ECS tasks, etc.)  
     ![](../images/gevanni-ecspresso-pipeline-outline.dio.png)

## Overview of ECS Deployment-Related Files

- The following three related files are used to deploy ecspresso

  1. workflow files (all files under `.github` directory)
  1. build related files ( `build.py` under `01_Rolling` directory)
  1. update-related files (`update_ecspresso_conf.py` and `update_secret.py` under `01_Rolling` directory)

- Related files are maintained in a repository at [here](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample), and the overall directory structure is as follows

  ```
  .github
  ├── workflows
  │   ├── run-build-backend.yml
  │   └── run-build-frontend.yml
  │
  01_Rolling
  ├── build_frontend
  │   ├── build.py
  │   ├── dev.ini
  │   ├── stg.ini
  │   ├── prod.ini
  │   ├── update_ecspresso_conf.py
  │   └── update_secret.py
  │
  ├── build_frontend
      ├── build.py
      ├── dev.ini
      ├── stg.ini
      ├── prod.ini
      ├── update_ecspresso_conf.py
      └── update_secret.py

  ```

### i.Workflow Files

- A workflow file is a file to execute GitHubActions under `.github/workflows`.
- `build.py` is executed through this workflow file.

### ii.Build Related Files

- Build-related files are `build.py` and configuration files under `01_Rolling`.
- Application builds and operations on AWS resources are performed through this shell.

### iii.Update-Related Files

- Update files are `update_ecspresso_conf.py` and `update_secret.py` under the `01_Rolling` directory and environment configuration files.
- `update_ecspresso_conf.py` is used for updating ECS tasks and ECS services, and `update_secret.py` is used for registering secrets to Secrets Manager.

## Flow for each use case

- There are two main patterns: **updating the app** and **updating the number of containers without updating the app**
- As mentioned above, GitHubAction is used for **updating the app**, while `update_ecspresso_conf.py` and `update_secret.py` are used for **updating the number of containers without updating the app** from a local PC.
- When deploying for the first time, **be sure to perform Flow** 1 due to Gevanni's specifications. 

### Flow 1：Updating the application

- Assumed case
  - Application code changes
  - Dockerfile changes

#### Preliminary Preparation

- Since the folders placed in the administrative repository are sample code, it is necessary to implement customization for each PJ after cloning them to the application's repository.

  ##### Environment files (dev,stg,prod)

  - In build\.py, a process is implemented to retrieve the relevant resource names of the pipeline
  - Modify `SERVICE_PREFIX` to the service ID of each service to identify the deployed service.
    - The service ID will be shared by the infrastructure team after the Gevanni Create New Service request.
  - In addition, the path `FILE_PATH` of the Dockerfile needs to be specified in order to perform docker build in the relevant shell.
    - The path should be set relative to `build.py`.
  - The following two variables do not need to be changed in the environment provided for the app team.

    - `PJ_PREFIX`.
      - To identify the name of the stack being deployed.
      - Fixed with `GEVANNI`.
    - `APP_NAME`.
      - Identifies the container name.
      - Fixed with the following values
        - frontend: `EcsApp` backend: `EcsBackend`.
        - Backend: `EcsBackend` Fixed with `GEVANNI`.

    **environment.ini**

    ```ini
    Env
    # # Prefix specified in the stack
    ENV=Stg01
    PJ_PREFIX=GEVANNI
    SERVICE_PREFIX=sample01-stg01-ab

    EcsApp
    # Fill in the APP_NAME specified on the CDK
    APP_NAME=EcsApp

    Docker
    # Path of the Dockerfile
    # Relative path from docker build execution directory
    FILE_PATH=app/Dockerfile.dev
    ```

#### Deployment Work

- Deployment is performed via GitHubAction.
- Due to Gevanni's specifications, the first time you deploy, be sure to follow the steps in Flow 1.
- For detailed instructions, please refer to [.github/workflows/README.md](../../.github/workflows/README_ja.md)

### Flow 2: Update container count, etc. without app update

- First of all, configuration files for ECS services and tasks are managed in S3 on the pipeline. Therefore, unlike the case of **Updating the application** in Flow 1, the policy is to use `update_ecspresso_conf.py` rather than GitHubAction to get the configuration files from S3.
- In addition, when environment variables are used on the app team side, the AWS Secrets Manager is used. The policy is to use AWS CLI commands for registration instead of the management console, but since manual registration one by one leads to operational errors, so`update_secret.py` is used for registration and updating.

- Assumed case
  - [Change of Health check setting](#how-to-change-health-check)
  - [Change of AutoScale setting](#how-to-change-autoscaling)
  - [Change of container specs, etc.](#how-to-change-container-spec-ect)
  - [Change of Capacity Provider](#how-to-change-capacityprovider)
  - [Registration of environment variables](#how-to-register-environment-variables)

#### Preparation

- Download image.zip from S3 bucket using `update_ecspresso_conf.py`.
- The flow is as follows

  1. Run `python update_ecspresso_conf.py {environment name}`.
  1. Choose whether you want to download the image template.zip from the S3 bucket or upload it to the S3 bucket.  
     Here, enter `1` to get the ecspresso conf.  
      ![](../images/update-s3-operetion.png)
  1. ecspresso_conf folder is created in the same directory and the downloaded ecspresso conf is stored in it. Basically, make changes to `ecs-service-def.json` and `ecs-task-def.json`. These are configuration files corresponding to ECS services and ECS tasks.  
     ![](../images/update-download-files.png)

  1. Edit the ecspresso conf and environment files according to each pattern described below.

#### How To Change Health Check

- Since health checks are performed differently in front-end and back-end containers, the method of updating the settings is also different.
- The front-end container is configured from the environment file, and the back-end container is configured from `ecs-task-default.json`.  
  ![](../images/difference-between-front-and-back.dio.png)

  ##### How to change the front end

  - Change the following items in the environment file.

    **environment.ini**

    ```ini
    HealthCheck
    # HealthCheck Path
    HEALTH_PATH=/
    # Time to delay the first abnormality determination after starting a health check (range:0~2,147,483,647seconds)
    GRACE_PERIOD=10
    # Interval (in seconds) at which health checks are performed(range:5~300seconds)
    INTERVAL=30
    # Time (in seconds) without response from the target that the health check is considered a failure(range:2~120seconds)
    TIMEOUT=5
    # The number of consecutive successful health checks required for a target to be considered healthy (range: 2-10count)
    HEALTHY_THRESHOLD_COUNT=5
    # The number of consecutive failed health checks required for a target to be considered unhealthy (range: 2-10count)
    UNHEALTHY_THRESHOLD_COUNT=2
    ```

  ##### How to change the back end

  - You need to change `healthCheck` in `ecs-task-def.json`

    **ecs-task-def.json**

    ```json
    "healthCheck": {
      "command":
          "CMD-SHELL",
          "curl -f http://localhost/ || exit 1"
      ,
      "interval": 30,
      "timeout": 5,
      "startPeriod": 5
    }
    ```

#### How to change CapacityProvider

- Auto-scaling changes should be made from the environment file for both frontend and backend.
- Fargate Spot
  - Utilizes AWS's spare capacity, offering up to a 70% discount.
  - Tasks may be interrupted if spare capacity is no longer available.
- base
  - Minimum number of tasks.
  - Only one of Fargate or Fargate Spot can be set to 1 or more.
- weight
  - Task launch ratio.
  - When tasks above the base number are launched, Fargate or Fargate Spot will be launched to approach the set ratio.
  - If not using Fargate Spot, set `FARGATE_SPOT_WEIGHT` to 0.

  **Environment name (dev/stg/prod).ini**
  ```ini
  [CapacityProvider]
  FARGATE_BASE=2
  FARGATE_WEIGHT=1
  FARGATE_SPOT_BASE=0
  FARGATE_SPOT_WEIGHT=2
  ```

#### How to Change AutoScaling

- You can change AutoScaling setting from `environment.ini` for both frontend and backend

  **environment.ini**

  ```ini
  AutoScale
  # Minimum number of tasks
  MIN_CAPACITY=2
  # Minimum number of tasks
  MAX_CAPACITY=5
  # If the burden of task is over TARGET_VALUE, the number of tasks increase vice versa
  TARGET_VALUE=60
  ```

### How to change container spec ect

- Change settings other than health check and AutoScaling from `ecs-task-def.json` and `ecs-service-def.json`.
  \*Change from JSON files for both front-end and back-end
- Configuration changes related to ECS services are made from`ecs-service-def.json`,configuration changes related to ECS tasks are made from `ecs-task-def.json`.

#### How to Register environment variables

- Register and update environment variables (Secrets Manager) using `update_secret.py`.
- As mentioned above, an error occurs when deploying for the first time from Flow 1. This is due to the fact that the Secrets Manager value set in `ecs-task-def.json` is a sample value.
- It is necessary to register the environment variables actually used using `update_secret.py` and reflect them in `ecs-task-def.json` as well.

#### Preparation

1. Execute `python update_secret.py {environment name}`.
1. `secret_list.json` will be added to the same directory.
1. Edit the JSON file as follows.

#### Registration

1. Execute `python update_secret.py {environment name}`.
1. When secret registration is completed, the JSON file will be deleted.

- Write the secret you wish to register in the key-value format.
- If the secret is no longer needed, delete all contents of the JSON file (empty file the first time the secret is acquired).

  **secret_list.json**

  ```json
  {
    "username": "Gevanni",
    "password": "p@ssw0rd",
    "dbname": "user1"
  }
  ```

  On the console it is registered as follows  
  ![](../images/secret_console.png)

- After the secret is registered, set environment variables in `ecs-task-def.json`.

  **ecs-task-def.json**

  ```json
  "secrets": [
      {
        # Set variable names to be used within the container
        "name": "DB_USER",
        # <SECRET_ARN>:{Secret Key}::
        "valueFrom": "<SECRET_ARN>:username::"
      },
      {
        "name": "DB_NAME",
        "valueFrom": "<SECRET_ARN>:dbname::"
      }
  ]
  ```

- When uploading to the S3 bucket, the secret ARN is replaced with the actual value.

#### Deployment work

- Upload the `image.zip` to the S3 bucket using `update_ecspresso_conf.py`.
- The pipeline is triggered by the deployment to the S3 bucket.
- The flow is as follows
  1. Run `python update_ecspresso_conf.py {environment name}`.
  1. Choose whether to download the image\.zip from the S3 bucket or upload it to the S3 bucket.  
     Here, enter `2` to start the pipeline.  
      ![](../images/update-s3-operetion2.png)
  1. You will be asked for a final confirmation before starting the pipeline, so enter the command while confirming it.  
     ![](../images/update-final-check.png)
  1. When the upload to the S3 bucket is completed, the ecspresso_conf folder will be deleted.
  1. The pipeline is triggered by the placement in the S3 bucket.
