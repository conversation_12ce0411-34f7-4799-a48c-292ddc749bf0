# HowToUseBastionContainer

This section describes how to use the bastion container.

## Summary
- Connect to the following resources via the bastion container
  - DB(TiDB Cloud)
  - Backend Container
  
- In order to use the bastion container, the AWS CLI and SSM plugin must be installed in the execution environment.
- In addition, process monitoring logic is implemented in the bastion container.the logic is as follows
  - Check the process of the SSM agent in the container every 30 minutes, and the process switches the LOGIN_USER flag depending on the number of SSM agent processes, i.e., the number of SSM user logins.
  - If the number of logins is zero 30 minutes after the LOGIN_USER flag becomes “no exist”, the script is terminated and the container is stopped.
  - The process flow diagram is as follows.  
![](../images/bastion-shell-logic.dio.png)
  
## Advance Preparation: Switch Role Settings to IAM Roles
### For MYNAVI internal users

- Setup Method: [CLI switch role after CLI login with AWS SSO](https://mynavi.docbase.io/posts/3230428)
- Information required for switch role configuration
  - Switch Source
    - Common to PRODUCTION/STG Environment
      - SSO Role Name: AssumeRoleOnlyAccess
    - STG Environment
      - AWS Account ID: ************
      - AWS Account Name: mynavi_csys-gevanni-orchestrator-stg01
  - Switch Destination
    - Arn of IAM Role: Coordination with application team leaders at the time of environment handover

### For Mynavi external users

- Setup Method: [CLI switch role after login](HowToSwitchRoleAndConfigLocal.md)

## How to Activate a Bastion Container
1.  Edit each setting value in the environment file.  
    **bastion/{environment name}.ini**
    ```ini
    [Env]
    ENV=Stg01
    PJ_PREFIX=GEVANNI
    SERVICE_PREFIX=sample01-stg01-ab

    [EcsApp]
    BACK_APP_NAME=EcsBackend

    [TiDB]
    USERNAME=root
    ```
1. Install AWS CLI and SSM plug-ins in the execution environment.
    - See the following link for AWS CLI installation instructions  
      https://docs.aws.amazon.com/ja_jp/cli/latest/userguide/getting-started-install.html
    - See the following link for SSM plugin installation instructions  
      https://docs.aws.amazon.com/ja_jp/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html
1. Start with `python run_task.py {environment name}`

1. When the startup is complete, the following login command is output, and executing it completes the login of the bastion container.  
    **Login command output after run_task.py is executed**  　*The output command changes with each script execution.
    ```
    -------------------------------------------
    Command to login to a bastion container
    aws ssm start-session --region ap-northeast-1 --target ecs:Stg01-GEVANNI-common-BastionEcsCommon-BastionEcsCommonConstructBastionEcsCluster3733E5BE-6sqD3UNZ3wKq_a065a013ae404dfa984db8a29bbbd98d_a065a013ae404dfa984db8a29bbbd98d-3822224002
    -------------------------------------------
    ```

## How to Connect to Each Resource
### TiDB
  - Make a connection using the MySQL client. The command is as follows.
  - `mysql --host TiDBEndpoint --port 4000 -u username -p`

### Backend Container
  - Specify the DNS and confirm the connection. The command is as follows.
  - `curl -X GET http://DNSRecordName/API_Path:PortNumber/`
