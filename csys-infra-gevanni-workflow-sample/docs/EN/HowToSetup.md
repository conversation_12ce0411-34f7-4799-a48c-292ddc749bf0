# HowToSetup

This section describes the preliminary preparation steps required to run the script.

## Overview

- [Copying files from the gevanni-workflow-sample repository to the app repository](#copying-files-from-the-gevanni-workflow-sample-repository-to-the-app-repository)
- [Handling changes in directory structure](#handling-changes-in-directory-structure)
- [Installing necessary resources](#installing-necessary-resources)
- [Granting execution permissions to the execution environment](#granting-execution-permissions-to-the-execution-environment)

## Copying files from the gevanni-workflow-sample repository to the app repository

【When operating via GUI.】

1. Download the code from the gevanni-workflow-sample repository to your local machine  
   ![](../images/github-download.png)
2. Extract the zip file.
3. Drag and drop the files you want to add to the app repository for upload  
   ![](../images/github-upload.png)  
   ![](../images/github-draganddrop.png)

※If you have already cloned the application repository, you can also copy it by drag-and-drop on a local folder.

## Handling changes in directory structure

There are several places in the script where file paths are specified. If the script location is changed from the sample, the path should also be changed.

### application container

【To change the location of build.py】

- Modify the execution command in ecs-deploy-frontend.yml or ecs-deploy-backend.yml.
- The path is an absolute path from root to build.py.

  **(例).github/workflows/ecs-deploy-frontend.yml**

  ```yml
  - name: Run frontend build.py
    run: python ./01_Rolling/build_frontend/build.py ${{env.environment}}
  ```

【To specify the Dockerfile path】

- Correct the Dockerfile path in the configuration files (dev.ini/stg.ini/prod.ini).
- The path should be relative to build.py.

  **(例)01_Rolling/build_frontend/stg.ini**

  ```ini
  [Docker]
  FILE_PATH=../app/Dockerfile
  ```

## Installing necessary resources

Install the following resources

> [!NOTE]  
> For Windows PC users within Mynavi  
> AWS CLI, Session Manager plugin, and Python 3.12 can be installed from “Add Software” on the desktop without PC administrator privileges.

### AWS CLI

- Follow the steps here.  
  [Installing or updating to the latest version of the AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)

### Session Manager plugin

- Follow the steps here.  
  [Install the Session Manager plugin for the AWS CLI](https://docs.aws.amazon.com/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html)

### Python 3.12

- Download the Python 3.12 package from the link below  
  [https://www.python.org/](https://www.python.org/)

### boto3

- Go directly under root and execute the following command to install boto3.
  ```sh
  pip install -r requirements.txt
  ```

## Granting execution permissions to the execution environment

Grant permissions to allow local operations by following the steps below

- For Mynavi internal users, [HowToSwitchRoleAndConfigLocalForMynavi.md](HowToSwitchRoleAndConfigLocalForMynavi.md)
- For external Mynavi users and vendors,[HowToSwitchRoleAndConfigLocal.md](HowToSwitchRoleAndConfigLocal.md)
