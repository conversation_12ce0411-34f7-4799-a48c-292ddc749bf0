# HowToUseEcspressoFirstStep

Since HowToUseEcspresso.md has a lot of explanations, this section describes only the necessary actions for first-time deployments.

## Overview

- There are two main overall deployment patterns.

  1. Run from GitHubActions
     - Use this procedure Flow 1：When updating the application
  1. Run update_ecspresso_conf\.py from your PC
     - Use this procedure Flow 2: Update container count, etc. without app update

## Flow 1：When updating the application

- To run GHA for the first time, the environment must be prepared in advance.
- Ensure all prerequisite settings are completed before proceeding with the operations.

### Prerequisites

Ensure the following settings are completed

- [Copying files from the gevanni-workflow-sample repository to the app repository](HowToSetup.md#copying-files-from-the-gevanni-workflow-sample-repository-to-the-app-repository)
- [Handling changes in directory structure](HowToSetup.md#handling-changes-in-directory-structure)
- [Register an OIDC-specific IAM role in GitHubVariables.](../../.github/workflows/README_en.md#ii-register-an-oidc-specific-iam-role-in-githubvariables)  
   ※Once the OIDC registration is completed, no further registration is required.

### Operation

- Execute Deploy ECS Frontend or Deploy ECS Backend from GHA to initiate the CodePipeline process.

### Error
- Since the default health check path is set to `http://localhost/`, if it is set to a different path, the deployment will succeed but the health check will fail. In such cases, you need to modify the health check path in Flow 2.

## Flow 2: Update container count, etc. without app update

- After the operations in Flow 1 are complete, execute Flow 2.

### Prerequisites

Ensure the following settings are completed

- [Installing necessary resources](HowToSetup.md#installing-necessary-resources)
- [Granting execution permissions to the execution environment](HowToSetup.md#granting-execution-permissions-to-the-execution-environment)

### Operation

1. Retrieve the `ecs-task-def.json`. (Front container ver.)

   1. Navigate to the `01_Rolling/build_frontend` or `01_Rolling/build_backend` directory.

   1. Run `python update_ecspresso_conf.py {environment_name(dev/stg/prod)}`.

   1. Choose whether to download image.zip from the S3 bucket or upload it to the S3 bucket.  
      Here, enter 1 to retrieve the ecspresso configuration.  
      ![](../images/update-s3-operetion.png)

   1. An `ecspresso_conf` folder will be created in the same directory, and edit the `ecs-task-def.json` file within that folder.  
      ![](../images/update-download-files.png)

1. Modify the health check path.
   - For the frontend:
      - Modify the `HEALTH_PATH` in the environment files (dev.ini/prod.ini/stg.ini) located in `01_Rolling/build_frontend/`.
      - If necessary, modify other parameters or the `ecs-task-def.json` as well.
   - For the backend:
      - Modify the `http://localhost/` in the `ecs-task-def.json` file located in the `ecspresso_conf` folder obtained in the previous step.
         ```json
            "healthCheck": {
               "command": [
                  "CMD-SHELL",
                  "curl -f http://localhost/ || exit 1"
               ],
               "interval": 30,
               "timeout": 5
               },
         ```
      - If necessary, modify the environment files or the `ecs-task-def.json` as well.

1. Restart CodePipeline.

   1. Navigate to the `01_Rolling/build_frontend` or `01_Rolling/build_backend` directory.
   1. Run `python update_ecspresso_conf.py {environment_name(dev/stg/prod)}`.

   1. Choose whether to download image.zip from the S3 bucket or upload it to the S3 bucket.  
      Here, enter 2 to start the pipeline.  
      ![](../images/update-s3-operetion2.png)

   1. Since a final confirmation is required before starting the pipeline, enter the commands while verifying.  
      ![](../images/update-final-check.png)

