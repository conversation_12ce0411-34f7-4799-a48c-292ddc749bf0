# How To TiDB Restore

## Summary

- Describe the restore procedure for TiDB Cloud Serverless Cluster.

### Premise

- <PERSON><PERSON><PERSON><PERSON> uses `TiDB Cloud Serverless` in principle, regardless of the deployment environment.
  - ref: [csys-infra-gevanni-infra/docs/02_Detaildesign/TiDB.md#プロビジョニング方式](https://github.com/mynavi-group/csys-infra-gevanni-infra/blob/develop/docs/02_Detaildesign/TiDB.md#%E3%83%97%E3%83%AD%E3%83%93%E3%82%B8%E3%83%A7%E3%83%8B%E3%83%B3%E3%82%B0%E6%96%B9%E5%BC%8F)

### Restoration Method

- The restore methods and destinations available in TiDB Cloud Serverless are as follows.
  - Method of restoration
    1.  Snapshot Restore
        - Restore a cluster from a specific backup snapshot.
    1.  PITR (Point in Time Restore) ※ Scalable cluster only.
        - Restore the cluster to a specific point in time.
          - ※ Specific Time: Any time within the last 14 days minus one minute before (current time minus one minute).
  
  - Restore to
    1.  in-place recovery
        - How to replace a running cluster. The cluster will not be available during the restoration.
        - Tables in mysql schema are affected. Changes to user credentials, permissions, and system variables will revert to the state at the time of backup.
    1.  Restore to a new cluster
        - A new cluster is created and restored.
        - Cluster user credentials and permissions are not restored to the new cluster.

### Policy

- In principle, we recommend `PITR` `in-place restoration` for the following reasons
  - Rapid recovery is possible.
  - User credentials and permissions revert to the state at the time of backup, with less need to rewrite secret information.
- It is assumed that there will be cases where a restored cluster will be created while retaining the existing cluster for the purpose of investigating data inconsistencies, etc.
- Therefore, the `restore to new cluster` procedure is also described.
- The `Restore Snapshot` procedure is not described because of the `PITR` recommendation in principle.
- Also, serverless cannot use manual snapshots, and deleting a cluster will also delete the snapshots.
  - Therefore, when obtaining a temporary backup manually, the policy shall be to prepare an S3 in CDK that temporarily stores data on the app account and export data to the S3.

## In-Place recovery procedure

1.  Open the TiDB Cloud console and select `Backup` from the menu in the left pane.\
    Click Restore in the upper right corner of the screen.\
    ![](/docs/images/tidb/tidb-restore-image01.png)

1.  Select `Point-in-Time Restore` from the item `Restore Mode` and select the date and time you want to restore from the `Restore to` pull-down menu.\
    ※ Time selection can be moved by moving the mouse wheel.\
    ![](/docs/images/tidb/tidb-restore-image02.gif)

1.  Select `In-place Restore` from the item `Destination`.\
    ![](/docs/images/tidb/tidb-restore-image03.png)

1.  Click `Restore` from `Summary` on the right side of the screen.\
    ![](/docs/images/tidb/tidb-restore-image04.png)

1.  When the restoration process is started, the cluster status changes to `Restoring`.\
    The cluster will be unavailable until the restoration is complete and the status changes to `Avaliable`.\
    ![](/docs/images/tidb/tidb-restore-image05.png)

1.  The procedure is complete when the cluster status becomes `Avaliable`.\
    ![](/docs/images/tidb/tidb-restore-image06.png)

## Procedure for restoring to a new cluster

### Premise

- If you choose to restore to a new cluster, the cluster's user credentials and permissions will not be restored to the new cluster.
- Therefore, it is necessary to obtain a new user name and password and update the secret.

### 手順

1.  Open the TiDB Cloud console and select `Backup` from the menu in the left pane.\
    Click Restore in the upper right corner of the screen.\
    ![](/docs/images/tidb/tidb-restore-image01.png)

1.  Select `Point-in-Time Restore` from the item `Restore Mode` and select the date and time you want to restore from the `Restore to` pull-down menu.\
    ※ Time selection can be moved by moving the mouse wheel.\
    ![](/docs/images/tidb/tidb-restore-image02.gif)

1.  Select `Restore to a New Cluster` from the item `Destination`.\
    ![](/docs/images/tidb/tidb-restore-image07.png)

1.  Enter a name for the new cluster in the field `Cluster Name` that is different from the cluster before the restore.\
    ![](/docs/images/tidb/tidb-restore-image08.png)\
    ※ Duplicate errors occur when the same name is entered.\
    ![](/docs/images/tidb/tidb-restore-image09.png)

1.  Select a cluster plan from the item `Cluster Plan`.\
    If `Scalable Cluster` is selected, set the monthly usage limit.\
    ![](/docs/images/tidb/tidb-restore-image10.png)

1.  Click `Restore` from `Summary` on the right side of the screen.\
    ![](/docs/images/tidb/tidb-restore-image11.png)

1.  When the restore is started, the `Status` changes to `Restoring`.\
    ![](/docs/images/tidb/tidb-restore-image12.png)

1.  `Restoration is complete when `Status` becomes `Available`.\
    ![](/docs/images/tidb/tidb-restore-image13.png)

1.  Open the `Overview` page and click `Connect` in the upper right corner of the screen.\
    ![](/docs/images/tidb/tidb-restore-image14.png)

1.  Select `Private Endpoint` from the `Connection Type` pull-down.\
    ![](/docs/images/tidb/tidb-restore-image15.png)

1.  Click `Generate Password`.\
    ![](/docs/images/tidb/tidb-restore-image16.png)

1.  From `Parameters`, refrain from `USERNAME` and `PASSWORD`.\
    ![](/docs/images/tidb/tidb-restore-image17.png)

1.  Register `USERNAME` and `PASSWORD` in the secret, run the pipeline and update the secret information.

## Manual backup (import/export) procedure

- In advance, S3 buckets for backup and IAM roles need to be deployed with CDK.
- Obtain TiDB's AWS account ID and External ID to be set in the trust policy from the TiDB Cloud console and deploy the CDK.
- When performing import/export, enter the S3 bucket and IAM role ARN created by CDK into TiDB Cloud and execute.

### Advance Preparation

1.  Open the TiDB Cloud console and select Import from the menu in the left pane.\
    Click `Import data from Cloud Strage` in the center of the screen and select `Amazon S3` from the pull-down menu.\
    ![](/docs/images/tidb/tidb-restore-image18.png)

1.  When you get to the `Import Data from Amazon S3` screen, click `Click here to create a new one with AWS CloudFormation` at the bottom.\
    ![](/docs/images/tidb/tidb-restore-image19.png)

1.  When you get to the `Add New Role ARN` screen, click `Having trouble? Create Role ARN manually` and make a note of your `TiDB Cloud Account ID` and `TiDB Cloud External ID`.\
    ![](/docs/images/tidb/tidb-restore-image20.png)

1.  Deploy a backup S3 Bucket and IAM Role from the CDK code in the `csys-infra-gevanni-cf-sample` repository.
    - Fill in the `TiDB Cloud Account ID ` and `TiDB Cloud External ID` that you have reserved in the following parameters of [csys-infra-gevanni-cf-sample/params/](https://github.com/mynavi-group/csys-infra-gevanni-cf-sample/blob/main/params/).\
      ```typescript
      export const TidbBackupParam: inf.ITidbBackupParam = {
        tidbCloudAccountId: '',
        tidbCloudExternalId: '',
      };
      ```
    - Deploy `tidb-backup-stack`.

1.  Go to the CloudFormation console and open the output tab of the `TidbBackup` stack.\
    Note down the values of `TidbBackupBucketUri` and `TidbBackupRoleArn`.\
    ![](/docs/images/tidb/tidb-restore-image21.png)

### Export

1.  Open the TiDB Cloud console and select Import from the menu in the left pane.\
    Select `Amazon S3` from the pull-down menu in the upper right corner of the screen.\
    ![](/docs/images/tidb/tidb-restore-image22.png)

1.  Once you are on the `Export Data to Amazon S3` page, select the data to be exported and the output format from `Exported Data`.\
    ![](/docs/images/tidb/tidb-restore-image23.png)

1.  Scroll to the bottom of the screen, fill in `Amazon S3 Setting`, and click `Export`.
    - `Folder URI`: Fill in the `TidbBackupBucketUri` that you wrote down in your note in [Advance Preparation](#advance-preparation) and enter the path to the export destination in the suffix.
    - `Role ARN`: Fill in the `TidbBackupRoleArn` that you wrote down in your notes in [Advance Preparation](#advance-preparation).\
    ![](/docs/images/tidb/tidb-restore-image24.png)

1.  Go to the `Import` menu screen and select `Export`.\
    If the `Status` is `Succeeded`, it is done.\
    ![](/docs/images/tidb/tidb-restore-image25.png)

### Import

1.  Open the TiDB Cloud console and select Import from the menu in the left pane.\
    Click `Import data from Cloud Strage` in the center of the screen and select `Amazon S3` from the pull-down menu.\
    ![](/docs/images/tidb/tidb-restore-image18.png)

1.  Set the `Source` to match the exported data and click `Connect`.\
    Below are the options for importing the sample data output from the export procedure.
    - `Import File Count`: Select `Multiple files`.
    - `Included Schema Files`: Select `No`.
    - `Data Format`: Select `SQL`.
    - `Folder URI`: Enter the `TidbBackupBucketUri` that you wrote down in your memo in [Advance Preparation](#advance-preparation), and enter the path where the data to be imported is stored in the suffix.
    - `Role ARN`: Fill in the `TidbBackupRoleArn` that you wrote down in your memo in [Advance Preparation](#advance-preparation).
    ![](/docs/images/tidb/tidb-restore-image26.png)

1.  When you get to the `Import Data from Amazon S3` screen, select the database to import and click `Start Import`.\
    ![](/docs/images/tidb/tidb-restore-image27.png)

1.  Go to the `Import` menu screen and select `Import`.\
    When the `Status` becomes `Completed`, it is done.\
    ![](/docs/images/tidb/tidb-restore-image28.png)