# HowToUseBatchScripts

The following is a description of the scripts used in the Gevanni batch.  
The scripts are located in [02_Batch/scripts](/02_Batch/scripts/).

## Prerequisite.

- Python >= 3.12
    - Because the script is written in Python
- Gevanni Account Credentials
    - To obtain authentication information, refer to [/docs/EN/HowToUseBastionContainer.md#advance-preparation-switch-role-settings-to-iam-roles](/docs/EN/HowToUseBastionContainer.md#advance-preparation-switch-role-settings-to-iam-roles)
- The batch must be deployed to a Gevanni account

### Advance preparation

Since boto3 is used in the script, it must be installed so that it can be used in the environment where the script is executed.  
boto3 must be installed using [requirements.txt](/requirements.txt) directly under root.

```bash
# Example of library addition
pip install -r requirements.txt
```

## HowToUseExecBatchScripts

Here, we will explain the [02_Batch/scripts/exec_batch.py](/02_Batch/scripts/exec_batch.py).  
This is a script for immediately executing the batch deployed in the Gevanni.  
It is prepared for confirming whether the deployed batch runs correctly or for use when an urgent execution of the batch is needed.

### Executable procedure

- Copy the script to the app repository
- Set Gevanni account credentials in the terminal
- Running `python <path to script> <dev/stg/prod> <batch config files path> <batch name>`
    - `<batch config files path>` example.exe must be relative to the script execution location
    - `<batch name>` should be the same value as `batch_name` in [HowToUseBatch](/docs/batch/HowToUseBatch_EN.md#Value-to-be-entered-in-the-configuration-file)
- Subsequent operations follow standard input

> [!NOTE]
> Post script execution batch logs can be viewed in NewRelic

### Execution example

An example of execution under the following assumptions is shown below.  

- Run from the repository root
- Deploying to the stg environment
- Scripts are stored in `<repository root>/infra/batch/scripts`
- Batch configuration files are stored in `<repository root>/infra/batch/configs/batch_app/`.
- The batch name is `batch_app`.

```bash
python ./scripts/batch/exec_batch.py stg ./infra/batch/configs/batch_app batch_app
```

### Script Overview

- The script performs the following operations:
    1. Reads the contents of the `<dev/stg/prod>.toml` file.
    1. Retrieves the Gevanni service prefix from the above file.  
        - The state machine name is in the format `<Gevanni service prefix>-<batch_name>`.
    1. Reads the `<batch_name>.trigger.json` file.
    1. Retrieves the list of triggers from the above file, and if there are multiple triggers, it displays options to choose which trigger configuration to use for running the batch.
        - If there is only one trigger, no options are displayed.
    1. Uses the above configurations and the state machine name to execute the batch.