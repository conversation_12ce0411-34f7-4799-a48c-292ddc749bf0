# HowToUseBatchApiTrigger

This document summarizes how to use the type of Gevanni batch that is executed via API.

Below is a diagram of the batch structure called from the API.

![.](/docs/images/batch/batch-api-trigger-architecture.png)

As shown in the diagram, the batch called from the API is called using API Gateway, and the call path is specified by the value of `apiPath` written in the definition file.  
The URL of the API Gateway will be provided by the Gevanni team.

## How to Execute a Batch

### Prerequisites

- The batch and the API Gateway that makes the call are already deployed.
- Credentials for the IAM Role for the application team provided by the Gevanni team.
    - The method to obtain the credentials is omitted here.

This time, we will show how to call using curl as a sample code.  
Since IAM authentication is applied to the API Gateway, you need to send a request with SigV4 signature when calling. If you call using something other than curl, use a library that performs SigV4 signature to make the request.  
Also, the API response does not include the batch execution result, so if you want to check the batch execution result, check the logs in NewRelic.

#### When Not Overriding the Command

```bash
# Check credentials
# $ echo $AWS_ACCESS_KEY_ID
# ASIASAMPLE12345
# $ echo $AWS_SECRET_ACCESS_KEY
# samplesecret123453abcd
# $ echo $AWS_SESSION_TOKEN
# IQoJbSAMPLE///////......

curl -X POST -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "<URL of the API Gateway provided by the Gevanni team>/<value of apiPath written in the definition file>"

# If executed successfully, the following result will be output
# {"executionArn": "arn:aws:states:ap-northeast-1:*******:execution:********:*********", "startDate": "2025-01-01T01:00:00.000000+00:00", ...  
```

##### Sample Command When Not Overriding the Command

```bash
curl -X POST -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/execute"
```

#### When Overriding the Command

> [!WARNING]
> When overriding the command, the `ContainerOverrides.$` must be included in the Override section of the StepFunctions definition file (\<batch name\>.asl.json).
> For more details, refer to [About StepFunctions Definition File](/docs/JPN/batch/HowToUseBatch_JA.md#stepfunctions-ステートマシンの定義ファイルについて) and [Sample File](/02_Batch/usecases/sidecar/sidecar.asl.json).

```bash
 curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    -d '{"batchName": "<Gevanni service name>-<batch name>", "command": [{"containerName": "<name of the container to override the command>", "passedCommand": "<command to pass (array format)>"}]}' \
    "<URL of the API Gateway provided by the Gevanni team>/<value of apiPath written in the definition file>"
```

##### Sample Command When Overriding the Command

```bash
 curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    -d '{"batchName": "sample01-dev01-ab-BatchApp", "command": [{"containerName": "BatchContainer", "passedCommand": ["echo", "hello gevanni"]}]}' \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/commandoverwrite"
```

## Checking Callable Batches

When you send a request to the root of the issued API Gateway, the names of the callable batches will be returned.
Since IAM authentication is also applied to this path, credentials are required when making the call.

```bash
curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/"

# {"statusCode":200,"message":"{\"availableBatchNames\":[\"sample01-dev01-ab-BatchApp\"]}"} ↲                                                                            
```
