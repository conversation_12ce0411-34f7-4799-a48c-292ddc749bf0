# List of Batch Use Cases

## Examples of settings for each use case

The `02_Batch` directory contains a set of sample files, so refer to the configuration files according to the desired use case. 

※ Note that only dev.toml is created in each directory.

### Batches that run periodically at specific times

![.](/docs/images/batch/batch-BatchApp.drawio.png)

An example implementation is available at [02_Batch/BatchApp/](/02_Batch/usecases/BatchApp/).

Create a definition file with the following settings.

- Batch Name: BatchApp
- Task Name: BatchTask
    - Container Name: BatchContainer
        - Container Image PATH: batch/simple-batch
        - Overwrite Command: None
- Startup Time: Daily 14:00

### Sequenced batches where Task A is performed followed by Task B

Define a StepFunctions state machine to launch multiple ECS tasks. Prepare task definition files for the number of tasks.

![.](/docs/images/batch/batch-sequence.drawio.png)

An example implementation is available at [02_Batch/sequence/](/02_Batch/usecases/sequence/)

Create a definition file with the following settings.

- Batch Name: sequence
- Task Name:
    - taska
        - Container Name: batch
            - Container Image PATH: batch/aggregate
            - Overwrite Command: `["-i", "billing"]`
                - An image where the execution command is specified in the container image's 'ENTORYPOINT'
    - taskb
        - Container Name: batch
            - Container Image PATH: batch/export
- Startup Time: Weekday 14:00

### Batch mounting shared file storage (EFS)

Add volume mount settings to the ECS definition file. (Assuming EFS is available). 
Note that the EFS file system ID is used in the configuration, and this value is provided by the Gevanni team.

![.](/docs/images/batch/batch-mount.drawio.png)

An example implementation is available at [02_Batch/mount/](/02_Batch/usecases/mount/)  

Create a definition file with the following settings.

- Batch Name: mount
- Task Name: mount_task
    - Containe Name: batch
        - Container Image PATH: batch/save
        - Overwrite Command: None
        - File System ID: fs-0a1b2c3d4e5f6g7h8
            - Container To Mount EFS: batch
            - Mount Settings: `/:/mnt/efs`
            - Readonly: false
- Startup Time: Per minute

### Batches that can manipulate resources for accounts other than the Gevanni account

Access resources in AWS accounts other than the Gevanni account using the same procedure as on the web app side.

![.](/docs/images/batch/batch-cross-account.drawio.png)

There is no sample because there is nothing in the configuration file that needs to be changed. (In the figure, the BatchApp batch is used as an example.) 

Follow the steps below to manipulate resources for accounts other than the Gevanni account.

- To be completed in advance.
    1. Create IAM roles that can be used by Gevanni accounts as well as the web app side.
    1. Set permissions for the resources you. want that role to have access to.
- What to do on the batch application side.
    1. Run AssumeRole API in the application
    1. Access desired resources using credentials obtained from AssumeRole API responses.

### Batch using environment variables

The same procedure as on the web app side is used to grant environment variables to ECS tasks. 
The secret is created for each task. 

![.](/docs/images/batch/batch-secret.drawio.png)

An example implementation is available at [02_Batch/secret/](/02_Batch/usecases/secret/)

Create a definition file with the following settings.

- Batch Name: secret
- Task Name: task
    - Container Name: batch
        - Container Image PATH: batch/app
        - Overwrite Command: None
        - Environment Variables to be set:
            - DB_HOST: Set to Secret
            - DB_PASSWORD: Set to Secret
            - DB_NAME: Set Environment Variables
            - DB_PORT: Set Environment Variables
        - ARN of SecretsManager: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - Key on DB_HOST's Secret Manager: DB_HOST
            - Key on Secret Manager for DB_PASSWORD: DB_PW
- Startup Time: Daily 14:00

To set environment variables, refer to [Registering environment variables](/docs/EN/HowToUseEcspresso.md#registering-environment-variables) and run [02_ Batch/scripts/update_batch_secret.py](/02_Batch/scripts/update_batch_secret.py).

The script requires the following: the environment in which the secret you wish to operate, the path to the batch configuration file, and the batch name.

```bash
# Assuming a directory layout similar to this repository
python ./02_Batch/scripts/update_batch_secret.py dev 02_Batch/BatchApp BatchApp
# You can use -h, --help to see the Usage, so if you are not sure how to use it, please look at this.
python ./02_Batch/scripts/update_batch_secret.py -h
```

### Batches with sidecar.

If you want to run multiple containers in one ECS task, use the sidecar method. 
Modify the ECS task definition to enable sidecar use.

![.](/docs/images/batch/batch-sidecar.drawio.png)

An example implementation is available at [02_Batch/sidecar/](/02_Batch/usecases/sidecar/)

Create a definition file with the following settings.

- Batch Name: sidecar
- Task Name: task
    - Container Name: batch
        - Container Image PATH: batch/app
        - Overwrite Command: `["node", "app.js"]`
        - Environment variables to be set:
            - DB_HOST: Set to Secret
            - DB_PASSWORD: Set to Secret
            - DB_NAME: Set Environment Variables
            - DB_PORT: Set Environment Variables
        - ARN of SecretsManager: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - Key on DB_HOST's Secret Manager: DB_HOST
            - Key on Secret Manager for DB_PASSWORD: DB_PW
        - Container Dependencies: If sidecar is HEALTHY
    - Container Name: sidecar
        - Container Image PATH: batch/sidecar
        - Overwrite Command: `["-sq"]`
        - Environment variables to be set:
            - API_KEY: Set to Secret
        - ARN of SecretsManager: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - API_KEY key on the Secret Manager: API_KEY
        - Container health check command: `["CMD-SHELL", "curl localhost:5000 || exit 1"]`
- Startup Time: Daily 14:00
