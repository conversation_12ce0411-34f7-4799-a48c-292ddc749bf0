# HowToUseGevanniBatch

This section describes the usage and details of the infrastructure configuration files used in Gevanni's batch infrastructure.

## Gevanni Batch Configuration Diagram

![.](/docs/images/batch/batch-overall-for-app.drawio.png)

The batch infrastructure consists mainly of the AWS service that trigger the execution of the batch app and the AWS service that executes the batch app.  
※ A batch application is an application that runs on a container that performs batch processing.  
Each is as follows.

- Service that trigger the execution of batch applications.
    - EventBridge Scheduler
    - Lambda
    - API Gateway (Currently not implemented, under design review)
- Services to run batch applications.
    - StepFunctions
    - ECS

> [!TIP]
> <PERSON><PERSON><PERSON><PERSON> considers the StepFunctions state machine as a single batch, and the ECS tasks running in the batch are denoted as tasks.

Gevanni batches can create the following types of batches, depending on the configuration file. 
For example, as shown in the figure below, a batch can have multiple triggers, depending on the definition file for batch execution.

![.](/docs/images/batch/batch-exec-multiple-triggers.drawio.png)

Also, as shown in the figure below, a single batch can contain multiple tasks, depending on the state machine definition.

![.](/docs/images/batch/batch-exec-multiple-tasks.drawio.png)

In this way, this batch foundation can modify the configuration file to create batches that are primarily.

- Batches that are executed periodically at specific times
- Sequenced batches, such as Task A followed by Task B
- Batches that mount shared file storage (EFS)
- Batch that can manipulate resources of accounts other than the Gevanni account.
    - Currently only S3 is supported.
- Batches that use environment variables
- Batches that use sidecars

Please refer to **[Examples of settings for each use case](/docs/batch/usecases.md#examples-of-settings-for-each-use-case)** for details on how to configure the settings for each use case.

The batch infrastructure is deployed via CDK code in the infrastructure repository and Code Pipeline for batch apps.
The resources to be created by each tool are as follows.

- CDK
    - Various IAM Roles
    - CodePipeline
    - Lambda
- CodePipeline
    - ECS Task Definition
    - StepFunctions StateMachine
        - Used to control the order in which ECS tasks are executed.
    - EventBridge Scheduler

For resources created in CodePipeline, it is necessary to have specific files managed in the app team repository,
This document does not describe the resources created by CDK, but rather the resources created by CodePipeline and the set of files used to create them.

### About the Batch Deploy Pipeline

Below is a diagram of the batch deploy pipeline and the flow of how the batch app is deployed. 
Basically, batch apps are deployed to the Gevanni environment through the same procedure as the web app side.

![.](/docs/images/batch/batch-app-workflow.drawio.png)

1. A GitHub Actions workflow for batch deployment, located within the app team's repository, is executed.
1. Execute build scripts in the workflow
1. Build batch apps in build scripts and push to ECR
1. In the build script, turn the configuration file (described below) into a zip file and upload it to the S3 bucket in the Gevanni environment.
    - This is where CodePipeline runs
1. Deploy batch infrastructure based on configuration files in CodeBuild in CodePipeline

> [!IMPORTANT]
> Therefor, the app repository creates and manages the following files.
>
> - ECS Task Definition File
> - StepFunctions state machine definition file
> - Definition files for creating services for batch execution
> - Environment files to control per-environment values
> - Script files for builds

Build scripts are stored in [02_Batch/scripts/build_batch](/02_Batch/scripts/build_batch/) and should be copied to the application team repository.  
Since the same script is used for all batches, there is no need to prepare a script for each batch configuration file.  
For details of the scripts, please refer to the detailed workflow section.  

The following sections describe the details and specifications of each configuration file.

## About Batch Configuration Files

### Summary

Each batch configuration file is basically a template file, and specific values are filled in within the batch pipeline. 
However, it is necessary to change the configuration file for each batch in order to include the values of the items you applied for when using Gevanni, such as the name of the batch and the name of the task, in the configuration file. 
In addition, a configuration file must be created for each batch. 

### Example of configuration file placement

An example of a directory structure is described below.

```plain-text
Any repository location/
└── batch/
    ├── configs/
    │   ├── batch1/
    │   │   ├── dev.toml
    │   │   ├── stg.toml
    │   │   ├── prod.toml
    │   │   ├── batch1.asl.json
    │   │   ├── batch1.trigger.json
    │   │   └── ecs-task-def.json
    │   └── batch2/
    │       ├── dev.toml
    │       ├── stg.toml
    │       ├── prod.toml
    │       ├── batch2.asl.json
    │       ├── batch2.trigger.json
    │       ├── task1-ecs-task-def.json
    │       └── task2-ecs-task-def.json
    └── scripts/
        └── build_batch/
            ├── main.py
            └── requirements.txt
```

### Value to be entered in the configuration file

The template file for the configuration file is stored in [/02_Batch/batch_config_templates](/02_Batch/batch_config_templates/).  
This template file is intended to be used by filling in the values enclosed by `<>` with the correct values.  
※ The file names of the files contained in the directory must also be changed, and the file names are enclosed in `[]` instead of `<>`.  
The following is a description of the values enclosed by `<>` and the values that should be set.  

| Item Name | Desctiprion |
|:---:|:---:|
| `batch_name` | **Name of the batch you applied for when using Gevanni** |
| `task_name` | **Name of the task you applied for when using Gevanni** |
| `container_name` | **Container name you applied for when using Gevanni** |
| `image_place_hodlder_value` | **Any value (only alphanumeric underscores are allowed)** |
| `trigger_name` | **Any value (only alphanumeric underscores are allowed)** |
| `Gevanni_env_name(env)` | **Gevanni environment name (example: Dev01, Stg01, Prod01)** |
| `Gevanni_service_prefix(env)` | **Gevanni Service Name (example: sample01-dev01-ab, sample01-stg01-ab, sample01-prod01-ab)** |
| `task_definition_file_name` | **File name containing the extension of the task definition file** |
| `batch_docker_build_context` | **Path to specify when building batch apps (relative to repository root)** |

### About ECS Task Definition Files

This file manages the definition of the ECS task that executes the batch app.  
The file name can be anything as long as the **end of the file is `ecs-task-def.json`.**

The following items can be set in the task definition file.

- Setting Environment Variables
    - How to set up is described below.
- Sidecar Setup
- (if using EFS) Volume mount settings

> [!NOTE]
> There are other items that can be set, but for more information, see [Amazon ECS Task Definition Parameters](https://docs.aws.amazon.com/ja_jp/AmazonECS/latest/developerguide/task_definition_parameters.html).

In Gevanni, however, the following parameters must either have predetermined values or be filled in with values in a predetermined format.  

- Place the values as described in [template file](/02_Batch/batch_config_templates/ecs-task-def.json).
    - `LogConfiguration` for each container definition
    - Container definition with container name `logRouter`.    
    - `family`
    - `executionRoleArn`
- Only the values in a certain format are changed from the values in the [template file](/02_Batch/batch_config_templates/ecs-task-def.json).
    - `cpu`
    - `taskRole`
    - `memory`
    - `name`
    - `image`

### About the StepFunctions StateMachine definition file

This file manages the state machine definitions used in the batch. 
The file name should be **`<batch_name>.asl.json`**. 

> [!NOTE]
> See [Amazon State Language](https://states-language.net/spec.html) for possible values.

In Gevanni, however, the following parameters must either have predetermined values or be filled in with values in a predetermined format. 

- The place to put a fixed value (the value set in [template file](/02_Batch/batch_config_templates/<batch_name>.asl.json) is used as it is).
    - Resource locations in states where `Type` is `Task`.
    - Parameters in states where `Type` is `Task` (except for `Overrides` state)
- Modify only the designated parts of the specified format based on the values set in ([Template file](/02_Batch/batch_config_templates/<batch_name>.asl.json))
    - Resource locations in states where `Type` is `Task`.
    - The `StartAt` part to start the above state.    
    - The `Overrides` part of the Parameters of a state whose `Type` is `Task`.
        - Please specify one of the following formats
            - `{}` (If you do not use command override)
            - `{ "ContainerOverrides.$": "$.containerOverrides.[{{ <task_name>_commands }}]" }` (To override a command)

### About the definition file for creating a service that performs batch execution

This file manages the startup time of the EventBridge Scheduler that executes the batch and the commands to be passed when executing ECS tasks. 
The file name should be **`<batch_name>.trigger.json`.**

The file is used only by Gevanni, so the available values are listed below.

#### Available values in trigger.json

The available values are shown in the table below. 
The path portion of the table shows the path from the root element to each item. (The root element is marked as `$`)

|Item|Path|Data Type|Required|Description|Remarks|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`version`|`$.version`|`string`|◎|Schema Version|Currently only “1” is available|
|`triggers`|`$.triggers`|`object`|◎|Trigger Definition||
|`<trigger_name>`|`$.triggers.<trigger_name>`|`string`|◎|Trigger Name||
|`type`|`$.triggers.<trigger_name>.type`|`cron` \| `api`|◎|Trigger Type|Specifies either `cron` or `api`.|

##### Possible values if type is set to cron

|Item|Path|Data Type|Required|Description|Remarks|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`cron`|`$.triggers.<trigger_name>.cron`|`object`|◯|Define EventBridge Scheduler startup time|For cron type, specification is mandatory (if none of the values mentioned below are used, an empty object must be specified.)|
|`minute`|`$.triggers.<trigger_name>.cron.minute`|`string`| |EventBridge Scheduler start time (minutes)|only cron type can be specified (default *)|
|`hour`|`$.triggers.<trigger_name>.cron.hour`|`object`| |EventBridge Scheduler start time (hour)|only cron type can be specified (default *)|
|`date`|`$.triggers.<trigger_name>.cron.date`|`object`| |EventBridge Scheduler の起動時刻 (date)|only cron type can be specified (default *)|
|`month`|`$.triggers.<trigger_name>.cron.month`|`object`| |EventBridge Scheduler startup time (date)|only cron type can be specified (default *)|
|`dayOfWeek`|`$.triggers.<trigger_name>.cron.dayOfWeek`|`object`| |EventBridge Scheduler start time (day of the week)|Only cron type can be specified (default *)|
|`year`|`$.triggers.<trigger_name>.cron.year`|`object`| |EventBridge Scheduler start time (year)|only cron type can be specified (default *)|
|`state`|`$.triggers.<trigger_name>.state`|`"ENABLED"` \| `"DISABLED"`|◯|Event Bridge Scheduler Settings|Required if `cron` is specified.|
|`inputs`|`$.triggers.<trigger_name>.inputs`|`object`|◯|Inputs that the trigger passes to the state machine|In the state machine definition above, if the `ContainerOverrides` option is specified, all of the following items are required|
|`<task_name>`|`$.triggers.<trigger_name>.inputs.<task_name>`|`object`|◯|Content of input passed to tasks included in the state machine|If `inputs` is specified, it must be specified.|
|`commands`|`$.triggers.<trigger_name>.inputs.<task_name>.commands`|`list`|◯|Commands to be passed to the task||
|`containerName`|`$.triggers.<trigger_name>.inputs.<task_name>.commands[].containerName`|`string`|◯|Name of the container for which you want to override the command|commands If commands are listed, be sure to specify|
|`passedCommand`|`$.triggers.<trigger_name>.inputs.<task_name>.commands[].passedCommand`|`list`|◯|Override command|If commands are listed, be sure to specify|

※ ◎ indicates that the item is required, and ◯ indicates that the item is conditionally required. 
※ A value enclosed in `<>` indicates that an arbitrary value can be specified.

##### Possible values if type is set to api

|Item|Path|Data Type|Required|Description|Remarks|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`cron`|`$.triggers.<trigger_name>.api`|`string`|◎|API paths|Required and specified in API Type|

※ ◎ indicates that the item is required, and ◯ indicates that the item is conditionally required. 
※ A value enclosed in `<>` indicates that an arbitrary value can be specified.

> [!TIP]
> Schema files are available in [.vscode/schemas/trigger-schema.json](/.vscode/schemas/trigger-schema.json), and should be used if necessary.  
> The usage of the schema file is described in [How to apply schema files](#how-to-apply-schema-files). (vscode only)

### For environment files that control values per environment

This file is used to set different values for different dev/stg/prod environments.  
The file should be named as **`(dev or stg or prod).toml`.**(You need to create a file for each environment you use.)  
For files used only with Gevanni, here are the possible values.  
The path part of the table shows the path from the root element to each item. (The root element is marked as `$`)  

|Item|Path|Data Type|Required|Description|Remarks|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`env`|`$.env`|`object`|◎|Gevanni environment name and service ID||
|`name`|`$.env.name`|`string`|◎|Environment name for Gevanni||
|`service_prefix`|`$.env.service_preifx`|`string`|◎|Gevanni Service ID||
|`tasks`|`$.tasks`|`object`|◎|Definition of tasks to be included in the batch||
|`<task_name>`|`$.tasks.<task_name>`|`object`|◎|Task Definition||
|`definition`|`$.tasks.<task_name>.definition`|`object`|◎|Parameter definitions for task definitions used in tasks||
|`name`|`$.tasks.<task_name>.definition.name`|`string`|◎|File name of task definition||
|`spec`|`$.tasks.<task_name>.definition.spec`|`object`|◎|Specification of task definition||
|`cpu`|`$.tasks.<task_name>.definition.spec.cpu`|int|◎|Task CPU||
|`memory`|`$.tasks.<task_name>.definition.spec.memory`|int|◎|Task Memory||
|`build`|`$.tasks.<task_name>.build`|`object`|◎|Definitions for container builds included in task definitions||
|`<container_name>`|`$.tasks.<task_name>.build.<container_name>`|`object`|◎|More definitions on building containers||
|`context`|`$.tasks.<task_name>.build.<container_name>.context`|`string`|◎|Container build context||
|`dockerfile`|`$.tasks.<task_name>.build.<container_name>.dockerfile`|`string`| |Relative path from Dockerfile build context|if not specified, Dockerfile directly under context is used|
|`image_placeholder`|`$.tasks.<task_name>.build.<container_name>.image_placeholder`|`string`|◎|Placeholder values listed in the task definition file||

※ ◎ indicates that the item is required, and ◯ indicates that the item is conditionally required.   
※ A value enclosed in `<>` indicates that an arbitrary value can be specified.  

> [!TIP]
※ ◎ indicates that the item is required, and ◯ indicates that the item is conditionally required.   
※ A value enclosed in `<>` indicates that an arbitrary value can be specified.

## About Deployment Workflow

This section describes the GitHub Actions workflow for kicking off the batch deployment pipeline.  
For the batch deployment workflow file, please refer to [/.github/workflows/simple-batch-deploy.yml](/.github/workflows/simple-batch-deploy.yml) for a sample,  
Refer to [/.github/workflows/README.md](/.github/workflows/) and the README of the workflow to do the following.  

- Register `vars.batch_role_arn`.
    - Add `batch_role_arn` variable to the repository, referring to the above file.
    - The value you add will be shared by the infrastructure team.
- Change the following values in the `env` section

    |Key|Value|Notes|
    |:---:|:---:|:---:|
    |`PYTHON_VERSION`|`>= 3.12`|Currently, using `3.12`.|
    |`SCRIPT_DIR`|Path to batch deploy scripts|Relative path from repository root|
    |`CONFIG_PATH`|Path to the directory containing the configuration files for the batch you want to deploy|Relative path from repository root|
    |`BATCH_NAME`|Batch Name|Name of the batch you applied for when using Gevanni|

- Customize the `on` part of the workflow (if necessary)

## How to apply schema files

If you are using vscode, you can apply the schema file in the following way

- When validating the schema in (dev/stg/prod).toml
    1. Install `tamasfe.even-better-toml`.
    1. Specify relative paths to schema files at the beginning of various files
        1. If /.vscode/schemas/env.toml-schema.json is the schema file and /infra/config/batch/batch1/stg.toml is the toml file you want to verify, then `../../../../.vscode/schemas/env.toml-schema.json`.

- To validate the schema in trigger.json
    1. add the following values to settings.json
        ```json
        {
            "json.schemas": [
                {
                "fileMatch": ["/**/*.trigger.json"],
                "url": "/.vscode/schemas/trigger-schema.json"
                }
            ]
        }
        ```
