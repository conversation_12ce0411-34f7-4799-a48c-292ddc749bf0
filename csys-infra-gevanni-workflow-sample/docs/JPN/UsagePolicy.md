## 概要
### この資料の目的
内製開発におけるGevanni利用方針の理解

## 前提情報
開発標準として、前提は以下資料にまとまっています。ここではGevanniに関係する箇所を掻い摘んで記載します。<br>
https://mynavi.docbase.io/posts/3570807

### インフラ構成標準
- 内製開発において、原則Gevanniを利用すること推奨しています。
    - Gevanni本体アカウント + CloudFrontアカウントの構成
        - 前者はクラウドエンジニアリング部管理、後者は各案件のシステムオーナー管理
![システム構成図](../images/gevannni-overall-architecture.dio.png)
    - Gevanniの仕様で実装できない要件（ノックアウト条件）が発生する場合のみ、個別のCDK構築となります。

## ノックアウト事項
### VPCピアリング
VPCピアリングを利用した外部VPCへの通信は原則NGとなります<br>
- 理由
    - Gevanniは複数のサービスが混在しており、ピアリング接続によってGevanni管理下の全てのサービスへ想定外の影響を与えるリスクがあるため
- 例外ルール
    - Gevanni内での別VPC通信
        - スケーリングの観点でECS FargateがVPC単位で分割・拡張されることを想定されるため、Gevanni内での別VPC通信を許可する(STG1→STG2など)

### PrivateLink
PrivateLinkを利用した外部VPCへの通信は原則NGとなります<br>
- 理由
    - Gevanniは複数のサービスが混在しており、PrivateLink経由の通信がGevanni管理下の全てのサービスへ想定外の影響を与えるリスクがあるため
- 例外ルール
    - Gevanni→他環境VPCの通信
        - ピアリングによる通信についてGevanni→他環境VPCの通信は影響範囲が他サービスのみになるので、他サービスへの影響が極めて低いため接続を許可する
    - Gevanni内での別VPC通信
        - VPCピアリングと同様の理由で例外的に許可

### DirectConnect
DirectConnectを利用したオンプレ環境とGevanni環境への通信は原則NGとなります<br>
- 理由
    - VPCピアリング、PrivateLink同様にDirectConnect接続によってGevanni管理下の全てのサービスへ想定外の影響を与えるリスクがあるため
    - AWS側だけで完結しないネットワーク構成となるため

### Windowsコンテナ
GevanniでのWindowsコンテナの利用は原則NGとなります<br>
- 理由
    - ECS FargateがWindowsコンテナをサポートしていない
        - GevanniではECS Fargate(Linuxベース)コンテナを標準とする設計になっているが、WindowsコンテナはFargateに非対応のため


### GevanniAWSアカウント（ポータル画面）へのログイン要件
GevanniのAWSアカウントに対して、Gevanni開発者及び管理者以外のログインは原則NGとなります。<br>
- 理由
    - 複数のサービスが混在する環境のため、誤操作によるインフラの破壊リスクがあるため
- 補足
    - Gevanni本体に存在するサービス単位リソースの状況についてはNewReilcのログとメトリクスで管理する設計です

### TiDB(MySQL)のDB利用
GevanniにおいてのDB利用はTiDB(MySQL)に限定しています<br>
- 理由
    - Gevanniの利用方針として基本はTiDBを利用する方針となっているため
        - コスト
            - 料金体系が使用したストレージ量＋クエリ処理で必要となったリソース量（RU）による従量課金
        - パフォーマンス
            - 変化するワークロードの需要に効率的に対応するために、storageとコンピューティング リソースを自動的に調整される

## 相談事項

- ノックアウト事項に乗っていないが、Gevanniの機能として実装されていないものは要相談事項となる<br>
- 既に追加開発決まっているものについては以下のスケジュールを参照
    - 参考: [Gevanni開発スケジュール](https://github.com/mynavi-group/csys-infra-gevanni-infra/wiki/%E9%96%8B%E7%99%BA%E3%82%B9%E3%82%B1%E3%82%B8%E3%83%A5%E3%83%BC%E3%83%AB)
