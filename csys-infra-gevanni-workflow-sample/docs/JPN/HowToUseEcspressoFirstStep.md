# HowToUseEcspressoFirstStep

ここでは、[HowToUseEcspresso.md](HowToUseEcspresso.md)に網羅的な説明が記載されているため、初回デプロイ時の必須対応に重点を置いて記載する。

## 概要

- Ecspresso のデプロイパターンは大きく分けて 2 つある。

  1. GitHubActions から実行
     - フロー 1：アプリを更新の際に利用
  2. ローカル PC から update_ecspresso_conf.py を実行
     - フロー 2：アプリの更新なしでコンテナ数などを更新の際に利用

## フロー 1：アプリを更新の初期設定

- 初めて GHA を実行するには、事前に環境を整えておく必要がある。
- 前提条件のすべての項目の設定が完了してから、操作を実施すること。

### 前提条件

- 以下設定が完了していること
  - [gevanni-workflow-sample リポジトリからアプリリポジトリへのファイルコピー](HowToSetup.md#gevanni-workflow-sampleリポジトリからアプリリポジトリへのファイルコピー)
  - [ディレクトリ構成変更時の対応](HowToSetup.md#ディレクトリ構成変更時の対応)
  - [OIDC 専用の IAM ロールを GitHubVariables に登録](../../.github/workflows/README_ja.md#ii-oidc-専用の-iam-ロールを-githubvariables-に登録)
  - [設定ファイルの事前準備](HowToUseEcspresso.md#事前準備)
- 環境構築は、リポジトリで 1 度登録が完了していれば、その後の登録対応は不要となる。

### 操作

- GHA から`Deploy ECS Frontend`または`Deploy ECS Backend`を実行して、CodePipeline の処理を開始する。

### 補足

- デフォルトでヘルスチェックパスを`http://localhost/`と設定しているため、それ以外のパスで設定している場合は、デプロイは成功するがヘルスチェックに失敗する。その場合はフロー2でヘルスチェックパスを修正する必要がある。

## フロー 2：アプリの更新なしでコンテナ数などを更新の初期設定

- フロー 1 の操作が完了したら、フロー 2 を実施する。

### 前提条件

- 以下設定が完了していること
  - [必要なリソースのインストール](HowToSetup.md#必要なリソースのインストール)
  - [実行環境に実行権限を付与する](HowToSetup.md#実行環境に実行権限を付与する)
  - [設定ファイルの事前準備](HowToUseEcspresso.md#事前準備)

### 操作

1. ローカル環境でスクリプトを実行できるように[HowToSetup.md](./HowToSetup.md)の手順を参考に、boto3 のインストールと実行権限付与を実施する。

1. ecspresso_confを取得する。

   1. `01_Rolling/build_frontend`または`01_Rolling/build_backend`ディレクトリに移動する。

   1. `python update_ecspresso_conf.py {環境名(dev/stg/prod)}`を実行する。

   1. image\.zip を S3 バケットからダウンロードするか、S3 バケットにアップロードするかを選択する。  
      ここでは、ecspresso conf を取得するために、`1`を入力する。  
      ![](../images/update-s3-operetion.png)

   1. カレントディレクトリに ecspresso_conf フォルダが作成される。  
      ![](../images/update-download-files.png)

1. ヘルスチェックパスを修正する。
   - フロントエンドの場合
      - 01_Rolling/build_frontend/環境ファイル（dev.ini/prod.ini/stg.ini）の`HEALTH_PATH`を修正する。
      - 必要があれば、他のパラメータやecs-task-def.jsoも修正する。
   - バックエンドの場合
      - 前の工程で取得した`ecspresso_conf`フォルダ内の`ecs-task-def.json`の`http://localhost/`を修正する。
         ```json
            "healthCheck": {
               "command": [
                  "CMD-SHELL",
                  "curl -f http://localhost/ || exit 1"
               ],
               "interval": 30,
               "timeout": 5
               },
         ```
      - 必要があれば、環境ファイルやecs-task-def.jsonを修正する。

1. 再度 CodePipeline を起動する。

   1. `01_Rolling/build_frontend`または`01_Rolling/build_backend`ディレクトリに移動する。
   1. `python update_ecspresso_conf.py {環境名(dev/stg/prod)}`を実行する。

   1. image\.zip を S3 バケットからダウンロードするか、S3 バケットにアップロードするかを選択する。  
      ここでは、パイプラインを起動させるために、`2`を入力する。  
      ![](../images/update-s3-operetion2.png)

   1. パイプライン起動前の最終確認が求められるため、確認しながらコマンドを入力していく。  
      ![](../images/update-final-check.png)

   1. CodePipeline が再実行され、更新した内容が反映される。
