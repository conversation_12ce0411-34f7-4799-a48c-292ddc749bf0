## HowToExceuteToECSTask

ここでは、ECS コンテナに接続する手順を記載する。

### 概要

- `exec_container.py`を実行すると、以下処理が実行される。
  - Systems Manager パラメータストアから ECS クラスター名を取得する。
  - クラスタ内で起動している全てのタスク ID を取得し、コンソール画面に表示する。
  - どのタスクにするか、「ecs execute-command」または「ssm start-session」の実行コマンドを選択することで、接続が完了する。

### 前提条件

下記の設定が完了していること

- [必要なリソースのインストール](HowToSetup.md#必要なリソースのインストール)
- [実行環境に実行権限を付与する](HowToSetup.md#実行環境に実行権限を付与する)

### 手順

1. 接続したいコンテナのディレクトリに移動する

   ```sh
   cd 01_Rolling/build_{app_name}/
   ```

   **例**

   ```sh
   cd 01_Rolling/build_backend/
   ```

1. 環境ファイルを修正する  
   **stg.ini**

   ```ini
    [Env]
    ENV=Stg01
    PJ_PREFIX=GEVANNI
    # サービスID
    SERVICE_PREFIX=service01-stg01-ab

    [EcsApp]
    # コンテナ名
    APP_NAME=EcsBackend
   ...
   ```

1. スクリプトを実行して、コンテナに接続する

   1. `exec_container.py`を実行する。

      ```sh
      python exec_container.py <environment>
      ```

      **例**

      ```sh
      python exec_container.py stg
      ```

   2. 接続するタスクを選択する。

      ```sh
      Select a task to exec into:
      1. arn:aws:ecs:ap-northeast-1:111111111111:task/Stg01-GEVANNI-service01-stg01-ab-ECS-ECSAppECSCommonClusterA1B2C3-c4d5e6/123abc321def
      2. arn:aws:ecs:ap-northeast-1:111111111111:task/Stg01-GEVANNI-service01-stg01-ab-ECS-ECSAppECSCommonClusterA1B2C3-c4d5e6/789xyz987xyz

      Enter the task number: 1
      ```

   3. 実行するコマンドを選択する。コマンドの使い分けは以下のとおり。

      1. ecs execute-command  
         コンテナにログインせず、任意のコマンドを実行するために使用する。
      1. ssm start-session  
         SSM セッションを開始して、コンテナにログインするために使用する。

      ```sh
      Choose an option:
      1. ECS Execute Command
      2. SSM Start Session

      Enter the option number: 1
      ```

   4. 接続用のコマンドが出力されるため、コマンドをコピーしてコマンドを実行する。

      ```sh
      aws ecs execute-command --region ap-northeast-1 --cluster Stg01-GEVANNI-service01-stg01-ab-ECS-ECSAppECSCommonClusterA1B2C3-c4d5e6 --task arn:aws:ecs:ap-northeast-1:111111111111:task/Stg01-GEVANNI-service01-stg01-ab-ECS-ECSAppECSCommonClusterA1B2C3-c4d5e6/123abc321def --container EcsBackend --interactive --command /bin/sh
      ```

      <img width="1170" alt="ecs execute-command" src="../images/ecs-execute-command.png">
      <img width="1181" alt="ssm start-session" src="../images/ecs-ssm-start-sesstion.png">
