# HowToUseEcspresso

ここでは、Ecspresso conf の利用方法について記載する。

## 目次

1. 概要
   1. [全体構成](#概要)
   1. [ファイル構成](#ecs-デプロイ関連ファイルの概要)
1. 実行手順
   1. [フロー 1 アプリを更新](#フロー-1アプリを更新)
   1. [フロー 2：アプリの更新なしでコンテナ数などを更新](#フロー-2アプリの更新なしでコンテナ数などを更新)  
      ※各フローの事前作業は[HowToUseEcspressoFirstStep.md](HowToUseEcspressoFirstStep.md)を参照

## 概要

- ECS サービスへのデプロイ作業は CodePipeline を利用する。
- 全体的なデプロイパターンは大きく分けて 2 つある。

  1. GitHubActions から実行
     - **フロー 1：アプリを更新**の際に利用（後述）
  1. ローカル PC から update_ecspresso_conf\.py を実行
     - **フロー 2：アプリの更新なしでコンテナ数などを更新**の際に利用（後述）

- **i. GitHubActions から実行**する場合のデプロイフローは以下のとおり

  1. アプリケーションチームのリポジトリから GitHubActions を実行する。
  2. ワークフローファイルから build\.py が実行される。
  3. build\.py 内で docker build が実行され ECR にイメージがプッシュされる。
  4. build\.py内で、AWS Secrets Manager にシークレットの値を作成または更新し、ECS タスク定義の JSON ファイル内のシークレット ARN を更新する。
  5. build\.py 内でタスク定義などの設定ファイルを image\.zip に圧縮して S3 バケットへ配置する。
  6. S3 配置をトリガーにパイプラインが起動する。
  7. CodeBuild 内で ecspresso コマンドが実行され、ECS サービスへのデプロイが実行される。
     ![](../images/gevanni-ecspresso-pipeline.dio.png)

- **ii. ローカル PC から update_ecspresso_conf.py を実行**する場合のデプロイフローは以下のとおり
  1. ローカル PC 上で update_ecspresso_conf\.py を実行する。
  1. S3 バケットから image\.zip をローカル PC にダウンロードしてくる。
  1. image\.zip を解凍し、ecspresso conf を取得する。
  1. ecspresso conf や環境ファイルを編集する。
  1. 編集した後に再圧縮して S3 上にアップロードする。
  - ※ecspresso conf とは ecspresso に関連する設定ファイルを指す(ECS サービスや ECS タスク等)  
     ![](../images/gevanni-ecspresso-pipeline-outline.dio.png)

## ECS デプロイ関連ファイルの概要

- ecspresso をデプロイする上で利用する関連ファイルは以下の 3 つ。

  1. ワークフローファイル （ `.github`ディレクトリ配下全てのファイル）
  1. ビルド関連のファイル （ `01_Rolling`ディレクトリ配下の`build.py`）
  1. 更新関連のファイル（`01_Rolling`ディレクトリ配下の`update_ecspresso_conf.py`と`update_secret.py`）

- 関連ファイルは[こちら](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample)のリポジトリで管理されており、全体的なディレクトリ構成は以下のとおり。

  ```
  .github
  ├── workflows
  │   ├── run-build-backend.yml
  │   └── run-build-frontend.yml
  │
  01_Rolling
  ├── build_frontend
  │   ├── build.py
  │   ├── dev.ini
  │   ├── stg.ini
  │   ├── prod.ini
  │   ├── update_ecspresso_conf.py
  │   └── update_secret.py
  │
  ├── build_frontend
      ├── build.py
      ├── dev.ini
      ├── stg.ini
      ├── prod.ini
      ├── update_ecspresso_conf.py
      └── update_secret.py

  ```

### i.ワークフローファイル

- ワークフローファイルとは、`.github/workflows`配下の GitHubActions を実行するためのファイルである。
- build\.py はこちらのワークファイルを通じて実行される。

### ii.ビルド関連のファイル

- ビルド関連のファイル とは、`01_Rolling`配下の `build.py` と環境設定ファイルを指す。
- アプリケーションのビルドや AWS リソースに対する操作は当該シェルを通じで実行される。

### iii.更新関連のファイル

- 更新関連のファイル とは、`01_Rolling`ディレクトリ配下の`update_ecspresso_conf.py`と`update_secret.py`、環境設定ファイルを指す。
- ECS タスクや ECS サービスに対する更新は update_ecspresso_conf\.py、Secrets Manager へのシークレット登録作業は update_secret\.py を利用する。

## ユースケースごとのフロー

- 大きく分けて、**アプリを更新**する場合と、**アプリの更新なしでコンテナ数などを更新**する 2 パターンが存在する。
- 先述のとおり、**アプリを更新**する場合は GitHubAction を利用し、**アプリの更新なしでコンテナ数などを更新**する場合は基本的にはローカル PC から update_ecspresso_conf\.py や update_secret\.py を利用する。
- 初回デプロイの際は Gevanni の仕様上、**必ずフロー 1 を実施すること。**

### フロー 1：アプリを更新

- 想定ケース
  - アプリケーションコードの変更
  - Dockerfile の変更

#### 【事前準備】

- 管理用リポジトリに配置しているフォルダはサンプルコードのため、アプリケーションのリポジトリにクローンした後、各 PJ 毎にカスタマイズを実施する必要がある。

  ##### 環境ファイル（dev,stg,prod）

  - build\.py 内では CloudFormation スタック内から、パイプラインの関連リソース名を取得する処理が実装されている。
  - デプロイされているサービスを特定するために `SERVICE_PREFIX` を各サービスのサービス ID に修正する。
    - サービス ID は、「Gevanni 新規サービス作成」の申請後にインフラチームから共有される。
  - さらに、当該シェル内で docker build を実施するため、Dockerfile のパス`FILE_PATH`を指定する必要がある。
    - パスは build.py からの相対パスで設定する。
  - 以下 2 つの変数は、アプリチーム向けに提供されている環境では変更不要。

    - `PJ_PREFIX`
      - デプロイされているスタック名を特定するためのもの
      - `GEVANNI` で固定
    - `APP_NAME`
      - コンテナ名を特定するためのもの
      - 以下の値で固定
        - フロントエンド: `EcsApp`
        - バックエンド: `EcsBackend`

    **環境名(dev/stg/prod).ini**

    ```ini
    [Env]
    # スタックで指定されているプレフィックスを指定
    ENV=Stg01
    PJ_PREFIX=GEVANNI
    SERVICE_PREFIX=sample01-stg01-ab

    [EcsApp]
    # CDK上で指定しているAPP_NAMEを記載する
    APP_NAME=EcsApp

    [Docker]
    # Dockerfileのパス
    # docker buildの実行ディレクトリからの相対パス
    FILE_PATH=app/Dockerfile.dev
    ```

#### 【デプロイ作業】

- デプロイ作業は GitHubAction から実行する。
- Gevanni の仕様上、初回デプロイ時は必ずこのフロー 1 の手順を実施すること。
- 詳細な手順は[.github/workflows/README.md](../../.github/workflows/README_ja.md)を参照。

### フロー 2：アプリの更新なしでコンテナ数などを更新

- 前提として、ECS サービスやタスクに関する設定ファイルは、パイプライン上の S3 で管理されている。したがって、フロー 1 の**アプリを更新**の場合と異なり、GitHubAction ではなくスクリプト(update_ecspresso_conf\.py)を利用して S3 から設定ファイルを取得する方針としている。
- また、アプリチーム側で環境変数を使用する場合は、AWS の Secrets Manager を利用する。登録する際はマネジメントコンソールからではなく、AWS CLI コマンドを利用する方針としているが、一つずつ手打ち作業をするとオペレーションミスに繋がるためスクリプト(update_secret\.py)を利用して登録・更新を行う。
- 想定ケース
  - [ヘルスチェックの設定変更](#ヘルスチェックの設定変更)
  - [オートスケールの設定変更](#オートスケールの設定変更)
  - [CapacityProviderの設定変更](#capacityproviderの設定変更)
  - [コンテナスペック等の変更](#コンテナスペック等の変更)
  - [環境変数の登録](#環境変数の登録)

#### 【デプロイ作業】

- デプロイ作業は 3 フェーズに分けられる
  1. [ダウンロード](#ダウンロード)
  2. [書き換え](#書き換え)
  3. [アップロード](#アップロード)

#### ダウンロード

- update_ecspresso_conf\.py を用いて、image\.zip を S3 バケットからダウンロードする。
- フローは以下のとおり

  1. `python update_ecspresso_conf.py {環境名(dev/stg/prod)}`を実行する。
  2. image\.zip を S3 バケットからダウンロードするか、S3 バケットにアップロードするかを選択する。  
     ここでは、ecspresso conf を取得するために、`1`を入力する。  
      ![](../images/update-s3-operetion.png)
  3. 同一ディレクトリに ecspresso_conf フォルダが作成され、その中にダウンロードした ecspresso conf が格納される。基本的には、`ecs-service-def.json`と`ecs-task-def.json`に対して変更をかけていく。これらは、それぞれ ECS サービスと ECS タスクに相当する設定ファイルである。

     ![](../images/update-download-files.png)

  4. ecspresso conf や環境ファイルを後述する各パターンに応じて編集する。

#### 書き換え

#### 【ヘルスチェックの設定変更】

- ヘルスチェックはフロントエンドコンテナとバックエンドコンテナで実施方法が異なるため、設定の更新方法も異なる。
- フロントエンドは環境ファイルから、バックエンドは ecs-task-def\.json から設定変更を行う。  
  ![](../images/difference-between-front-and-back.dio.png)

  ##### フロントエンドの変更方法

  - 環境ファイルの以下項目を変更する。

    **環境名(dev/stg/prod).ini**

    ```ini
    [HealthCheck]
    # ヘルスチェックパス
    HEALTH_PATH=/
    # ヘルスチェック開始後、最初の異常判定を遅らせる時間（範囲：0~2,147,483,647秒）
    GRACE_PERIOD=10
    # ヘルスチェックを実施する間隔(範囲：5~300秒)
    INTERVAL=30
    # ヘルスチェックを失敗と見なす、ターゲットからレスポンスがない時間(範囲：2~120秒)
    TIMEOUT=5
    # ターゲットを正常と見なすのに必要なヘルスチェックの連続成功回数(範囲：2~10回)
    HEALTHY_THRESHOLD_COUNT=5
    # ターゲットを非正常と見なすのに必要なヘルスチェックの連続失敗回数(範囲：2~10回)
    UNHEALTHY_THRESHOLD_COUNT=2
    ```

  ##### バックエンドの変更方法

  - ecs-task-def.json の`healthCheck`を変更する。

    **ecs-task-def.json**

    ```json
    "healthCheck": {
      "command": [
          "CMD-SHELL",
          "curl -f http://localhost/ || exit 1"
      ],
      "interval": 30,
      "timeout": 5,
      "startPeriod": 5
    }
    ```

#### 【オートスケールの設定変更】

- オートスケールの変更はフロントエンド、バックエンド双方とも環境ファイルから変更する。

  **環境名(dev/stg/prod).ini**

  ```ini
  [AutoScale]
  # タスク数の最小値
  MIN_CAPACITY=2
  # タスク数の最大値
  MAX_CAPACITY=5
  # タスクの負荷がTARGET_VALUEを超えるとタスク起動数を増やし、収まるとタスク数が減る。
  TARGET_VALUE=60
  ```

#### 【CapacityProviderの設定変更】

- オートスケールの変更はフロントエンド、バックエンド双方とも環境ファイルから変更する。
- Fargate Spot
  - AWSの空きキャパシティを利用するため、最大70%の割引が適用
  - 空きキャパシティが確保できなくなった場合にタスクが中断される可能性がある
- base
  - 最小タスク数
  - FatgateとFargate Spotのどちらかのみ、1以上に設定可能
- weight
  - タスク起動比率
  - base数以上のタスクが起動する際には、設定した比率に近づくようにFatgateまたはFargate Spotが起動
  - Fargate Spotを使用しない場合は、`FARGATE_SPOT_WEIGHT`を0に設定する

  **環境名(dev/stg/prod).ini**
  ```ini
  [CapacityProvider]
  FARGATE_BASE=2
  FARGATE_WEIGHT=1
  FARGATE_SPOT_BASE=0
  FARGATE_SPOT_WEIGHT=2
  ```

### コンテナスペック等の変更

- ヘルスチェック、オートスケール以外の設定変更は、`ecs-task-def.json`や`ecs-service-def.json`から変更する。  
  ※フロントエンド、バックエンドとも JSON ファイルから変更
- ECS サービスに関わる設定変更は、`ecs-service-def.json`から、ECS タスクに関わる設定変更は`ecs-task-def.json`から行う。

#### 【環境変数の登録】

- update_secret\.py を用いて環境変数（Secrets Manager）の登録・更新作業を行う。
- 先述のとおり、フロー 1 から初回デプロイを実行した際はエラーが発生する。これは、`ecs-task-def.json`に設定されている Secrets Manager の値がサンプル値であることが原因。対処方法は[HowToUseEcspressoFirstStep.md](HowToUseEcspressoFirstStep.md)を参照。

##### 【事前準備】

1. `python update_secret.py {環境名(dev/stg/prod)}`を実行する。
2. 同一ディレクトリに`secret_list.json` が追加される。
3. JSON ファイルを下記のように編集する。

##### 【登録作業】

1. `python update_secret.py {環境名(dev/stg/prod)}`を実行する。
2. シークレット登録が完了すると、JSON ファイルは削除される。

- 登録したいシークレットをキーバリュー型で記載する。
- シークレットが不要な場合は JSON ファイルの中身をすべて削除する（初回のシークレット取得時は空ファイル）

  **secret_list.json**

  ```json
  {
    "username": "Gevanni",
    "password": "p@ssw0rd",
    "dbname": "user1"
  }
  ```

  コンソール上では以下のように登録されている  
  ![](../images/secret_console.png)

- シークレットの登録が完了したら、ecs-task-def.json で環境変数を設定する。

  **ecs-task-def.json**

  ```json
  "secrets": [
        {
          # コンテナ内で使用する変数名を設定
          "name": "DB_USER",
          # <SECRET_ARN>:{シークレットキー}:: となるように追加
          "valueFrom": "<SECRET_ARN>:username::"
        },
        {
          "name": "DB_NAME",
          "valueFrom": "<SECRET_ARN>:dbname::"
        }
      ]
  ```

  ※S3 バケットにアップロードする際に、シークレット ARN は実際の値に置換される。

#### アップロード

- update_ecspresso_conf\.py を用いて、image\.zip を S3 バケットにアップロードする。
- S3 バケットへの配置をトリガーにパイプラインが起動する。
- フローは以下のとおり
  1. `python update_ecspresso_conf.py  {環境名(dev/stg/prod)}`を実行する。
  2. image\.zip を S3 バケットからダウンロードするか、S3 バケットにアップロードするかを選択する。  
     ここでは、パイプラインを起動させるために、`2`を入力する。  
      ![](../images/update-s3-operetion2.png)
  3. パイプライン起動前の最終確認が求められるため、確認しながらコマンドを入力していく。  
     ![](../images/update-final-check.png)
  4. S3 バケットへのアップロードが完了すると、ecspresso_conf フォルダは削除される。
  5. S3 バケットへの配置をトリガーにパイプラインが起動する。
