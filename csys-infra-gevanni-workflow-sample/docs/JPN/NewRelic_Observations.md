# NewRelic Observations

ここでは、NewRelicで監視可能な項目とデフォルトのアラート設定について記載する。

## 監視項目
|Namespace|Metrics|Detail|
|-|-|-|
|AWS/ApplicationELB|RequestCount|リクエスト数|
|AWS/ApplicationELB|NewConnectionCount|新規接続数|
|AWS/ApplicationELB|RejectedConnectionCount|拒否された接続数|
|AWS/ApplicationELB|ClientTLSNegotiationErrorCount|クライアントTLS交渉エラー数|
|AWS/ApplicationELB|HTTPCode_ELB_4XX_Count|ELBの4XXエラー数|
|AWS/ApplicationELB|HTTPCode_ELB_5XX_Count|ELBの5XXエラー数|
|AWS/ApplicationELB|HTTPCode_Target_2XX_Count|ターゲットの2XXエラー数|
|AWS/ApplicationELB|HTTPCode_Target_4XX_Count|ターゲットの4XXエラー数|
|AWS/ApplicationELB|HTTPCode_Target_5XX_Count|ターゲットの5XXエラー数|
|AWS/ApplicationELB|TargetConnectionErrorCount|ターゲット接続エラー数|
|AWS/ApplicationELB|TargetTLSNegotiationErrorCount|ターゲットTLS交渉エラー数|
|AWS/ApplicationELB|TargetResponseTime|ターゲット応答時間|
|AWS/ApplicationELB|RequestCountPerTarget|ターゲットごとのリクエスト数|
|AWS/ECS|CPUUtilization|CPU使用率|
|AWS/ECS|MemoryUtilization|メモリ使用率|
|ECS/ContainerInsights|DesiredTaskCount|希望タスク数|
|ECS/ContainerInsights|RunningTaskCount|実行中のタスク数|
|ECS/ContainerInsights|PendingTaskCount|保留中のタスク数|
|TiDB|CostMetrics|今月の請求コスト|

## デフォルトのアラーム設定

|Namespace|Metrics|Aggregate Function|Threshold|Severity level|
|-|-|-|-|-|
|AWS/ApplicationELB|HTTPCode_Target_5XX_Count|sum|(暫定)10以上([推奨値は平均トラフィックの5%](https://docs.aws.amazon.com/ja_jp/AmazonCloudWatch/latest/monitoring/Best_Practice_Recommended_Alarms_AWS_Services.html#ECS))|Critical|
|AWS/ApplicationELB|TargetResponseTime|average|(暫定)5(s)以上|Warning|
|AWS/ECS|CPUUtilization|average|60(%)以上|Warning|
|AWS/ECS|CPUUtilization|average|80(%)以上|Critical|
|AWS/ECS|MemoryUtilization|average|60(%)以上|Warning|
|AWS/ECS|MemoryUtilization|average|80(%)以上|Critical|
|TiDB|CostMetrics|max|(暫定)1.00($)以上|Warning|
|TiDB|CostMetrics|max|(暫定)10.00($)以上|Critical|
