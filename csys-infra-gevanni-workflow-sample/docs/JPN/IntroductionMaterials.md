## 概要
### この資料の目的
Gevanniとは何かを理解できること

## 前提
以下資料の内製開発を基準とする<br>
https://mynavi.docbase.io/posts/3570807

Gevanni（内製インフラ集約基盤）or 内製開発環境を個別CDKで作成するか
- 判断材料は要件定義から
- 要件定義をみてGevanniの機能として対応していない要件がないか判断
  - インフラチームとしてはGevanniで対応できる場合は基本的にGevanniを推奨する
  - 対応できない例
    - VPCピアリング
    - フルマネージドな環境(APIGateway+lambda)などの未実装なもの

## Gevanni(ジェバンニ) とは何か
Gevanniとはマイナビ社内で作成されたコンテナ基盤を集約・統一化した仕組みです<br>
内製案件が年々増加する中で、従来の方法では以下のような課題が発生しています
- 案件ごとにAWSアカウントを払い出し、インフラ構築が都度手動で行われるため、リードタイムが長くなる
- ヒアリングやコードカスタマイズのためインフラチームの工数負荷が増加
- インフラチームの担当者が基本1人のためカスタマイズを行うことによるインフラ運用・保守の属人化

上記課題を解消するため、コンテナ集約基盤となります。<br>
Gevanniには以下機能が搭載されています。

**自動化されたプロビジョニング**<br>
利用者がGoogleFormで必要項目を入力し申請を行うだけで、バックエンドでインフラ構築パイプラインが起動し、コンテナ環境に必要なリソースが自動的に構築されます

**メトリクス・ログ監視**<br>
GevanniはNewRelicと連携しているため、NewRelicのアカウントさえあれば、GevanniのAWSアカウントに入らなくてもログやメトリクスを確認することが可能です<br>
※アカウントはGevanniを申請すれば、発行されます


### 共有リソースと専有リソース
Gevanniには**共有リソース**と**専有リソース**という概念が存在します。
それぞれについては以下の様に定義しています。
- 共有リソース: 環境単位で作成
  - 他のサービスと共有して利用するリソース群
  - リソースによる費用負担はクラウドインテグレーション統括部
- 専有リソース: サービス単位で作成
  - サービス固有のリソース群
  - リソースによる費用負担は各事業部

#### 共有リソース一覧

- VPC
- サブネット
- NAT Gateway
- VPCエンドポイント
- セキュリティグループ
  - VPCエンドポイント用
- ECS(踏み台)
  - ECSクラスター
  - ECR
- CDKデプロイパイプライン
- Route53ホストゾーン(ALBドメイン用)
- TiDB組織

#### 専有リソース一覧

- セキュリティグループ
  - VPCエンドポイント用以外
- ALB
- ACM
- WAF
- ECS(踏み台)
  - ECSタスク
- ECS(アプリ、バッチ)
  - ECSサービス
  - ECSタスク
  - ECR
  - コンテナデプロイパイプライン
  - Secrets Manager(コンテナ環境変数用)
- TiDB
  - プロジェクト
  - Serverlessクラスター
- EFS
- ElastiCache
- OpenSearch接続用VPCエンドポイント
- CloudWatch
  - メトリクス
  - ダッシュボード

### 従来との比較

| 条件 | 工数 |
| ---- | ---- |
|従来の場合|数週間-1か月|
|Gevanniを利用する場合|1日|

### メリットデメリット
#### 導入メリット
インフラチームにとってのメリット
- CDKの理解度が低い状態でもデプロイ可能
- 保守・運用対応の属人化是正

アプリチームにとってのメリット
- 迅速なクラウド環境構築
- 障害発生時の迅速な対応
  - NewRelicにより調査が容易になるため
  - 保守運用について集約基盤チームで対応するため

事業部にとってのメリット
- デジ戦人件費振替費用の削減
- 新サービスの迅速なリリース 

#### 導入デメリット
事業部にとってのメリット
- (既存環境から移行する場合)DBのデータ移行が必要※移行時メンテナンスが必要

アプリチームにとってのデメリット
- コンテナデプロイフローが難解※改善の余地あり


## Gevanniの全体構成
## システム構成図
![システム構成図](../images/gevannni-overall-architecture.dio.png)

### コンポーネント

- VPC：ネットワークを分離してセキュアに管理する基盤3階層のサブネットに分けられる
- CloudFront：CDNサービスでユーザーからのアクセスを効率化
- ALB：負荷分散用サービス。Cloudfrontからの通信を受け入れ、リクエストを適切なECSタスクに振り分ける
- ECS：アプリケーション(コンテナ)のデプロイと運用をサポート。自動スケーリングやリソース管理機能を利用
- EFS：ファイルストレージサービス。複数のコンテナ間で共有可能なファイルストレージとして、コンテンツの配置や設定データの共有として利用
- ECR：コンテナイメージ用のリポジトリ。アプリケーションのコンテナイメージを保存・管理するため利用
- ACM：SSL/TLS証明書を簡単に発行・管理するサービス。ウェブサイトの通信を暗号化してセキュアに保つ
- WAF：ファイアウォールサービス。SQLインジェクションやクロスサイトスクリプティングなどの攻撃を防御。IP制限も可能
- S3：オブジェクトストレージ。ログやコンテナデプロイに必要なリソースの配置場所として利用
- Secrets Manager：コンテナの環境変数(APIキーなどの認証情報)を安全に格納
- CodePipeline：S3に配置されたコンテナデプロイリソースの更新をトリガーにCodeBuildによるデプロイを自動で実行
- CodeBuild：S3に入っているecspressoファイルを利用しコンテナのデプロイを実行
- GitHub Actions：アプリケーション・インフラデプロイに利用する。後続のCodePipelineに繋ぐ
- NewRelic：メトリクス、ログの監視基盤ツール。コンテナ/ALB/CodeBuildのログを一元管理
- TiDB：HTAPを内包した分散SQLフルマネージドクラウドデータベース。従量制課金のため、小規模プロジェクトでは低コストで利用できる
- PagerDuty：障害検知ツール※2024/12月時点では未実装

#### SaaSサービスの選定理由
#### TiDB Cloud Serverless
- RDS(Aurora)における課題点
  - パフォーマンス視点
    - Provisionedの場合は指定のインスタンスクラスのスペックからスケールされない
    - ライターインスタンスのスケールアウトはできない（必ず1台）
      - 技術的にできないという訳ではない（マルチマスターのDB構成を組む）が、考慮ポイントなどハードルは高い
  - コスト視点
    - Provisonedの場合、起動時間が費用となり、自動停止/起動の仕組みも組み込みづらい
     - クエリの有無に関わらずコストが発生する
    - ServerlessV2によってリソースのスケール（0スケール）ができるが、単価が高い
      - 復旧要件によってはbinlogの有効化が必要
      - 有効化を行うと0スケールの戦略が取れなくなる
    - 大量のコネクションが発生するシステムにおいてはRDS Proxyの用意も必要
      - 特にLambdaからDB接続がある場合には必須
  - 開発・運用視点
    - RDSをIaCで管理する場合にDB復旧対応においてアプリ・インフラエンジニア双方での対応が必要
- TiDB Cloud Serverless（NewSQL）の選定理由
  - パフォーマンス視点
    - 変化するワークロードの需要に効率的に対応するために、storageとコンピューティング リソースを自動的に調整される
    - SQL処理（TiDB）とストレージ（TiKV）が分離しており、それぞれがスケールをする（ライターがスケールアウトしない問題を解消できる）
      - FYI
        - https://zenn.dev/nnaka2992/articles/learning_tidb_internal_for_beginner
    - データはTiKVに分散して保管されるためオートシャーディングされている
      - FYI
        - https://dev.classmethod.jp/articles/cedec2023-tidb/
  - コスト視点
    - 料金体系が使用したストレージ量＋クエリ処理で必要となったリソース量（RU）による従量課金
      - FYI
        - https://pingcap.co.jp/tidb-serverless-pricing-details/
    - TiDBの合計接続数制限は無制限となっているためRDSと比較する際はRDS Proxy込みでの金額比較となる
      - FYI
        - https://docs.pingcap.com/ja/tidbcloud/tidb-limitations#limitations-on-the-total-number-of-databases-tables-views-and-connections
    - 開発・運用視点
      - MySQLと高い互換性がある（完全新規のDBと比較して導入ハードルが低い）
        - 一部サポートされていない機能あるので開発において注意は必要
          - FYI
            - https://docs.pingcap.com/ja/tidb/stable/mysql-compatibility#unsupported-features
        - 検証確認は必要ではあるもののほぼ改修することなく移行が成功するケースもある
          - FYI
            - https://pingcap.co.jp/case-study/superstudio/
      - SaaS（マネージドサービス）なので操作はTiDBの管理画面から実施となる
        - IaC管理範囲外であるため、操作におけるステークホルダーを減らせる
      - AWS以外のインフラ環境（GoogleCloud、オンプレ※）も選択肢がある
        - オープンソースであるため、オンプレ物理サーバにTiDBをデプロイすることが可能（マネージドではないので運用負荷はトレードオフ）
#### NewRelic
- Gevanniにおけるオブザーバビリティツールで解決すべき課題
  - 必須要件
    - ログやCloudwatchメトリクスがツール上から（AWSポータル画面を使わずに）確認可能であること
  - 任意要件
    - アプリケーション性能ボトルネックの可視化が行えること
    - ビジネス的な指標（KPI/KGI）の達成状況を可視化できること
- NewRelicの選定理由
  - 機能面については任意要件まで満たせる見込みがある
    - 比較対象のDatadogもこの点については同様
  - 料金体系が集約基盤にマッチしている
    - Datadog
      - ホスト台数課金のため、コンテナ数増加に比例して費用も増加
    - NewRelic
      - ユーザーライセンス課金＋データ転送量課金のため、コンテナ数に比例しない
      - ライセンス課金が必要なユーザー（以下、フルプラットフォームユーザーと表記）はNewRelic設定変更が必要なユーザーのみであるため、最低限Gevanni運用者の数だけ用意があれば事足りる
        - APMによるドリルダウンを実施するにはフルプラットフォームユーザーが必要
  - オンボーディング（NewRelicエンジニアによる伴走）のサービス提供が契約に含まれている

### 各環境の定義
Gevanniでは環境は以下のように別れており、環境ごとにGoogleFormの申請が必要となる
- prod
  - 本番環境
- stg
  - 検証環境(アプリチーム向け)
    - 1つのprod環境に対し、stg環境が2つ以上存在する場合もある。
      - アプリチームの開発方針上、開発環境とステージングを分けたい場合
      - 負荷試験環境が必要な場合
- dev
  - 検証環境(Gevanni管理者向け)
    - Gevanni自体の検証に使用
    - アプリチームはこの環境にはアクセス不可
### AWSアカウントの分割方法

#### 本体用アカウント
- 本体のリソース
  - VPC
  - ALB
  - ECS
  - ECR
  - コンテナのパイプライン
- 初期リリース(2024年4月)時点では、各環境ごとに1アカウント
  - サービスの増加とともに、クォータを考慮してスケールアウト予定

#### Route53用アカウント

- ALBのドメイン用ホストゾーンを管理
  - CloudFront(または他CDN)のオリジンとして設定するドメイン
  - 上記ドメインのACM認証用CNAMEレコード
  - 上記についてもできればCDKで管理したい。
- 本体用から分ける理由
  - マイナビの全ドメインは、インフラチームの1つのAWSアカウント内のRoute53で集中管理している。
  - しかし、ALBのドメインは社外ユーザーが直接アクセスしないので、新規サービス作成時のリードタイム短縮のため、集約基盤専用のサブドメインをこのアカウントに移譲する。
  - 本体用アカウントがスケールする際にCDKコードの差異を生じさせないため

#### マーケットプレイス購入用アカウント

- 各環境の本体用アカウントから共通で使うリソースのマーケットプレイス購入用
  - AWSマーケットプレイスからこのアカウントでTiDB Cloudを購入する。
  - 購入リソースの例
    - TiDB Cloud
    - PostgreSQL版のTiDBのようなサービス(CockroachDBやYugabyteDBなど)
- 本体用から分ける理由
  - クォータの都合により、本体用アカウント間でサービスを移動する必要が生じる可能性がある。
  - 移動の際は、データ移行が一番のネックになると思われる。
  - そのため、TiDBを管理するAWSアカウントは、各環境ごとに共通としたい。

#### アプリ管理用アカウント

- アプリ側が管理するリソース
- CloudFrontやCloudFrontにアタッチするWAF、OpenSearch Serverless等はアプリ側のAWSアカウントで管理する方針となっている。理由は以下のとおり。
  - CloudFront
    - キャッシュ設定やパス設定がアプリケーションごとに大きく異なるため。
    - Traffic料金は請求タグによる費用振り替えできないため、CloudFront宛通信は主管部署が請求先となっているAWSアカウントで管理するため。
    - GevanniのALBの仕様として、CloudFront以外のCDNも対応可能にしているが、マイナビではCloudFront以外のCDNは事業部管理になることが多いため、CDNは基本的にアプリ管理としている。
  - WAF
    - 使用するマネージドルールやどのルールを除外するかがアプリケーションごとに大きく異なるため。
    - 誤検知があった場合、対象ルールの除外をアプリチームだけで迅速に行えるようにするため。
  - OpenSearch Serverless
    - サーバレスの管理ユニットであるOpenSearch Compute Units (OCU) はアカウント全体で共有されるため、本体用アカウントにデプロイすると上限に抵触する可能性があるため。 

### アプリチーム向けポイント
1. **本体用アカウント**にコンテナをデプロイする
1. 実際にリリースする際は**アプリ管理用アカウント**を発行する必要がある
1. コンテナ関連リソース(ECSサービス、ECSタスク、Secrets Manager)とCloudfront、WAFについてはアプリチームで管理を行う(不明な点があればインフラチームへ連絡)
1. 全体構成図にないAWSリソースや他のクラウド環境(GCP,azure)については現時点ではサポート対象外

## 具体的な利用方法
利用方法としては以下フローに従う

| 作業項番 | 対応内容 | 作業者 |
|:-----------|:------------ |:------------ |
| 1 | GoogleForm申請 | アプリチーム |
| 2 | 専有リソースデプロイ対応 | インフラチーム |
| 3 | TiDBCloudプロジェクト払い出し | インフラチーム |
| 4 | NewRelicのアカウント払い出し | インフラチーム |
| 5 | アプリチームへ情報を展開 | インフラチーム |
| 6 | TiDBCloudの設定対応 | アプリチーム |
| 7 | アプリケーションデプロイ対応 | アプリチーム |
| 8 | アプリアカウントへAWSリソース(CloudFront/WAF)デプロイ対応 | アプリチーム |

### GoogleForm申請
以下GoogleFormから申請を行う<br>
[Gevanni新規申請](https://docs.google.com/forms/d/e/1FAIpQLSc2DNtWofMn_bMaawnEQwvzhBYcCFw4x3Im22Mh2w23CVIzsg/viewform)

### 専有リソースデプロイ対応
GoogleForm申請の内容を元に専有リソースをCDKデプロイする

- デプロイ時、インフラチームの役割としては承認者と作業者に分かれる。作業内容は下記
  - 承認者
    - Backlog上の申請に対して妥当性を確認・承認する
  - 作業者
    - 自動生成されたCDKコードの内容を確認する
    - Githubプルリクエストをマージする
    - CDK管理外の設定を行う

デプロイフローは下記
![デプロイフロー](../images/application-flow-for-creating-aws-resource.dio.svg)

### TiDBCloudプロジェクト払い出し
CDK管理外のため、手動で対応する

1. TiDBCloudにログインする
1. ユーザー＞Organization　Settings＞Projects＞New Project Createでプロジェクトを作成する
1. Users＞InvlteからGoogleForm申請で指定された管理者ユーザーを設定する
    1. Email：GoogleForm申請で指定された管理者ユーザーを設定
    2. Role：Organization Viewer

### New Relicのアカウント払い出し
CDK管理外のため、手動で対応する

1. New Relicにログインする
1. ユーザー＞administration＞Access management＞Accounts＞Create accountでアカウント作成する
1. Groupsから以下を設定してグループを作成する
    1. Members：GoogleForm申請で指定された管理者ユーザーを設定
    1. Account access：
        1. Select a role: All Product Admin
        1. Select an account: Create accountで設定したアカウント名
1. API Keys＞Create a keyから以下設定をしてAPIキーを作成※作成後シークレットキー表示されるので控える
    1. Account：Create accountで設定したアカウント名
    1. Key type：Ingest-License
    1. Name：service_key
1. 本体用アカウントのマネジメントコンソールにログインする
1. AWS Sercret Managerを検索
1. New RelicのAPIキー用のSecretにシークレットキーを設定する
 
### アプリチームへ情報を展開
専有リソースデプロイ対応・ TiDBCloudプロジェクト払い出し・New Relicのアカウント払い出しが完了後、<br>
アプリケーションデプロイ対応で必要なAWSリソースの情報をアプリチームに連携する

### TiDBCloudの設定対応
1. GoogleFormからの申請後、Gメールにてサインアップ通知が届くのでサインアップ
1. clusterについて以下の設定を行う
    1. Region：ap-northeast-1
    1. ClousterPlan：Scalable Cluster
    1. Monthly Spending Limit：任意の数値

![ClusterConfiguration](../images/ClusterConfiguration.png)

### アプリケーションデプロイ対応
アプリをデプロイするため、アプリに関連するAWSリソース(ecsタスク、ecsサービス)をAWS環境(本体用アカウント)にデプロイする<br>
手順については次の資料を参考にする<br>
[コンテナデプロイ手順](https://github.com/mynavi-group/csys-infra-gevanni-workflow-sample/blob/main/docs/JPN/HowToUseEcspresso.md)

### アプリアカウントへAWSリソース(CloudFront/WAF)デプロイ対応
アプリチーム管理のAWSアカウントへのデプロイ方法を記述する<br>
将来の運用では、アプリチームに作業を実施してもらう方針だが、現状はインフラチームで1~2人アサインし、リソースのデプロイ及び、運用対応を実施する

#### サンプルコードの構成
サンプルコードではCDKで、WAF・CloudFront・S3・OIDCロール・OpenSearchServerlessを構築することができる<br>
![アプリチーム管理AWSリソース構成図](../images/OperationsdepartmentAccount_resource.dio.png)

```
サンプルコードのディレクトリ構造
csys-infra-gevanni-cf-sample/
|──bin/
|	└── gevanni-cf-sample.ts
|
|──lib/
|   ├─construct/
|   |  ├─cloudfront-construct.ts
|   |  ├─dashboard-construct.ts
|   |  ├─oidc-iamrole-construct.ts
|   |  └─waf-construct.ts  
|   ├─stack/
|      ├─cloudfront-stack.ts
|      ├─monitor-stack.ts
|      ├─oidc-stack.ts  
|      ├─opensearch-stack.ts
|      ├─role-for-app-team-construct.ts
|      └─waf-cloudfront-stack.ts
|
|──params/
   ├─dev.ts
   ├─stage.ts
   ├─prod.ts
   └─interface.ts

```

**WAFの初期設定**
- Mynavi-CSIRTが配布しているルールとIPリストの適用
  - 過去マイナビシステムに攻撃を仕掛けてきたIPアドレスの拒否
  - 重大な脆弱性に対する暫定的な対応
- 以下AWSマネジメントルールをカウントモードで適応
  - overrideAction_CommonRuleSet
  - overrideAction_KnownBadInputsRuleSet
  - overrideAction_AmazonIpReputationList
  - overrideAction_LinuxRuleSet
  - overrideAction_LinuxRuleSet
  - overrideAction_SQLiRuleSet
  - overrideAction_CSCRuleSet
- マイナビIP記載のIPリストをallowモードで適応
  - 設定IP：***************/25
- ベーシック認証を適応
- WAFのアクセスログはS3に保存する

**CloudFront初期設定**
- デフォルトビヘイビアはGevanni側のALBオリジンへ接続
  - カスタムヘッダー「x-pre-shared-key」を設定しCloudFrontからのみALBに接続を制御
- /static/*パスの場合、アプリアカウント内で作成されているS3オリジンへ接続
  - OACを設定し、CloudFrontからのみS3に接続を制御
- CloudFrontのアクセスログはS3に保存

**OpenSearchServerless初期設定**
- 検索用途のコレクションを作成

**OIDCロール初期設定**
- 任意のGithubActionsで利用するOIDCロールを作成
  - paramsファイルでGithubActionsを利用するGithubリポジトリを指定

#### 構築手順
**初回構築手順**
初回構築の時点でアプリチーム要件を反映することも可能だが、今回は初回構築時必ず必要な作業について記載する
1. サンプルコードを任意のGithubリポジトリにcloneする
	1. [Cloneするサンプルコード](https://github.com/mynavi-group/csys-infra-gevanni-cf-sample)
1. ローカルブランチを切り出して、サンプルコードのparamsファイルを変更する※変更内容は下記参照
1. 修正したローカルブランチについてレビュアーを集約基盤チームに指定して、プルリクを作成する
1. プルリクがマージされたら、SSOからデプロイ対象AWSアカウントのアクセスキーを取得する
	1. [SSOポータル](https://d-95671fcbe9.awsapps.com/start/#/)
![アクセスキー取得方法](../images/SSO_Access.png)
1. ローカル環境で`cdk bootstrap -c environment=環境名`を実行する
1. ローカル環境で`cdk diff --all -c environment=環境名`を実行し、デプロイ対象を確認する
1. ローカル環境で`cdk deploy --all -c environment=環境名`を実行し、デプロイする

paramsファイルについては、以下を修正<br>
- paramsファイル内のEnvのaccountをデプロイ先のAWSアカウントIDに変更する
```
	export const Env: inf.IEnv = {
    　envName: 'Stg',
    　account: '************',←ここを変更する
    　region: 'ap-northeast-1',
	};
```

- paramsファイル内のappAlbsParamについて、appAlbDomainsとpreSharedKeyの値をGevanni構築後に払い出されるファイルから転記する
```
　　export const appAlbsParam: inf.IAppAlbsParam = {
  　　appAlbDomains: ['www.example.com'],
  　　preSharedKey: 'pre-string-for-preSharedKey',
　　};
```

- paramsファイル内のCSIRTWAFParamCFについて、ruleGroupArnとCSIRTIpSetArnデプロイ先のAWSアカウントのArnに変更する
```
　　export const CSIRTWAFParamCF: inf.ICSIRTWAFParam = {
  　　isUseCSIRTManageRules: true,
  　　CSIRTManagerRules: {
    　overrideAction: { none: {} },
    　ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:global/rulegroup/CSIRTManagerRules/XXXX',
  　　},
  　　CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:global/ipset/CSIRTIpSet/XXXX',
　　};
```
**更新手順**
1. 初回構築でマージしたブランチからローカルブランチを切り出す
1. アプリ要件沿って修正を行う
1. 修正したローカルブランチについてレビュアーを集約基盤チームに指定して、プルリクを作成する
1. プルリクがマージされたら、SSOからデプロイ対象AWSアカウントのアクセスキーを取得する
1. ローカル環境で`cdk diff --all -c environment=環境名`を実行し、デプロイ対象を確認する
1. ローカル環境で`cdk deploy --all -c environment=環境名`を実行し、デプロイする

#### 更新ユースケース
**DNS設定**<br>
初回構築後、サービスのDNSが決まり次第、CloudFrontに紐づける代替えドメインを設定する必要がある<br>
以下手順
- アプリ管理AWSアカウントのマネジメントコンソールへログインし、ACM(certificate-manager)を選択
  - CloudFrontに紐づけるため、リージョンがバージニア北部(us-east-1)になっていること確認
- 証明書をリクエストを実施
  - DNS検証のため、DNS申請をBacklog上で作成
-  SSL証明書の発行されたことを確認したら以下のようにparamsファイルを更新する
```
　　export const CertificateIdentifier: inf.ICertificateIdentifier = {
  　　identifier: '',←証明書IDを設定
　　};

　　export const CloudFrontParam: inf.ICloudFrontParam = {
　　　fqdn: '',←CloudFrontに紐づける代替えドメインを設定
　　　createClosedBucket: false,
　　};　　
```

**クロスアカウントアクセス**<br>
Gevanni側から(主にECS Fargate側から)アプリ管理AWSのリソースにアクセスしたい要件がある場合は、以下手順での更新作業が必要となる<br>
集約基盤チーム・サービス担当のインフラメンバー・サービス担当のアプリメンバーそれぞれに作業が発生するため、注意すること
[クロスアカウントアクセス手順](https://github.com/mynavi-group/csys-infra-gevanni-infra/blob/develop/docs/02_Detaildesign/ECS_Cross_Account_Access.md)

**CloudFrontが複数必要な場合**<br>
サービスによっては、複数のCloudFrontが必要になるケースが存在する(ユーザーサイトと管理サイトが別れている場合など)
CloudFrontを複数作成する一例を記載する

- gevanni-cf-sample.tsの方で管理用のstackを作成する

```
以下例）

const cloudfront = new CloudfrontStack(app, `${pjPrefix}-admin-Cloudfront`, {
  pjPrefix: pjPrefix,
  webAcl: wafCloudfront.webAcl,
  CertificateIdentifier: config.AdminCertificateIdentifier,←Admin用に別のインターフェースを追加する
  cloudFrontParam: config.AdminCloudFrontParam,←Admin用に別のインターフェースを追加する
  appAlbsParam: config.appAlbsParam,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
  env: getProcEnv(),
  crossRegionReferences: true,
  accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
  webContentVersionExpiration: config.webContentVersionExpiration,
});
```



