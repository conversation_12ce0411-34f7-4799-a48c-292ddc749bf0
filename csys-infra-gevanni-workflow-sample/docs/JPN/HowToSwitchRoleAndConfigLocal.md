# HowToSwitchRoleAndConfigLocal

ここでは、IAMロールの認証情報を利用してローカルPCでスクリプトを実行する方法について記載する。

## 想定読者
- マイナビ社外ユーザ

## 手順
1. mynavicsys-vendor-bastionアカウントでの操作
      1. IAMユーザーでログイン
      1. CloudShellを起動
        ![](../images/open-cloudshell.png)
      1. AWS CLIを実行して、各サービス用のIAMロールにスイッチ
          ```
          aws sts assume-role --role-arn <role-arn-to-switch> --role-session-name AWSCLI-Session
          ```
      1. 一時クレデンシャルを取得
      1. ローカルPCに一時クレデンシャルを設定するコマンドを表示
1. 各メンバーのローカルPCでの操作
      1. 一時的な認証情報を設定
         - 各メンバーのローカルPCで以下のコマンドを実行
  
          ```
          export AWS_ACCESS_KEY_ID="XXXX"
          export AWS_SECRET_ACCESS_KEY="XXXX"
          export AWS_SESSION_TOKEN="XXXX"
          ```
      1. 踏み台コンテナ起動スクリプトや、コンテナデプロイスクリプトが、ローカルPCで実行可能になる。
  

  
