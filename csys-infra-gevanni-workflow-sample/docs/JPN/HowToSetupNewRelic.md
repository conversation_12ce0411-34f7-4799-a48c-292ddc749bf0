# HowToSetupNewRelic

ここでは、NewRelicの各種設定方法について記載する。

## 目次
- [権限対応表](#権限対応表)
- [ユーザー管理](#ユーザー管理)
  - [ユーザーの追加](#ユーザーの追加)
  - [ユーザーの権限変更](#ユーザーの権限変更)
- [アラート設定](#アラート設定)
  - [アラートポリシーの作成](#アラートポリシーの作成)
  - [通知先の設定](#通知先の設定)
  - [新規アラートの作成](#新規アラートの作成)

## 権限対応表
|主な役割|グループ|ライセンス|可能な操作|
|-|-|-|-|
|インフラチーム|`Infra`|`Full Platform User`|全ServiceのNew Relic Infrastructure<br>全ServiceのNew Relic Logs<br>全Serviceのアラート設定|
|アプリチームリーダー|`<ServiceID>_admin`|`Basic User`/`Full Platform Use`r|担当ServiceのNew Relic Infrastructure ダッシュボード閲覧<br>担当ServiceのNew Relic Logs閲覧<br>担当Serviceのアラート設定<br>(`Full Platform User`の場合)<br>担当Serviceのアプリチームユーザー管理<br>担当ServiceのAPM閲覧|
|アプリチームメンバー|`<ServiceID>_member`|`Basic User`|担当ServiceのNew Relic Infrastructure ダッシュボード閲覧<br>担当ServiceのNew Relic Logs閲覧|



## ユーザー管理
※ユーザー管理は、ライセンスが`Full Platform User`のユーザーのみ行うことができる。  
`Basic User`が新規でユーザーを招待したい場合は、SlackからGevanni管理者へ連絡する。（今後Formに移行予定）  

### ユーザーの追加
#### Step1 ユーザーの追加

左下にある`Add User`をクリック。  
`Name`と`Email`を入力、`Type`は特別な理由がない限りBasicを選択。  
`Create User`をクリックすると招待が送信される。  

#### Step2 ユーザー管理画面に移動する

左下の自分のユーザー名をクリックし、`Administator`>`User Management`を選択。  
ユーザー一覧から、対象ユーザーをクリック。  
`Access`から追加対象のグループに追加する。  

##### 追加対象のグループについて

- インフラメンバーの場合は、`Infra`  
- アプリメンバーでアラートの設定を行う場合は、`ServiceID_admin`  
- アプリメンバーでアラートやログの閲覧のみの場合は、`ServiceID_member`  

### ユーザーの権限変更

左下の自分のユーザー名をクリックし、`Administator`>`User Management`を選択。  
ユーザー一覧から、対象ユーザーをクリック。  
`Access`から対象のグループに変更する。  

## アラート設定
※アラート設定は、`Infra`/`<ServiceID>_admin`グループに所属するユーザーのみ行うことができる。  

アラート条件は、アラートポリシーに纏められる。  
アラート時の挙動(通知先など)を別にしたい場合は、アラートポリシーを分ける必要がある。  

[![text](../images/NR_Alert_Map.png)](https://docs.newrelic.com/jp/docs/alerts/create-alert/create-alert-condition/alert-conditions/)


### アラートポリシーの作成

左のサイドメニューから、`Alerts`>`Alert Policies`を選択

![alt text](../images/NR_Alert_Policies._View.png)

ポリシー名とグルーピング方法を指定する  

- ポリシー名：`<ServiceID>-policy`
- グルーピング方法：任意のものを選択

最後に`Set up notifications`を押すことで完了

![alt text](../images/NR_Alert_Policy_Creation.png)

### 通知先の設定

通知方法はポリシーごとに設定できる。  
またポリシー内でも複数設定が可能である。  
通知設定は、ポリシー作成後又はポリシー詳細画面から設定できる。  

![alt text](../images/NR_Alert_Policy_Notification.png)

![alt text](../images/NR_Alert_Policy_Setting.png)

各種通知設定方法はこちら  
https://docs.newrelic.com/docs/alerts/get-notified/notification-integrations/

### 新規アラートの作成

アラートポリシーは既にあるものとする。  

#### Step1

左のサイドメニューから、`Alerts`>`Alert Conditions`を選択

![alt text](../images/NR_Alert_Condition_Step1.png)

#### Step2

右上の`New alert condition`から、`Write your own query`を選択

![alt text](../images/NR_Alert_Condition_Step2.png)

#### Step3

続いて、監視対象データのQueryを設定する。  

まず左上の`Condition Owner`が自分のプロジェクトになっているかを確認。（違う場合は修正）  

Queryの上にあるドロップダウンは、データソースのアカウントである。  
メトリクスから作成する場合は、データソースにGevanni本体のアカウントを指定する。  
また、クエリはダッシュボード上のメトリクス右上の`View query`から確認できる。 (`TIMESERIES`/`SINCE`/`LIMIT`は不要)   
※ データソース名は、小文字・大文字を区別するので注意
※ その他、クエリ用のアラート構文等は[こちら](https://docs.newrelic.com/jp/docs/alerts/create-alert/create-alert-condition/create-nrql-alert-conditions/#syntax)を参照

![alt text](../images/NR_Metrics_View_Query.png)

![alt text](../images/NR_Alert_Condition_Step3.png)

設定しQueryに問題が無ければ`Next`を押す。

#### Step4
対象のメトリクスのクエリが表示されるので、次の画面でアラートの閾値などを設定  
今回は閾値を80に設定し、`Next`で次へ  

![alt text](../images/NR_Alert_Condition_Step4.png)

#### Step5

最後にアラート名と作成先のポリシーを選択し`Save condition`を押すことでアラートの作成は完了である。
(ポリシーは後から変更できないので注意)

![alt text](../images/NR_Alert_Condition_Step5.png)
