# HowToUseBatchApiTrigger

本資料では、Gevanni バッチの API で実行するタイプの物に関して使用方法をまとめる。

以下に API から呼び出されるバッチの構成図を示す。

![.](/docs/images/batch/batch-api-trigger-architecture.png)

図に示した通り、API から呼び出されるバッチは、API Gateway を利用して呼び出され、呼び出しのパスは [定義ファイルの apiPath](/docs/JPN/batch/HowToUseBatch_JA.md#type-を-api-にした場合に指定可能な値) で指定したものとなる。  
なお、API Gateway の URL は Gevanni チームより連携される。

## バッチ実行方法

### 前提

- バッチと呼び出しを行う API Gateway がデプロイ済みであること
- Gevanni チームより連携されたアプリチーム用 IAM Role のクレデンシャル
    - クレデンシャルの取得方法についてはここでは割愛

今回はサンプルコードとして curl での呼び出し方法を示す。  
API Gateway には IAM 認証がかかっているため、呼び出す場合は SigV4 署名を行ったリクエストを送る必要があるため、curl 以外で呼び出す場合は、SigV4 署名を行うライブラリ等を使用してリクエストすること。  
また、API のレスポンスには、**バッチの実行結果は記載されない** ため、バッチの実行結果が確認したい場合は NewRelic のログを確認すること。

#### コマンドの上書きをしない場合

```bash
# クレデンシャルの確認
# $ echo $AWS_ACCESS_KEY_ID
# ASIASAMPLE12345
# $ echo $AWS_SECRET_ACCESS_KEY
# samplesecret123453abcd
# $ echo $AWS_SESSION_TOKEN
# IQoJbSAMPLE///////......

curl -X POST -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "<Gevanniチームに連携されたAPIGatewayのURL>/<定義ファイルに書いたapiPathの値>"

# うまく実行できた場合以下のような結果が出力されます
# {"executionArn": "arn:aws:states:ap-northeast-1:*******:execution:********:*********", "startDate": "2025-01-01T01:00:00.000000+00:00", ...  
```

##### コマンドの上書きをしない場合のサンプルコマンド

```bash
curl -X POST -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/execute"
```

#### コマンドの上書きを行う場合

> [!WARNING]
> コマンドの上書きを行う場合は、StepFunctions の定義ファイル (<バッチ名>.asl.json) の Overridde セクションに `ContainerOverrides.$` が含まれている必要がある。
> 詳しくは、[StepFunctions 定義ファイルについて](/docs/JPN/batch/HowToUseBatch_JA.md#stepfunctions-ステートマシンの定義ファイルについて) や、[サンプルファイル](/02_Batch/usecases/sidecar/sidecar.asl.json) を確認すること。

```bash
 curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    -d '{"batchName": "<Gevanniサービス名>-<バッチ名>", "command": [{"containerName": "<コマンドを上書きしたいコンテナの名前>", "passedCommand": "<渡したいコマンド(配列形式)>"}]}' \
    "<Gevanniチームに連携されたAPIGatewayのURL>/<定義ファイルに書いたapiPathの値>"
```

##### コマンドの上書きを行う場合のサンプルコマンド

```bash
 curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    -d '{"batchName": "sample01-dev01-ab-BatchApp", "command": [{"containerName": "BatchContainer", "passedCommand": ["echo", "hello gevanni"]}]}' \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/commandoverwrite"
```

## 呼び出し可能なバッチの確認

払い出された API Gateway のルートにリクエストを送ると呼び出し可能なバッチの名前が返却される。
このパスにも IAM 認証がかかっているため、呼び出しの際、クレデンシャルが必要となる。

```bash
curl -H "X-Amz-Security-Token: ${AWS_SESSION_TOKEN}" \
    --user "${AWS_ACCESS_KEY_ID}:${AWS_SECRET_ACCESS_KEY}" \
    --aws-sigv4 "aws:amz:ap-northeast-1:execute-api" \
    "https://sampleendpoint1.execute-api.ap-northeast-1.amazonaws.com/sample01-dev01-ab/"

# {"statusCode":200,"message":"{\"availableBatchNames\":[\"sample01-dev01-ab-BatchApp\"]}"} ↲                                                                            
```
