# バッチユースケース一覧

## ユースケースごとの設定例

`02_Batch` ディレクトリにサンプルのファイル群を格納しているため、所望のユースケースに従って設定ファイルを参照すること。  
※ 各ディレクトリには dev.toml しか作成していないので注意してください。

### 特定の時間に定期的に実行されるバッチ

![.](/docs/images/batch/batch-BatchApp.drawio.png)

[02_Batch/BatchApp/](/02_Batch/usecases/BatchApp/) に実装例を記載。  
以下の設定で定義ファイルを作成。

- バッチ名: BatchApp
- タスク名: BatchTask
    - コンテナ名: BatchContainer
        - コンテナイメージパス: batch/simple-batch
        - 上書きコマンド: なし
- 起動時間: 毎日 14:00

### タスク A を行ったあとタスク B を行うような順序あるバッチ

StepFunctions ステートマシンを複数の ECS タスクを起動するように定義する。タスク定義ファイルをタスクの数分用意する。

![.](/docs/images/batch/batch-sequence.drawio.png)

[02_Batch/sequence/](/02_Batch/usecases/sequence/) に実装例を記載。  
以下の設定で定義ファイルを作成。

- バッチ名: sequence
- タスク名:
    - taska
        - コンテナ名: batch
            - コンテナイメージパス: batch/aggregate
            - 上書きコマンド: `["-i", "billing"]`
                - コンテナイメージの `ENTRYPOINT` に実行コマンドが記載されているイメージ
    - taskb
        - コンテナ名: batch
            - コンテナイメージパス: batch/export
- 起動時間: 平日 14:00

### 共有ファイルストレージ (EFS) をマウントするようなバッチ

ECS の定義ファイルにボリュームマウントの設定を追加する。(EFS が用意されている前提)  
なお設定の際 EFS のファイルシステム ID を使用するが、この値は Gevanni チームから連携される。

![.](/docs/images/batch/batch-mount.drawio.png)

[02_Batch/mount/](/02_Batch/usecases/mount/) に実装例を記載。  
以下の設定で定義ファイルを作成。

- バッチ名: mount
- タスク名: mount_task
    - コンテナ名: batch
        - コンテナイメージパス: batch/save
        - 上書きコマンド: なし
        - ファイルシステム ID: fs-0a1b2c3d4e5f6g7h8
            - EFS をマウントするコンテナ: batch
            - マウント設定: `/:/mnt/efs`
            - 読み取り専用: false
- 起動時間: 毎分

### Gevanni アカウント以外のアカウントのリソースを操作できるバッチ

ウェブアプリ側と同様の手順で Gevanni アカウント以外の AWS アカウントのリソースにアクセスする。

![.](/docs/images/batch/batch-cross-account.drawio.png)

設定ファイルに変更が必要な箇所はないため、サンプルはない。 (図では、BatchApp バッチを例にしている)  
以下の手順で Gevanni アカウント以外のアカウントのリソースを操作する。

- 事前に済ませておくこと
    1. ウェブアプリ側と同様に Gevanni アカウントで使用可能な IAM ロールを作成
    1. そのロールにアクセスしたいリソースへのアクセス許可を設定
- バッチアプリ側で行うこと
    1. アプリケーション内で AssumeRole API を実行
    1. AssumeRole API のレスポンスから得た認証情報を使用して、所望のリソースにアクセス

### 環境変数を使用するバッチ

ウェブアプリ側と同様の手順で ECS タスクに環境変数を付与する。  
シークレットはタスクごとに作成されている。  

![.](/docs/images/batch/batch-secret.drawio.png)

[02_Batch/secret/](/02_Batch/usecases/secret/) に実装例を記載。  
以下の設定で定義ファイルを作成。

- バッチ名: secret
- タスク名: task
    - コンテナ名: batch
        - コンテナイメージパス: batch/app
        - 上書きコマンド: なし
        - 設定する環境変数:
            - DB_HOST: シークレットに設定
            - DB_PASSWORD: シークレットに設定
            - DB_NAME: 環境変数に設定
            - DB_PORT: 環境変数に設定
        - SecretsManager の ARN: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - DB_HOST のシークレットマネージャー上のキー: DB_HOST
            - DB_PASSWORD のシークレットマネージャー上のキー: DB_PW
- 起動時間: 毎日 14:00

環境変数の設定方法は、[ウェブアプリ側の環境変数追加方法](/docs/JPN/HowToUseEcspresso.md#環境変数の登録) を参考に、[02_Batch/scripts/update_batch_secret.py](/02_Batch/scripts/update_batch_secret.py) を実行すること。  
このスクリプトでは以下のように、操作したいシークレットがある環境と、バッチの設定ファイルへのパス、バッチ名が必要となる。

```bash
# このリポジトリと同様のディレクトリレイアウトになっている想定
python ./02_Batch/scripts/update_batch_secret.py dev 02_Batch/BatchApp BatchApp
# -h, --help で Usage が見れるので、使い方がわからなくなったらこれを見てください
python ./02_Batch/scripts/update_batch_secret.py -h
```

### サイドカーを使用するバッチ

1つの ECS タスク内で複数のコンテナを実行するしたい場合、サイドカー方式を利用する。  
ECS のタスク定義を変更し、サイドカーで利用できるように設定する。

![.](/docs/images/batch/batch-sidecar.drawio.png)

[02_Batch/sidecar/](/02_Batch/usecases/sidecar/) に実装例を記載。  
以下の設定で定義ファイルを作成。

- バッチ名: sidecar
- タスク名: task
    - コンテナ名: batch
        - コンテナイメージパス: batch/app
        - 上書きコマンド: `["node", "app.js"]`
        - 設定する環境変数:
            - DB_HOST: シークレットに設定
            - DB_PASSWORD: シークレットに設定
            - DB_NAME: 環境変数に設定
            - DB_PORT: 環境変数に設定
        - SecretsManager の ARN: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - DB_HOST のシークレットマネージャー上のキー: DB_HOST
            - DB_PASSWORD のシークレットマネージャー上のキー: DB_PW
        - コンテナの依存関係: sidecar が HEALTHY の場合
    - コンテナ名: sidecar
        - コンテナイメージパス: batch/sidecar
        - 上書きコマンド: `["-sq"]`
        - 設定する環境変数:
            - API_KEY: シークレットに設定
        - SecretsManager の ARN: `arn:aws:secretsmanager:ap-northeast-1:123456789012:secret:Dev01-GEVANNI-sample01-dev01-ab/secret-AbCdEF`
            - API_KEY のシークレットマネージャー上のキー: API_KEY
        - コンテナのヘルスチェックコマンド: `["CMD-SHELL", "curl localhost:5000 || exit 1"]`
- 起動時間: 毎日 14:00
