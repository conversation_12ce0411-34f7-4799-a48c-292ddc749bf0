# HowToUseGevanniBatch

ここでは Gevanni のバッチ基盤で使用するインフラ設定ファイルの使い方や詳細について説明する。

## Gevanni バッチ構成図

![.](/docs/images/batch/batch-overall-for-app.drawio.png)

バッチ基盤は主に、バッチアプリの実行を指示する AWS サービスとバッチアプリを実行する AWS サービスで構成されている。  
※ バッチアプリとは、バッチ処理を行うコンテナ上で実行されるアプリケーションを指す。  
それぞれ以下の通り。

- バッチアプリの実行を指示するサービス
    - EventBridge Scheduler
    - Lambda
    - API Gateway (現在未実装、設計検討中)
- バッチアプリを実行するサービス
    - StepFunctions
    - ECS

> [!TIP]
> Gevanni では、StepFunctions のステートマシンを1つのバッチとして捉え、バッチ内で実行している ECS タスクをタスクと表記する。

Gevanni のバッチでは設定ファイルによって、以下のようなバッチを作成することができる。  
例えば、下の図のようにバッチ実行に関する定義ファイルによっては、1つのバッチに複数のトリガーを設定できる。

![.](/docs/images/batch/batch-exec-multiple-triggers.drawio.png)

また、下の図のようにステートマシンの定義によっては1つのバッチに複数のタスクを含めることができる。

![.](/docs/images/batch/batch-exec-multiple-tasks.drawio.png)

このようにして、このバッチ基盤では設定ファイルを変更して、主に以下のようなバッチを作成することができる。  

- 特定の時間に定期的に実行されるバッチ
- タスク A を行ったあとタスク B を行うような順序あるバッチ
- 共有ファイルストレージ (EFS) をマウントするようなバッチ
- Gevanni アカウント以外のアカウントのリソースを操作できるバッチ
    - 今のところ S3 のみサポート
- 環境変数を使用するバッチ
- サイドカーを使用するバッチ

それぞれのユースケースについてどのような設定を施せばよいかについては、**[ユースケースごとの設定例](/docs/batch/usecases.md#ユースケースごとの設定例)** に記載してあるため適宜参照すること。

バッチ基盤はインフラリポジトリ内の CDK コードと、バッチアプリ用 Code Pipeline によってデプロイされる。
それぞれのツールで作成するリソースは以下の通り。

- CDK
    - 各種 IAM Role
    - CodePipeline
    - Lambda
- CodePipeline
    - ECS タスク定義
    - StepFunctions ステートマシン
        - ECS タスクの実行順の制御のために使用する
    - EventBridge Scheduler

CodePipeline で作成するリソースについては、アプリチームリポジトリで特定のファイルを管理してもらう必要があるため、
この資料では、CDK で作成されるリソースについては説明せず、CodePipeline で作成するリソースやそのとき使用するファイル群について説明する。

### バッチデプロイパイプラインについて

以下にバッチデプロイパイプラインの構成図と、バッチアプリがデプロイされるまでの流れを示す。  
基本はウェブアプリ側と同様の手順を経て、Gevanni 環境にバッチアプリがデプロイされる。

![.](/docs/images/batch/batch-app-workflow.drawio.png)

1. アプリチームリポジトリ内のバッチデプロイ用 GitHub Actions ワークフローが実行される
1. ワークフローで、ビルド用スクリプトを実行
1. ビルド用スクリプト内で、バッチアプリをビルドして、ECR へプッシュ
1. ビルド用スクリプト内で、後述の設定ファイルを zip 化し、Gevanni 環境の S3 バケットへアップロード
    - ここで CodePipeline が実行される
1. CodePipeline 内の CodeBuild で設定ファイルを元に、バッチ基盤をデプロイ

> [!IMPORTANT]
> このため、アプリリポジトリでは以下のファイルを作成し、管理する。
>
> - ECS のタスク定義ファイル
> - StepFunctions ステートマシンの定義ファイル
> - バッチ実行を行うサービスを作成するための定義ファイル
> - 環境ごとの値を制御する環境ファイル
> - ビルド用スクリプトファイル

ビルド用スクリプトは、[02_Batch/scripts/build_batch](/02_Batch/scripts/build_batch/) に格納されているため、こちらをアプリチームリポジトリにコピーして使用すること。  
すべてのバッチで同じスクリプトを使用するため、バッチの設定ファイルごとにスクリプトを用意する必要はない。  
スクリプトの詳細はワークフローの詳細部分に記載してあるためそちらを参照すること。

次に各設定ファイルの詳細と仕様について説明する

## バッチ設定ファイルについて

### 概要

各バッチ設定ファイルは基本的にはテンプレートファイルとなり、バッチパイプライン内で具体的な値が埋められる形となる。  
しかし、バッチの名前やタスクの名前など Gevanni を利用する際申請した項目の値を設定ファイル内に記載するためバッチごとに設定ファイルを変更する必要がある。  
また、設定ファイルはバッチごとに作成する必要がある。  

### 設定ファイルの配置例

以下にディレクトリ構造の一例を記載する。

```plain-text
リポジトリの任意の場所/
└── batch/
    ├── configs/
    │   ├── batch1/
    │   │   ├── dev.toml
    │   │   ├── stg.toml
    │   │   ├── prod.toml
    │   │   ├── batch1.asl.json
    │   │   ├── batch1.trigger.json
    │   │   └── ecs-task-def.json
    │   └── batch2/
    │       ├── dev.toml
    │       ├── stg.toml
    │       ├── prod.toml
    │       ├── batch2.asl.json
    │       ├── batch2.trigger.json
    │       ├── task1-ecs-task-def.json
    │       └── task2-ecs-task-def.json
    └── scripts/
        └── build_batch/
            ├── main.py
            └── requirements.txt
```

### 設定ファイルに入力する値

設定ファイルのテンプレートファイルが [/02_Batch/batch_config_templates](/02_Batch/batch_config_templates/) に格納してあるため、こちらを例に以降の説明を行う。  
なおこのテンプレートファイルは、`<>` によって囲まれた値を正しい値で埋めて利用することを想定している。  
※ 当該ディレクトリに含まれているファイルのファイル名も変更する必要があり、ファイル名は、`<>` ではなく `[]` で囲まれている。  
以下に `<>` に囲まれた値と設定すべき値について記す。

| 項目名 | 説明 |
|:---:|:---:|
| `batch_name` | **Gevanni を利用する際申請したバッチ名** |
| `task_name` | **Gevanni を利用する際申請したタスク名** |
| `container_name` | **Gevanni を利用する際申請したコンテナ名** |
| `image_place_hodlder_value` | **どのような値でも良い (英数字アンダースコアのみ使用可能)** |
| `trigger_name` | **どのような値でも良い (英数字アンダースコアのみ使用可能)** |
| `Gevanni_env_name(env)` | **Gevanni の環境名 (例: Dev01, Stg01, Prod01)** |
| `Gevanni_service_prefix(env)` | **Gevanni のサービス名 (例: sample01-dev01-ab, sample01-stg01-ab, sample01-prod01-ab)** |
| `task_definition_file_name` | **タスク定義ファイルの拡張子を含むファイル名** |
| `batch_docker_build_context` | **バッチアプリのビルド時に指定するパス (リポジトリルートからの相対パス)** |

### ECS タスク定義ファイルについて

このファイルでは、バッチアプリを実行する ECS タスクの定義を管理する。  
ファイル名は、**ファイルの終わりが、`ecs-task-def.json` となっていれば何でも良い。**

タスク定義ファイルで設定できる項目は以下の通り。

- 環境変数の設定
    - 設定方法は後述
- サイドカーの設定
- (EFS を使用している場合) ボリュームマウントの設定

> [!NOTE]
> 他にも設定できる項目があるが、詳細については、[Amazon ECS タスク定義パラメータ](https://docs.aws.amazon.com/ja_jp/AmazonECS/latest/developerguide/task_definition_parameters.html) を参照すること。

ただし、Gevanni では以下のパラメータはあらかじめ決まった値を入れるか、決まったフォーマットで値を埋める必要がある。  

- 決まった値をいれる箇所 ([テンプレートファイル](/02_Batch/batch_config_templates/ecs-task-def.json) に記載の通りの値を設定する)
    - 各コンテナ定義の `logConfiguration`
    - コンテナ名が `"logRouter"` のコンテナの定義
    - `family`
    - `executionRoleArn`
- 決まったフォーマットで値をいれる箇所 ([テンプレートファイル](/02_Batch/batch_config_templates/ecs-task-def.json) に記載されている値から決まった箇所だけ変更する)
    - `cpu`
    - `taskRole`
    - `memory`
    - `name`
    - `image`

### StepFunctions ステートマシンの定義ファイルについて

このファイルでは、バッチで使用されるステートマシンの定義を管理する。  
ファイル名は、**`<batch_name>.asl.json`** とすること。  

> [!NOTE]
> 設定できる値については [Amazon State Language](https://states-language.net/spec.html) を参照すること。

ただし、Gevanni では以下のパラメータはあらかじめ決まった値を入れるか、決まったフォーマットで値を埋める必要がある。  

- 決まった値をいれるところ ([テンプレートファイル](/02_Batch/batch_config_templates/<batch_name>.asl.json) に設定されている値をそのまま使用する)
    - `Type` が `Task` のステートの Resource 部分
    - `Type` が `Task` のステートの Parameters 部分 (`Overrides` 部分以外)
- 決まったフォーマットで値をいれる箇所 ([テンプレートファイル](/02_Batch/batch_config_templates/<batch_name>.asl.json) に設定されている値から決まった箇所だけ変更する)
    - `Type` が `Task` のステートのステート名部分
    - 上記のステートを開始するための `StartAt` 部分
    - `Type` が `Task` のステートの Parameters の `Overrides` 部分
        - 以下のどちらかの形式を記述すること
            - `{}` (コマンドの上書きを使用しない場合)
            - `{ "ContainerOverrides.$": "$.containerOverrides.[{{ <task_name>_commands }}]" }` (する場合)

### バッチ実行を行うサービスを作成するための定義ファイルについて

このファイルは、バッチの実行を行う EventBridge Scheduler の起動時間や、ECS タスク実行時にわたすコマンドなどを管理する。  
ファイル名は、**`<batch_name>.trigger.json` とすること。**

Gevanni のみで使用されるファイルであるため、以下に使用可能な値を記す。

#### trigger.json で使用可能な値

使用可能な値を、以下の表で示す。  
表のパス部分には、ルート要素から各項目までのパスが記載されている。(ルート要素は `$` と記載)

|項目|パス|データ型|必須|説明|備考|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`version`|`$.version`|`string`|◎|スキーマのバージョン|現状 "1" のみ利用可能|
|`triggers`|`$.triggers`|`object`|◎|トリガーの定義||
|`<trigger_name>`|`$.triggers.<trigger_name>`|`string`|◎|トリガーの名前||
|`type`|`$.triggers.<trigger_name>.type`|`cron` \| `api`|◎|トリガーのタイプ|`cron` か `api` のどちらかを指定する|

##### type を cron にした場合に指定可能な値

|項目|パス|データ型|必須|説明|備考|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`cron`|`$.triggers.<trigger_name>.cron`|`object`|◯|EventBridge Scheduler の起動時刻の定義|cron タイプでは必須で指定 (後述の値をどれも使用しない場合は空オブジェクトを指定すること)|
|`minute`|`$.triggers.<trigger_name>.cron.minute`|`string`| |EventBridge Scheduler の起動時刻 (分)|cron タイプのみ指定可能 (デフォルト *)|
|`hour`|`$.triggers.<trigger_name>.cron.hour`|`object`| |EventBridge Scheduler の起動時刻 (時間)|cron タイプのみ指定可能 (デフォルト *)|
|`date`|`$.triggers.<trigger_name>.cron.date`|`object`| |EventBridge Scheduler の起動時刻 (日付)|cron タイプのみ指定可能 (デフォルト *)|
|`month`|`$.triggers.<trigger_name>.cron.month`|`object`| |EventBridge Scheduler の起動時刻 (月)|cron タイプのみ指定可能 (デフォルト *)|
|`dayOfWeek`|`$.triggers.<trigger_name>.cron.dayOfWeek`|`object`| |EventBridge Scheduler の起動時刻 (曜日)|cron タイプのみ指定可能 (デフォルト *)|
|`year`|`$.triggers.<trigger_name>.cron.year`|`object`| |EventBridge Scheduler の起動時刻 (年)|cron タイプのみ指定可能 (デフォルト *)|
|`state`|`$.triggers.<trigger_name>.state`|`"ENABLED"` \| `"DISABLED"`|◯|EventBridge Scheduler の設定|`cron` を指定した場合は必須|
|`inputs`|`$.triggers.<trigger_name>.inputs`|`object`|◯|トリガーがステートマシンに渡す入力|前述のステートマシンの定義で、`ContainerOverrides` オプションを指定した場合以下のすべての項目が必須となる|
|`<task_name>`|`$.triggers.<trigger_name>.inputs.<task_name>`|`object`|◯|ステートマシンに含まれるタスクにわたす入力の内容|`inputs` を指定した場合は必ず指定する|
|`commands`|`$.triggers.<trigger_name>.inputs.<task_name>.commands`|`list`|◯|タスクに渡すコマンド||
|`containerName`|`$.triggers.<trigger_name>.inputs.<task_name>.commands[].containerName`|`string`|◯|コマンドを上書きしたいコンテナの名前|commands を記載した場合は必ず指定する|
|`passedCommand`|`$.triggers.<trigger_name>.inputs.<task_name>.commands[].passedCommand`|`list`|◯|上書きするコマンド|commands を記載した場合は必ず指定する|

※ ◎は、必須項目であることを示し、◯は条件付きで必須となる項目を示す。  
※ `<>` で囲まれている値は任意の値が指定できることを示す。

##### type を api にした場合に指定可能な値

|項目|パス|データ型|必須|説明|備考|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`apiPath`|`$.triggers.<trigger_name>.apiPath`|`string`|◎|API のパス|API タイプでは必須で指定|

※ ◎は、必須項目であることを示し、◯は条件付きで必須となる項目を示す。  
※ `<>` で囲まれている値は任意の値が指定できることを示す。

> [!TIP]
> スキーマファイルが [.vscode/schemas/trigger-schema.json](/.vscode/schemas/trigger-schema.json) においてあるため必要であれば使用すること。  
> スキーマファイルの使い方は [使い方](#スキーマファイルの適用方法) に記載。(vscode のみ)

### 環境ごとの値を制御する環境ファイルについて

このファイルは、dev/stg/prod 環境ごとに異なる値を設定する際使用する。  
ファイル名は、**`(dev or stg or prod).toml` とすること。**(使用する環境ごとにファイルを作成する必要がある)
Gevanni でのみ使用するファイルのため、以下に使用可能な値を示す。  
表のパス部分には、ルート要素から各項目までのパスが記載されている。(ルート要素は `$` と記載)

|項目|パス|データ型|必須|説明|備考|
|:---:|:---:|:---:|:---:|:---:|:---:|
|`env`|`$.env`|`object`|◎|Gevanni の環境名とサービスID||
|`name`|`$.env.name`|`string`|◎|gevanni の環境名||
|`service_prefix`|`$.env.service_preifx`|`string`|◎|gevanni サービスID||
|`tasks`|`$.tasks`|`object`|◎|バッチに含まれるタスクの定義||
|`<task_name>`|`$.tasks.<task_name>`|`object`|◎|タスクの定義||
|`definition`|`$.tasks.<task_name>.definition`|`object`|◎|タスクで使用するタスク定義に関するパラメータ定義||
|`name`|`$.tasks.<task_name>.definition.name`|`string`|◎|タスク定義のファイル名||
|`spec`|`$.tasks.<task_name>.definition.spec`|`object`|◎|タスク定義のスペック||
|`cpu`|`$.tasks.<task_name>.definition.spec.cpu`|int|◎|タスクのCPU||
|`memory`|`$.tasks.<task_name>.definition.spec.memory`|int|◎|タスクのメモリ||
|`build`|`$.tasks.<task_name>.build`|`object`|◎|タスク定義に含まれるコンテナのビルドに関する定義||
|`<container_name>`|`$.tasks.<task_name>.build.<container_name>`|`object`|◎|コンテナのビルドに関する定義の詳細||
|`context`|`$.tasks.<task_name>.build.<container_name>.context`|`string`|◎|コンテナのビルドコンテキスト||
|`dockerfile`|`$.tasks.<task_name>.build.<container_name>.dockerfile`|`string`| |Dockerfile のビルドコンテキストからの相対パス|指定しない場合はコンテキスト直下の Dockerfile を使用|
|`image_placeholder`|`$.tasks.<task_name>.build.<container_name>.image_placeholder`|`string`|◎|タスク定義ファイルに記載しているプレースホルダーの値||

※ ◎は、必須項目であることを示し、◯は条件付きで必須となる項目を示す。  
※ `<>` で囲まれている値は任意の値が指定できることを示す。

> [!TIP]
> スキーマファイルが [.vscode/schemas/env.toml-schema.json](/.vscode/schemas/env.toml-schema.json) においてあるため必要であれば使用すること。  
> スキーマファイルの使い方は [使い方](#スキーマファイルの適用方法) に記載。(vscode のみ)

## デプロイワークフローについて

ここでは、バッチデプロイパイプラインをキックするための GitHub Actions ワークフローについて説明する。  
バッチのデプロイワークフローファイルについては、サンプルが [/.github/workflows/simple-batch-deploy.yml](/.github/workflows/simple-batch-deploy.yml) に置いてあるため、
そちらと [/.github/workflows/README.md](/.github/workflows/) のワークフローの README を参考に以下を行うこと。  

- `vars.batch_role_arn` の登録
    - 上記ファイルを参考に、`batch_role_arn` 変数をリポジトリに追加
    - 追加する値はインフラチームにより共有される
- `env` セクションの以下の値を変更する

    |キー|値|補足など|
    |:---:|:---:|:---:|
    |`PYTHON_VERSION`|`>= 3.12`|現状 `3.12` で入れてます|
    |`SCRIPT_DIR`|バッチのデプロイスクリプトのパス|リポジトリルートからの相対パス|
    |`CONFIG_PATH`|デプロイしたいバッチの設定ファイルがおいてあるディレクトリへのパス|リポジトリルートからの相対パス|
    |`BATCH_NAME`|バッチ名|Gevanni 利用の際申請したバッチ名|

- (必要なら) ワークフローの `on` 部分のカスタマイズ

## スキーマファイルの適用方法

vscode を使用している場合以下の方法でスキーマファイルを適用することができる。

- (dev/stg/prod).toml のスキーマを検証する場合
    1. `tamasfe.even-better-toml` をインストール
    1. 各種ファイルの先頭にスキーマファイルへの相対パスを指定
        1. /.vscode/schemas/env.toml-schema.json がスキーマファイルで、/infra/config/batch/batch1/stg.toml が検証したい toml ファイルの場合、`../../../../.vscode/schemas/env.toml-schema.json` となる

- trigger.json のスキーマを検証する場合
    1. settings.json に以下の値を追加

        ```json
        {
            "json.schemas": [
                {
                "fileMatch": ["/**/*.trigger.json"],
                "url": "/.vscode/schemas/trigger-schema.json"
                }
            ]
        }
        ```
