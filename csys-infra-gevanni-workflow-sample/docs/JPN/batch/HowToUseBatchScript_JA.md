# HowToUseBatchScripts

Gevanni バッチで使用するスクリプトの説明を行う。  
スクリプトは [02_Batch/scripts](/02_Batch/scripts/) に配置されている。

## 前提条件

- Python >= 3.12
    - スクリプトが Python で記述されているため
- Gevanni アカウントの認証情報
    - 認証情報の取得方法は [docs/JPN/HowToUseBastionContainer.md#事前準備iamロールへのスイッチロール設定](/docs/JPN/HowToUseBastionContainer.md#事前準備iamロールへのスイッチロール設定) を参考にすること
- バッチが Gevanni アカウントにデプロイされていること

### 事前準備

スクリプト内で、boto3 を使用しているため、スクリプトを実行する環境で使用できるようにインストールしておくこと。  
boto3 は [ルート直下の requirements.txt](/requirements.txt) を利用してインストールすること。

```bash
# ライブラリ追加の例
pip install -r requirements.txt
```

## HowToUseExecBatchScripts

ここでは、[02_Batch/scripts/exec_batch.py](/02_Batch/scripts/exec_batch.py) の説明を行う。  
これは、Gevanni にデプロイされたバッチを即時実行させるためのスクリプトで、
デプロイしたバッチが動くかどうかの動作確認や、
緊急でバッチを実行する際使用すること目的に用意している。

### 実行手順

- スクリプトをアプリリポジトリにコピー
- Gevanni アカウント認証情報をターミナルに設定
- `python <path to script> <dev/stg/prod> <batch config files path> <batch name>` を実行
    - `<batch config files path>` はスクリプト実行場所からの相対パスを指定すること
    - `<batch name>` は [HowToUseBatch](/docs/batch/HowToUseBatch_JA.md#設定ファイルに入力する値) の `batch_name` と同様の値をいれること
- 後の操作は標準入力に従う

> [!NOTE]
> スクリプト実行後バッチのログは NewRelic で確認可能

### 実行例

以下のような想定での実行例を示す。  

- リポジトリルートからの実行
- stg 環境へのデプロイ
- スクリプトは `<リポジトリルート>/infra/batch/scripts` に格納
- バッチの設定ファイルは `<リポジトリルート>/infra/batch/configs/batch_app/` に格納
- バッチ名は `batch_app`

```bash
python ./scripts/batch/exec_batch.py stg ./infra/batch/configs/batch_app batch_app
```

### スクリプトの概要

- スクリプト内では以下の処理を実行している
    1. `<dev/stg/prod>.toml` の中身を読み込む
    1. 上記のファイルから、Gevanni のサービスプレフィックスを取得
        - ステートマシン名は `<Gevanni サービスプレフィックス>-<batch_name>` であるため
    1. `<batch_name>.trigger.json` を読み込む
    1. 上記のファイルからトリガー一覧を取得し、トリガーが複数ある場合、どのトリガーの設定でバッチを動かすかを選ぶための選択肢を表示
        - トリガーが1つしかない場合は選択肢は表示されない
    1. 上記の設定とステートマシン名を利用してバッチを実行
