# HowToSetup

ここでは、スクリプトを実装するために必要な事前準備の手順について記載する。

## 目次

- [gevanni-workflow-sample リポジトリからアプリリポジトリへのファイルコピー](#gevanni-workflow-sampleリポジトリからアプリリポジトリへのファイルコピー)
- [ディレクトリ構成変更時の対応](#ディレクトリ構成変更時の対応)
- [必要なリソースのインストール](#必要なリソースのインストール)
- [実行環境に実行権限を付与する](#実行環境に実行権限を付与する)

## gevanni-workflow-sample リポジトリからアプリリポジトリへのファイルコピー

【GUI で操作する場合】

1. gevanni-workflow-sample リポジトリのコードをローカルにダウンロードする。  
   ![](../images/github-download.png)
2. zip ファイルを解凍する。
3. アプリリポジトリに追加したいファイルをドラッグアンドドロップでアップロードしていく。  
   ![](../images/github-upload.png)  
   ![](../images/github-draganddrop.png)

※アプリリポジトリをクローン済の場合は、ローカルのフォルダー上でドラッグアンドドロップでコピーする方法でも可能。

## ディレクトリ構成変更時の対応

スクリプトでファイルパスを指定している箇所がいくつか存在する。スクリプトの配置場所をサンプルから変更した場合には、合わせてパスも変更する。

### アプリコンテナ

【build.py ファイルの配置を変更した場合】

- ecs-deploy-frontend.yml または ecs-deploy-backend.yml 内の実行コマンドを修正する。
- パスはルートから build.py までの絶対パスを指定する。

  **(例).github/workflows/ecs-deploy-frontend.yml**

  ```yml
  - name: Run frontend build.py
    run: python ./01_Rolling/build_frontend/build.py ${{env.environment}}
  ```

【Dockerfile のパスを指定する場合】

- 設定ファイル（dev.ini/stg.ini/prod.ini）内の Dockerfile のパスを修正する。
- パスは build.py からの相対パスを指定する。

  **(例)01_Rolling/build_frontend/stg.ini**

  ```ini
  [Docker]
  FILE_PATH=../app/Dockerfile
  ```

## 必要なリソースのインストール

以下リソースをインストールする。

> [!NOTE]  
> マイナビ社内の Windows PC 利用者向け  
> AWS CLI、Session Manager プラグイン、Python 3.12 は、PC の管理者権限がなくても、デスクトップの「ソフトウェア追加」からインストールが可能。

### AWS CLI

- [AWS CLI の最新バージョンへのインストールまたは更新](https://docs.aws.amazon.com/ja_jp/cli/latest/userguide/getting-started-install.html)の手順を実行する。

### Session Manager プラグイン

- [AWS CLI 用の Session Manager プラグインをインストールする](https://docs.aws.amazon.com/ja_jp/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html)の手順を実行する。

### Python 3.12

- [Python の公式サイト](https://www.python.org/)から Python3.12 のパッケージをダウンロードする。

### boto3

- ルート直下に移動し、以下コマンドを実行して、boto3 をインストールする。
  ```sh
  pip install -r requirements.txt
  ```

## 実行環境に実行権限を付与する

以下手順に従って、ローカルで操作できるように権限を付与する。

- マイナビ社内ユーザの場合は、[HowToSwitchRoleAndConfigLocalForMynavi.md](HowToSwitchRoleAndConfigLocalForMynavi.md)を参照
- マイナビ社外ユーザ・ベンダーの場合は、[HowToSwitchRoleAndConfigLocal.md](HowToSwitchRoleAndConfigLocal.md)を参照
