# HowToSwitchRoleAndConfigLocal

- ここでは、IAMロールの認証情報を利用してローカルPCでスクリプトを実行する方法について記載します。

## 想定読者

- マイナビ社内ユーザ
  - グループ会社社員の場合、マイナビ本体と同様にAWS SSO導入済みであることが前提となります。

## 手順
### 初期設定

- `~/.aws/config` に以下の内容を追記します。
  - `~/.aws/config` ファイル自体がない場合は、新規作成します。
- `[profile <サービスID>]` 以下の設定は、サービスID単位で追記します。
  - 対象サービスによってsource_profileを変更します。
    - 本番環境: bastion-prod
    - 検証環境: bastion-stg
- 以下の項目を、接続先のサービスに合わせて変更します。
  - サービスID/スイッチ用IAMロールのARN
    - 新規サービス作成後、Gevanni基盤管理者からアプリチームリーダーに連携しています。
    - アプリチームの各メンバーはリーダーから共有を受けてください。
- 以下の項目を、利用者に合わせて変更します。
  - 自身のメールアドレス

```
# スイッチ元ロールへのSSOログイン用設定
[profile bastion-prod]
sso_start_url = https://d-95671fcbe9.awsapps.com/start/
sso_region = ap-northeast-1
sso_account_id = ************
sso_role_name = AssumeRoleOnlyAccess
output = json

[profile bastion-stg]
sso_start_url = https://d-95671fcbe9.awsapps.com/start/
sso_region = ap-northeast-1
sso_account_id = ************
sso_role_name = AssumeRoleOnlyAccess
output = json

# スイッチ先ロール設定(source_profileでスイッチ元ロール用profileを指定）
[profile <サービスID>]
role_arn = <スイッチ用IAMロールのARN>
source_profile = bastion-prodまたはbastion-stg
role_session_name = <自身のメールアドレス>
region = ap-northeast-1
```

### スクリプト実行時に都度必要な設定

- AWS SSOの一時的な認証情報を使用しているため、一定時間(2時間)経過後やPC再起動後には、都度実行が必要です。

#### スイッチ元ロール用profileを指定してSSOログイン

```
$ aws sso login --profile bastion-prodまたはbastion-stg
# （ブラウザでSSO画面が立ち上がるので、Confirm and Continue → Allowを押下）
```

#### デフォルトプロファイルを設定

```
$ export AWS_DEFAULT_PROFILE=<接続先のサービスID>

# ロールがスイッチできているかの確認
$ aws sts get-caller-identity
{
    "UserId": "AROAXXXXXXXXXXXXXXXXX:<EMAIL>",
    "Account": "profileで設定したAWSアカウントID",
    "Arn": "profileで設定したロールのArn"
}
```

- エラーが出ていなければ、スクリプトを実行できる権限がついている状態になっています。
