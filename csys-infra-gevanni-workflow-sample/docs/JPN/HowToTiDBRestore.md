# TiDB リストア手順

## 概要

- TiDB Cloud Serverless Cluster の リストア手順について記載する。

### 前提

- Gevanni ではデプロイ環境問わず、原則 `TiDB Cloud Serverless` を利用する。
  - [csys-infra-gevanni-infra/docs/02_Detaildesign/TiDB.md#プロビジョニング方式](https://github.com/mynavi-group/csys-infra-gevanni-infra/blob/develop/docs/02_Detaildesign/TiDB.md#%E3%83%97%E3%83%AD%E3%83%93%E3%82%B8%E3%83%A7%E3%83%8B%E3%83%B3%E3%82%B0%E6%96%B9%E5%BC%8F)

### 復元方法

- TiDB Cloud Serverless において利用できる復元方法と復元先は以下の通り。
  - 復元手段
    1.  スナップショットの復元
        - 特定のバックアップ スナップショットからクラスターを復元する。
    1.  PITR (ポイントインタイム リストア) ※ スケーラブルクラスターのみ。
        - クラスターを特定の時点に復元する。
          - ※ 特定の時点： 過去 14 日から 1 分前 (現在時刻から 1 分引いた時刻) 以内の任意の時刻。
  
  - 復元先
    1.  インプレース復元
        - 稼働中のクラスターを置き換える方法。復元中はクラスターの利用が不可になる。
        - mysql スキーマ内のテーブルに影響あり。ユーザー資格情報、権限、システム変数の変更はバックアップ時の状態に戻る。
    1.  新しいクラスターへの復元
        - 新しいクラスターを作成して復元される。
        - クラスターのユーザー資格情報と権限は新しいクラスターに復元されない。

### 方針

- 以下の理由から原則 `PITR` の `インプレース復元` を推奨する。
  - 迅速な復旧が可能。
  - ユーザー資格情報と権限がバックアップ時の状態に戻るため、ログイン情報を更新する必要がない。
- データ不整合時の調査等のために、既存クラスターを残存させたまま、リストアしたクラスターを作成したいケースが想定される。そのため、`新しいクラスターへの復元` 手順も記載する。
- 原則 `PITR` 推奨のため `スナップショットの復元` 手順については記載しない。
- また、 serverless は手動スナップショットが使えないかつ、クラスタ削除するとスナップショットも削除される。
  - そのため手動で一時バックアップを取得する場合は、アプリアカウント上にデータを一時的に保存する S3 を CDK で用意し、S3 にデータをエクスポートする方針とする。

## インプレース復元手順

1.  TiDB Cloud コンソールを開き、左ペインのメニューから `Backup` を選択。\
    画面右上の Restore をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image01.png)

1.  項目 `Restore Mode` から `Point-in-Time Restore` を選択し、`Restore to` のプルダウンから、戻したい日時を選択する。\
    ※ 時間選択はマウスホイールを動かすことで選択移動できる。\
    ![](/docs/images/tidb/tidb-restore-image02.gif)

1.  項目 `Destination` から `In-place Restore` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image03.png)

1.  画面右の `Summary` から `Restore` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image04.png)

1.  復元プロセスが開始されるとクラスターのステータスが `Restoring` に変わる。\
    復元が完了してステータスが `Avaliable` に変わるまでクラスターは使用不可になる。\
    ![](/docs/images/tidb/tidb-restore-image05.png)

1.  クラスターのステータスが `Avaliable` になれば手順は完了。\
    ![](/docs/images/tidb/tidb-restore-image06.png)

## 新しいクラスターへの復元手順

### 前提

- 新しいクラスターへの復元を選択した場合、クラスターのユーザー資格情報と権限は新しいクラスターに復元されない。
- そのため、ユーザー名とパスワードを新たに取得しシークレットを更新する必要がある。

### 手順

1.  TiDB Cloud コンソールを開き、左ペインのメニューから `Backup` を選択。\
    画面右上の Restore をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image01.png)

1.  項目 `Restore Mode` から `Point-in-Time Restore` を選択し、`Restore to` のプルダウンから、戻したい日時を選択する。\
    ※ 時間選択はマウスホイールを動かすことで選択移動できる。\
    ![](/docs/images/tidb/tidb-restore-image02.gif)

1.  項目 `Destination` から `Restore to a New Cluster` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image07.png)

1.  項目 `Cluster Name` に復元前のクラスターと異なる、新しいクラスターの名前を入力する。\
    ![](/docs/images/tidb/tidb-restore-image08.png)\
    ※ 同じ名前を入力すると重複エラーが発生する。\
    ![](/docs/images/tidb/tidb-restore-image09.png)

1.  項目 `Cluster Plan` から、クラスタープランを選択する。\
    `Scalable Cluster` を選択している場合は、月間使用限度額を設定する。\
    ![](/docs/images/tidb/tidb-restore-image10.png)

1.  画面右の `Summary` から `Restore` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image11.png)

1.  リストアが開始されると `Status` が `Restoring` に変わる。\
    ![](/docs/images/tidb/tidb-restore-image12.png)

1.  `Status` が `Available` になればリストアが完了する。\
    ![](/docs/images/tidb/tidb-restore-image13.png)

1.  `Overview` ページを開き、画面右上の `Connect` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image14.png)

1.  `Connection Type` のプルダウンから `Private Endpoint` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image15.png)

1.  `Generate Password` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image16.png)

1.  `Parameters` から、`USERNAME` と `PASSWORD` を控える。\
    ![](/docs/images/tidb/tidb-restore-image17.png)

1.  シークレットに `USERNAME` と `PASSWORD` を登録し、パイプラインを実行しシークレット情報を更新する。

## 手動バックアップ (インポート/エクスポート) 手順

- 事前準備として、バックアップ用の S3 バケットと IAM ロールを CDK でデプロイする必要がある。
- 信頼ポリシーに設定する TiDB の AWS アカウント ID と External ID を TiDB Cloud コンソールから取得し、CDK デプロイする。
- インポート/エクスポート を実施する際は、CDK で作成された S3 バケットと IAM ロール ARN を TiDB Cloud に入力し実行する。

### 事前準備

1.  TiDB Cloud コンソールを開き、左ペインのメニューから Import を選択。\
    画面中央の `Import data from Cloud Strage` をクリックし、プルダウンから `Amazon S3` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image18.png)

1.  `Import Data from Amazon S3` 画面に遷移したら、一番下にある `Click here to create new one with AWS CloudFormation` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image19.png)

1.  `Add New Role ARN` 画面に遷移したら、`Having trouble? Create Role ARN manually` をクリックし、`TiDB Cloud Account ID` と `TiDB Cloud External ID` を控える。\
    ![](/docs/images/tidb/tidb-restore-image20.png)

1.  `csys-infra-gevanni-cf-sample` リポジトリの CDK コードからバックアップ用の S3 Bucket と IAM Role をデプロイする。
    - [csys-infra-gevanni-cf-sample/params/](https://github.com/mynavi-group/csys-infra-gevanni-cf-sample/blob/main/params/) の以下パラメーターに控えた `TiDB Cloud Account ID ` と `TiDB Cloud External ID` を記入する。\
      ```typescript
      export const TidbBackupParam: inf.ITidbBackupParam = {
        tidbCloudAccountId: '',
        tidbCloudExternalId: '',
      };
      ```
    - `tidb-backup-stack` をデプロイする。

1.  CloudFormation コンソールへアクセスし、`TidbBackup` スタックの出力タブを開く。\
    `TidbBackupBucketUri` と `TidbBackupRoleArn` の値を控える。\
    ![](/docs/images/tidb/tidb-restore-image21.png)

### エクスポート

1.  TiDB Cloud コンソールを開き、左ペインのメニューから Import を選択。\
    画面右上のプルダウンから `Amazon S3` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image22.png)

1.  `Export Data to Amazon S3` ページに遷移したら `Exported Data` からエクスポートするデータと出力形式を選択する。\
    ![](/docs/images/tidb/tidb-restore-image23.png)

1.  最下部まで画面をスクロールし、`Amazon S3 Setting` を記入したら `Export` をクリックする。
    - `Folder URI`: [事前準備](#事前準備) でメモに控えた `TidbBackupBucketUri` を記入し、サフィックスにエクスポート先のパスを入力する。
    - `Role ARN`: [事前準備](#事前準備) で控えた `TidbBackupRoleArn` を記入する。\
    ![](/docs/images/tidb/tidb-restore-image24.png)

1.  `Import` メニュー画面に遷移し、`Export` を選択する。\
    `Status` が `Succeeded` になれば完了。\
    ![](/docs/images/tidb/tidb-restore-image25.png)

### インポート

1.  TiDB Cloud コンソールを開き、左ペインのメニューから Import を選択。\
    画面中央の `Import data from Cloud Strage` をクリックし、プルダウンから `Amazon S3` を選択する。\
    ![](/docs/images/tidb/tidb-restore-image18.png)

1.  エクスポートで出力したデータに合わせて、`Source` の設定し `Connect` をクリックする。\
    下記はエクスポート手順で出力したサンプルデータをインポートする場合の選択肢。
    - `Import File Count`: `Multiple files` を選択。
    - `Included Schema Files`: `No` を選択。
    - `Data Format`: `SQL` を選択。
    - `Folder URI`: [事前準備](#事前準備) で控えた `TidbBackupBucketUri` を記入し、サフィックスにインポートするデータが格納されたパスを入力する。
    - `Role ARN`: [事前準備](#事前準備) で控えた `TidbBackupRoleArn` を記入する。
    ![](/docs/images/tidb/tidb-restore-image26.png)

1.  `Import Data from Amazon S3` 画面に遷移したら、インポート先のデータベースを選択し、`Start Import` をクリックする。\
    ![](/docs/images/tidb/tidb-restore-image27.png)

1.  `Import` メニュー画面に遷移し、`Import` を選択する。\
    `Status` が `Completed` になれば完了。\
    ![](/docs/images/tidb/tidb-restore-image28.png)