# HowToUseBastionContainer

ここでは、踏台コンテナの利用方法を記載する

## 概要
- 踏台コンテナを経由して、以下リソースに接続する。
  - DB(TiDB Cloud)
  - バックエンドコンテナ
  
- 踏み台コンテナを使用するために、実行環境にAWS CLIとSSMプラグインのインストールが必要である。
- また、踏台コンテナにはプロセスの監視ロジックが実装されいる。
  - 30分ごとにコンテナ内のSSMエージェントのプロセスを確認して、SSMエージェントのプロセス数、つまりSSMユーザーのログイン数によってLOGIN_USERフラグを切り替える処理を行う。
  - LOGIN_USERフラグが"no exist"になってから30分後にログイン数が0であればスクリプトを終了し、コンテナが停止する。
  - 処理フロー図は以下のとおり。  
![](../images/bastion-shell-logic.dio.png)
  
## 事前準備:IAMロールへのスイッチロール設定
### マイナビ社内ユーザー向け

- 設定方法: [AWS SSOによるCLIスイッチロール](HowToSwitchRoleAndConfigLocalForMynavi.md)

### マイナビ社外ユーザー向け

- 設定方法: [ログイン後のCLIスイッチロール](HowToSwitchRoleAndConfigLocal.md)

## 踏台コンテナの起動方法
1.  環境ファイルに各設定値を編集する。  
    **bastion/{環境名}.ini**
    ```ini
    [Env]
    ENV=Stg01
    PJ_PREFIX=GEVANNI
    SERVICE_PREFIX=sample01-stg01-ab

    [EcsApp]
    BACK_APP_NAME=EcsBackend

    [TiDB]
    USERNAME=root
    ```
1. 実行環境にAWS CLIとSSMプラグインをインストールする。
    - AWS CLIのインストール手順は下記リンクを参照  
      https://docs.aws.amazon.com/ja_jp/cli/latest/userguide/getting-started-install.html
    - SSM プラグインのインストール手順は下記リンクを参照  
      https://docs.aws.amazon.com/ja_jp/systems-manager/latest/userguide/session-manager-working-with-install-plugin.html
1. `python run_task.py {環境名}`で起動する。

1. 起動が完了すると下記のログインコマンドが出力されるため、実行することで踏台コンテナのログインが完了する。  
    **run_task.py実行後に出力されるログインコマンド**  　※出力されるコマンドはスクリプトの実行毎に代わる
    ```
    -------------------------------------------
    踏み台コンテナへログインするコマンド
    aws ssm start-session --region ap-northeast-1 --target ecs:Stg01-GEVANNI-common-BastionEcsCommon-BastionEcsCommonConstructBastionEcsCluster3733E5BE-6sqD3UNZ3wKq_a065a013ae404dfa984db8a29bbbd98d_a065a013ae404dfa984db8a29bbbd98d-3822224002
    -------------------------------------------
    ```

## 各リソースへの接続方法
### TiDB
  - MySQL クライアントを利用して接続を行う。コマンドは以下のとおり。
  - `mysql --host TiDBのエンドポイント --port 4000 -u ユーザ名 -p`

### バックエンドコンテナ
  - DNSを指定して、接続確認を行う。コマンドは以下のとおり。
  - (例)`curl -X GET http://DNSのレコード名/APIのパス:ポート番号/`