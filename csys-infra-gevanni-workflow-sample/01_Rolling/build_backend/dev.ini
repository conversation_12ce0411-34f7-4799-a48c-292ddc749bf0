[Env]
ENV=Dev35
PJ_PREFIX=GEVANNI
SERVICE_PREFIX=sample04-dev35-gh

[EcsApp]
APP_NAME=EcsBackend

[Docker]
# docker buildの実行ディレクトリ
# リポジトリのルートからの相対パス
BUILD_DIR=./

# Dockerfileのパス
# BUILD_DIRからの相対パス
FILE_PATH=container/backend/dockerfile

[Task]
# 初期起動タスク数
DESIRED_COUNT=1

[AutoScale]
MIN_CAPACITY=1
MAX_CAPACITY=3
TARGET_VALUE=60

[CapacityProvider]
FARGATE_BASE=2
FARGATE_WEIGHT=1
FARGATE_SPOT_BASE=0
FARGATE_SPOT_WEIGHT=2