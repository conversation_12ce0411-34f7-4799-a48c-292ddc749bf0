[Env]
ENV=Prod01
PJ_PREFIX=GEVANNI
SERVICE_PREFIX=sample01-prod01-ab

[EcsApp]
APP_NAME=EcsBackend

[Docker]
# docker buildの実行ディレクトリ
# リポジトリのルートからの相対パス
BUILD_DIR=frontend/

# Dockerfileのパス
# BUILD_DIRからの相対パス
FILE_PATH=app/Dockerfile

[Task]
# 初期起動タスク数
DESIRED_COUNT=2

[AutoScale]
MIN_CAPACITY=2
MAX_CAPACITY=5
TARGET_VALUE=60

[CapacityProvider]
FARGATE_BASE=2
FARGATE_WEIGHT=1
FARGATE_SPOT_BASE=0
FARGATE_SPOT_WEIGHT=0