import os
import subprocess
import sys
import boto3
import configparser
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from common import parse_args

ssm = boto3.client("ssm")
ecs = boto3.client("ecs")

aws_region = "ap-northeast-1"

script_dir = os.path.dirname(os.path.abspath(__file__))

    
def read_param_file(environment):
    param_file = os.path.join(script_dir, f"{environment}.ini")
    if not os.path.isfile(param_file):
        sys.exit(f"Configuration file '{environment}.ini' not found.")

    param = configparser.ConfigParser()
    param.read(param_file)
    return param

def get_ssm_parameter(ssm_name):
    ssm = boto3.client("ssm", region_name=aws_region)
    try:
        response = ssm.get_parameter(Name=ssm_name)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except ssm.exceptions.ParameterNotFound:
        sys.exit("Parameter not found.")
    except Exception as e:
        sys.exit(f"Failed to get parameter: {e}")

def list_tasks(cluster_name, service_name):
    try:
        response = ecs.list_tasks(cluster=cluster_name, serviceName= service_name)
        task_arns = response["taskArns"]
        return task_arns
    except Exception as e:
        sys.exit(f"Failed to list tasks: {e}")

def describe_task(cluster_name, task_arn):
    try:
        response = ecs.describe_tasks(cluster=cluster_name, tasks=[task_arn])
        return response["tasks"][0]
    except Exception as e:
        sys.exit(f"Failed to describe task: {e}")

def get_runtime_id(cluster_name, task_id, app_name):
    try:
        containers = ecs.describe_tasks(cluster=cluster_name, tasks=[task_id])["tasks"][0]["containers"]
        for container in containers:
            if container["name"] == app_name:
                return container["runtimeId"]
        sys.exit(f"Failed to find container: {app_name}")
    except Exception as e:
        sys.exit(f"Failed to describe tasks. Error: {e}")

def exec_into_container(cluster_name, task_arn, container_name):
    command = f"aws ecs execute-command --region {aws_region} --cluster {cluster_name} --task {task_arn} --container {container_name} --interactive --command /bin/sh"
    # Information
    print(f"Cluster Name: {cluster_name}")
    print(f"Task ARN: {task_arn}")
    print(f"Container Name: {container_name}")
    print(f"Executing command: {command}")
    # Execute command

def start_ssm_session(cluster_name, task_arn, app_name):
    task_id = task_arn.split("/")[-1]
    runtime_id = get_runtime_id(cluster_name, task_id, app_name)
    target = f"ecs:{cluster_name}_{task_id}_{runtime_id}"
    command = f"aws ssm start-session --region {aws_region} --target {target}"
    # Information
    print(f"Starting SSM session with command: {command}")
    print(f"Target: {target}")
    # Execute command
    subprocess.call(command, shell=True)

def main():
    args = parse_args()
    environment = args.environment
    print(f"Your environment is {environment}")

    # Read configuration file
    param = read_param_file(environment)

    try:
        env = param["Env"]["ENV"]
        pj_prefix = param["Env"]["PJ_PREFIX"]
        service_prefix = param["Env"]["SERVICE_PREFIX"]
        app_name = param["EcsApp"]["APP_NAME"]
        service_name = f"{app_name}-Service"

    except KeyError as e:
        sys.exit(f"KeyError: The key '{e.args[0]}' is not found in the configuration file")

    print("Getting resource name...")

    cluster_name = get_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/ECSClusterName"
    )
    print(f"ClusterName is {cluster_name}")

    task_arns = list_tasks(cluster_name, service_name)
    if not task_arns:
        sys.exit("No tasks found in the cluster.")

    print("Select a task to exec into:")
    for i, task_arn in enumerate(task_arns):
        print(f"{i + 1}. {task_arn}")

    try:
        task_index = int(input("Enter the task number: ")) - 1
        if task_index < 0 or task_index >= len(task_arns):
            sys.exit("Invalid task number.")
    except ValueError:
        sys.exit("Invalid input. Please enter a number.")

    selected_task_arn = task_arns[task_index]
    task = describe_task(cluster_name, selected_task_arn)
    container_name = app_name

    print("Choose an option:")
    print("1. ECS Execute Command")
    print("2. SSM Start Session")

    try:
        option = int(input("Enter the option number: "))
        if option == 1:
            exec_into_container(cluster_name, selected_task_arn, container_name)
        elif option == 2:
            start_ssm_session(cluster_name, selected_task_arn, app_name)
        else:
            sys.exit("Invalid option number.")
    except ValueError:
        sys.exit("Invalid input. Please enter a number.")

if __name__ == "__main__":
    main()
