from argparse import Argument<PERSON>ars<PERSON>

def build_arguments():
    parser = ArgumentParser()
    parser.add_argument("environment", choices=["dev", "stg", "prod"], help="Environment such as dev/stg/prod", metavar="{dev/stg/prod}")
    parser.add_argument("enable_futurevuls_scan", choices=["true", "false"], help="FutureVuls scanning (true/false)", type=str.lower, metavar="{true/false}")
    parser.add_argument("--env-vars", help="Comma-separated list of environment variables", type=lambda s: s.split(',') if s else [], nargs='?')
    parser.add_argument("--build-vars", help="Comma-separated list of build variables", type=lambda s: s.split(',') if s else [], nargs='?')
    args = parser.parse_args()
    return args

def parse_args():
    parser = ArgumentParser()
    parser.add_argument("environment", choices=["dev", "stg", "prod"], help="Target environment where the ECS container is running")
    return parser.parse_args()

