import os
import json
import re
import subprocess
import sys
import boto3
import zipfile
import botocore
import configparser
import shutil
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError
from common import build_arguments

aws_region = "ap-northeast-1"
folder_name = "ecspresso_conf"
zip_file = "image_backend.zip"

ssm = boto3.client("ssm", aws_region)
sts = boto3.client("sts", aws_region)
ecr = boto3.client("ecr", aws_region)
s3 = boto3.client("s3", aws_region)
secret = boto3.client("secretsmanager", aws_region)

script_dir = os.path.dirname(os.path.abspath(__file__))


# シェルコマンド実行
def run_command(command):
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        return result.stdout.decode().strip()
    except subprocess.SubprocessError as e:
        sys.exit(f"{e.stderr.decode().strip()}")

# 環境変数取得
def get_environment(env_key):
    try:
        result = os.environ[env_key]
        if not result:
            sys.exit(f"environment: {env_key} is empty.")        
        return result
    except KeyError as e:
        sys.exit(f"KeyError: The key '{e.args[0]}' is not found")


# 設定ファイル読み込み
def read_param_file(environment):
    param_file = os.path.join(script_dir, f"{environment}.ini")
    if not os.path.isfile(param_file):
        sys.exit(f"paramuration file '{environment}.ini' not found.")

    param = configparser.ConfigParser()
    param.read(param_file)
    return param


# AWSアカウントIDの設定
def get_aws_account_id():
    print("Setting account ID...")

    try:
        aws_account_id = sts.get_caller_identity()["Account"]
        if len(aws_account_id) == 12 and aws_account_id.isdigit():
            return aws_account_id
        else:
            sys.exit("Invalid AWS AccountID format.")
    except (NoCredentialsError, PartialCredentialsError) as e:
        sys.exit(f"Credentials error: {e}")


# Systems Managerパラメータの取得
def get_ssm_parameter(ssm_name):
    print("Setting Systems parameter...")

    try:
        response = ssm.get_parameter(Name=ssm_name)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except ssm.exceptions.ParameterNotFound:
        sys.exit("Parameter not found.")
    except Exception as e:
        sys.exit(f"Failed to get parameter: {e}")


# Systems Managerパラメータの更新
def update_ssm_parameter(ssm_name, value):
    print("Updating SSM parameter...")

    try:
        ssm.put_parameter(Name=ssm_name, Value=value, Type="String", Overwrite=True)
        print("Update complete")
    except Exception as e:
        sys.exit(f"Failed to update SSM parameter value: {e}")


# ECRへログイン
def login_ecr(aws_account_id):
    print("Logging in to Amazon ECR...")

    try:
        login_command = f"aws ecr get-login-password --region {aws_region} | docker login --username AWS --password-stdin {aws_account_id}.dkr.ecr.{aws_region}.amazonaws.com"
        run_command(login_command)
        print("Login succeeded")
    except Exception as e:
        sys.exit(f"Failed ECR login: {e}")


# Dockerイメージのビルド
def build_docker_image(image_repo_name, image_tag, build_dir, docker_path, build_vars):
    print("Building the Docker image...")
    
    build_arg_str = ""
    # Handle case where build_vars might be None or contain 'false'
    if build_vars and build_vars != ['false']:
        for var in build_vars:
            if var and var.strip() and var.lower() != 'false':
                build_arg_str += f" --build-arg {var}"

    try:
        build_command = (
            f"docker build{build_arg_str} -t {image_repo_name}:{image_tag} . -f {docker_path}"
        )
        print(f"build command: {build_command}")
        os.chdir(build_dir)
        run_command(build_command)
        os.chdir(script_dir)
        print("build complete")
    except Exception as e:
        sys.exit(f"Failed Docker build: {e}")


# FutureVuls による Docker イメージの脆弱性スキャンと結果のアップロード
def futurevuls_docker_image_scan(vuls_saas_groupid, vuls_saas_token, image_tag, vuls_saas_uuid):
    print("Starting FutureVuls Docker image scan...")

    try:
        scan_command = (
            "curl -s https://installer.vuls.biz/vuls-trivy-light.sh | "
            f'VULS_SAAS_GROUPID="{vuls_saas_groupid}" '
            f'VULS_SAAS_TOKEN="{vuls_saas_token}" '
            f'TARGET_IMAGE="{image_tag}" '
            f'VULS_SAAS_UUID="{vuls_saas_uuid}" '
            "bash -s inst"
        )
        # os.chdir()
        run_command(scan_command)
        print("Scan completed")
    except Exception as e:
        sys.exit(f"Failed FutureVuls Docker image scan: {e}")


# Dockerイメージの登録
def push_docker_image(aws_account_id, image_repo_name, image_tag):
    print("Pushing the Docker image...")

    try:
        tag_command = f"docker tag {image_repo_name}:{image_tag} {aws_account_id}.dkr.ecr.{aws_region}.amazonaws.com/{image_repo_name}:{image_tag}"
        push_command = f"docker push {aws_account_id}.dkr.ecr.{aws_region}.amazonaws.com/{image_repo_name}:{image_tag}"
        run_command(tag_command)
        run_command(push_command)
        print("push succeeded")
    except Exception as e:
        sys.exit(f"Failed Docker push: {e}")


# download zip file from S3
def download_zip_file(conf_master_bucket, conf_bucket, zip_name, source_dir):
    print("Downloading zip file from S3...")

    try:
        s3.head_object(Bucket=conf_bucket, Key=zip_name)
        file_exists = True
    except botocore.exceptions.ClientError:
        file_exists = False

    if not file_exists:
        print(
            f"{zip_name} does not exist in ConfBucket, so get it from ConfMasterBucket"
        )
        download_from_s3(conf_master_bucket, source_dir)
    else:
        print(f"{zip_name} already exists in ConfBucket")
        download_from_s3(conf_bucket, source_dir)


# image.zipのダウンロード
def download_from_s3(bucket_name, source_dir):
    print("Downloading from S3...")

    try:
        s3.download_file(bucket_name, zip_file, zip_file)
        with zipfile.ZipFile(zip_file, "r") as zip_ref:
            zip_ref.extractall(source_dir)
        os.remove(zip_file)
        print("Download and extraction complete!")
    except Exception as e:
        sys.exit(f"Download or extraction failed: {e}")


# image.zipのアップロード
def upload_to_s3(bucket_name, source_dir):
    print("Uploading to S3...")

    try:
        shutil.make_archive(zip_file.replace(".zip", ""), "zip", source_dir)
        s3.upload_file(zip_file, bucket_name, zip_file)
        shutil.rmtree(source_dir)
        os.remove(zip_file)
        print("Upload complete!")
    except Exception as e:
        sys.exit(f"Upload failed: {e}")


# Secrets ManagerのARN取得
def get_secret_arn(env, pj_prefix, service_prefix, app_name):
    ssm_name = f"/{env}-{pj_prefix}-{service_prefix}/{app_name}/SecretArn"

    try:
        response = ssm.get_parameter(Name=ssm_name)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except ssm.exceptions.ParameterNotFound:
        sys.exit("Parameter not found.")
    except Exception as e:
        sys.exit(f"Failed to get parameter: {e}")


# Create/update secret value to SecretManager
def put_secret_value(secret_arn, env_vars):
    print("Putting secret value...")

    secret_string = {}
    for env_var in env_vars:
        secret_string[env_var] = os.environ[env_var]

    try:
        secret.put_secret_value(
            SecretId=secret_arn, SecretString=json.dumps(secret_string)
        )
        print("Update secrets is complete!")
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except Exception as e:
        sys.exit(f"Failed to put secret value: {e}")


def create_updated_secrets(secret_arn, secret_names):
    return [
        {"name": name, "valueFrom": f"{secret_arn}:{name}::"} for name in secret_names
    ]


# Update secrets in ecs task definition json file
def update_secret_in_task_def(source_dir, secret_arn, env_vars):
    print("Updating Secret Manager to ecs task definition...")
    task_def_path = os.path.join(source_dir, "ecs-task-def.json")

    update_secrets = create_updated_secrets(secret_arn, env_vars)
    updated_secrets_str = json.dumps(update_secrets)

    try:
        with open(task_def_path, "r", encoding="utf-8") as file:
            task_def_content = file.read()

        updated_task_definition_content = re.sub(
            r"\"secrets\": \[.*?\]",  # Match "secrets": [ ... ]
            f'"secrets": {updated_secrets_str}',  # Replace with updated secrets
            task_def_content,
            flags=re.DOTALL,
        )

        # Write updated task definition
        with open(task_def_path, "w", encoding="utf-8") as file:
            file.write(updated_task_definition_content)
        print("Secret updated successfully!")
    except Exception as e:
        sys.exit(f"Failed to update Secret to ecs task definition: {e}")


# create param.ini for config.py
def create_param_ini(environment):
    try:
        shutil.copy(
            os.path.join(script_dir, f"{environment}.ini"),
            os.path.join(script_dir, f"{folder_name}/param.ini"),
        )
    except FileNotFoundError:
        print(f"Configuration file '{environment}.ini' not found.")
    except PermissionError:
        print("Permission denied while accessing")
    except Exception as e:
        print(f"An error occurred: {e}")


def main():
    args = build_arguments()
    if args.env_vars:
        print(f"Your env-vars is {args.env_vars}")
    
    if args.build_vars:
        print(f"Your build-vars is {args.build_vars}")
    
    env_vars = args.env_vars
    build_vars = args.build_vars
    environment = args.environment
    enable_futurevuls_scan = args.enable_futurevuls_scan
    print(f"Your environment is {environment}")
    
    # 設定ファイル読み込み
    param = read_param_file(environment)

    try:
        env = param["Env"]["ENV"]
        pj_prefix = param["Env"]["PJ_PREFIX"]
        service_prefix = param["Env"]["SERVICE_PREFIX"]
        app_name = param["EcsApp"]["APP_NAME"]
        build_dir = param["Docker"]["BUILD_DIR"]
        docker_path = param["Docker"]["FILE_PATH"]
    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    # FutureVuls のシークレット情報取得
    if enable_futurevuls_scan == "true":
        vuls_saas_groupid = get_environment('VULS_SAAS_GROUPID')
        vuls_saas_token = get_environment('VULS_SAAS_TOKEN')
        vuls_saas_uuid = get_environment('VULS_SAAS_UUID')

    aws_account_id = get_aws_account_id()
    print(f"AWS AccountID is {aws_account_id}")

    # ECRのARN取得
    print("Setting ECR ARN...")
    image_repo_name = get_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/{app_name}/EcrRepo"
    )
    print(f"ECR Repo is {image_repo_name}")

    # ECRにログイン
    login_ecr(aws_account_id)

    # コミットID最初の7文字をECRタグに設定
    image_tag = run_command("git rev-parse --short=7 HEAD")

    # ECRタグのSSMパラメータの値を更新
    update_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/{app_name}/ecrTag", image_tag
    )

    # Dockerイメージのビルド
    build_docker_image(image_repo_name, image_tag, build_dir, docker_path, build_vars)

    # FutureVuls の脆弱性スキャン
    if enable_futurevuls_scan == "true":
        futurevuls_docker_image_scan(vuls_saas_groupid, vuls_saas_token, image_tag, vuls_saas_uuid)

    # Dockerイメージの登録
    push_docker_image(aws_account_id, image_repo_name, image_tag)

    # マスターバケット名の取得
    print("Setting ecspresso conf master Bucket...")
    conf_master_bucket = get_ssm_parameter(
        f"/{env}-{pj_prefix}-common/ConfMasterBucketName"
    )
    print(f"CONF_MASTER_BUCKET is {conf_master_bucket}")

    # confバケット名の取得
    print("Setting ecspresso conf Bucket...")
    conf_bucket = get_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/{app_name}/SourceBucketName"
    )
    print(f"CONF_BUCKET is {conf_bucket}")

    # Get AWS Secret Manager ARN
    print("Setting SecretManager ARN...")
    secret_arn = get_secret_arn(env, pj_prefix, service_prefix, app_name)
    print(f"SecretManager ARN is {secret_arn}")

    # If env-vars argument is exist, then put secret value to SecretManager, and update secret variables to ecs task definition json file
    if env_vars:
        # create/update secret value to SecretManager
        put_secret_value(secret_arn, env_vars)
        # Download image.zip from conf bucket
        download_zip_file(
            conf_master_bucket,
            conf_bucket,
            zip_file,
            os.path.join(script_dir, folder_name),
        )
        # update secret ARN in ecs task definition json file
        update_secret_in_task_def(
            os.path.join(script_dir, folder_name), secret_arn, env_vars
        )
    else:
        download_zip_file(
            conf_master_bucket,
            conf_bucket,
            zip_file,
            os.path.join(script_dir, folder_name),
        )

    create_param_ini(environment)

    # zip image_front/image_backend folder to conf bucket
    upload_to_s3(conf_bucket, os.path.join(script_dir, folder_name))


if __name__ == "__main__":
    main()
