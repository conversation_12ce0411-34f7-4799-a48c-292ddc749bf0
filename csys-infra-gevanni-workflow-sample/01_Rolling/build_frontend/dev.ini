[Env]
ENV=Dev35
PJ_PREFIX=GEVANNI
SERVICE_PREFIX=sample04-dev35-gh

[EcsApp]
APP_NAME=EcsBackend

[Docker]
# docker buildの実行ディレクトリ
# リポジトリのルートからの相対パス
BUILD_DIR=container/frontend

# Dockerfileのパス
# BUILD_DIRからの相対パス
FILE_PATH=dockerfile

[Task]
# 初期起動タスク数
DESIRED_COUNT=2

[AutoScale]
MIN_CAPACITY=2
MAX_CAPACITY=5
TARGET_VALUE=1

[CapacityProvider]
FARGATE_BASE=2
FARGATE_WEIGHT=1
FARGATE_SPOT_BASE=0
FARGATE_SPOT_WEIGHT=2

# ALB HealthCheck Setting
[HealthCheck]
HEALTH_PATH=/
GRACE_PERIOD=10
INTERVAL=30
TIMEOUT=5
HEALTHY_THRESHOLD_COUNT=5
UNHEALTHY_THRESHOLD_COUNT=2
