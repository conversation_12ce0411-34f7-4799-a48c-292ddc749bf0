import os
import json
import gspread
from google.oauth2.service_account import Credentials

def get_auth_info_from_env():
    # Get authentication info from environment variable
    auth_info_json = os.getenv("GOOGLE_AUTH_INFO")
    if auth_info_json is None:
        raise ValueError("GOOGLE_AUTH_INFO environment variable is not set.")
    auth_info = json.loads(auth_info_json)
    return auth_info

def get_secret_info_from_sheet():
    # Get authentication info from environment variables
    auth_info = get_auth_info_from_env()

    # Get spreadsheet_id and sheet_name from environment variables
    spreadsheet_id = os.getenv("SPREADSHEET_ID")
    sheet_name = os.getenv("SHEET_NAME")

    if spreadsheet_id is None:
        raise ValueError("SPREADSHEET_ID environment variable is not set.")
    if sheet_name is None:
        raise ValueError("SHEET_NAME environment variable is not set.")

    # Print spreadsheet_id and sheet_name for debugging
    print(f"SPREADSHEET_ID: {spreadsheet_id}")
    print(f"SHEET_NAME: {sheet_name}")

    # Define the scope of the API
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/spreadsheets",
             "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive"]

    # Authenticate and initialize the client
    creds = Credentials.from_service_account_info(auth_info, scopes=scope)
    client = gspread.authorize(creds)

    # Try to open the Google Sheet and get all values
    try:
        # Open the Google Sheet by ID and sheet name
        sheet = client.open_by_key(spreadsheet_id).worksheet(sheet_name)
        # Get all values from the sheet
        data = sheet.get_all_values()
    except Exception as e:
        raise RuntimeError(f"Failed to retrieve data from Google Sheets: {e}")

    # Collect keys and values to write to GITHUB_ENV
    keys = []
    github_env = os.getenv('GITHUB_ENV')

    if github_env:
        with open(github_env, 'a') as github_env_file:
            for row in data:
                if len(row) >= 2:
                    key, value = row[0], row[1]
                    github_env_file.write(f"{key}={value}\n")
                    keys.append(key)

            # Create ENV_VARS variable with keys joined by commas
            env_vars_value = ','.join(keys)
            github_env_file.write(f"ENV_VARS={env_vars_value}\n")

    return ','.join(keys)

def main():
    # List key 
    keys = get_secret_info_from_sheet()
    print(f"Keys set in environment variables: ENV_VARS={keys}")

if __name__ == "__main__":
    main()
