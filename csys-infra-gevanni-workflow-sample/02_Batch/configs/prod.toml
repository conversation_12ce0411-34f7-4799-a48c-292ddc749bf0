#:schema ./../../../../.vscode/schemas/env.toml-schema.json

[env]
name = "<Gevanni_env_name(prod)>"
service_prefix = "<Gevanni_service_prefix(prod)>"


[tasks.<task_name>.definition]
name = "<task_definition_file_name>"
spec = { cpu = 512, memory = 1024 }

# <タスク名>.<コンテナ名>
[tasks.<task_name>.build.<container_name>]
context = "<batch_docker_build_context>"
image_placeholder = "<image_place_hodlder_value>"
