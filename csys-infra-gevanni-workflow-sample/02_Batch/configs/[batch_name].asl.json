{"StartAt": "{{ <task_name> }}", "States": {"{{ <task_name> }}": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"Cluster": "{{ CLUSTER_NAME }}", "LaunchType": "FARGATE", "EnableExecuteCommand": true, "NetworkConfiguration": {"AwsvpcConfiguration": {"SecurityGroups": ["{{ SECURITY_GROUP_ID }}"], "Subnets": ["{{ SUBNET_ID_0 }}", "{{ SUBNET_ID_1 }}", "{{ SUBNET_ID_2 }}"]}}, "PlatformVersion": "LATEST", "TaskDefinition": "{{ <task_name>_task_def }}", "PropagateTags": "TASK_DEFINITION", "Overrides": {}}, "End": true}}}