{"StartAt": "{{ task }}", "States": {"{{ task }}": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"Cluster": "{{ CLUSTER_NAME }}", "LaunchType": "FARGATE", "EnableExecuteCommand": true, "NetworkConfiguration": {"AwsvpcConfiguration": {"SecurityGroups": ["{{ SECURITY_GROUP_ID }}"], "Subnets": ["{{ SUBNET_ID_0 }}", "{{ SUBNET_ID_1 }}", "{{ SUBNET_ID_2 }}"]}}, "PlatformVersion": "LATEST", "TaskDefinition": "{{ task_task_def }}", "PropagateTags": "TASK_DEFINITION", "Overrides": {}}, "Next": "Success Notification", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Set Error Context", "ResultPath": "$.errorInfo"}]}, "Set Error Context": {"Type": "Pass", "Parameters": {"failedTask": "{{ task }}", "errorInfo.$": "$.errorInfo"}, "ResultPath": "$.errorDetails", "Next": "Failure Notification"}, "Success Notification": {"Type": "Task", "Resource": "arn:aws:states:::sns:publish", "Parameters": {"TopicArn": "{{ SNS_TOPIC_ARN }}", "Message": {"status": "SUCCESS", "executionId.$": "$$.Execution.Id"}, "MessageAttributes": {"status": {"DataType": "String", "StringValue": "SUCCESS"}}}, "End": true}, "Failure Notification": {"Type": "Task", "Resource": "arn:aws:states:::sns:publish", "Parameters": {"TopicArn": "{{ SNS_TOPIC_ARN }}", "Message": {"Error.$": "$.errorDetails.errorInfo.Error", "Cause.$": "$.errorDetails.errorInfo.Cause", "Status": "FAILURE", "TaskName.$": "$.errorDetails.failedTask", "ExecutionId.$": "$$.Execution.Id", "StateMachine.$": "$$.StateMachine.Name"}, "MessageAttributes": {"status": {"DataType": "String", "StringValue": "FAILURE"}, "failedTask": {"DataType": "String", "StringValue.$": "$.errorDetails.failedTask"}}}, "ResultPath": "$.snsFailureNotificationResult", "End": true}}}