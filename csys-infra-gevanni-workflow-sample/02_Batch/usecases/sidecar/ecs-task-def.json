{"containerDefinitions": [{"name": "batch", "essential": true, "image": "{{ image0 }}", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "region": "{{ AWS_REGION }}", "delivery_stream": "{{ STREAM_NAME }}", "retry_limit": "2"}}, "dependsOn": [{"containerName": "logRouter", "condition": "HEALTHY"}, {"containerName": "sidecar", "condition": "HEALTHY"}], "secrets": [{"name": "DB_HOST", "valueFrom": "{{ secrets_manager_build_placeholder }}:DB_HOST::"}, {"name": "DB_PASSWORD", "valueFrom": "{{ secrets_manager_build_placeholder }}:DB_PW::"}], "environment": [{"name": "DB_PORT", "value": "4000"}, {"name": "DB_NAME", "value": "application"}]}, {"name": "sidecar", "essential": true, "image": "{{ image1 }}", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "region": "{{ AWS_REGION }}", "delivery_stream": "{{ STREAM_NAME }}", "retry_limit": "2"}}, "dependsOn": [{"containerName": "logRouter", "condition": "HEALTHY"}], "secrets": [{"name": "API_KEY", "valueFrom": "{{ secrets_manager_build_placeholder }}:API_KEY::"}], "healthCheck": {"command": ["CMD-SHELL", "curl localhost:5000 || exit 1"]}}, {"name": "logRouter", "firelensConfiguration": {"type": "fluentbit"}, "essential": true, "image": "{{ FIRELENS_IMAGE_URI }}", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "{{ LOG_GROUP_FIRELENS }}", "awslogs-region": "{{ AWS_REGION }}", "awslogs-create-group": "true", "awslogs-stream-prefix": "{{ family }}-fire<PERSON>s"}}, "memoryReservation": 50, "healthCheck": {"command": ["CMD-SHELL", "echo '{\"health\": \"check\"}' | nc 127.0.0.1 8877 || exit 1"]}}], "cpu": "{{ task_cpu }}", "executionRoleArn": "{{ EXECUTION_ROLE_ARN }}", "taskRoleArn": "{{ task_taskRoleArn }}", "family": "{{ family }}", "memory": "{{ task_memory }}", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"]}