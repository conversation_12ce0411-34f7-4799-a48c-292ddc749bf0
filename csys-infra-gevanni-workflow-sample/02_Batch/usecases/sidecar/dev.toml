#:schema ./../../../../.vscode/schemas/env.toml-schema.json

[env]
name = "Dev01"
service_prefix = "sample01-dev01-ab"


[tasks.task.definition]
name = "ecs-task-def.json"
spec = { cpu = 512, memory = 1024 }

# <タスク名>.<コンテナ名>
[tasks.task.build.batch]
context = "batch/app" # 仮の値
image_placeholder = "image0"
secrets_manager_build_placeholder = "automatically_fetched_by_build_script"

[tasks.task.build.sidecar]
context = "batch/sidecar" # 仮の値
image_placeholder = "image1"
secrets_manager_build_placeholder = "automatically_fetched_by_build_script"