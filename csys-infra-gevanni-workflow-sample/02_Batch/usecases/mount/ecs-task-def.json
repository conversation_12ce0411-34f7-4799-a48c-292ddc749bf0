{"containerDefinitions": [{"name": "batch", "essential": true, "image": "{{ image0 }}", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "region": "{{ AWS_REGION }}", "delivery_stream": "{{ STREAM_NAME }}", "retry_limit": "2"}}, "dependsOn": [{"containerName": "logRouter", "condition": "HEALTHY"}], "mountPoints": [{"containerPath": "/mnt/efs", "sourceVolume": "efs-volume", "readOnly": false}]}, {"name": "logRouter", "firelensConfiguration": {"type": "fluentbit"}, "essential": true, "image": "{{ FIRELENS_IMAGE_URI }}", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "{{ LOG_GROUP_FIRELENS }}", "awslogs-region": "{{ AWS_REGION }}", "awslogs-create-group": "true", "awslogs-stream-prefix": "{{ family }}-fire<PERSON>s"}}, "memoryReservation": 50, "healthCheck": {"command": ["CMD-SHELL", "echo '{\"health\": \"check\"}' | nc 127.0.0.1 8877 || exit 1"]}}], "cpu": "{{ mount_task_cpu }}", "executionRoleArn": "{{ EXECUTION_ROLE_ARN }}", "taskRoleArn": "{{ mount_task_taskRoleArn }}", "family": "{{ family }}", "memory": "{{ mount_task_memory }}", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "volumes": [{"name": "efs-volume", "efsVolumeConfiguration": {"fileSystemId": "{{ efs_id_build_placeholder }}", "rootDirectory": "/", "transitEncryption": "ENABLED"}}]}