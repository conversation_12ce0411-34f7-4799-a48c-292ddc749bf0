#:schema ./../../../../.vscode/schemas/env.toml-schema.json

[env]
name = "Dev01"
service_prefix = "sample01-dev01-ab"


[tasks.taska.definition]
name = "taska-ecs-task-def.json"
spec = { cpu = 512, memory = 1024 }

# <タスク名>.<コンテナ名>
[tasks.taska.build.batch]
context = "batch/aggregate" # 仮の値
image_placeholder = "image0"

[tasks.taskb.definition]
name = "taskb-ecs-task-def.json"
spec = { cpu = 512, memory = 1024 }

# <タスク名>.<コンテナ名>
[tasks.taskb.build.batch]
context = "batch/export" # 仮の値
image_placeholder = "image0"
