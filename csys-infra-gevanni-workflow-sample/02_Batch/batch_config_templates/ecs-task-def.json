{"containerDefinitions": [{"name": "<container_name>", "essential": true, "image": "{{ <place_hodlder_value> }}", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "region": "{{ AWS_REGION }}", "delivery_stream": "{{ STREAM_NAME }}", "retry_limit": "2"}}, "dependsOn": [{"containerName": "logRouter", "condition": "HEALTHY"}]}, {"name": "logRouter", "firelensConfiguration": {"type": "fluentbit"}, "essential": true, "image": "{{ FIRELENS_IMAGE_URI }}", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "{{ LOG_GROUP_FIRELENS }}", "awslogs-region": "{{ AWS_REGION }}", "awslogs-create-group": "true", "awslogs-stream-prefix": "{{ family }}-fire<PERSON>s"}}, "memoryReservation": 50, "healthCheck": {"command": ["CMD-SHELL", "echo '{\"health\": \"check\"}' | nc 127.0.0.1 8877 || exit 1"]}}], "cpu": "{{ <task_name>_cpu }}", "executionRoleArn": "{{ EXECUTION_ROLE_ARN }}", "taskRoleArn": "{{ <task_name>_taskRoleArn }}", "family": "{{ family }}", "memory": "{{ <task_name>_memory }}", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"]}