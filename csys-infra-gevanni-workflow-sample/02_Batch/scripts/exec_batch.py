import json
import sys
import tomllib
from argparse import Argument<PERSON><PERSON><PERSON>
from pathlib import Path

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError


class Args:
    def __init__(self, environment: str, definition_file_dir: str, batch_name: str):
        self._validate(environment, definition_file_dir)
        self.environment = environment
        self.definition_file_dir = Path(definition_file_dir).resolve()
        self.batch_name = batch_name

    def _validate(self, environment: str, definition_file_dir: str):
        if environment not in ["dev", "stg", "prod"]:
            sys.exit("Please set an argument such as dev/stg/prod.")
        if not Path(definition_file_dir).resolve().exists():
            sys.exit("Definition file directory not found.")


def build_arguments():
    parser = ArgumentParser(prog="update_batch_secret")
    parser.add_argument("environment", help="Environment such as dev/stg/prod", metavar="{dev/stg/prod}")
    parser.add_argument("definition_file_dir", help="Definition file directory", metavar="[definition_file_dir]")
    parser.add_argument("batch_name", help="Batch name", metavar="[batch_name]")
    return parser


# 設定ファイル読み込み
def read_param_file(environment: str, path: Path):
    param_file = path / f"{environment}.toml"
    if not param_file.exists():
        sys.exit(f"Configuration file '{environment}.toml' is not found.")

    with open(param_file, "rb") as f:
        obj = tomllib.load(f)

    return obj


def read_triggers(batch_name: str, path: Path):
    trigger_file = path / f"{batch_name}.trigger.json"
    if not trigger_file.exists():
        sys.exit(f"Configuration file '{batch_name}.trigger.json' is not found.")

    with open(trigger_file, "r") as f:
        data = json.load(f)

    return data['triggers']


def choose_input_command_in_triggers(triggers: list[tuple[str, dict]]):
    def transform_input(input_: dict):
        overrides = []
        for commands in [*input_.values()]:
            overrides.append(
                [
                    {'Name': command['containerName'], 'Command': command['passedCommand']}
                    for command in commands['commands']
                ]
            )
        return {
            "containerOverrides": overrides
        }


    if len(triggers) == 1:
        _, value = triggers[0]
        input_: dict = value.get('inputs', None)
        if input_ is None:
            return None
        return transform_input(input_)

    if len(triggers) > 1:
        trigger_names = [key for key, _ in triggers]
        msgs = [
            "\nSelect inputs in your triggers:",
            f"{"\n".join([f"[{i + 1}] {trigger}" for i, trigger in enumerate(trigger_names)])}",
            f"Enter number [1 to {len(triggers)}]: "
        ]
        try:
            choice = input("\n".join(msgs)).strip()
            _, value = triggers[int(choice) - 1]
            input_ = value.get('inputs', None)
            if input_ is None:
                return None
            return transform_input(input_)
        except KeyboardInterrupt:
            sys.exit("\nOperation canceled.")


def get_account_id():
    try:
        return boto3.client("sts").get_caller_identity()["Account"]
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")


def execute_batch(batch_name: str, service_prefix: str, input_: dict | None = None):
    sfn = boto3.client("stepfunctions")
    state_machine_arn = f"arn:aws:states:ap-northeast-1:{get_account_id()}:stateMachine:{service_prefix}-{batch_name}"
    try:
        sfn.describe_state_machine(stateMachineArn=state_machine_arn)
    except sfn.exceptions.StateMachineDoesNotExist:
        sys.exit(f"State machine '{state_machine_arn}' is not found.")

    print(f"Start execution of the batch '{batch_name}'")
    start_execution_args = {
        "stateMachineArn": state_machine_arn
    }
    if input_ is not None:
        start_execution_args["input"] = json.dumps(input_)
    sfn.start_execution(**start_execution_args)
    print("Please check the log at NewRelic.")


def main():
    parser = build_arguments()
    args = Args(**parser.parse_args().__dict__)

    # 設定ファイル読み込み
    param = read_param_file(args.environment, path=args.definition_file_dir)

    try:
        service_prefix = param["env"]["service_prefix"]
        batch_name = args.batch_name
    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    triggers = [*read_triggers(batch_name, args.definition_file_dir).items()]
    input_ = choose_input_command_in_triggers(triggers)
    execute_batch(batch_name, service_prefix, input_)



if __name__ == "__main__":
    main()
