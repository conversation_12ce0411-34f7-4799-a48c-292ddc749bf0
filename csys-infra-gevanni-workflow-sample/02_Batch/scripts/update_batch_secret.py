import json
import os
import sys
from argparse import Argument<PERSON>ars<PERSON>
from pathlib import Path

import boto3
import tomllib
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

ssm = boto3.client("ssm")
secret = boto3.client("secretsmanager")

GET_SECRET_RETVAL_SAMPLE_STRING = """Please describe as indicated below
{
    "key1":"value",
    "key2":"value"
}"""

SECERT_FILE_NAME_TEMPLATE = "{task_name}_secret_list.json"

class Args:
    def __init__(self, environment: str, definition_file_dir: str, batch_name: str):
        self._validate(environment, definition_file_dir)
        self.environment = environment
        self.definition_file_dir = Path(definition_file_dir).resolve()
        self.batch_name = batch_name

    def _validate(self, environment: str, definition_file_dir: str):
        if environment not in ["dev", "stg", "prod"]:
            sys.exit("Please set an argument such as dev/stg/prod.")
        if not Path(definition_file_dir).resolve().exists():
            sys.exit("Definition file directory not found.")


def build_arguments():
    parser = ArgumentParser(prog="update_batch_secret")
    parser.add_argument("environment", help="Environment such as dev/stg/prod", metavar="{dev/stg/prod}")
    parser.add_argument("definition_file_dir", help="Definition file directory", metavar="[definition_file_dir]")
    parser.add_argument("batch_name", help="Batch name", metavar="[batch_name]")
    return parser


# 設定ファイル読み込み
def read_param_file(environment: str, path: Path):
    param_file = path / f"{environment}.toml"
    if not param_file.exists():
        sys.exit(f"Configuration file '{environment}.toml' is not found.")

    with open(param_file, "rb") as f:
        obj = tomllib.load(f)

    return obj


def choose_operation_target_task_names(task_names: list[str]):
    if len(task_names) == 1:
        return task_names

    if len(task_names) > 1:
        msgs = [
            "\nSelect Secret operation:",
            f"{"\n".join([f"[{i + 1}] {task_name}" for i, task_name in enumerate(task_names)])}",
            f"[{len(task_names) + 1}] all of tasks",
            f"Enter number [1 to {len(task_names) + 1}]: "
        ]
        try:
            choice = input("\n".join(msgs)).strip()
        except KeyboardInterrupt:
            sys.exit("\nOperation canceled.")

        if int(choice) == len(task_names) + 1:
            return task_names

        try:
            task_name = [task_names[int(choice) - 1]]
            return task_name
        except IndexError:
            sys.exit(f"Invalid choice. Please enter a number from 1 to {len(task_names) + 1}.")


# Secrets ManagerのARN取得
def get_secret_arn(gevanni_env: str, service_prefix: str, batch_name: str, task_name: str):
    print("Setting Secrets manager ARN...")

    ssm_name = f"/{gevanni_env}-GEVANNI-{service_prefix}/batch/{batch_name}/secret/task/{task_name}"
    print(ssm_name)

    try:
        response = ssm.get_parameter(Name=ssm_name)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except ssm.exceptions.ParameterNotFound:
        sys.exit("Parameter not found.")
    except Exception as e:
        sys.exit(f"Failed to get parameter: {e}")


# シークレットの取得
def get_secret_value(secret_arn: str, secret_file: Path):
    print("Getting secret value...")

    try:
        response = secret.get_secret_value(SecretId=secret_arn)
        secret_string = response.get("SecretString")
        if secret_string:
            with open(secret_file, "w", encoding="utf-8") as file:
                json.dump(json.loads(secret_string), file, indent=2)
        else:
            sys.exit("No SecretString found.")
        print("Download is complete!")
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except secret.exceptions.ResourceNotFoundException:
        sys.exit("Secret not found.")
    except Exception as e:
        sys.exit(f"Failed to get secret value: {e}")


# シークレットの登録
def put_secret_value(secret_arn: str, secret_file: Path):
    try:
        with open(secret_file, "r", encoding="utf-8") as file:
            secret_string = file.read()
        secret.put_secret_value(SecretId=secret_arn, SecretString=secret_string)
        os.remove(secret_file)
        print("Update is complete!")
    except (NoCredentialsError, PartialCredentialsError):
        sys.exit("Credentials are not configured properly.")
    except FileNotFoundError:
        sys.exit("secrets_list.json file not found.")
    except Exception as e:
        sys.exit(f"Failed to put secret value: {e}")


def main():
    parser = build_arguments()
    args = Args(**parser.parse_args().__dict__)

    # 設定ファイル読み込み
    param = read_param_file(args.environment, path=args.definition_file_dir)

    try:
        gevanni_env = param["env"]["name"]
        service_prefix = param["env"]["service_prefix"]
        task_names = param["tasks"].keys()
        batch_name = args.batch_name
    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    operation_target_task_names = choose_operation_target_task_names(list(task_names))

    # Secrets ManagerのARN取得
    secret_arns = []
    for task_name in operation_target_task_names:
        secret_arn = get_secret_arn(gevanni_env, service_prefix, batch_name, task_name)
        print(f"Secrets manager is {secret_arn}")
        secret_arns.append((task_name, secret_arn))

    choice = input(
        "\nSelect Secret operation:\n[1] Get secret\n[2] Update secret\nEnter number [1 or 2]: "
    ).strip()

    for task_name, secret_arn in secret_arns:
        secret_file = Path(SECERT_FILE_NAME_TEMPLATE.format(task_name=task_name))
        # シークレットの取得
        if choice == "1":
            get_secret_value(secret_arn, secret_file)
            print(f"Secret output file: {secret_file.resolve()}")
            print(GET_SECRET_RETVAL_SAMPLE_STRING)
        # シークレットの登録
        elif choice == "2":
            put_secret_value(secret_arn, secret_file)
            pass
        else:
            sys.exit("Invalid choice. Please enter 1 or 2.")


if __name__ == "__main__":
    main()
