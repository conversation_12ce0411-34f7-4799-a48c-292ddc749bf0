import base64
import io
import json
import os
import sys
import urllib
import urllib.parse
from pathlib import Path
from typing import Generator, Literal, Optional, TypedDict
from zipfile import ZIP_DEFLATED, ZipFile

import boto3
import click
import docker
import tomllib
import jinja2

FIRELENS_CONTAINER_NAME = "logRouter"
REGION = os.environ.get("AWS_DEFAULT_REGION") or "ap-northeast-1"


class Config(TypedDict):
    env: "EnvironmentParameter"
    tasks: dict[str, "TaskParameter"]


class EnvironmentParameter(TypedDict):
    name: str
    service_prefix: str


class TaskParameter(TypedDict):
    definition: "TaskDefinitionParameter"
    build: dict[str, "DockerParameter"]


class TaskDefinitionParameter(TypedDict):
    name: str
    spec: "TaskSpecParameter"


class TaskSpecParameter(TypedDict):
    cpu: int
    memory: int


class DockerParameter(TypedDict):
    context: str
    dockerfile: Optional[str]
    image_placeholder: str


def main():
    build_app()


@click.command()
@click.argument("env", type=click.Choice(["dev", "stg", "prod"]))
@click.argument("definition_file_dir", type=click.Path(exists=True))
@click.argument("batch_name", type=str)
@click.option("--image_tag", "-i", type=str, default="latest")
def build_app(env: Literal["dev", "stg", "prod"], definition_file_dir: str, batch_name: str, image_tag: str):
    print(f"deploy target environment is {env}")

    # This workflow runs in the repository root directory
    definition_file_dir_path = Path(definition_file_dir).resolve()

    param: Config = read_parameter_file(env, definition_file_dir_path)

    parameter_prefix = f"/{param['env']['name']}-GEVANNI-{param['env']['service_prefix']}/batch/{batch_name}"
    repository_names = get_ecr_repository_names(parameter_prefix)

    docker_user, password, registry = login_to_ecr()

    docker_client = docker.from_env()
    docker_client.login(username=docker_user, password=password, registry=registry)

    repo_uri_map = push_images(docker_client, param["tasks"], repository_names, registry, image_tag)

    gevanni_common_parameter_prefix = f"/{param['env']['name']}-GEVANNI-common"
    shared_bucket_name = boto3.client("ssm").get_parameter(
        Name=f"{gevanni_common_parameter_prefix}/BatchMasterBucketName"
    )["Parameter"]["Value"]
    shared_bucket_contents: dict[str, bytes] = download_shared_bucket_contents(shared_bucket_name)
    render_task_def(parameter_prefix, param, definition_file_dir_path)
    content = compress_files(definition_file_dir_path, env, repo_uri_map, shared_bucket_contents)
    upload_to_s3(parameter_prefix, content.read())


def read_parameter_file(env: str, dir_path: Path):
    param_file = (dir_path / f"{env}.toml").resolve()
    if not param_file.is_file():
        raise Exception(f"'{env}.toml' is not found.")

    with open(param_file, "rb") as f:
        obj = tomllib.load(f)

    return obj


def get_ecr_repository_names(parameter_prefix: str):
    client = boto3.client("ssm")
    parameter_name = f"{parameter_prefix}/repository/"
    parameters = client.get_parameters_by_path(Path=parameter_name, Recursive=True)["Parameters"]

    # The parameter name is like /<Gevanni prfix>/batch/<batch name>/repository/task/<task name>/container/<container name>
    repository_names: dict[str, str] = {}
    for parameter in parameters:
        parameter_name_splited_by_slash = parameter["Name"].split("/")
        key = parameter_name_splited_by_slash[-3] + parameter_name_splited_by_slash[-1]
        # The parameter value is the repository name
        repository_names[key] = parameter["Value"]

    return repository_names


def login_to_ecr():
    client = boto3.client("ecr", region_name=REGION)
    authorization_data = client.get_authorization_token()["authorizationData"][0]

    # The authorization token is base64 encoded and contains username and password separated by a colon
    token = authorization_data["authorizationToken"]
    decoded_token = base64.b64decode(token).decode("utf-8")
    username, password = decoded_token.split(":")

    # The proxy_endpoint is like https://<aws_account_id>.dkr.ecr.<region>.amazonaws.com
    proxy_endpoint = authorization_data["proxyEndpoint"]
    registry = urllib.parse.urlparse(proxy_endpoint).netloc

    return username, password, registry


def push_images(
    client: docker.DockerClient,
    param: dict[str, TaskParameter],
    repository_names: dict[str, str],
    registry: str,
    tag: str,
):
    repo_uri_map: dict[str, str] = {}
    for task_name, task_param in param.items():
        for container_name, container_param in task_param["build"].items():
            repo_name = repository_names[f"{task_name}{container_name}"]
            repo_uri_without_tag = f"{registry}/{repo_name}"
            context = container_param["context"]
            dockerfile = container_param["dockerfile"] if "dockerfile" in container_param else None

            _, logs = client.images.build(path=f"{context}", dockerfile=dockerfile, tag=f"{repo_uri_without_tag}:{tag}")
            for log_json in logs:
                for log in log_json.values():
                    print(log)

            log_stream = client.images.push(repository=repo_uri_without_tag, tag=tag, stream=True, decode=True)
            show_push_log(log_stream)

            repo_uri_map[f"{task_name}{container_name}"] = f"{repo_uri_without_tag}:{tag}"

    return repo_uri_map


def show_push_log(log_stream: Generator):
    layers: dict[str, str] = {}

    # log_stream is a generator that yields a dictionary like {"status": "Preparing", "id": "sha256:xxxxx"}
    # The id is the layer id and status is the status of the layer.
    for log in log_stream:
        if "status" not in log:
            print(log)
            continue

        if "id" not in log:
            print(log["status"])
        elif log["status"] == "Preparing":
            layers[log["id"]] = f"{log['id']}: {log['status']}"
            print(layers[log["id"]])
        elif "progress" in log:
            layers[log["id"]] = f"{log['id']}: {log['status']} {log['progress']}"
            print(layers[log["id"]])
        elif "error" in log:
            print(f"Error: {log['error']}", file=sys.stderr)
        else:
            layers[log["id"]] = f"{log['id']}: {log['status']}"
            print(layers[log["id"]])


def download_shared_bucket_contents(shared_bucket_name: str):
    s3 = boto3.client("s3", region_name=REGION)
    deploy_batch_contents = s3.list_objects_v2(Bucket=shared_bucket_name, Prefix="deploy_batch")["Contents"]
    content_bodies: dict[str, bytes] = {
        content["Key"].split("/")[-1]: s3.get_object(Bucket=shared_bucket_name, Key=content["Key"])["Body"].read()
        for content in deploy_batch_contents
    }

    return content_bodies


def compress_files(dir_path: Path, env: str, repo_uri_map: dict[str, str], shared_bucket_contents: dict[str, bytes]):
    compress_target_files = list(dir_path.glob("*.json")) + [(dir_path / f"{env}.toml")]
    zip_buffer = io.BytesIO()
    with ZipFile(zip_buffer, "w", ZIP_DEFLATED) as z:
        print("Compressing files...")
        for compress_target in compress_target_files:
            print(f"    target: {compress_target.name}")
            z.write(compress_target, arcname=compress_target.name)

        for file_name, body in shared_bucket_contents.items():
            print(f"    target: {file_name}")
            z.writestr(file_name, body)
        text = json.dumps(repo_uri_map).encode("utf-8")
        print("    target: repo_uri_map.json")
        z.writestr("repo_uri_map.json", text)

    zip_buffer.seek(0)
    return zip_buffer


def upload_to_s3(parameter_prefix: str, body: bytes):
    ssm = boto3.client("ssm")
    bucket_name = ssm.get_parameter(Name=f"{parameter_prefix}/bucket/source")["Parameter"]["Value"]
    s3 = boto3.client("s3", region_name=REGION)
    s3.put_object(Bucket=bucket_name, Key="image.zip", Body=body)
    print(f"upload to s3://{bucket_name}/image.zip")

def render_template(template_path: Path, props: dict):
    with open(template_path) as f:
        text: str = f.read()

    class KeepPlaceholder(jinja2.Undefined):
        def __str__(self):
            return '{{ ' + self._undefined_name + ' }}'
        
    template = jinja2.Template(text, undefined=KeepPlaceholder)
    rendered: str = template.render(props)
    return json.loads(rendered)

def render_task_def(parameter_prefix: str, params: Config, file_path: Path):
    ssm = boto3.client("ssm", region_name=REGION)
    
    tasks: dict[str, str] = {}
    for task_name, task_param in params["tasks"].items():
        tasks[task_name] = task_name
        secrets_manager_arn = ssm.get_parameter(Name=f"{parameter_prefix}/secret/task/{task_name}")["Parameter"]["Value"]
        build_placeholders: dict[str, str] = {}
        for container_name, container_param in task_param["build"].items():
            for param_key, param_value in container_param.items():
                if param_key == "secrets_manager_build_placeholder":
                    build_placeholders[param_key] = secrets_manager_arn
                elif param_key.endswith('_build_placeholder'):
                    build_placeholders[param_key] = param_value
    
    task_def_path = (file_path / task_param["definition"]["name"]).resolve()
    ecs_task_def_rendered: dict = render_template(task_def_path, {**build_placeholders})
    
    with open(task_def_path, "w") as f:
        json.dump(ecs_task_def_rendered, f, indent=2)
    print(f"replace secret task def: {task_def_path}")


if __name__ == "__main__":
    main()
