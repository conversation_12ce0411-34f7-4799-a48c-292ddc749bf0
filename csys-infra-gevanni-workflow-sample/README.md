# csys-infra-gevanni-workflow-sample

Gevanni 利用のための GitHub Actions ワークフローのサンプル(アプリチーム向け)

## バッチに関するワークフローとドキュメント

バッチに関するワークフローやドキュメントは以下に格納してあります。

- ワークフロー
  - [.github/workflows/simple-batch-deploy.yml](/.github/workflows/simple-batch-deploy.yml)
- 設定ファイルのテンプレート
  - [02_Batch/batch_config_templates/](/02_Batch/batch_config_templates/)
- 設定ファイルのサンプル
  - [02_Batch/usecases](/02_Batch/usecases/)
- ビルド用スクリプト
  - [/02_Batch/scripts/build_batch](/02_Batch/scripts/build_batch)
- 設定ファイルのドキュメント
  - JPN: [docs/JPN/batch/HowToUseBatch_JA.md](/docs/JPN/batch/HowToUseBatch_JA.md)
  - ENG: [docs/EN/batch/HowToUseBatch_EN.md](/docs/EN/batch/HowToUseBatch_EN.md)

### バッチをデプロイするための手順

以下にバッチをデプロイするために対応することの手順を示す。

1. [02_Batch/batch_config_templates](/02_Batch/batch_config_templates/) の中身のファイル (設定ファイル) をアプリチームリポジトリにコピー
1. [02_Batch/build_batch](/02_Batch/build_batch) の中身のファイルをアプリチームリポジトリにコピー
1. [設定ファイルに入力する値](/docs/JPN/batch/HowToUseBatch_JA.md#設定ファイルに入力する値) を確認し、各種設定ファイルの必要箇所を埋める
   - [設定ファイルに入力する値 (英語)](/docs/EN/batch/HowToUseBatch_EN.md#設定ファイルに入力する値)
1. バッチアプリの要件に合わせて各種設定ファイルを変更する
   - 以下のような要件のバッチアプリに関しては、[ユースケースごとの設定例](/docs/JPN/batch/usecases_ja.md#ユースケースごとの設定例) に設定ファイルのサンプルが用意されているため、そちらを参考にしたり、組み合わせたりして設定ファイルを変更すること
     - 特定の時間に定期的に実行されるバッチ
     - タスク A を行ったあとタスク B を行うような順序あるバッチ
     - 共有ファイルストレージ (EFS) をマウントするようなバッチ
     - Gevanni アカウント以外のアカウントのリソースを操作できるバッチ
       - 今のところ S3 のみサポート
     - 環境変数を使用するバッチ
     - サイドカーを使用するバッチ
   - 設定可能な値などについては以下を確認すること
     - 日本語
       - [ecs-task-def.json](/docs/JPN/batch/HowToUseBatch_JA.md#ecs-タスク定義ファイルについて)
       - [[batch_name].asl.json](/docs/JPN/batch/HowToUseBatch_JA.md#stepfunctions-ステートマシンの定義ファイルについて)
       - [[batch_name].trigger.json](/docs/JPN/batch/HowToUseBatch_JA.md#バッチ実行を行うサービスを作成するための定義ファイルについて)
       - [<dev/stg/prod>.toml](/docs/JPN/batch/HowToUseBatch_JA.md#環境ごとの値を制御する環境ファイルについて)
     - 英語
       - [ecs-task-def.json](/docs/EN/batch/HowToUseBatch_EN.md#about-ecs-task-definition-files)
       - [[batch_name].asl.json](/docs/EN/batch/HowToUseBatch_EN.md#about-the-stepfunctions-statemachine-definition-file)
       - [[batch_name].trigger.json](/docs/EN/batch/HowToUseBatch_EN.md#about-the-definition-file-for-creating-a-service-that-performs-batch-execution)
       - [<dev/stg/prod>.toml](/docs/EN/batch/HowToUseBatch_EN.md#for-environment-files-that-control-values-per-environment)
1. [.github/workflows/simple-batch-deploy.yml](/.github/workflows/simple-batch-deploy.yml) を参考に、アプリチームリポジトリにワークフローファイルを作成
   - ファイル名・ワークフローの実行条件、スクリプトのパスなどは適宜適切なものに変更すること
1. 上記ワークフローを実行する

### バッチを実行するための手順

- 手動実行する場合
   - 日本語: [/docs/JPN/batch/HowToUseBatchScript_JA](/docs/JPN/batch/HowToUseBatchScript_JA.md#howtouseexecbatchscripts)
   - 英語: [/docs/EN/batch/HowToUseBatchScript_EN](/docs/EN/batch/HowToUseBatchScript_EN.md#howtouseexecbatchscripts)
- API から実行する場合
   - 日本語: [/docs/JPN/batch/HowToUseBatchApiTrigger_JA](/docs/JPN/batch/HowToUseBatchApiTrigger_JA.md#howtousebatchapitrigger)
   - 英語: [/docs/EN/batch/HowToUseBatchApiTrigger_EN](/docs/EN/batch/HowToUseBatchApiTrigger_EN#howtousebatchapitrigger)
