import boto3
import time
import sys
import os
import configparser
from argparse import ArgumentParser

# クライアントの初期化
ssm = boto3.client("ssm")
ecs = boto3.client("ecs")

aws_region = "ap-northeast-1"

# カレントディレクトリの取得
script_dir = os.path.dirname(os.path.abspath(__file__))

def parse_args():
    parser = ArgumentParser()
    parser.add_argument("environment", choices=["dev", "stg", "prod"], help="Target environment where the ECS container is running")
    return parser.parse_args()

# 設定ファイル読み込み
def read_param_file(environment):
    param_file = os.path.join(script_dir, f"{environment}.ini")
    if not os.path.isfile(param_file):
        sys.exit(f"paramuration file '{environment}.ini' not found.")

    param = configparser.ConfigParser()
    param.read(param_file)
    return param


# Systems Managerパラメータの取得
def get_ssm_parameter(parameter_name):
    return ssm.get_parameter(Name=parameter_name)["Parameter"]["Value"]


# 起動したタスクのステータスを取得
def get_task_status(cluster_name, task_id):
    try:
        return ecs.describe_tasks(cluster=cluster_name, tasks=[task_id])["tasks"][0][
            "lastStatus"
        ]
    except Exception as e:
        sys.exit(f"Failed to describe tasks. Error: {e}")


# 起動したタスクのランタイムを取得
def get_runtime_id(cluster_name, task_id):
    try:
        return ecs.describe_tasks(cluster=cluster_name, tasks=[task_id])["tasks"][0][
            "containers"
        ][0]["runtimeId"]
    except Exception as e:
        sys.exit(f"Failed to describe tasks. Error: {e}")


def main():

    args = parse_args()
    environment = args.environment
    print(f"Your environment is {environment}")

    # 設定ファイル読み込み
    param = read_param_file(environment)

    try:
        env = param["Env"]["ENV"]
        pj_prefix = param["Env"]["PJ_PREFIX"]
        service_prefix = param["Env"]["SERVICE_PREFIX"]
        back_appname = param["EcsApp"]["BACK_APP_NAME"]
        tidb_username = param["TiDB"]["USERNAME"]
    except KeyError as e:
        print(f"KeyError: The key '{e.args[0]}' is not found")

    # リソース名取得
    print("Getting resource name...")

    cluster_name = get_ssm_parameter(
        f"/{env}-{pj_prefix}-common/bastion/ecs/cluster-name"
    )
    print(f"ClusterName is {cluster_name}")

    subnet_id = get_ssm_parameter(f"/{env}-{pj_prefix}-common/privateSubnetId")
    print(f"SubnetId is {subnet_id}")

    task_definition = get_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/bastion/ecs/task-def-name"
    )
    print(f"TaskDefinition is {task_definition}")

    securitygroup_id = get_ssm_parameter(
        f"/{env}-{pj_prefix}-{service_prefix}/bastion/sg-id"
    )
    print(f"SecurityGroup is {securitygroup_id}")

    # 最新タスク定義のARN取得
    try:
        task_definition_arn = ecs.describe_task_definition(
            taskDefinition=task_definition
        )["taskDefinition"]["taskDefinitionArn"]
    except Exception as e:
        print(f"Failed to describe task definition. Error: {e}")
        return

    # 踏み台コンテナ起動コマンド
    print("Running bastion container...")
    run_task_params = {
        "cluster": cluster_name,
        "count": 1,
        "enableExecuteCommand": True,
        "launchType": "FARGATE",
        "networkConfiguration": {
            "awsvpcConfiguration": {
                "subnets": [subnet_id],
                "securityGroups": [securitygroup_id],
                "assignPublicIp": "DISABLED",
            }
        },
        "platformVersion": "1.4.0",
        "taskDefinition": task_definition_arn,
    }

    # Dev環境ではタグを使用しない
    if not env.startswith("Dev"):
        run_task_params["propagateTags"] = "TASK_DEFINITION"

    # 踏み台コンテナを起動
    try:
        run_task = ecs.run_task(**run_task_params)
        task_id = run_task["tasks"][0]["taskArn"].split("/")[-1]
    except Exception as e:
        print(f"Failed to run task. Error: {e}")
        return

    # コンテナが起動するまで待機
    while True:
        task_status = get_task_status(cluster_name, task_id)
        if task_status == "RUNNING":
            runtime_id = get_runtime_id(cluster_name, task_id)
            break
        print("Waiting for container status is running...")
        time.sleep(5)

    print("Task successfully started")
    print("-------------------------------------------")
    print("踏み台コンテナへログインするコマンド")
    print(
        f"aws ssm start-session --region {aws_region} --target ecs:{cluster_name}_{task_id}_{runtime_id}"
    )
    print("-------------------------------------------")

    tidb_endpoint = get_ssm_parameter(
        f"/{env}-{pj_prefix}-common/bastion/tidb/endpoint"
    )

    print("TiDB接続コマンド")
    print(f"mysql --host {tidb_endpoint} --port 4000 -u {tidb_username} -p")
    print("-------------------------------------------")

    try:
        dns_name = get_ssm_parameter(
            f"/{env}-{pj_prefix}-{service_prefix}/{back_appname}/DnsRecordName"
        )
        port_number = get_ssm_parameter(
            f"/{env}-{pj_prefix}-{service_prefix}/{back_appname}/PortNumber"
        )   
        
        print("バックエンドコンテナの接続確認コマンド")
        print(f"curl -X GET http://{dns_name}/API_Path:{port_number}/")
        print("-------------------------------------------")
    
    except Exception as e:
        if "ParameterNotFound" in str(e):
            pass
        else:
            print(e)
    
if __name__ == "__main__":
    main()
