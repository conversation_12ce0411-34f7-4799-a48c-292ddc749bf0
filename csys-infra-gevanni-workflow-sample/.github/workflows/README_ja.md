# HowToUse-workflows

ここでは、ワークフローファイルの利用方法について記載する。

## 概要

- Gevanni においてワークフローファイルは主に AWS と疎通する際に利用する。
- YAML 形式で記載されたワークフローファイルを GitHubAction から実行し、ワークフローファイル内で AWS に対する操作を実施する。
- AWS との疎通は OpenID Connect（OIDC）を利用しており、OIDC 専用の IAM ロール ARN を GitHubVariables に登録することで AWS に対する操作を可能にしている。
  ![](./../../docs/images/gevanni-workflow-outline.dio.png)

- workflow ファイルの一覧は以下のとおりである。アプリケーションチームは必要なワークフローファイルを自身のリポジトリにクローンして利用する。
- **workflow ファイル一覧（順次更新）**

  | 名称                | 説明                                                                                          |
  | ------------------- | --------------------------------------------------------------------------------------------- |
  | ecs-deploy-frontend | フロントエンド用 ecspresso の build.py を実行し、コンテナをデプロイする。                     |
  | ecs-deploy-backend  | バックエンド用 ecspresso の build.py を実行し、コンテナをデプロイする。                       |

  ※build.py はアプリケーションのビルド、ECR へのプッシュ、パイプライン稼働の一連動作を担う

    - ビルドしたアプリケーションに脆弱性診断を行う必要があり、脆弱性管理に FutureVuls を利用している。
    - 脆弱性診断と、FutureVuls への診断結果のアップロードを build.py で実行している。
    - そのため、事前準備として FutureVuls の ID 情報を GitHub Secrets に登録する必要がある。

## 事前に必要な設定

- ワークフローファイルを実行する上で、以下 4 つの手順を実施する必要がある。
  1. FutureVuls の利用申請
  1. ワークフローファイルのカスタマイズ
  1. OIDC 専用の IAM ロールを GitHubVariables に登録
  1. FutureVuls の ID を GitHub Secrets に登録
  1. GitHub Secrets または Google スプレッドシートから環境変数を設定

### i. FutureVuls の利用申請

- ビルドするアプリケーション毎に、マイナビ社内で FutureVuls の利用申請を事前に行う。
- 環境毎 (dev,stg,prod) にフロント/バックエンドのアプリケーションビルドするため、計6つ分の申請が必要。
- 診断結果を FutureVuls にアップロードするため、申請時に払い出された FutureVuls のグループ ID、トークン、UUID が環境ごとにそれぞれ必要となる。
  - 参考：https://help.vuls.biz/manual/scan/trivy_scan/#cicdパイプラインに組み込む方法
- ビルド時に払い出された ID が必要となるため、ワークフロー実行環境の GitHub Secrets に登録する必要がある。
  - 登録手順は後述の [iiii. FutureVuls の ID　を GitHubSecrets に登録](#iiii-futurevuls-の-idを-githubsecrets-に登録) を参照すること。

### ii. ワークフローファイルのカスタマイズ

- ワークフローファイルをアプリケーションチームのリポジトリにクローンしたらカスタマイズを行う。
- カスタマイズする箇所は以下の２つ

  1. 手動・自動実行の選択
  1. スクリプトファイルのディレクトリ修正

  #### i.手動・自動実行の選択

  - 以下のように、デフォルトでは手動で GitHubAction を実行する形式になっているが、プッシュされた段階で自動実行させることも可能
  - 自動実行の箇所はコメントアウトされているため、必要に応じてこちらをコメントインし、手動処理をコメントアウトする。

    ```yaml
    on:
      workflow_dispatch:
        inputs:
          environment:
            type: choice
            description: environment
            options:
              - stg
              - dev
              - prod
    env:
      environment: ${{github.event.inputs.environment}}
    # ※自動実行(プッシュ)の場合
    #   push:
    #     branches: [ "main" ] #トリガー対象のブランチ
    #     paths: ["frontend/*"] #トリガー対象ファイル指定箇所
    # env:
    #   environment: '<dev/stg/prod>から選択'
    ```

  #### ii.スクリプトファイルのディレクトリ修正

  - AWS への操作をワークフローファイルに直接記載している場合もあるが、スクリプトファイルを呼び出している場合もある。その場合は配置しているスクリプトファイルのパスが正しいかを確認し、必要に応じて修正する。
  - 例）ecs-deploy-frontend の場合、以下のようなパスでスクリプトファイルを実行している。
    ```yaml
    - name: Run build.py
      run: python ./01_Rolling/build_frontend/build.py ${{env.environment}}
    ```

### iii. OIDC 専用の IAM ロールを GitHubVariables に登録

- OIDC にて接続を行う際は下記の手順を実行し、該当のロールを GithubVariables に登録する。

1.  Github にアクセスし、対象リポジトリの「Settings」をクリックする。
    ![](./images/how-to-register-GitHubVariables-1.png)
1.  左ペインにある「Environments」をクリックしたら、環境（例：dev、stg、prod）をクリック。
    ![](./images/how-to-register-GitHubVariables-2.png)
1. 「Environment variables」エリアで「Add environment variable」ボタンをクリック。
    ![](./images/how-to-register-GitHubVariables-3.png)
1. `Name`には利用するワークフローファイル内に記載されている`role-to-assume`の値を記入する。`Value`には IAM ロールの ARN を記入する。こちらについては、インフラチームより別途連携。

    - 例）ecs-deploy-frontend.yml の場合は`ecs_front`を`Name`に記入
      ```yaml
      with:
        role-to-assume: ${{ vars.ecs_front}}
      ```

    ![](./images/how-to-register-GitHubVariables-4.png)

### iiii. FutureVuls の ID　を GitHubSecrets に登録

- グループ ID とトークンはマイナビ社内の FutureVuls 利用申請時に払い出されたものを登録する。
  - FutureVuls の利用申請が完了していない状態で開発を進める場合、後述の「[FutureVuls 脆弱性スキャンの無効化手順](#futurevuls-脆弱性スキャンの無効化手順)」を参照して FutureVuls スキャンを無効化する。
  - **※ 利用申請が完了して ID 情報が払い出されたら、有効化し ID 情報の登録をすること。**
- Secrets 登録手順は下記の通り。

1.  Github にアクセスし、対象リポジトリの「Settings」をクリックする。  
    ![](./images/how-to-register-GitHubSecrets-1.png)
1.  左ペインにある「Environments」をクリックしたら、環境（例：dev、stg、prod）をクリック。
    ![](./images/how-to-register-GitHubSecrets-2.png)
1. 「Environment secrets」エリアで「Add environment secret」ボタンをクリックする。
    ![](./images/how-to-register-GitHubSecrets-3.png)
1.  `Name` には下記の Secrets 名を入力する。  
    ![](./images/how-to-register-GitHubSecrets-4.png)

#### 登録する Secrets 

- 登録が必要な Secrets 名は以下の通り。
  - VULS_SAAS_GROUPID_{FRONT|BACK}
    - スキャン結果をアップロードするグループの ID.
  - VULS_SAAS_TOKEN_{FRONT|BACK}
    - スキャン権限をもった FutureVuls のトークン。
  - VULS_SAAS_UUID_{FRONT|BACK}
    - 対象イメージを一意に識別する FutureVuls 内のサーバ UUID.
    - 新規登録の場合は uuidgen コマンド等で生成した値を指定する。

#### FutureVuls 脆弱性スキャンの無効化手順

- 以下のファイルを編集する。
  - [ecs-deploy-frontend.yml](/.github/workflows/ecs-deploy-frontend.yml)
  - [ecs-deploy-backend.yml](/.github/workflows/ecs-deploy-backend.yml)
- yaml ファイル内の `env` > `enable-futurevuls-scan` の値を `false` に変更する。
  - 有効化する際は、`true` にする。

**※ 利用申請が完了し、ID 情報が払い出されたら有効化と Secrets の登録すること。**

### v. GitHub Secrets または Google スプレッドシートから環境変数を設定

- 環境変数を GitHub Secrets または Google スプレッドシートから設定するには、以下の手順に従ってください。
  1. IS_USE_ENV_VARS_FROM_SPREADSHEET 変数を GitHub Secrets に設定する。
  1. 環境変数を GitHub Secrets から設定する。
  1. 環境変数を Google スプレッドシートから設定する。

  #### i. IS_USE_ENV_VARS_FROM_SPREADSHEET 変数を GitHub Secrets に設定する
  - 登録手順は以下の通りである。
    - 対象リポジトリの `Settings` をクリックする。
    - 左側のサイドバーで、 `Environments` をクリック。
    - secrets を追加したい環境をクリック。
    - `Environment secrets` で、 `Add environment secret` をクリック。
    - `Name` 入力ボックスにSecrets 名を入力。
    - secrets の値を入力。
    - `Add secret` をクリック。
  - 登録するシークレット
    - `IS_USE_ENV_VARS_FROM_SPREADSHEET`
      - `false`: GitHub Secrets から環境変数を設定する。
      - `true`: Google スプレッドシートから環境変数を設定する。

  #### ii. 環境変数を GitHub Secrets から設定する
  - App コンテナの環境変数を GitHub Secrets に手動で登録
    - 変数の命名規則
      - `VARIABLE_NAME`
      - 例: `API_KEY`

    - GitHub コンソールインターフェースを使用する場合
      - 対象リポジトリの `Settings` をクリックする。
      - 左側のサイドバーで、 `Environments` をクリック。
      - secret を追加したい環境をクリック。
      - `Environment secrets` で、 `Add environment secret` をクリック。
      - `Name` 入力ボックスにSecrets 名を入力します。
      - secrets の値を入力します。
      - `Add secret` をクリックします。
    - **注意**: シークレットを追加したいリポジトリへのアクセス権 (Admin または Write) が必要である。
  - GitHub Action ワークフローファイルに App コンテナの環境変数を登録する
    - `.github/workflows/ecs-deploy-frontend.yml` ファイルまたは `.github/workflows/ecs-deploy-backend.yml` ファイルの `Run backend build.py` または`Run frontend build.py`ステップに環境変数を追加することで、App コンテナ用の環境変数を設定することができる。
    - 環境変数の名前を `env_vars_value` に追加する。例：

      ```yml
        env_vars_value="SAMPLE_SECRET_1,SAMPLE_SECRET_2"
      ```
    - ワークフロー内で以下をコメント解除してください。

      ```yml
      - name: Run backend build.py
        # env:
        #   SAMPLE_SECRET_1: ${{ secrets.SAMPLE_SECRET_1 }}
        #   SAMPLE_SECRET_2: ${{ secrets.SAMPLE_SECRET_2 }}
      ```
      
    - GitHub Secrets から環境変数を追加・編集する。例：

      ```yml
      - name: Run backend build.py
        env:
          SAMPLE_SECRET_1: ${{ secrets.SAMPLE_SECRET_1 }}
          SAMPLE_SECRET_2: ${{ secrets.SAMPLE_SECRET_2 }}
          SAMPLE_SECRET_3: ${{ secrets.SAMPLE_SECRET_3 }}
      ```

    - **既存の環境変数を更新する場合の注意事項**
      - 環境変数のリストは `env_vars_value` で完全に上書きされる。
      - `env_vars_value` で指定されていない環境変数が登録されている場合、その環境変数は削除される。
      - 例
        - `env_vars_value="SAMPLE_SECRET_1,SAMPLE_SECRET_2"`
        - 変更前の環境変数
          - SAMPLE_SECRET_2
          - SAMPLE_SECRET_3
        - 変更内容
          - SAMPLE_SECRET_1: 新規作成
          - SAMPLE_SECRET_2: GitHub Secrets の値で上書き
          - SAMPLE_SECRET_3: 削除

  #### iii. 環境変数を Google スプレッドシートから設定する。
    - Google スプレッドシートに環境変数ファイルを作成する。
      - `Key`: シートの列 `A` の n 行目の値
      - `Value`: シートの列 `B` の n 行目の値
      ![](./images/sample-environment-variables-files-on-Google-spreadsheet.png)
      - **既存の環境変数を更新する場合の注意事項**
      - Google スプレッドシートファイルの内容は、環境変数のリストを完全に上書きします。
      - Google スプレッドシートファイルに指定されていない環境変数が登録されている場合、それらは削除されます。
      - Example:
        - 変更前の環境変数
          - SAMPLE_SECRET_2
          - SAMPLE_SECRET_3
        - Google スプレッドシートファイルの詳細を変更する
          - SAMPLE_SECRET_1: 新規作成
          - SAMPLE_SECRET_2: GitHub Secrets の値で上書き
          - SAMPLE_SECRET_3: 削除
   - GitHubSecrets に GOOGLE AUTH INFO、SPREADSHEET ID、SHEET NAME を登録する。
      - Github にアクセスし、対象リポジトリの `Settings` をクリックする。
      - 左側のサイドバーで、 `Environments` をクリック。
      - secretを追加したい環境をクリック。
      - `Environment secrets` で、 `Add environment secret` をクリック。
      - `Name` 入力ボックスにシークレットの名前を入力。
      - secretの値を入力。
      - `Add secret` をクリック。
    - 登録するシークレット
      - `GOOGLE_AUTH_INFO`
        - Google 認証情報
        - 参考:
          - [API キーの設定](https://support.google.com/googleapi/answer/6158862?hl=ja)
          - [サービスアカウントを使用して Google Sheets のデータにアクセスする方法](https://genezio.com/blog/google-sheets-as-apis/)
      - `SPREADSHEET_ID`
        - Google スプレッドシートの環境変数ファイルの SPREADSHEET ID
        - 参考: [Google Sheets API の概要におけるスプレッドシート ID](https://developers.google.com/sheets/api/guides/concepts#:~:text=unique%20spreadsheetId%20value.-,Spreadsheet%20ID,-The%20unique%20identifier)
      - `SHEET_NAME`
        - Google スプレッドシートの環境変数ファイルのシート名

## 実行手順（手動）

- 手動実行の手順は以下のとおり。自動実行についてはブランチにプッシュされたタイミングで自動発火する。

  1. 「Actions」タブを選択。
  1. 実行したいワークフロー名を選択。
  1. 「Run workflow」のプルダウンを開く。
  1. 「environment」で環境を選択し、「Run workflow」ボタンを押下し、実行する。
     ![](./images/how-to-execute-GitHubActions.png)