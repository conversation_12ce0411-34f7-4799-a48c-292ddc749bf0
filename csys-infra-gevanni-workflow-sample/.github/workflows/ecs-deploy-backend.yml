name: Deploy ECS Backend

# ※ For manual execution
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: environment
        options:
          - stg
          - dev
          - prod
env:
  environment: ${{github.event.inputs.environment}}
  python-version: "3.12" # Specify the Python version to be used.
  enable-futurevuls-scan: true # Enable vulnerability scanning with FutureVuls
  is_use_build_vars: true # use --build-arg with docker build

# ※ For automatic execution (push)
#   push:
#     branches: [ "main" ] # Trigger target branch.
#     paths: ["SET_YOUR_APP_PATH/*"] # Trigger target file designation point.
# env:
#   environment: 'Selected from <dev/stg/prod>'
#   python-version: "3.12" # Specify the Python version to be used.

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    environment: ${{github.event.inputs.environment}}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{vars.ecs_back}}
          role-session-name: GitHubActions
          aws-region: ap-northeast-1

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{env.python-version}}

      - name: Install dependencies
        run: |
          pip install -r requirements.txt

      - name: Run pull_env_from_spreadsheet.py
        env:
          IS_USE_ENV_VARS_FROM_SPREADSHEET: ${{ secrets.IS_USE_ENV_VARS_FROM_SPREADSHEET }}
        run: |
          if [ ${{ env.IS_USE_ENV_VARS_FROM_SPREADSHEET }} == "true" ]; then
            echo "GOOGLE_AUTH_INFO=${{ secrets.GOOGLE_AUTH_INFO }}" >> $GITHUB_ENV
            echo "SPREADSHEET_ID=${{ secrets.SPREADSHEET_ID }}" >> $GITHUB_ENV
            echo "SHEET_NAME=${{ secrets.SHEET_NAME }}" >> $GITHUB_ENV

            python ./01_Rolling/build_backend/pull_env_from_spreadsheet.py
          else
            # If you don't want to use the environment variables from the spreadsheet, you can set the environment variables from the GitHub Secrets, and set the name of the environment variables to be used in the build.py script to $env_vars.
            env_vars_value="SAMPLE_SECRET_1,SAMPLE_SECRET_2"
            echo "ENV_VARS=$env_vars_value" >> $GITHUB_ENV
          fi

      - name: Set BUILD_VARS
        if: ${{env.is_use_build_vars}}
        run: |
          build_vars_value="BUILD_VAR1,BUILD_VAR2"
          echo "BUILD_VARS=$build_vars_value" >> $GITHUB_ENV

      - name: Run backend build.py
        env:
          VULS_SAAS_GROUPID: ${{ secrets.VULS_SAAS_GROUPID_BACK }}
          VULS_SAAS_TOKEN: ${{ secrets.VULS_SAAS_TOKEN_BACK }}
          VULS_SAAS_UUID: ${{ secrets.VULS_SAAS_UUID_BACK }}
          # SAMPLE_SECRET_1: ${{ secrets.SAMPLE_SECRET_1 }}
          # SAMPLE_SECRET_2: ${{ secrets.SAMPLE_SECRET_2 }}
          # BUILD_VAR1: ${{ vars.BUILD_VAR1 }}
          # BUILD_VAR2: ${{ vars.BUILD_VAR2 }}
        run: python ./01_Rolling/build_backend/build.py ${{env.environment}} ${{env.enable-futurevuls-scan}} --env-vars ${{env.ENV_VARS}} --build-vars ${{env.BUILD_VARS}}
