name: Deploy Bat<PERSON>

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: environment
        options:
          - stg
          - dev
          - prod
env:
  ENVIRONMENT: ${{ github.event.inputs.environment }}
  PYTHON_VERSION: "3.12" # Specify the Python version to be used.
  SCRIPT_DIR: ./02_Batch/scripts/build_batch # Specify the path to the batch build script
  CONFIG_PATH: ./02_Batch/BatchApp # Specify the path to the batch configuration file
  BATCH_NAME: BatchApp # Specify the batch name


# ※ For automatic execution (push)
#   push:
#     branches: [ "main" ] # Trigger target branch.
#     paths: ["SET_YOUR_APP_PATH/*"] # Trigger target file designation point.
# env:
#   environment: 'Selected from <dev/stg/prod>'
#   python-version: "3.12" # Specify the Python version to be used.
#   SCRIPT_DIR: ./02_Batch/build_batch # Specify the path to the batch build script
#   CONFIG_PATH: ./02_Batch/BatchApp # Specify the path to the batch configuration file
#   BATCH_NAME: BatchApp # Specify the batch name

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    environment: ${{github.event.inputs.environment}}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: configure aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.batch_role_arn }}
          role-session-name: GitHubActions
          aws-region: ap-northeast-1

      - name: Setup Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install python
        run: pip install -r ${{ env.SCRIPT_DIR }}/requirements.txt

      - name: Run build_batch
        run: |
          tag=$(echo ${{ github.sha }} | cut -c 1-7)

          # usage:
          # build_batch [OPTIONS] {dev|stg|prod} DEFINITION_FILE_DIR BATCH_NAME
          #
          # Options:
          #  -i, --image_tag TEXT
          #  --help Show this message and exit.

          python ${{ env.SCRIPT_DIR }}/main.py \
            -i "$tag" ${{ env.ENVIRONMENT }} \
            ${{ env.CONFIG_PATH }} \
            ${{ env.BATCH_NAME }}
