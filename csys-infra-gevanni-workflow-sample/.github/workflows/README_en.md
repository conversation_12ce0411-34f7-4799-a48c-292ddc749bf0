# HowToUse-workflows

This section describes the use of workflow files.

## Summary

- In Gevanni, workflow files are mainly used for communication with AWS.
- Execute a workflow file described in YAML format from GitHubAction to perform operations on AWS in the workflow file.
- OpenID Connect (OIDC) is used to communicate with AWS, and an OIDC-specific IAM role ARN is registered in GitHub Variables to enable operations against AWS.
  ![](./../../docs/images/gevanni-workflow-outline.dio.png)

- The list of workflow files is as follows. The application team clones the required workflow files into its own repository.
- **List of workflow files (updated progressively)**

| Name                | Description                                                                                                             |
| ------------------- | ----------------------------------------------------------------------------------------------------------------------- |
| ecs-deploy-frontend | Executes `build.py` of ecspresso for the frontend and deploys the container.                                            |
| ecs-deploy-backend  | Executes `build.py` of ecspresso for the backend and deploys the container.                                             |

  ※build.py is responsible for the sequence of actions of building the application, pushing it to the ECR and running the pipeline.
    - We need to run vulnerability diagnostics on the built application and are using FutureVuls for vulnerability management.
    - We use build.py to perform the vulnerability assessment and upload the assessment results to FutureVuls.
    - Therefore, it is necessary to register FutureVuls ID information in GitHub Secrets as a preliminary preparation.

## Prerequisite setting.

- The following four steps need to be undertaken in running a workflow file.
  1. Application to use FutureVuls
  1. Customize the workflow file.
  1. Register an OIDC-specific IAM role in GitHubVariables.
  1. Register your FutureVuls ID with GitHubSecrets  
  1. Set environment variables from GitHub Secrets or Google spreadsheet

### i. Application to use FutureVuls

- Apply for the use of FutureVuls within Mynavi in advance for each application to be built.
- Application for a total of six applications is required for each environment (dev, stg, prod) to build front/back-end applications.
- In order to upload the diagnostic results to FutureVuls, the FutureVuls group ID, token, and UUID issued at the time of application are required for each environment.
  - Reference: https://help.vuls.biz/manual/scan/trivy_scan/#cicdパイプラインに組み込む方法
- Since the IDs issued at the time of build are required, you need to register them in the GitHub Secrets of the environment where the workflow will be executed.
  - See [iiii. Register your FutureVuls ID with GitHubSecrets](#iiii-register-your-futurevuls-id-with-githubsecrets) described below for the registration procedure.

### ii. Customizing workflow files

- After cloning the workflow file into the application:s repository, customize it as needed.
- There are two areas to customise.

  1. Selection of manual or automatic execution.
  1. Directory modification of script files.

  #### i. Selection of manual or automatic execution

  - As shown below, the default format is to run the GitHubAction manually, but it can also be run automatically at the stage it is pushed.
  - The automatic execution section is commented out, so comment this in and comment out the manual processing if necessary.

    ```yaml
    on:
      workflow_dispatch:
        inputs:
          environment:
            type: choice
            description: environment
            options:
              - stg
              - dev
              - prod
    env:
      environment: ${{github.event.inputs.environment}}
    # ※In the case of automatic execution (on push)
    #   push:
    #     branches: [ "main" ] # Trigger target branch.
    #     paths: ["frontend/*"] # Trigger target file designation point.
    # env:
    #   environment: 'Selected from <dev/stg/prod>'
    ```

  #### ii. Directory modification of script files.

  - In some cases, operations to AWS are described directly in the workflow file, but in other cases a script file is called. In such cases, check that the path of the script file in which it is placed is correct and correct it if necessary.
  - Example: for ecs-deploy-frontend, the script file is executed in the following path.
    ```yaml
    - name: Run build.py
      run: python ./01_Rolling/build_frontend/build.py ${{env.environment}}
    ```

### iii. Register an OIDC-specific IAM role in GitHubVariables.

- When making a connection at OIDC, perform the following steps and register the relevant role in GithubVariables.

1.  Go to Github and click on "Settings" in the target repository.
    ![](./images/how-to-register-GitHubVariables-1.png)
1.  After clicking on "Environments" in the left pane, click on environment (ex: dev, stg, prod)
    ![](./images/how-to-register-GitHubVariables-2.png)
1.  In "Environment variables" area, click button "Add environment variable"
    ![](./images/how-to-register-GitHubVariables-3.png)
1.  For the "Name" field, enter the value of "role-to-assume" as specified in the workflow file you are using. For the "Value" field, enter the ARN of the IAM role. This infomation will be provided separately by the infrastructure team.

    - Example: for ecs-deploy-frontend.yml, enter "ecs_front" in "Name".
      ```yaml
      with:
        role-to-assume: ${{ vars.ecs_front}}
      ```

    ![](./images/how-to-register-GitHubVariables-4.png)

### iiii. Register your FutureVuls ID with GitHubSecrets

- Group IDs and tokens will be registered with those issued at the time of application for use of FutureVuls within Mynavi.
  - If you proceed with development without having completed the FutureVuls usage application, disable FutureVuls scanning by referring to [FutureVuls Vulnerability Scanning Disable Instructions](#futurevuls-vulnerability-scanning-disable-instructions) described below.
  - **※ Once the application for use is completed and the ID information is paid out, activate it and register the ID information.**
- The registration procedure is as follows.

1. Access Github and click “Settings” of the target repository.  
    ![](./images/how-to-register-GitHubSecrets-1.png)
1. After clicking on "Environments" in the left pane, click on environment (ex: dev, stg, prod)
    ![](./images/how-to-register-GitHubSecrets-2.png)
1. In "Environment secrets" area, click button "Add environment secret"
    ![](./images/how-to-register-GitHubSecrets-3.png)
1. enter the following Secrets name in the `Name` field.  
    ![](./images/how-to-register-GitHubSecrets-4.png)

#### Secrets to register 

- The names of the Secrets that need to be registered are as follows
  - VULS_SAAS_GROUPID_{FRONT|BACK}
    - ID of the group to upload the scan results.
  - VULS_SAAS_TOKEN_{FRONT|BACK}
    - The FutureVuls token with scan permissions.
  - VULS_SAAS_UUUID_{FRONT|BACK}
    - Server UUID in FutureVuls that uniquely identifies the target image.
    - In case of new registration, specify the value generated by uuidgen command etc.

#### FutureVuls Vulnerability Scanning Disable Instructions

- Edit the following file.
  - [ecs-deploy-frontend.yml](/.github/workflows/ecs-deploy-frontend.yml)
  - [ecs-deploy-backend.yml](/.github/workflows/ecs-deploy-backend.yml)
- Change the value of `env` > `enable-futurevuls-scan` in the yaml file to `false`.
  - Set it to `true` to enable it.

**※ After the application is completed and the ID information is paid out, activate it and register Secrets.**

### v. Set environment variables from GitHub Secrets or Google spreadsheet

- You can set environment variables from GitHub Secrets or Google spreadsheet following these steps
  1. Set the IS_USE_ENV_VARS_FROM_SPREADSHEET variable to GitHub Secrets
  1. Set environment variables from GitHub Secrets
  1. Set environment variables from Google spreadsheet

  #### i. Set the IS_USE_ENV_VARS_FROM_SPREADSHEET variable to GitHub Secrets
  - The registration procedure is as follows.
    - Access Github and click `Settings` of the target repository.
    - In the left sidebar, click `Environments`.
    - Click on the environment that you want to add a secret to.
    - Under `Environment secrets`, click `Add environment secret`.
    - Type a name for your secret in the `Name` input box.
    - Enter the value for your secret.
    - Click `Add secret`.

  - Secrets to register
    - `IS_USE_ENV_VARS_FROM_SPREADSHEET`
      - `false`: Set environment variables from GitHub Secrets
      - `true`: Set environment variables from Google spreadsheet

  #### ii. Set environment variables from GitHub Secrets
  - Manually Register the Environment Variables of the App Container in GitHub Secrets
    - Variable naming rules
      - `VARIABLE_NAME`
      - Examples: `API_KEY`

    - Can use the GitHub Console interface
      - Access Github and click `Settings` of the target repository.
      - In the left sidebar, click `Environments`.
      - Click on the environment that you want to add a secret to.
      - Under `Environment secrets`, click `Add environment secret`.
      - Type a name for your secret in the `Name` input box.
      - Enter the value for your secret.
      - Click `Add secret`.
    - **Note**: You need access rights (Admin or Write) for the repository where you want to add secrets.
  - Register the Environment Variables of the App Container in the GitHub Action Workflow File
    - To add environment variables for the App container, you can include them in the `Run frontend build.py` or `Run backend build.py` step in either the `.github/workflows/ecs-deploy-frontend.yml` file or the `.github/workflows/ecs-deploy-backend.yml` file.
    - Add the names of the environment variables to `env_vars_value`, for example:

      ```yml
        env_vars_value="SAMPLE_SECRET_1,SAMPLE_SECRET_2"
      ```
    - Uncomment the following in the workflow

      ```yml
      - name: Run backend build.py
        # env:
        #   SAMPLE_SECRET_1: ${{ secrets.SAMPLE_SECRET_1 }}
        #   SAMPLE_SECRET_2: ${{ secrets.SAMPLE_SECRET_2 }}
      ```

    - Add/edit environment variables from GitHub secrets, for example:

      ```yml
      - name: Run backend build.py
        env:
          SAMPLE_SECRET_1: ${{ secrets.SAMPLE_SECRET_1 }}
          SAMPLE_SECRET_2: ${{ secrets.SAMPLE_SECRET_2 }}
          SAMPLE_SECRET_3: ${{ secrets.SAMPLE_SECRET_3 }}
      ```

    - **Notes on updating existing environment variables**
      - The list of environment variables is completely overwritten by `env_vars_value`.
      - If any environment variables are registered that are not specified in `env_vars_value`, they will be deleted.
      - Example.
        - `env_vars_value="SAMPLE_SECRET_1,SAMPLE_SECRET_2"`
        - Environment variables before modification
          - SAMPLE_SECRET_2
          - SAMPLE_SECRET_3
        - Change details
          - SAMPLE_SECRET_1: Create New
          - SAMPLE_SECRET_2: Overwrite with GitHub Secrets value
          - SAMPLE_SECRET_3: Delete

  #### iii. Set environment variables from Google spreadsheet
  - Create Environment Variables files on Google spreadsheet
    - `Key`: Value of column `A` row n of sheet
    - `Value`: Value of column `B` row n of sheet
    ![](./images/sample-environment-variables-files-on-Google-spreadsheet.png)
    - **Notes on updating existing environment variables**
      - The contents of the Google spreadsheet file completely overwrite the list of environment variables.
      - If any environment variables are registered that are not specified in the Google spreadsheet file, they will be deleted.
      - Example:
        - Environment variables before modification
          - SAMPLE_SECRET_2
          - SAMPLE_SECRET_3
        - Change details on the Google spreadsheet file
          - SAMPLE_SECRET_1: Create New
          - SAMPLE_SECRET_2: Overwrite with GitHub Secrets value
          - SAMPLE_SECRET_3: Delete
  - Register your GOOGLE AUTH INFO, SPREADSHEET ID, SHEET NAME with GitHubSecrets
    - Access Github and click `Settings` of the target repository.
    - In the left sidebar, click `Environments`.
    - Click on the environment that you want to add a secret to.
    - Under `Environment secrets`, click `Add environment secret`.
    - Type a name for your secret in the `Name` input box.
    - Enter the value for your secret.
    - Click `Add secret`.
  - Secrets to register
    - `GOOGLE_AUTH_INFO`
      - Google auth info
      - Refer:
        - [Setting up API keys](https://support.google.com/googleapi/answer/6158862?hl=en)
        - [How to Access Google Sheets Data Using a Service Account](https://genezio.com/blog/google-sheets-as-apis/)
    - `SPREADSHEET_ID`
      - SPREADSHEET ID of Environment Variables files on Google spreadsheet
      - Refer: [Spreadsheet ID in Google Sheets API Overview](https://developers.google.com/sheets/api/guides/concepts#:~:text=unique%20spreadsheetId%20value.-,Spreadsheet%20ID,-The%20unique%20identifier)
    - `SHEET_NAME`
      - Sheet name of  Environment Variables files on Google spreadsheet

## Execution procedure (manual)

- The procedure for manual execution is as follows. For automatic execution, it is executed at the time it is pushed to the branch.

  1. Select the "Actions" tab.
  1. Select the name of the workflow you want to run.
  1. Open the "Run workflow" pull-down.
  1. Select the environment in "environment", press the "Run workflow" button and run it.
     ![](./images/how-to-execute-GitHubActions.png)