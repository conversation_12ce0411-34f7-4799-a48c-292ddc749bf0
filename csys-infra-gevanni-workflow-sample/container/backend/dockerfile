# ベースイメージとしてDebianを使用
FROM --platform=linux/amd64 debian:latest

# パッケージリストの更新と最低限のパッケージインストール
RUN apt update && apt install -y \
    default-mysql-client \
    postgresql-client \
    curl \
    wget \
    vim \
    nginx \
    && apt clean

# Tạo file health check đơn giản
RUN echo '{"status": "healthy", "service": "backend"}' > /var/www/html/health.html
RUN echo '{"status": "ok", "service": "backend"}' > /var/www/html/index.html

# Expose port cho health check
EXPOSE 80

# コンテナ起動時に実行するコマンド - nginx
CMD ["nginx", "-g", "daemon off;"]
