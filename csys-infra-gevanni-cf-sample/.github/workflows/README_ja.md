# HowToUse-workflows

ここでは、ワークフローファイルの利用方法について記載する。

## 概要

- YAML 形式で記載されたワークフローファイルを GitHubAction から実行し、ワークフローファイル内で AWS に対する操作を実施する。
- AWS との疎通は OpenID Connect（OIDC）を利用しており、OIDC 専用の IAM ロール ARN を GitHubVariables に登録することで AWS に対する操作を可能にしている。

- workflow ファイルの一覧は以下のとおりである。アプリケーションチームは必要なワークフローファイルを自身のリポジトリにクローンして利用する。
- **workflow ファイル一覧（順次更新）**

  | 名称            | 説明                                                                                          |
  | --------------- | --------------------------------------------------------------------------------------------- |
  | maintenance-on  | CloudFront 用 WAF の IP 制限と Basic 認証ルールを有効化し、一般ユーザのアクセスを遮断する。   |
  | maintenance-off | CloudFront 用 WAF の IP 制限と Basic 認証ルールを無効化するし、一般ユーザにサイトを公開する。 |

## 事前に必要な設定

- ワークフローファイルを実行する上で、以下手順を実施する必要がある。
  1. OIDC 専用の IAM ロールを GitHubVariables に登録

### i. OIDC 専用の IAM ロールを GitHubVariables に登録

- OIDC にて接続を行う際は下記の手順を実行し、該当のロールを GithubVariables に登録する。

1.  Github にアクセスし、対象リポジトリの「Settings」をクリックする。
    ![](./../../docs/images/how-to-register-GitHubVariables-1.png)
1.  左ペインにある「Environments」をクリックしたら、環境（例：dev、stg、prod）をクリック。
    ![](./../../docs/images/how-to-register-GitHubVariables-2.png)
1.  「Environment variables」エリアで「Add environment variable」ボタンをクリック。
    ![](./../../docs/images/how-to-register-GitHubVariables-3.png)
1.  `Name`には利用するワークフローファイル内に記載されている`role-to-assume`の値を記入する。`Value`には IAM ロールの ARN を記入する。こちらについては、インフラチームより別途連携。

    - 例）ecs-deploy-frontend.yml の場合は`ecs_front`を`Name`に記入
      ```yaml
      with:
        role-to-assume: ${{ vars.ecs_front}}
      ``
      ```

    ![](./../../docs/images/how-to-register-GitHubVariables-4.png)
