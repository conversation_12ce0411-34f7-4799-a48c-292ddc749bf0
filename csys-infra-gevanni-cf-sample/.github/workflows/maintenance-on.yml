name: メンテナンス画面表示

on:
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    # Production environment only
    environment: prod

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.maintenance }}
          role-session-name: GitHubActions
          aws-region: us-east-1

      - name: update WebACL
        run: |

          # Get WebACL ID
            WEB_ACL_ID=`aws wafv2 list-web-acls \
              --scope=CLOUDFRONT \
              --region=us-east-1 \
              --query "WebACLs[?Name=='${{ vars.WebACL_NAME }}'].Id" \
              --output text`

          # Get WebACL token
            WEB_ACL_TOKEN=`aws wafv2 list-web-acls \
              --scope=CLOUDFRONT \
              --region=us-east-1 \
              --query "WebACLs[?Name=='${{ vars.WebACL_NAME }}'].LockToken" \
              --output text`

          # Change IPset rule action to Block
            aws wafv2 get-web-acl \
              --name ${{ vars.WebACL_NAME }} \
              --scope=CLOUDFRONT \
              --region=us-east-1 \
              --id $WEB_ACL_ID | jq '.WebACL.Rules[] |= if .Name == "IPset" then .Action |= {"Block": {}} else . end' > webacl-rules-update-IPset.json

          # Change BasicAuth rule action to Block
            cat webacl-rules-update-IPset.json | jq '.WebACL.Rules[] |= if .Name == "BasicAuth" then .Action |= { "Block": { "CustomResponse": { "ResponseCode": 401, "ResponseHeaders": [ { "Name": "www-authenticate", "Value": "Basic" } ] } } } else . end' | jq .WebACL.Rules > webacl-rules-update.json

          # Update WebACL
            aws wafv2 update-web-acl \
              --name ${{ vars.WebACL_NAME }} \
              --scope=CLOUDFRONT \
              --region=us-east-1 \
              --id $WEB_ACL_ID \
              --lock-token $WEB_ACL_TOKEN \
              --visibility-config SampledRequestsEnabled=true,CloudWatchMetricsEnabled=true,MetricName=BLEAWebACL \
              --default-action Allow={} \
              --rules file://webacl-rules-update.json
