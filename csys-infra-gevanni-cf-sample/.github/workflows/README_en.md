# HowToUse-workflows

This section describes the use of workflow files.

## Summary

- Execute a workflow file described in YAML format from GitHubAction to perform operations on AWS in the workflow file.
- OpenID Connect (OIDC) is used to communicate with AWS, and an OIDC-specific IAM role ARN is registered in GitHub Variables to enable operations against AWS.
  ![](./../../docs/images/gevanni-workflow-outline.dio.png)

- The list of workflow files is as follows. The application team clones the required workflow files into its own repository.
- **List of workflow files (updated progressively)**

| Name            | Description                                                                                                             |
| --------------- | ----------------------------------------------------------------------------------------------------------------------- |
| maintenance-on  | Enables IP restriction and Basic Authentication rules for CloudFront WAF, blocking access for general users.            |
| maintenance-off | Disables IP restriction and Basic Authentication rules for CloudFront WAF, making the site accessible to general users. |

## Prerequisite setting.

- The following four steps need to be undertaken in running a workflow file.
  1. Register an OIDC-specific IAM role in GitHubVariables.

### iii. Register an OIDC-specific IAM role in GitHubVariables.

- When making a connection at OIDC, perform the following steps and register the relevant role in GithubVariables.

1.  Go to Github and click on "Settings" in the target repository.
    ![](./images/how-to-register-GitHubVariables-1.png)
1.  After clicking on "Environments" in the left pane, click on environment (ex: dev, stg, prod)
    ![](./images/how-to-register-GitHubVariables-2.png)
1.  In "Environment variables" area, click button "Add environment variable"
    ![](./images/how-to-register-GitHubVariables-3.png)
1.  For the "Name" field, enter the value of "role-to-assume" as specified in the workflow file you are using. For the "Value" field, enter the ARN of the IAM role. This infomation will be provided separately by the infrastructure team.

    - Example: for ecs-deploy-frontend.yml, enter "ecs_front" in "Name".
      ```yaml
      with:
        role-to-assume: ${{ vars.ecs_front}}
      ```

    ![](./images/how-to-register-GitHubVariables-4.png)
