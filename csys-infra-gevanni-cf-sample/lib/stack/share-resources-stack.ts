import { Construct } from 'constructs';
import { StringParameter } from 'aws-cdk-lib/aws-ssm';
import { LambdaLayer } from '../construct/lambda-layer-construct';
import { Stack, StackProps } from 'aws-cdk-lib';

export interface ShareResourcesStackProps extends StackProps {
  pjPrefix: string;
}

/**
 * ShareResourcesStack is a CDK stack that creates shared resources for the project.
 * It includes a Lambda layer for New Relic.
 * The stack is designed to be reusable and can be used in multiple environments.
 *
 * @extends Stack
 * @param scope - The scope in which this stack is defined.
 * @param id - The unique identifier for this stack.
 * @param props - The properties for configuring the stack.
 * @param props.pjPrefix - The prefix used for naming resources in this stack.
 * 
 * @property {string} pjPrefix - The prefix used for naming resources in this stack.
 * @property {string} env - The environment in which this stack is deployed.
 * 
 * @example
 * 
*/

export class ShareResourcesStack extends Stack {
  constructor(scope: Construct, id: string, props: ShareResourcesStackProps) {
    super(scope, id, props);

    // Commonly used LambdaLayer
    const lambdaLayer = new LambdaLayer(this, 'LambdaLayer', {
      pjPrefix: props.pjPrefix,
    });

    new StringParameter(this, 'SSMNewRelicLayerArn', {
      parameterName: `/${props.pjPrefix}/NewRelicLayerArn`,
      stringValue: lambdaLayer.nrLayer.layerVersionArn,
    });
  }
}
