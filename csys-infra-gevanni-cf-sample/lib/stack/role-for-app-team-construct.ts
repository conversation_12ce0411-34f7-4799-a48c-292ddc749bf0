import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';

export interface RoleForAppTeamProps extends cdk.StackProps {
  pjPrefix: string;
  allowedRoles: string[];
}

export class RoleForAppTeam extends cdk.Stack {
  constructor(scope: Construct, id: string, props: RoleForAppTeamProps) {
    super(scope, id, props);

    // Create IAM Role that allows assuming from other accounts
    const role = new iam.Role(this, `${props.pjPrefix}-RoleForAppTeam`, {
      assumedBy: new iam.PrincipalWithConditions(
        new iam.CompositePrincipal(...props.allowedRoles.map((roleArn) => new iam.ArnPrincipal(roleArn))),
        {},
      ),
      roleName: `${props.pjPrefix}-RoleForAppTeam`,
    });

    // Assign SNS access to send SMS
    role.addToPolicy(
      new iam.PolicyStatement({
        actions: ['sns:Publish'],
        resources: ['*'],
      }),
    );

    new cdk.CfnOutput(this, `${props.pjPrefix}-RoleForAppTeamArn`, {
      key: 'RoleForAppTeamArn',
      value: role.roleArn,
      exportName: `${props.pjPrefix}-RoleForAppTeamArn`,
    });
  }
}
