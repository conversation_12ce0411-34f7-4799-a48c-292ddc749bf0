import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { IRemovalPolicyParam } from '../../params/interface';

export interface TidbBackupStackProps extends cdk.StackProps {
  pjPrefix: string;
  tidbCloudAccountId: string;
  tidbCloudExternalId: string;
  backupBucketRemovalPolicy?: IRemovalPolicyParam;
}

export class TidbBackupStack extends cdk.Stack {
  public readonly bucket: s3.Bucket;
  public readonly role: iam.Role;

  constructor(scope: Construct, id: string, props: TidbBackupStackProps) {
    super(scope, id, props);

    const bucket = new s3.Bucket(this, 'TidbBackupBucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      encryption: s3.BucketEncryption.S3_MANAGED,
      autoDeleteObjects: props.backupBucketRemovalPolicy?.autoDeleteObjects ?? false,
      removalPolicy: props.backupBucketRemovalPolicy?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
    });
    this.bucket = bucket;

    const role = new iam.Role(this, 'TidbBackupRole', {
      assumedBy: new iam.AccountPrincipal(props.tidbCloudAccountId).withConditions({
        StringEquals: { 'sts:ExternalId': props.tidbCloudExternalId },
      }),
      inlinePolicies: {
        exportPolicies: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['s3:PutObject'],
              resources: [`${bucket.bucketArn}/*`],
            }),
            new iam.PolicyStatement({
              actions: ['s3:ListBucket'],
              resources: [bucket.bucketArn],
            }),
          ],
        }),
        importPolicies: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              actions: ['s3:GetObjectVersion', 's3:GetObject'],
              resources: [`${bucket.bucketArn}/*`],
            }),
            new iam.PolicyStatement({
              actions: ['s3:ListBucket'],
              resources: [bucket.bucketArn],
            }),
          ],
        }),
      },
    });
    this.role = role;

    new cdk.CfnOutput(this, 'TidbBackupRoleArn', {
      value: role.roleArn,
    });

    new cdk.CfnOutput(this, 'TidbBackupBucketUri', {
      value: `s3://${bucket.bucketName}`,
    });
  }
}
