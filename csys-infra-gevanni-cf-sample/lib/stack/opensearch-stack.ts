import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { aws_opensearchserverless as opensearchserverless } from 'aws-cdk-lib';
import { Role, ServicePrincipal, ManagedPolicy } from 'aws-cdk-lib/aws-iam';
import { CfnSecurityPolicy, CfnAccessPolicy, CfnCollection } from 'aws-cdk-lib/aws-opensearchserverless';
import { Construct } from 'constructs';
import * as iam from 'aws-cdk-lib/aws-iam';

export interface OpenSearchStackProps extends cdk.StackProps {
  pjPrefix: string;
  vpceIds: string[];
  collectionName: string;
  connectAccountId: string;
  type: string;
}

export class OpenSearchStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: OpenSearchStackProps) {
    super(scope, id, props);

    // Create Opensearch serverless

    const collection = new CfnCollection(this, 'OpenSearchCollection', {
      name: props?.collectionName,
      type: props?.type,
    });

    // Encryption policy is needed in order for the collection to be created
    const encPolicy = new opensearchserverless.CfnSecurityPolicy(this, `${props.pjPrefix}SecurityPolicy`, {
      name: 'opensearch-collection-policy',
      policy: `{"Rules":[{"ResourceType":"collection","Resource":["collection/${props?.collectionName}"]}],"AWSOwnedKey":true}`,
      type: 'encryption',
    });
    collection.addDependency(encPolicy);

    // Create network policy for opensearch

    const networkPolicy = new CfnSecurityPolicy(this, 'OpenSearchNetworkPolicy', {
      name: 'opensearch-network-policy',
      policy: JSON.stringify([
        {
          Rules: [
            {
              Resource: [`collection/${props?.collectionName}`],
              ResourceType: 'collection',
            },
          ],
          AllowFromPublic: false,
          SourceVPCEs: props.vpceIds,
        },
      ]),
      type: 'network',
    });
    collection.addDependency(networkPolicy);

    // Create data access for opensearch

    // Create Opensearch Role
    const opensearchRole = new Role(this, 'OpenSearchRole', {
      roleName: `${props.pjPrefix.toLowerCase()}-RoleForOpenSearchRole`,
      assumedBy: new iam.AccountPrincipal(props?.connectAccountId),
    });

    const dataAccessPolicy = new CfnAccessPolicy(this, 'OpenSearchDataAccessPolicy', {
      name: 'opensearch-data-access-policy',
      policy: JSON.stringify([
        {
          Rules: [
            {
              Resource: [`collection/${props?.collectionName}`],
              Permission: [
                'aoss:CreateCollectionItems',
                'aoss:DeleteCollectionItems',
                'aoss:UpdateCollectionItems',
                'aoss:DescribeCollectionItems',
              ],
              ResourceType: 'collection',
            },
            {
              Resource: [`index/${props?.collectionName}/*`],
              Permission: [
                'aoss:CreateIndex',
                'aoss:DeleteIndex',
                'aoss:UpdateIndex',
                'aoss:DescribeIndex',
                'aoss:ReadDocument',
                'aoss:WriteDocument',
              ],
              ResourceType: 'index',
            },
          ],
          Principal: [opensearchRole.roleArn],
          Description: 'Full access to data',
        },
      ]),
      type: 'data',
    });
    collection.addDependency(dataAccessPolicy);

    new cdk.CfnOutput(this, 'CollectionEndpoint', {
      key: 'CollectionEndpoint',
      value: collection.attrCollectionEndpoint,
      exportName: `${props.pjPrefix}-CollectionEndpoint`,
    });
  }
}
