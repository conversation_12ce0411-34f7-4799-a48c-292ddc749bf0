import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Dashboard } from '../construct/dashboard-construct';

interface MonitorStackProps extends cdk.StackProps {
  pjPrefix: string;
  dashboardName: string;
  cfDistributionId: string;
}

export class MonitorStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: MonitorStackProps) {
    super(scope, id, props);

    // Dashboard
    new Dashboard(this, `${props.pjPrefix}-Dashboard`, {
      dashboardName: props.dashboardName,
      cfDistributionId: props.cfDistributionId,
    });
  }
}
