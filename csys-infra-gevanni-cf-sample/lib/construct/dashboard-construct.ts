import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { aws_cloudwatch as cw } from 'aws-cdk-lib';

interface DashboardProps {
  /**
   * Cloudwatch dashboard name
   */
  dashboardName: string;
  /**
   * Cloudfront Distribution ID
   */
  cfDistributionId: string;
}

export class Dashboard extends Construct {
  constructor(scope: Construct, id: string, props: DashboardProps) {
    super(scope, id);

    /*
     *
     * Metrics definition
     * Note: These definitions do not create any resource. Just dashboard widget refer to these metrics.
     *
     */

    // CloudFront
    // Available metrics: https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/programming-cloudwatch-metrics.html
    const cfRequests = new cw.Metric({
      namespace: 'AWS/CloudFront',
      metricName: 'Requests',
      dimensionsMap: {
        Region: 'Global',
        DistributionId: props.cfDistributionId,
      },
      period: cdk.Duration.minutes(1),
      statistic: cw.Stats.SUM,
      label: "${PROP('MetricName')} /${PROP('Period')}sec",
      unit: cw.Unit.NONE,
      region: 'us-east-1', // cloudfront defined on us-east-1
    });
    const cf5xxErrorRate = new cw.Metric({
      namespace: 'AWS/CloudFront',
      metricName: '5xxErrorRate',
      dimensionsMap: {
        Region: 'Global',
        DistributionId: props.cfDistributionId,
      },
      period: cdk.Duration.minutes(1),
      statistic: cw.Stats.AVERAGE,
      label: "${PROP('MetricName')} /${PROP('Period')}sec",
      unit: cw.Unit.PERCENT,
      region: 'us-east-1', // cloudfront defined on us-east-1
    });
    const cf4xxErrorRate = new cw.Metric({
      namespace: 'AWS/CloudFront',
      metricName: '4xxErrorRate',
      dimensionsMap: {
        Region: 'Global',
        DistributionId: props.cfDistributionId,
      },
      period: cdk.Duration.minutes(1),
      statistic: cw.Stats.AVERAGE,
      label: "${PROP('MetricName')} /${PROP('Period')}sec",
      unit: cw.Unit.PERCENT,
      region: 'us-east-1', // cloudfront defined on us-east-1
    });
    new cw.Metric({
      namespace: 'AWS/CloudFront',
      metricName: 'TotalErrorRate',
      dimensionsMap: {
        Region: 'Global',
        DistributionId: props.cfDistributionId,
      },
      period: cdk.Duration.minutes(1),
      statistic: cw.Stats.AVERAGE,
      label: "${PROP('MetricName')} /${PROP('Period')}sec",
      unit: cw.Unit.PERCENT,
      region: 'us-east-1', // cloudfront defined on us-east-1
    });

    /*
     *
     * Dashboard definition
     *
     * Note:
     * - This sample summarize widgets in metrics group such as Requests, ResponseTime, Errors, Resources.
     *   We added header text widget on top of each metrics group.
     * - If you use the name CloudWatch-Default, the dashboard appears on the overview on the CloudWatch home page.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/create_dashboard.html
     *
     * - Widget Array Structure (height, width, x, y)
     *   width=24 means Full screen width. This sample is define widget height as 6.
     *   You can just add widgets in array, x and y axis are defined well by CDK.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/APIReference/CloudWatch-Dashboard-Body-Structure.html#CloudWatch-Dashboard-Properties-Widgets-Structure
     *
     * - "stacked: true," means stack(add) each metrics.
     *
     * - Label for each metrics is defined on metrics object and you can use "Dynamic Label".
     *   We usually use "${PROP('MetricName')} /${PROP('Period')}sec" so we can see period of the metrics.
     *   See: https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/graph-dynamic-labels.html
     *
     */

    const dashboard = new cw.Dashboard(this, 'Dashboard', {
      dashboardName: props.dashboardName,
    });

    dashboard.addWidgets(
      // Requests
      new cw.TextWidget({
        markdown: '# Requests',
        height: 1,
        width: 24,
      }),
      new cw.GraphWidget({
        title: 'CloudFront Requests',
        width: 6,
        height: 24,
        stacked: false,
        left: [cfRequests],
      }),

      // Errors
      new cw.TextWidget({
        markdown: '# Errors',
        height: 1,
        width: 24,
      }),
      new cw.GraphWidget({
        title: 'CloudFront Error Rates',
        width: 6,
        height: 24,
        // stacked: false,
        // left: [cf5xxErrorRate, cf4xxErrorRate, cfTotalErrorRate],
        stacked: true,
        left: [cf5xxErrorRate, cf4xxErrorRate],
      }),
    );
  }
}
