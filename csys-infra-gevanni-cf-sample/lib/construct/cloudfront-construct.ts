import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { ICertificateIdentifier, ICloudFrontParam, IAppAlbsParam } from '../../params/interface';

interface CloudFrontConstructProps extends cdk.StackProps {
  /**
   * Whether to integrate with Web ACL
   *
   * @default - do not integrate with Web ACL
   */
  webAcl?: wafv2.CfnWebACL;
  /**
   * Parameters for Cloudfront
   */
  cloudFrontParam: ICloudFrontParam;
  /**
   * Certificate Identifier created for Cloudfront
   *
   * @example - '562acf37-1fd4-4d34-8e9e-035da510c3db'
   */
  CertificateIdentifier: ICertificateIdentifier;
  /**
   * Application Load Balance Prameter
   *
   */
  appAlbsParam: IAppAlbsParam;
  /**
   * Lifecycle rules of cloudfront access log bucket
   */
  myAccessLogBucketLifecycleRule: s3.LifecycleRule[];
  /**
   * Log removal policy parameter
   */
  logRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  /**
   * Web Content Bucket removal policy parameter
   */
  webContentBucketRemovalPolicyParam?: {
    removalPolicy: cdk.RemovalPolicy;
    autoDeleteObjects: boolean;
  };
  /**
   * Content web bucket version lifecycle
   *
   * @default - 14 days
   */
  webContentVersionExpiration?: cdk.Duration;
}

export class CloudFrontConstruct extends Construct {
  public readonly cfDistributionId: string;
  public readonly cfDistribution: cloudfront.Distribution;
  public readonly logBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props: CloudFrontConstructProps) {
    super(scope, id);

    //Check if a certificate is specified
    const hasValidAlbCert = props.CertificateIdentifier.identifier !== '';
    //Check if a FQDN is specified
    const hasValidFqdn = props.cloudFrontParam.fqdn !== '';
    //Flag SSL enabled/disabled
    const sslFlag = hasValidAlbCert && hasValidFqdn;

    // ------------------------------------------------------------------------
    // Certificates
    //
    // Note:  CloudFront and ALB need certificate with the same FQDN

    // for cloudfront (us-east-1 Cert)
    const cfCertificateArn = `arn:aws:acm:us-east-1:${cdk.Stack.of(this).account}:certificate/${
      props.CertificateIdentifier.identifier
    }`;
    const cloudfrontCert = acm.Certificate.fromCertificateArn(this, 'cfCertificate', cfCertificateArn);

    // ------------ S3 Bucket for Web Contents ---------------
    // This bucket cannot be encrypted with KMS CMK
    // See: https://aws.amazon.com/premiumsupport/knowledge-center/s3-website-cloudfront-error-403/
    //

    const s3AccessLogsBucket = new s3.Bucket(this, 's3AccessLogsBucket', {
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
      enforceSSL: true,
      lifecycleRules: props.myAccessLogBucketLifecycleRule,
    });

    const webContentBucket = new s3.Bucket(this, 'WebBucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      removalPolicy: props.webContentBucketRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.webContentBucketRemovalPolicyParam?.autoDeleteObjects ?? false,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      enforceSSL: true,
      serverAccessLogsBucket: s3AccessLogsBucket,
      serverAccessLogsPrefix: 'webContentBucket/',
      lifecycleRules: [
        {
          noncurrentVersionExpiration: props.webContentVersionExpiration ?? cdk.Duration.days(14),
        },
        {
          abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
        },
      ],
    });

    const closedBucket = new s3.Bucket(this, 'ClosedBucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      enforceSSL: true,
      serverAccessLogsBucket: s3AccessLogsBucket,
      serverAccessLogsPrefix: 'closedBucket/',
      lifecycleRules: [
        {
          noncurrentVersionExpiration: props.webContentVersionExpiration ?? cdk.Duration.days(14),
        },
        {
          abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
        },
      ],
    });

    const backendBucket = new s3.Bucket(this, 'BackendBucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
      versioned: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      enforceSSL: true,
      serverAccessLogsBucket: s3AccessLogsBucket,
      serverAccessLogsPrefix: 'backendBucket/',
      lifecycleRules: [
        {
          noncurrentVersionExpiration: props.webContentVersionExpiration ?? cdk.Duration.days(14),
        },
        {
          abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
        },
      ],
    });

    // --------- S3 Bucket for CloudFront Logs ---------------
    this.logBucket = new s3.Bucket(this, 'CloudFrontLogBucket', {
      accessControl: s3.BucketAccessControl.PRIVATE,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      removalPolicy: props.logRemovalPolicyParam?.removalPolicy ?? cdk.RemovalPolicy.RETAIN,
      autoDeleteObjects: props.logRemovalPolicyParam?.autoDeleteObjects ?? false,
      enforceSSL: true,
      objectOwnership: s3.ObjectOwnership.OBJECT_WRITER,
    });

    // --------- CloudFront Distrubution
    if (props.cloudFrontParam.createClosedBucket) {
      this.cfDistribution = new cloudfront.Distribution(this, 'Distribution', {
        defaultBehavior: {
          origin: origins.S3BucketOrigin.withOriginAccessControl(closedBucket),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        },

        // Domain and SSL Certificate
        ...(sslFlag
          ? {
              domainNames: [props.cloudFrontParam.fqdn],
              certificate: cloudfrontCert,
            }
          : {}),

        // logging
        enableLogging: true,
        logBucket: this.logBucket,
        logIncludesCookies: true,
        logFilePrefix: 'CloudFrontAccessLogs/',
      });
      this.cfDistributionId = this.cfDistribution.distributionId;
    } else {
      this.cfDistribution = new cloudfront.Distribution(this, 'Distribution', {
        defaultBehavior: {
          origin: new origins.HttpOrigin(props.appAlbsParam.appAlbDomains[0], {
            protocolPolicy: sslFlag
              ? cloudfront.OriginProtocolPolicy.HTTPS_ONLY
              : cloudfront.OriginProtocolPolicy.HTTP_ONLY,
            customHeaders: props.appAlbsParam.preSharedKey
              ? {
                  'x-pre-shared-key': props.appAlbsParam.preSharedKey,
                }
              : undefined,
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER,
        },
        additionalBehaviors: {
          '/static/*': {
            origin: origins.S3BucketOrigin.withOriginAccessControl(webContentBucket),
            viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
            cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
          },
        },
        errorResponses: [
          {
            httpStatus: 403,
            responseHttpStatus: 403,
            responsePagePath: '/static/sorry.html',
            ttl: cdk.Duration.seconds(20),
          },
        ],
        defaultRootObject: '/', // Need for SecurityHub Findings CloudFront.1 compliant

        // Domain and SSL Certificate
        ...(sslFlag
          ? {
              domainNames: [props.cloudFrontParam.fqdn],
              certificate: cloudfrontCert,
            }
          : {}),

        // WAF defined on us-east-1
        webAclId: props.webAcl?.attrArn,

        // logging
        enableLogging: true,
        logBucket: this.logBucket,
        logIncludesCookies: true,
        logFilePrefix: 'CloudFrontAccessLogs/',
      });
      this.cfDistributionId = this.cfDistribution.distributionId;
    }
  }
}
