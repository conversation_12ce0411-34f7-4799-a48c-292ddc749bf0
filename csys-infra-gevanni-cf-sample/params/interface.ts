import * as cdk from 'aws-cdk-lib';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { aws_s3 as s3 } from 'aws-cdk-lib';

export interface IWafParam {
  defaultAction?: wafv2.CfnWebACL.DefaultActionProperty;
  basicAuthUserName?: string;
  basicAuthUserPass?: string;
  overrideAction_CommonRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_KnownBadInputsRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_AmazonIpReputationList?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_LinuxRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_SQLiRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  overrideAction_CSCRuleSet?: wafv2.CfnWebACL.OverrideActionProperty;
  ruleAction_IPsetRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  ruleAction_BasicRuleSet?: wafv2.CfnWebACL.RuleActionProperty;
  allowIPList?: string[];
  preSharedKey?: string;
}
export interface IOidcParam {
  OrganizationName: string;
  RepositoryNames: Record<string, string>;
}

export interface ICertificateIdentifier {
  identifier: string;
}

export interface ICloudFrontParam {
  fqdn: string;
  createClosedBucket: boolean;
}

export interface IEnv {
  envName: string;
  account?: string;
  region?: string;
}

export interface IRemovalPolicyParam {
  removalPolicy: cdk.RemovalPolicy;
  autoDeleteObjects: boolean;
  emptyOnDelete: boolean;
}

export const DestroyRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.DESTROY,
  autoDeleteObjects: true,
  emptyOnDelete: true,
};

export const RetainRemovalPolicyParam: IRemovalPolicyParam = {
  removalPolicy: cdk.RemovalPolicy.RETAIN,
  autoDeleteObjects: false,
  emptyOnDelete: false,
};

export interface logToNewRelicSettings {
  api_key_name: string,
  path_pattern: string,
}

export interface IAppAlbsParam {
  appAlbDomains: string[];
  preSharedKey?: string;
}

export interface ICloudfrontLogToNewRelicSetting {
  isCreate?: boolean;
  logToNewRelicSettings?: logToNewRelicSettings[];
  newrelicSecretArn: string;
}

export interface IWAFLogToNewRelicSetting {
  isCreate?: boolean;
  newrelicSecretArn: string;
}

export interface RoleForAppTeamParam {
  allowedRoles: string[];
}

export interface IOpenSearchParam {
  vpceIds: string[];
  collectionName: string;
  connectAccountId: string;
  type: 'SEARCH' | 'TIMESERIES' | 'VECTORSEARCH';
}

export interface ICSIRTWAFParam {
  isUseCSIRTManageRules: boolean;
  CSIRTManagerRules: {
    overrideAction: wafv2.CfnWebACL.OverrideActionProperty;
    ruleGroupArn: string;
  };
  CSIRTIpSetArn: string;
}

export interface ITidbBackupParam {
  tidbCloudAccountId: string;
  tidbCloudExternalId: string;
}

export interface IConfig {
  WafParam: IWafParam;
  WafAlbParam: IWafParam;
  OidcParam: IOidcParam;
  CertificateIdentifier: ICertificateIdentifier;
  s3AuditLogLifecycleRules: s3.LifecycleRule[];
  CloudFrontParam: ICloudFrontParam;
  Env: IEnv;
  LogRemovalPolicyParam?: IRemovalPolicyParam; // For LogGroup and S3 for log
  OtherRemovalPolicyParam?: IRemovalPolicyParam; // For ECR, Pipeline, etc.
  appAlbsParam: IAppAlbsParam;
  webContentVersionExpiration: cdk.Duration;
  roleForAppTeamParam?: RoleForAppTeamParam;
  openSearchParam?: IOpenSearchParam;
  CSIRTWAFParamCF?: ICSIRTWAFParam;
  TidbBackupParam: ITidbBackupParam;
  cloudfrontLogToNewRelicSetting: ICloudfrontLogToNewRelicSetting;
  wafLogToNewRelicSetting: IWAFLogToNewRelicSetting;
}