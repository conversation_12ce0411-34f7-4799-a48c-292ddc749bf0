import * as cdk from 'aws-cdk-lib';
import * as inf from './interface';
import { aws_s3 as s3 } from 'aws-cdk-lib';

export const WafParam: inf.IWafParam = {
  basicAuthUserName: 'admin',
  basicAuthUserPass: 'pass',
  overrideAction_CommonRuleSet: { count: {} },
  overrideAction_KnownBadInputsRuleSet: { count: {} },
  overrideAction_AmazonIpReputationList: { count: {} },
  overrideAction_LinuxRuleSet: { count: {} },
  overrideAction_SQLiRuleSet: { count: {} },
  overrideAction_CSCRuleSet: { count: {} },
  ruleAction_IPsetRuleSet: { allow: {} },
  ruleAction_BasicRuleSet: {
    block: {
      customResponse: {
        responseCode: 401,
        responseHeaders: [
          {
            name: 'www-authenticate',
            value: 'Basic',
          },
        ],
      },
    },
  },
  allowIPList: ['***************/25'],
};

export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'OrganizationName',
  RepositoryNames: { WafRepositoryName: 'WafRepositoryName', InfraRepositoryName: 'InfraRepositoryName' },
};

//Used when creating front-end stacks
export const CertificateIdentifier: inf.ICertificateIdentifier = {
  identifier: '',
};

export const s3AuditLogLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(1825), //Store for 5 years
  },
  {
    abortIncompleteMultipartUploadAfter: cdk.Duration.days(30), //delete incomplete multipart uploads after 30 days
  },
];

export const CloudFrontParam: inf.ICloudFrontParam = {
  fqdn: '',
  createClosedBucket: false,
};

export const Env: inf.IEnv = {
  envName: 'Stg',
  account: '************',
  region: 'ap-northeast-1',
};

export const LogRemovalPolicyParam = inf.RetainRemovalPolicyParam;
export const OtherRemovalPolicyParam = inf.RetainRemovalPolicyParam;

export const appAlbsParam: inf.IAppAlbsParam = {
  appAlbDomains: ['www.example.com'],
  preSharedKey: 'pre-string-for-preSharedKey',
};

export const webContentVersionExpiration: cdk.Duration = cdk.Duration.days(14);

export const roleForAppTeamParam: inf.RoleForAppTeamParam = {
  allowedRoles: ['role-arn-sample'],
};

export const openSearchParam: inf.IOpenSearchParam = {
  vpceIds: ['vpce-sample'], // Fill in vpc endpoint id
  collectionName: 'gevanni', // Fill name of opensearch serverless
  connectAccountId: 'accountid-sample', // Fill aws account id of ecs task
  type: 'SEARCH',
};

export const CSIRTWAFParamCF: inf.ICSIRTWAFParam = {
  isUseCSIRTManageRules: true,
  CSIRTManagerRules: {
    overrideAction: { none: {} },
    ruleGroupArn: 'arn:aws:wafv2:us-east-1:************:global/rulegroup/CSIRTManagerRules/XXXX',
  },
  CSIRTIpSetArn: 'arn:aws:wafv2:us-east-1:************:global/ipset/CSIRTIpSet/XXXX',
};

export const TidbBackupParam: inf.ITidbBackupParam = {
  tidbCloudAccountId: '',
  tidbCloudExternalId: '',
};


export const cloudfrontLogToNewRelicSetting: inf.ICloudfrontLogToNewRelicSetting = {
  isCreate: false,
  newrelicSecretArn: 'arn:aws:secretsmanager:ap-northeast-1:***************:secret:xxx/xxx',
  logToNewRelicSettings: [
    {
      api_key_name: 'api_key', // key name in SecretManager
      path_pattern: '' // leave blank to use default path pattern
    },
    {
      api_key_name: 'api_key_team_a', // key name in SecretManager
      path_pattern: '/static'
    }
  ]
};

export const wafLogToNewRelicSetting: inf.IWAFLogToNewRelicSetting = {
  isCreate: false,
  newrelicSecretArn: 'arn:aws:secretsmanager:ap-northeast-1:***************:secret:xxx/xxx',
};