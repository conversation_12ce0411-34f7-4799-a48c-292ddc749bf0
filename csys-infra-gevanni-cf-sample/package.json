{"private": true, "name": "gevanni-cf-sample", "version": "1.0.0", "description": "<PERSON><PERSON> genvanni <PERSON>", "license": "MIT-0", "bin": {"gevanni-cf-sample": "bin/gevanni-cf-sample.js"}, "scripts": {"synth_dev_context_test": "npx cdk synth -c", "depcheck": "npx depcheck --ignore-dirs cdk.out", "build": "tsc --build", "clean": "tsc --build --clean && rm -rf cdk.out", "watch": "tsc -w", "test": "jest", "lint": "eslint --fix .", "format": "prettier --write ."}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/jest": "^27.5.2", "@types/node": "18.7.14", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.0", "aws-cdk": "^2.115.0", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "jest": "^27.5.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "simple-git-hooks": "^2.8.0", "ts-jest": "^27.1.5", "ts-node": "^10.9.1", "typescript": "~4.8.4"}, "dependencies": {"@aws-cdk/aws-synthetics-alpha": "^2.38.1-alpha.0", "aws-cdk-lib": "^2.181.0", "cdk": "^2.181.0", "constructs": "^10.1.137", "crypto-js": "^4.1.1"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": ["git-secrets --scan"], "*.(ts|tsx|js|jsx)": ["npx eslint --fix"], "*.(ts|tsx|js|jsx|json|html|yml|yaml|md|graphql|css|scss|less|vue|flow)": ["npx prettier --write"]}}