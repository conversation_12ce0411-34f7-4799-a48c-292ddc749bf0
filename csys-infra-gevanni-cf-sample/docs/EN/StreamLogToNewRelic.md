# Stream cloudfront log to New Relic

- tất cả nội dung log truy cập tới cloudfront được lưu xuống s3 cần đồng bộ nội dung log sang 3rd-party New Relic
- tất cả nội dung log của WAF được lưu xuống s3 cần đồng bộ nội dung log sang 3rd-party New Relic

## Content

- Đồng bộ log cho mỗi origin (ALB end-point) tới từng New Relic account tương ứng
- Đồng bộ log WAF tới New Relic chỉ định

## Workflow Contents

### Đồng bộ log WAF
  - nội dung triển khai như bộ infra của Gevanni, stream all nội dung ở S3 log tới New Relic chỉ đinh

### Đồng bộ log Cloudfront cho từng origin
  #### Option 1: đọc nội dung log và đưa ra quyết định để gửi tới account NewRelic nào
  - nội dung triển khai như bộ infra củ<PERSON>, stream all nội dung tới New Relic chỉ đinh
    - load tất cả NewRelic api_key tương ứng từng path
    - update logic lambda kiểm tra path để send request tới NewRelic với api_key tương ứng
      - ex: /path-1 -> sử dụng api_key của NewRelic_1 (ALB 1)
            /path-2 -> sử dụng api_key của NewRelic_2 (ALB 2)
  - nhược điểm:
    - thay đổi logic code nhiều ở lambda (code default của NewRelic )
    - thêm origin (ALB end-point) phải setting và deploy lại lambda
    - log có độ trể theo cơ chế lưu log của cloudfront
  - ưu điểm:
    - chỉ phát sinh thêm cost của lambda

  ![](../images/s3-origin-newrelic-inframap.png)

  #### Option 2: Sử dụng AWS Cloudfront - Realtime log
  - nhược điểm:
    - nội dung log có thể bị mất ( cho dù có set 100% rate )
    - phát sinh thêm cost vì phải tạo thêm Kinesis Stream Data và Kinesis Firehose
  - ưu điểm:
    - Log realtime tới NewRelic
    - Dễ dàng phân loại log theo từng origin tới mỗi account NewRelic 
    - NewRelic hỗ trợ kiểu realtime log, và có dashboard để phân tích log: cache hits, cache miss, error rate, error sucess, request, bandwidth ..., ref: https://docs.newrelic.com/docs/logs/forward-logs/cloudfront-web-logs/#what-next
  ![](../images/cloudfront-log-realtime.png)