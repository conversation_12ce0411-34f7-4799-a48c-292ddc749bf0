# HowToUseWAF

This document describes how to use [.github/workflows/maintenance-on.yml](../../.github/workflows/maintenance-on.yml) and [.github/workflows/maintenance-off.yml](../../.github/workflows/maintenance-off.yml)

## Purpose

- To allow application administrators to switch the maintenance screen on and off using only the GitHub interface.

## Workflow Contents

- maintenance-on.yml
  - Blocks access for general users and makes the site private.
    - Changes the action of the IPset rule to Block.
    - Changes the action of the Basic Authentication rule to Block.
- maintenance-off.yml
  - Allows access for general users and makes the site public.
    - Changes the action of the IPset rule to Count.
    - Changes the action of the Basic Authentication rule to Count.

## Prerequisites

### AWS Settings

- Set repository information in the OIDC stack and deploy it.

**param/environment file**

```typescript
export const OidcParam: inf.IOidcParam = {
  OrganizationName: 'mynavi-group',
  RepositoryNames: {
    WafRepositoryName: 'application repository name',
  },
};
```

- Obtain the IAM role information to operate WAF.

  - Access the CloudFormation console, select `Devgevanni-cf-sample-OIDC (example)`, and obtain the ARN of the IAM role.
    ![](../images/waf-stack.png)

- Obtain the information of the WAF to be operated.
  - Access the WAF console, select `Web ACLs` from the left pane, and obtain the name of the Web ACLs.
    ![](../images/waf-console.png)

### GitHub Settings

- Register the IAM role ARN and WebACL in GitHub Variables.
  Refer to README.md for registration steps.
  |Name|value|description|
  |-|-|-|
  |maintenance|(example) arn:aws:iam::730335585723:role/Devgevanni-cf-sample-OIDC-WafRoleA713F7DC-NTBFHtUHigNV|ARN of the IAM role|
  |WebACL_NAME|(example) DevgevannicfsampleWafCloudfrontWebAcl-xlxVq2vNqbGy|Name of the Web ACLs|

## Execution Steps

- Running the workflow
  1. Navigate to the "Actions" tab, and select the "Display Maintenance Screen" (or "Disable Maintenance Screen") workflow from the left pane.
  2. Open the "Run Workflow" dropdown and click the "Run Workflow" button.
