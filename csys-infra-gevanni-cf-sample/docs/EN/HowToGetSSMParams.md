# HowToGetSSMParams

This document describes how to get SSM Params and Bind it to Header CloudFront.

## Purpose

Before deploying CloudFront, App Team needs to get the hash value of `preSharedKey` stored on SSM Parameter Store of gevanni-infra repo. The result will return the value stored in Parameter Store (Example: `a1b2c3d4`): 

- Appteam gets SSM Params from CLI and attaches it to CloudFront Header.

## Workflow Contents

- The workflow involves retrieving the SSM parameter and binding it to the header CloudFront.
- Below is a diagram
![](../images/how-to-get-ssm-params.png)

## Prerequisites

### SSM Parameter Path Provider by Infra Team

- App team will get path SSM Parameter from Infra team, this path have Structure like `/Dev01-GEVANNI-sample01-dev01-gh/waf/PreSharedKeyValue-1`
- This path is used to retrieve the `preSharedKey` value (e.g., `a1b2c3d4`), which will be bound to the `preSharedKey` in the CloudFront configuration.

**param/environment file**

```typescript
export const appAlbsParam: inf.IAppAlbsParam = {
  appAlbDomains: ['www.example.com'],
  preSharedKey: 'pre-string-for-preSharedKey',
};
```

### AWS CLI Setup

- Ensure the AWS CLI is installed and configured with appropriate permissions to access the SSM Parameter Store.

## Execution Steps

### Retrieve the SSM Parameter:

- Use the AWS CLI to fetch the value of the SSM parameter.
- Run the following command `aws ssm get-parameter --name "path" --query "Parameter.Value" --output text` 
(e.g., `aws ssm get-parameter --name "/Dev01-GEVANNI-sample01-dev01-gh/waf/PreSharedKeyValue-1" --query "Parameter.Value" --output text`)
- This will return the stored value (e.g., `a1b2c3d4`)

### Bind the Retrieved Value to CloudFront Header

- Update the `preSharedKey` in the `appAlbsParam` environment file with the retrieved value (e.g., replace `pre-string-for-preSharedKey` with `a1b2c3d4`).
- Save the updated configuration run command `cdk deploy` to update header Cloudfront.

### Deploy CloudFront:

- Proceed with the CloudFront deployment using the updated configuration.