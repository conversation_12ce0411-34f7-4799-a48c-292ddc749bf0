# Stream CloudFront Logs to New Relic

- All access log content to CloudFront stored in S3 needs to be synchronized to the 3rd-party New Relic.
- All WAF log content stored in S3 needs to be synchronized to the 3rd-party New Relic.

## Content

- Synchronize logs for each origin (ALB endpoint) to the corresponding New Relic account.
- Synchronize WAF logs to a designated New Relic account.

## Workflow Contents

### Synchronize WAF Logs
  - Deployment content follows <PERSON><PERSON><PERSON><PERSON>'s infra setup, streaming all content from S3 logs to the designated New Relic.

### Synchronize CloudFront Logs for Each Origin
  #### Option 1: Read log content and decide which New Relic account to send it to
  - Deployment content follows <PERSON><PERSON><PERSON><PERSON>'s infra setup, streaming all content to the designated New Relic.
    - Load all New Relic API keys corresponding to each path.
    - Update Lambda logic to check the path and send requests to New Relic with the corresponding API key.
      - Example: 
            - /path-1 -> use the API key of New Relic_1 (ALB 1)
            - /path-2 -> use the API key of New Relic_2 (ALB 2)
  - Disadvantages:
    - Significant changes to Lambda code (default code of New Relic).
    - Adding an origin (ALB endpoint) requires setting up and redeploying the Lambda.
    - Logs have latency due to CloudFront's log storage mechanism.
  - Advantages:
    - Only incurs additional Lambda costs.

  ![](../images/s3-origin-newrelic-inframap.png)

  #### Option 2: Use AWS CloudFront - Realtime Logs
  - Disadvantages:
    - Log content may be lost (even if set to 100% rate).
    - Additional costs due to the need to create Kinesis Stream Data and Kinesis Firehose.
  - Advantages:
    - Real-time logs to New Relic.
    - Easy to classify logs by origin to each New Relic account.
    - New Relic supports real-time logs and provides a dashboard for log analysis: cache hits, cache misses, error rate, success rate, requests, bandwidth, etc. Reference: https://docs.newrelic.com/docs/logs/forward-logs/cloudfront-web-logs/#what-next
  ![](../images/cloudfront-log-realtime.png)
