# SSMパラメータの取得方法
このドキュメントでは、SSMパラメータを取得し、それをCloudFrontのヘッダーにバインドする方法について説明します。

## 目的
CloudFrontをデプロイする前に、App Teamはgevanni-infraリポジトリのSSM Parameter Storeに保存されている`preSharedKey`のハッシュ値を取得する必要があります。結果として、Parameter Storeに保存されている値（例：`a1b2c3d4`）が返されます。

- AppteamはCLIからSSM Paramsを取得し、それをCloudFrontのヘッダーにバインドします。

## ワークフロー内容
- ワークフローには、SSMパラメータの取得とCloudFrontのヘッダーへのバインドが含まれます。
- 以下はダイアグラムです
![](../images/how-to-get-ssm-params.png)

## 前提条件

InfraチームがSSMパラメータパスを提供する場合

- アプリチームは、インフラチームからSSMパラメータのパスを受け取ります。このパスは、`/Dev01-GEVANNI-sample01-dev01-gh/waf/PreSharedKeyValue-1` のような構造をします。

- このパスは、CloudFrontの設定で`preSharedKey`にバインドされる`preSharedKey`の値（例：`a1b2c3d4`）を取得するために使用されます。

**param/environmentファイル**

```typescript
export const appAlbsParam: inf.IAppAlbsParam = {
  appAlbDomains: ['www.example.com'],
  preSharedKey: 'pre-string-for-preSharedKey',
};
```

### AWS CLIのセットアップ

- AWS CLIがインストールされており、SSM Parameter Storeにアクセスするための適切な権限が設定されていることを確認してください。

## 実行手順

### SSMパラメータの取得：

- AWS CLIを使用してSSMパラメータの値を取得します。
- 以下のコマンドを実行します：
`aws ssm get-parameter --name "path" --query "Parameter.Value" --output text`
(例：`aws ssm get-parameter --name "/Dev01-GEVANNI-sample01-dev01-gh/waf/PreSharedKeyValue-1" --query "Parameter.Value" --output text`)
- これにより、保存されている値（例：`a1b2c3d4`）が返されます。

### 取得した値をCloudFrontヘッダーにバインドする

- appAlbsParam 環境ファイル内の `preSharedKey` を、取得した値（例：`pre-string-for-preSharedKey` を `a1b2c3d4` に置き換える）で更新します。
- 設定を保存した後、`cdk deploy` コマンドを実行して、CloudFront のヘッダーを更新します。

### CloudFrontのデプロイ：

- 更新した設定で CloudFront のデプロイを行います。