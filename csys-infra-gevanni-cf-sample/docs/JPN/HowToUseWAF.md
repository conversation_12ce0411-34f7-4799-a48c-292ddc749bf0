# HowToUseWAF

ここでは、[.github/workflows/maintenance-on.yml](../../.github/workflows/maintenance-on.yml)と[.github/workflows/maintenance-off.yml](../../.github/workflows/maintenance-off.yml)の使用手順について記載する。

## 目的

- アプリ担当者が GitHub 画面上の操作のみで、メンテナンス画面を切り替えられるようにする。

## ワークフローの内容

- maintenance-on.yml
  - 一般ユーザのアクセスを遮断し、サイトを非公開にする。
    - IPset ルールの Action を Block に変更
    - Basic 認証ルールの Action を Block に変更
- maintenance-off.yml
  - 一般ユーザのアクセスを許可し、サイトを公開する。
    - IPset ルールの Action を Count に変更
    - Basic 認証ルールの Action を Count に変更

## 事前に必要な設定

### AWS 側の設定

- OIDC スタックにリポジトリ情報を設定し、デプロイする。

  **param/環境ファイル**

  ```typescript
  export const OidcParam: inf.IOidcParam = {
    OrganizationName: 'mynavi-group',
    RepositoryNames: {
      WafRepositoryName: 'アプリリポジトリ名',
    },
  };
  ```

- WAF を操作するための IAM ロール情報を取得する。

  - CloudFormation コンソールにアクセスし、`Devgevanni-cf-sample-OIDC(例)`を選択し、IAM コンソールに遷移する。IAM ロールの ARN を取得する。
    ![](./../images/waf-stack.png)

- 操作する WAF の情報を取得する。
  - WAF コンソールにアクセスし、左ペインから`Web ACLs`を選択し、Web ACLs 名を取得する。
    ![](./../images/waf-console.png)

### GitHub 側の設定

- GitHub Variable に IAM ロール ARN と WebACL を登録する。  
  登録手順は README.md を参照すること。
  |Name|value|概要|
  |-|-|-|
  |maintenance|(例)arn:aws:iam::730335585723:role/Devgevanni-cf-sample-OIDC-WafRoleA713F7DC-NTBFHtUHigNV|IAM ロールの ARN|
  |WebACL_NAME|(例)DevgevannicfsampleWafCloudfrontWebAcl-xlxVq2vNqbGy|Web ACLs の名前|

## 実行手順

- ワークフローの実行
  1.  「Actions」タブに遷移し、左ペインより「メンテナンス画面表示」(または「メンテナス画面解除」)ワークフローを選択する。
  2.  「Run Workflow」のプルダウンを開き、「Run Workflow」のボタンを押下する。
