# Testing Guidelines

## Test Naming Standards

Use the following naming convention for tests:

**Format:**  
`Should <ExpectedBehavior> When <StateUnderTest>`

Examples:
- `Should Create S3 Bucket When BucketSetting is true`
- `Should Create Correct Policy When Allowing Lambda to Get Object from S3`
- `Should Throw exception if settings are wrong`

**Purpose:**  
Adopting naming conventions in unit tests not only improves code quality but also enhances team collaboration efficiency.
Test case names should be clear and descriptive, making it easy for readers to quickly grasp the purpose of the test and the functionality or behavior being assessed.

### Example:
```typescript
describe("The cloudfront stack", () => {
    test("should create 2 S3 when closed bucket setting is true", () => {});

    test("should create 5 S3 when closed bucket setting is false", () => {});
});
```

## Best Practices

- Write minimally passing tests.
- Avoid magic strings (string values hard-coded directly).
- Avoid coding logic in unit tests.

## Checklist when you write unittest for stack
- Ensure that loop processing works correctly
- Verify that conditional branches function as expected
- Check that property overrides are implemented correctly
- Ensure that required definitions are guaranteed
- Verify that values are correctly specified using props
## Testing for Each Stack

For each stack:
1. **Assert Snapshot**
   - Ensure the stack matches the expected snapshot.
2. **Assert Number of Resources**
   - Verify the number of resources created.
3. **Assert Correct Policy**
   - Use matchers to validate the correct policy for roles.
4. **Assert Resource Properties**
   - Ensure resources have the expected properties.

## Common unittest assertion
### Object Matchers
Matchers are a core functionality of CDK Assertions. They provide various options to validate how the template is structured.
#### Match.objectLike
This matcher is non-strict and validates if the given parameter exists in the resource, ignoring other parameters.

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: 'test',
    }),
});
```

In the above example, even if the resource under test has additional parameters, they will be ignored, and the test will pass.

#### Match.objectEquals
This matcher performs strict validation, requiring the resource to match the given parameters exactly.

---

### Existence Checks

#### Match.anyValue
This matcher checks only for the existence of a value, similar to a "notNull" check.

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.anyValue(),
    }),
});
```

#### Match.absent
This matcher validates that a value is not present.

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.absent(),
    }),
});
```

---

### Matchers for Arrays

#### Match.arrayWith
This matcher is used to validate arrays containing specific matching items, even if the array has additional items.

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.arrayWith(["test"]),
    }),
});
```

#### Match.arrayEquals
This matcher requires an exact match of the array's contents.

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.arrayEquals(["test", "test1"]),
    }),
});
```

---

### Matchers for Strings

#### Match.stringLikeRegexp
This matcher validates a string using wildcards (e.g., `*`) or ranges (e.g., `|`).

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.stringLikeRegexp("tes*"),
    }),
});
```
