import * as cdk from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import { IConfig } from '../params/interface';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { RoleForAppTeam } from '../lib/stack/role-for-app-team-construct';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';

// Account and Region on test
//  cdk.process.env.* returns undefined, and cdk.Stack.of(this).* returns ${Token[Region.4]} at test time.
//  In such case, RegionInfo.get(cdk.Stack.of(this).region) returns error and test will fail.
//  So we pass 'ap-northeast-1' as region code.
const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjPrefix = 'gevanni-cf-sample';
var app: any;
const envKey = 'dev';

const config: IConfig = require('../params/' + envKey);

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);
})

describe(`${pjPrefix} Guest Stacks`, () => {
  test('should snapshot matching', () => {
    const wafCloudfront = new WafCfStack(app, `${pjPrefix}-Waf`, {
      pjPrefix: pjPrefix,
      scope: 'CLOUDFRONT',
      env: {
        account: getProcEnv().account,
        region: 'us-east-1',
      },
      crossRegionReferences: true,
      ...config.WafParam,
      wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
      csirtWAFParam: config.CSIRTWAFParamCF,
    });

    const oidc = new OidcStack(app, `${pjPrefix}-OIDC`, {
      OrganizationName: config.OidcParam.OrganizationName,
      RepositoryNames: config.OidcParam.RepositoryNames,
      env: getProcEnv(),
    });

    const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
      pjPrefix: pjPrefix,
      webAcl: wafCloudfront.webAcl,
      CertificateIdentifier: config.CertificateIdentifier,
      cloudFrontParam: config.CloudFrontParam,
      appAlbsParam: config.appAlbsParam,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      env: getProcEnv(),
      crossRegionReferences: true,
      accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      webContentVersionExpiration: config.webContentVersionExpiration,
    });

    // Monitor stack: dashboard
    const monitor = new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
      pjPrefix: `${pjPrefix}`,
      dashboardName: `${pjPrefix}-ECSApp`,
      cfDistributionId: cloudfront.cfDistributionId,
      env: getProcEnv(),
    });

    if (config.roleForAppTeamParam) {
      const role = new RoleForAppTeam(app, `${pjPrefix}-SMSStack`, {
        pjPrefix: `${pjPrefix}`,
        allowedRoles: config.roleForAppTeamParam?.allowedRoles ?? [],
        env: getProcEnv(),
      });
      expect(Template.fromStack(role)).toMatchSnapshot();
    }

    // Tagging "Environment" tag to all resources in this app

    // test with snapshot
    expect(Template.fromStack(wafCloudfront)).toMatchSnapshot();
    expect(Template.fromStack(oidc)).toMatchSnapshot();
    expect(Template.fromStack(cloudfront)).toMatchSnapshot();
    expect(Template.fromStack(monitor)).toMatchSnapshot();
  });

  test('should snapshot matching open search', () => {
    if (config.openSearchParam) {
    const opensearch = new OpenSearchStack(app, `${pjPrefix}-Opensearch`, {
      pjPrefix: `${pjPrefix}`,
      env: getProcEnv(),
      ...config.openSearchParam,
    });
    expect(Template.fromStack(opensearch)).toMatchSnapshot();
    }
  })
});
