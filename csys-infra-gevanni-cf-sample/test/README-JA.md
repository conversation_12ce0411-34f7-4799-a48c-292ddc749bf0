# テストガイドライン

## テスト命名規則

テストには以下の命名規則を使用してください：

**形式:**  
`Should <ExpectedBehavior> When <StateUnderTest>`
(＜テスト対象の状態＞ の場合に ＜期待動作＞ べき)

例：
- `Should Create S3 Bucket When BucketSetting is true`  
  （BucketSettingがtrueの場合にS3バケットを作成するべき）
- `Should Create Correct Policy When Allowing Lambda to Get Object from S3`  
  （LambdaがS3オブジェクトを取得できるようにする正しいポリシーを作成するべき）
- `Should Throw exception if settings are wrong`  
  （設定が間違っている場合に例外をスローするべき）

**目的:**  
ユニットテストで一貫した命名規則を採用することは、コードの品質を向上させるだけでなく、チームの協力効率も高める。
テストケースの名前は明確で説明的であるべきで、読者がテストの目的や評価されている機能・挙動をすばやく把握できるようにする。

### 例:
```typescript
describe("The cloudfront stack", () => {
    test("should create 2 S3 when closed bucket setting is true", () => {});

    test("should create 5 S3 when closed bucket setting is false", () => {});
});
```

## ベストプラクティス

- 最小限の条件で通るテストを書く。
- マジック文字列（ハードコーディングされた文字列）は避ける。
- 単体テストでロジックをコーディングするのは避ける。

## スタックの単体テストを書く際のチェックリスト
- ループ処理が正しく実行されているか
- 条件分岐が期待どおりに動作しているか
- プロパティのオーバーライドが正しく行われているか
- 保証したいリソースの定義が含まれているか
- `props` を使って値が正しく指定されているか

## 各スタックのテスト

各スタックについて：
1. **スナップショットの確認**
   - スタックが期待されるスナップショットと一致するか確認する。
2. **リソースの数を確認**
   - 作成されたリソースの数を確認する。
3. **正しいポリシーの確認**
   - マッチャーを使用して、ロールのポリシーが正しいことを検証する。
4. **リソースプロパティの確認**
   - リソースが期待されるプロパティを持っているか確認する。

## 一般的な単体テストのアサーション

### オブジェクトマッチャー
マッチャーはCDKアサーションのコア機能である。テンプレートがどのように構成されているかを検証するためのさまざまなオプションを提供する。

#### Match.objectLike
このマッチャーは厳密ではなく、指定されたパラメーターがリソース内に存在するかどうかを検証する。他のパラメーターは無視される。

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: 'test',
    }),
});
```

上記の例では、テスト対象のリソースに追加のパラメーターがあっても、それらは無視され、テストは成功する。

#### Match.objectEquals
このマッチャーは厳密な検証を行い、リソースが指定されたパラメータと完全に一致する必要がある。

---

### 存在チェック

#### Match.anyValue
このマッチャーは、「notNull 」チェックと同様に、値の存在のみをチェックする。

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.anyValue(),
    }),
});
```

#### Match.absent
このマッチャーは値が存在しないことを検証する。

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.absent(),
    }),
});
```

---

### 配列のマッチャー

#### Match.arrayWith
このマッチャーは、配列に特定の一致する要素が含まれていることを検証するために使用される。たとえ配列に追加の要素が含まれていても、検証を行う。

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.arrayWith(["test"]),
    }),
});
```

#### Match.arrayEquals
このマッチャーは、配列の内容が完全に一致することを要求する。

```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.arrayEquals(["test", "test1"]),
    }),
});
```

---

### 文字列のマッチャー

#### Match.stringLikeRegexp
このマッチャーはワイルドカード（例：`*`）や範囲（例：`|`）を使用して文字列を検証する。
```typescript
template.hasResourceProperties('Foo::Bar', {
    Fred: Match.objectLike({
        test: Match.stringLikeRegexp("tes*"),
    }),
});
```
