// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`gevanni-cf-sample Guest Stacks GuestAccount ECS App Stacks 1`] = `
Object {
  "Outputs": Object {
    "RoleForAppTeamArn": Object {
      "Export": Object {
        "Name": "gevanni-cf-sample-RoleForAppTeamArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "gevannicfsampleRoleForAppTeam50144836",
          "Arn",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "gevannicfsampleRoleForAppTeam50144836": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": "role-arn-sample",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "RoleName": "gevanni-cf-sample-RoleForAppTeam",
      },
      "Type": "AWS::IAM::Role",
    },
    "gevannicfsampleRoleForAppTeamDefaultPolicyD6C26E74": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sns:Publish",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "gevannicfsampleRoleForAppTeamDefaultPolicyD6C26E74",
        "Roles": Array [
          Object {
            "Ref": "gevannicfsampleRoleForAppTeam50144836",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks GuestAccount ECS App Stacks 2`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomCrossRegionExportWriterCustomResourceProviderHandlerD8786E8A": Object {
      "DependsOn": Array [
        "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-us-east-1",
          },
          "S3Key": "246cb27aa0cb552c81fdca061092d0905aa4d2529e8b52b5598c069f18be51d7.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1",
            "Arn",
          ],
        },
        "Runtime": "nodejs18.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ssm:DeleteParameters",
                    "ssm:ListTagsForResource",
                    "ssm:GetParameters",
                    "ssm:PutParameter",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          "arn:",
                          Object {
                            "Ref": "AWS::Partition",
                          },
                          ":ssm:ap-northeast-1:",
                          Object {
                            "Ref": "AWS::AccountId",
                          },
                          ":parameter/cdk/exports/*",
                        ],
                      ],
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ExportsWriterapnortheast12334E1B81D43DF3F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportWriterCustomResourceProviderHandlerD8786E8A",
            "Arn",
          ],
        },
        "WriterProps": Object {
          "exports": Object {
            "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886": Object {
              "Fn::GetAtt": Array [
                "gevannicfsampleWafWebAcl3033DA86",
                "Arn",
              ],
            },
          },
          "region": "ap-northeast-1",
        },
      },
      "Type": "Custom::CrossRegionExportWriter",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleWafWebAcl3033DA86": Object {
      "Properties": Object {
        "DefaultAction": Object {
          "Allow": Object {},
        },
        "Rules": Array [
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "IPset",
            "Priority": 11,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleWafWebAclIPsetEEF99F1F",
                    "Arn",
                  ],
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Block": Object {
                "CustomResponse": Object {
                  "ResponseCode": 401,
                  "ResponseHeaders": Array [
                    Object {
                      "Name": "www-authenticate",
                      "Value": "Basic",
                    },
                  ],
                },
              },
            },
            "Name": "BasicAuth",
            "Priority": 13,
            "Statement": Object {
              "NotStatement": Object {
                "Statement": Object {
                  "ByteMatchStatement": Object {
                    "FieldToMatch": Object {
                      "SingleHeader": Object {
                        "name": "authorization",
                      },
                    },
                    "PositionalConstraint": "EXACTLY",
                    "SearchString": "Basic YWRtaW46ZDc0ZmYwZWU=",
                    "TextTransformations": Array [
                      Object {
                        "Priority": 0,
                        "Type": "NONE",
                      },
                    ],
                  },
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": false,
              "MetricName": "BasicAuthRule",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesCommonRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 2,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesCommonRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesCommonRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesKnownBadInputsRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 3,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesKnownBadInputsRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesKnownBadInputsRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesAmazonIpReputationList",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 4,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesAmazonIpReputationList",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesAmazonIpReputationList",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesLinuxRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 5,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesLinuxRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesLinuxRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesSQLiRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 6,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesSQLiRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesSQLiRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Block": Object {},
            },
            "Name": "CSIRTBlockSpecificIPs",
            "Priority": 0,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:us-east-1:111111111111:global/ipset/CSIRTIpSet/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTBlockSpecificIPs",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "CSIRTManagerRules",
            "OverrideAction": Object {
              "None": Object {},
            },
            "Priority": 1,
            "Statement": Object {
              "RuleGroupReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:us-east-1:111111111111:global/rulegroup/CSIRTManagerRules/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTManagerRules",
              "SampledRequestsEnabled": true,
            },
          },
        ],
        "Scope": "CLOUDFRONT",
        "VisibilityConfig": Object {
          "CloudWatchMetricsEnabled": true,
          "MetricName": "WafAcl",
          "SampledRequestsEnabled": true,
        },
      },
      "Type": "AWS::WAFv2::WebACL",
    },
    "gevannicfsampleWafWebAclIPsetEEF99F1F": Object {
      "Properties": Object {
        "Addresses": Array [
          "***************/25",
        ],
        "IPAddressVersion": "IPV4",
        "Name": "IPset",
        "Scope": "CLOUDFRONT",
      },
      "Type": "AWS::WAFv2::IPSet",
    },
    "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "BucketName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "aws-waf-logs-",
              Object {
                "Ref": "AWS::AccountId",
              },
              "-bucket",
            ],
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Retain",
    },
    "gevannicfsampleWafWebAclgevannicfsampleWafwafV2LoggingConfiguration3F5759B1": Object {
      "DependsOn": Array [
        "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E",
        "gevannicfsampleWafWebAcl3033DA86",
      ],
      "Properties": Object {
        "LogDestinationConfigs": Array [
          Object {
            "Fn::GetAtt": Array [
              "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E",
              "Arn",
            ],
          },
        ],
        "ResourceArn": Object {
          "Fn::GetAtt": Array [
            "gevannicfsampleWafWebAcl3033DA86",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::LoggingConfiguration",
    },
    "gevannicfsampleWafWebAclmaintenanceUserNameA1D6E2B7": Object {
      "Properties": Object {
        "Name": "maintenanceUserName-c8aebcff0ae562663d24ce3c94d3a1de63b477a0c2",
        "Type": "String",
        "Value": "admin",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "gevannicfsampleWafWebAclmaintenanceUserPass66AAD16D": Object {
      "Properties": Object {
        "Name": "maintenanceUserPass-c8aebcff0ae562663d24ce3c94d3a1de63b477a0c2",
        "Type": "String",
        "Value": "d74ff0ee",
      },
      "Type": "AWS::SSM::Parameter",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks GuestAccount ECS App Stacks 3`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0": Object {
      "DependsOn": Array [
        "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "977fc1649d2dbcce16e23f6332faef6fa0f48aa74a0afe35f4a3467754e20cd8.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
            "Arn",
          ],
        },
        "Runtime": "nodejs18.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "iam:CreateOpenIDConnectProvider",
                    "iam:DeleteOpenIDConnectProvider",
                    "iam:UpdateOpenIDConnectProviderThumbprint",
                    "iam:AddClientIDToOpenIDConnectProvider",
                    "iam:RemoveClientIDFromOpenIDConnectProvider",
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "GithubActionsOidcProviderF9E986BE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ClientIDList": Array [
          "sts.amazonaws.com",
        ],
        "CodeHash": "977fc1649d2dbcce16e23f6332faef6fa0f48aa74a0afe35f4a3467754e20cd8",
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0",
            "Arn",
          ],
        },
        "Url": "https://token.actions.githubusercontent.com",
      },
      "Type": "Custom::AWSCDKOpenIdConnectProvider",
      "UpdateReplacePolicy": "Delete",
    },
    "InfraResourcesRoleDefaultPolicy650E5DA3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "cloudformation:DescribeStacks",
                "s3:PutObject",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "InfraResourcesRoleDefaultPolicy650E5DA3",
        "Roles": Array [
          Object {
            "Ref": "InfraResourcesRoleE6C761A4",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "InfraResourcesRoleE6C761A4": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/InfraRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleA713F7DC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/WafRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleDefaultPolicy729EDA04": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "wafv2:ListWebACLs",
                "wafv2:GetWebACL",
                "wafv2:UpdateWebACL",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "WafRoleDefaultPolicy729EDA04",
        "Roles": Array [
          Object {
            "Ref": "WafRoleA713F7DC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks GuestAccount ECS App Stacks 4`] = `
Object {
  "Outputs": Object {
    "ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D": Object {
      "Export": Object {
        "Name": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
      },
      "Value": Object {
        "Ref": "gevannicfsampleCloudFrontDistributionAC208915",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomCrossRegionExportReaderCustomResourceProviderHandler46647B68": Object {
      "DependsOn": Array [
        "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "8acca95a9957d02a9f3ec124c9869c5d5b70a7fb3e332120850781ecc9363037.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD",
            "Arn",
          ],
        },
        "Runtime": "nodejs18.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ssm:AddTagsToResource",
                    "ssm:RemoveTagsFromResource",
                    "ssm:GetParameters",
                  ],
                  "Effect": "Allow",
                  "Resource": Object {
                    "Fn::Join": Array [
                      "",
                      Array [
                        "arn:",
                        Object {
                          "Ref": "AWS::Partition",
                        },
                        ":ssm:ap-northeast-1:",
                        Object {
                          "Ref": "AWS::AccountId",
                        },
                        ":parameter/cdk/exports/gevanni-cf-sample-Cloudfront/*",
                      ],
                    ],
                  },
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "2eb6a831b107939f63cfebf68e6316e1a40f79fc99cae0fee9b333bac8d29bc4.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs18.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ExportsReader8B249524": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ReaderProps": Object {
          "imports": Object {
            "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886": "{{resolve:ssm:/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886}}",
          },
          "prefix": "gevanni-cf-sample-Cloudfront",
          "region": "ap-northeast-1",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportReaderCustomResourceProviderHandler46647B68",
            "Arn",
          ],
        },
      },
      "Type": "Custom::CrossRegionExportReader",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucket194144B2": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "backendBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketAutoDeleteObjectsCustomResource50AFDC77": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontBackendBucketPolicy7054983D",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketPolicy7054983D": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontClosedBucket522EF4F5": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "closedBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketAutoDeleteObjectsCustomResource866CF88C": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketAutoDeleteObjectsCustomResource77545AD2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontDistributionAC208915": Object {
      "Properties": Object {
        "DistributionConfig": Object {
          "CacheBehaviors": Array [
            Object {
              "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
              "Compress": true,
              "PathPattern": "/static/*",
              "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "ViewerProtocolPolicy": "redirect-to-https",
            },
          ],
          "CustomErrorResponses": Array [
            Object {
              "ErrorCachingMinTTL": 20,
              "ErrorCode": 403,
              "ResponseCode": 403,
              "ResponsePagePath": "/static/sorry.html",
            },
          ],
          "DefaultCacheBehavior": Object {
            "AllowedMethods": Array [
              "GET",
              "HEAD",
              "OPTIONS",
              "PUT",
              "PATCH",
              "POST",
              "DELETE",
            ],
            "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
            "Compress": true,
            "OriginRequestPolicyId": "216adef6-5c7f-47e4-b989-5492eafa07d3",
            "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
            "ViewerProtocolPolicy": "redirect-to-https",
          },
          "DefaultRootObject": "/",
          "Enabled": true,
          "HttpVersion": "http2",
          "IPV6Enabled": true,
          "Logging": Object {
            "Bucket": Object {
              "Fn::GetAtt": Array [
                "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                "RegionalDomainName",
              ],
            },
            "IncludeCookies": true,
            "Prefix": "CloudFrontAccessLogs/",
          },
          "Origins": Array [
            Object {
              "CustomOriginConfig": Object {
                "OriginProtocolPolicy": "http-only",
                "OriginSSLProtocols": Array [
                  "TLSv1.2",
                ],
              },
              "DomainName": "www.example.com",
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
              "OriginCustomHeaders": Array [
                Object {
                  "HeaderName": "x-pre-shared-key",
                  "HeaderValue": "pre-string-for-preSharedKey",
                },
              ],
            },
            Object {
              "DomainName": Object {
                "Fn::GetAtt": Array [
                  "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                  "RegionalDomainName",
                ],
              },
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "S3OriginConfig": Object {
                "OriginAccessIdentity": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "origin-access-identity/cloudfront/",
                      Object {
                        "Ref": "gevannicfsampleCloudFrontDistributionOrigin2S3OriginA87093B7",
                      },
                    ],
                  ],
                },
              },
            },
          ],
          "WebACLId": Object {
            "Fn::GetAtt": Array [
              "ExportsReader8B249524",
              "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886",
            ],
          },
        },
      },
      "Type": "AWS::CloudFront::Distribution",
    },
    "gevannicfsampleCloudFrontDistributionOrigin2S3OriginA87093B7": Object {
      "Properties": Object {
        "CloudFrontOriginAccessIdentityConfig": Object {
          "Comment": "Identity for gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
        },
      },
      "Type": "AWS::CloudFront::CloudFrontOriginAccessIdentity",
    },
    "gevannicfsampleCloudFrontWebBucket9A2CE5C9": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "webContentBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketAutoDeleteObjectsCustomResource4EDA3CC2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontWebBucketPolicyED75C388",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketPolicyED75C388": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "s3:GetObject",
              "Effect": "Allow",
              "Principal": Object {
                "CanonicalUser": Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontDistributionOrigin2S3OriginA87093B7",
                    "S3CanonicalUserId",
                  ],
                },
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                        "Arn",
                      ],
                    },
                    "/*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketAutoDeleteObjectsCustomResource7C6FC775": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "LogDeliveryWrite",
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks GuestAccount ECS App Stacks 5`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "gevannicfsampleDashboard1A3EDEB7": Object {
      "Properties": Object {
        "DashboardBody": Object {
          "Fn::Join": Array [
            "",
            Array [
              "{\\"widgets\\":[{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":0,\\"properties\\":{\\"markdown\\":\\"# Requests\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":24,\\"x\\":0,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"Requests\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":25,\\"properties\\":{\\"markdown\\":\\"# Errors\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":24,\\"x\\":0,\\"y\\":26,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Error Rates\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"5xxErrorRate\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}],[\\"AWS/CloudFront\\",\\"4xxErrorRate\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}]],\\"yAxis\\":{}}}]}",
            ],
          ],
        },
        "DashboardName": "gevanni-cf-sample-ECSApp",
      },
      "Type": "AWS::CloudWatch::Dashboard",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching 1`] = `
Object {
  "Outputs": Object {
    "RoleForAppTeamArn": Object {
      "Export": Object {
        "Name": "gevanni-cf-sample-RoleForAppTeamArn",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "gevannicfsampleRoleForAppTeam50144836",
          "Arn",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "gevannicfsampleRoleForAppTeam50144836": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": "role-arn-sample",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "RoleName": "gevanni-cf-sample-RoleForAppTeam",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "gevannicfsampleRoleForAppTeamDefaultPolicyD6C26E74": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sns:Publish",
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "gevannicfsampleRoleForAppTeamDefaultPolicyD6C26E74",
        "Roles": Array [
          Object {
            "Ref": "gevannicfsampleRoleForAppTeam50144836",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching 2`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomCrossRegionExportWriterCustomResourceProviderHandlerD8786E8A": Object {
      "DependsOn": Array [
        "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-us-east-1",
          },
          "S3Key": "8a17fb3ceee821f797100bf65fa3047c164a08c03aa5a07fbcff9a676fb73b97.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCrossRegionExportWriterCustomResourceProviderRoleC951B1E1": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ssm:DeleteParameters",
                    "ssm:ListTagsForResource",
                    "ssm:GetParameters",
                    "ssm:PutParameter",
                  ],
                  "Effect": "Allow",
                  "Resource": Array [
                    Object {
                      "Fn::Join": Array [
                        "",
                        Array [
                          "arn:",
                          Object {
                            "Ref": "AWS::Partition",
                          },
                          ":ssm:ap-northeast-1:",
                          Object {
                            "Ref": "AWS::AccountId",
                          },
                          ":parameter/cdk/exports/*",
                        ],
                      ],
                    },
                  ],
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ExportsWriterapnortheast12334E1B81D43DF3F": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportWriterCustomResourceProviderHandlerD8786E8A",
            "Arn",
          ],
        },
        "WriterProps": Object {
          "exports": Object {
            "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886": Object {
              "Fn::GetAtt": Array [
                "gevannicfsampleWafWebAcl3033DA86",
                "Arn",
              ],
            },
          },
          "region": "ap-northeast-1",
        },
      },
      "Type": "Custom::CrossRegionExportWriter",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleWafWebAcl3033DA86": Object {
      "Properties": Object {
        "DefaultAction": Object {
          "Allow": Object {},
        },
        "Rules": Array [
          Object {
            "Action": Object {
              "Allow": Object {},
            },
            "Name": "IPset",
            "Priority": 11,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleWafWebAclIPsetEEF99F1F",
                    "Arn",
                  ],
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "IPset",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Block": Object {
                "CustomResponse": Object {
                  "ResponseCode": 401,
                  "ResponseHeaders": Array [
                    Object {
                      "Name": "www-authenticate",
                      "Value": "Basic",
                    },
                  ],
                },
              },
            },
            "Name": "BasicAuth",
            "Priority": 13,
            "Statement": Object {
              "NotStatement": Object {
                "Statement": Object {
                  "ByteMatchStatement": Object {
                    "FieldToMatch": Object {
                      "SingleHeader": Object {
                        "name": "authorization",
                      },
                    },
                    "PositionalConstraint": "EXACTLY",
                    "SearchString": "Basic YWRtaW46ZDc0ZmYwZWU=",
                    "TextTransformations": Array [
                      Object {
                        "Priority": 0,
                        "Type": "NONE",
                      },
                    ],
                  },
                },
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": false,
              "MetricName": "BasicAuthRule",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesCommonRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 2,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesCommonRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesCommonRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesKnownBadInputsRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 3,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesKnownBadInputsRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesKnownBadInputsRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesAmazonIpReputationList",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 4,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesAmazonIpReputationList",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesAmazonIpReputationList",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesLinuxRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 5,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesLinuxRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesLinuxRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "AWSManagedRulesSQLiRuleSet",
            "OverrideAction": Object {
              "Count": Object {},
            },
            "Priority": 6,
            "Statement": Object {
              "ManagedRuleGroupStatement": Object {
                "Name": "AWSManagedRulesSQLiRuleSet",
                "VendorName": "AWS",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "AWS-AWSManagedRulesSQLiRuleSet",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Action": Object {
              "Block": Object {},
            },
            "Name": "CSIRTBlockSpecificIPs",
            "Priority": 0,
            "Statement": Object {
              "IPSetReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:us-east-1:111111111111:global/ipset/CSIRTIpSet/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTBlockSpecificIPs",
              "SampledRequestsEnabled": true,
            },
          },
          Object {
            "Name": "CSIRTManagerRules",
            "OverrideAction": Object {
              "None": Object {},
            },
            "Priority": 1,
            "Statement": Object {
              "RuleGroupReferenceStatement": Object {
                "Arn": "arn:aws:wafv2:us-east-1:111111111111:global/rulegroup/CSIRTManagerRules/XXXX",
              },
            },
            "VisibilityConfig": Object {
              "CloudWatchMetricsEnabled": true,
              "MetricName": "CSIRTManagerRules",
              "SampledRequestsEnabled": true,
            },
          },
        ],
        "Scope": "CLOUDFRONT",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VisibilityConfig": Object {
          "CloudWatchMetricsEnabled": true,
          "MetricName": "WafAcl",
          "SampledRequestsEnabled": true,
        },
      },
      "Type": "AWS::WAFv2::WebACL",
    },
    "gevannicfsampleWafWebAclIPsetEEF99F1F": Object {
      "Properties": Object {
        "Addresses": Array [
          "***************/25",
        ],
        "IPAddressVersion": "IPV4",
        "Name": "IPset",
        "Scope": "CLOUDFRONT",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::WAFv2::IPSet",
    },
    "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E": Object {
      "DeletionPolicy": "Retain",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "BucketName": Object {
          "Fn::Join": Array [
            "",
            Array [
              "aws-waf-logs-",
              Object {
                "Ref": "AWS::AccountId",
              },
              "-dev35-thachlh-bucket",
            ],
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Retain",
    },
    "gevannicfsampleWafWebAclgevannicfsampleWafwafV2LoggingConfiguration3F5759B1": Object {
      "DependsOn": Array [
        "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E",
        "gevannicfsampleWafWebAcl3033DA86",
      ],
      "Properties": Object {
        "LogDestinationConfigs": Array [
          Object {
            "Fn::GetAtt": Array [
              "gevannicfsampleWafWebAclgevannicfsampleWafWafLogsBucket6ABF4B5E",
              "Arn",
            ],
          },
        ],
        "ResourceArn": Object {
          "Fn::GetAtt": Array [
            "gevannicfsampleWafWebAcl3033DA86",
            "Arn",
          ],
        },
      },
      "Type": "AWS::WAFv2::LoggingConfiguration",
    },
    "gevannicfsampleWafWebAclmaintenanceUserNameA1D6E2B7": Object {
      "Properties": Object {
        "Name": "maintenanceUserName-c8aebcff0ae562663d24ce3c94d3a1de63b477a0c2",
        "Tags": Object {
          "Environment": "dev",
        },
        "Type": "String",
        "Value": "admin",
      },
      "Type": "AWS::SSM::Parameter",
    },
    "gevannicfsampleWafWebAclmaintenanceUserPass66AAD16D": Object {
      "Properties": Object {
        "Name": "maintenanceUserPass-c8aebcff0ae562663d24ce3c94d3a1de63b477a0c2",
        "Tags": Object {
          "Environment": "dev",
        },
        "Type": "String",
        "Value": "d74ff0ee",
      },
      "Type": "AWS::SSM::Parameter",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching 3`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0": Object {
      "DependsOn": Array [
        "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "62fa02efcaa700e1c247e1d3cc2aa0cd07a0808a9a3e3d2230e51f57a02233fb.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderRole517FED65": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "iam:CreateOpenIDConnectProvider",
                    "iam:DeleteOpenIDConnectProvider",
                    "iam:UpdateOpenIDConnectProviderThumbprint",
                    "iam:AddClientIDToOpenIDConnectProvider",
                    "iam:RemoveClientIDFromOpenIDConnectProvider",
                  ],
                  "Effect": "Allow",
                  "Resource": "*",
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "GithubActionsOidcProviderF9E986BE": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ClientIDList": Array [
          "sts.amazonaws.com",
        ],
        "CodeHash": "62fa02efcaa700e1c247e1d3cc2aa0cd07a0808a9a3e3d2230e51f57a02233fb",
        "RejectUnauthorized": false,
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomAWSCDKOpenIdConnectProviderCustomResourceProviderHandlerF2C543E0",
            "Arn",
          ],
        },
        "Url": "https://token.actions.githubusercontent.com",
      },
      "Type": "Custom::AWSCDKOpenIdConnectProvider",
      "UpdateReplacePolicy": "Delete",
    },
    "InfraResourcesRoleDefaultPolicy650E5DA3": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "cloudformation:DescribeStacks",
                "s3:PutObject",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "InfraResourcesRoleDefaultPolicy650E5DA3",
        "Roles": Array [
          Object {
            "Ref": "InfraResourcesRoleE6C761A4",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
    "InfraResourcesRoleE6C761A4": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/InfraRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleA713F7DC": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRoleWithWebIdentity",
              "Condition": Object {
                "StringEquals": Object {
                  "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
                },
                "StringLike": Object {
                  "token.actions.githubusercontent.com:sub": "repo:OrganizationName/WafRepositoryName:*",
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Federated": Object {
                  "Ref": "GithubActionsOidcProviderF9E986BE",
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "WafRoleDefaultPolicy729EDA04": Object {
      "Properties": Object {
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": Array [
                "wafv2:ListWebACLs",
                "wafv2:GetWebACL",
                "wafv2:UpdateWebACL",
              ],
              "Effect": "Allow",
              "Resource": "*",
            },
          ],
          "Version": "2012-10-17",
        },
        "PolicyName": "WafRoleDefaultPolicy729EDA04",
        "Roles": Array [
          Object {
            "Ref": "WafRoleA713F7DC",
          },
        ],
      },
      "Type": "AWS::IAM::Policy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching 4`] = `
Object {
  "Outputs": Object {
    "ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D": Object {
      "Export": Object {
        "Name": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
      },
      "Value": Object {
        "Ref": "gevannicfsampleCloudFrontDistributionAC208915",
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomCrossRegionExportReaderCustomResourceProviderHandler46647B68": Object {
      "DependsOn": Array [
        "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "d41c8e6342cd078b5ea5aec11522bdb605eae00f4bb98a3fb0b44c827e9b5ca9.zip",
        },
        "Handler": "__entrypoint__.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomCrossRegionExportReaderCustomResourceProviderRole10531BBD": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
        "Policies": Array [
          Object {
            "PolicyDocument": Object {
              "Statement": Array [
                Object {
                  "Action": Array [
                    "ssm:AddTagsToResource",
                    "ssm:RemoveTagsFromResource",
                    "ssm:GetParameters",
                  ],
                  "Effect": "Allow",
                  "Resource": Object {
                    "Fn::Join": Array [
                      "",
                      Array [
                        "arn:",
                        Object {
                          "Ref": "AWS::Partition",
                        },
                        ":ssm:ap-northeast-1:",
                        Object {
                          "Ref": "AWS::AccountId",
                        },
                        ":parameter/cdk/exports/gevanni-cf-sample-Cloudfront/*",
                      ],
                    ],
                  },
                },
              ],
              "Version": "2012-10-17",
            },
            "PolicyName": "Inline",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "ExportsReader8B249524": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "ReaderProps": Object {
          "imports": Object {
            "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886": "{{resolve:ssm:/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886}}",
          },
          "prefix": "gevanni-cf-sample-Cloudfront",
          "region": "ap-northeast-1",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomCrossRegionExportReaderCustomResourceProviderHandler46647B68",
            "Arn",
          ],
        },
      },
      "Type": "Custom::CrossRegionExportReader",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucket194144B2": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "backendBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketAutoDeleteObjectsCustomResource50AFDC77": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontBackendBucketPolicy7054983D",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketPolicy7054983D": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontClosedBucket522EF4F5": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "closedBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketAutoDeleteObjectsCustomResource866CF88C": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketAutoDeleteObjectsCustomResource77545AD2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontDistributionAC208915": Object {
      "Properties": Object {
        "DistributionConfig": Object {
          "CacheBehaviors": Array [
            Object {
              "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
              "Compress": true,
              "PathPattern": "/static/*",
              "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "ViewerProtocolPolicy": "redirect-to-https",
            },
          ],
          "CustomErrorResponses": Array [
            Object {
              "ErrorCachingMinTTL": 20,
              "ErrorCode": 403,
              "ResponseCode": 403,
              "ResponsePagePath": "/static/sorry.html",
            },
          ],
          "DefaultCacheBehavior": Object {
            "AllowedMethods": Array [
              "GET",
              "HEAD",
              "OPTIONS",
              "PUT",
              "PATCH",
              "POST",
              "DELETE",
            ],
            "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
            "Compress": true,
            "OriginRequestPolicyId": "216adef6-5c7f-47e4-b989-5492eafa07d3",
            "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
            "ViewerProtocolPolicy": "redirect-to-https",
          },
          "DefaultRootObject": "/",
          "Enabled": true,
          "HttpVersion": "http2",
          "IPV6Enabled": true,
          "Logging": Object {
            "Bucket": Object {
              "Fn::GetAtt": Array [
                "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                "RegionalDomainName",
              ],
            },
            "IncludeCookies": true,
            "Prefix": "CloudFrontAccessLogs/",
          },
          "Origins": Array [
            Object {
              "CustomOriginConfig": Object {
                "OriginProtocolPolicy": "http-only",
                "OriginSSLProtocols": Array [
                  "TLSv1.2",
                ],
              },
              "DomainName": "www.example.com",
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
              "OriginCustomHeaders": Array [
                Object {
                  "HeaderName": "x-pre-shared-key",
                  "HeaderValue": "pre-string-for-preSharedKey",
                },
              ],
            },
            Object {
              "DomainName": Object {
                "Fn::GetAtt": Array [
                  "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                  "RegionalDomainName",
                ],
              },
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "OriginAccessControlId": Object {
                "Fn::GetAtt": Array [
                  "gevannicfsampleCloudFrontDistributionOrigin2S3OriginAccessControl13D1FEFE",
                  "Id",
                ],
              },
              "S3OriginConfig": Object {
                "OriginAccessIdentity": "",
              },
            },
          ],
          "WebACLId": Object {
            "Fn::GetAtt": Array [
              "ExportsReader8B249524",
              "/cdk/exports/gevanni-cf-sample-Cloudfront/gevannicfsampleWafuseast1FnGetAttgevannicfsampleWafWebAcl3033DA86ArnDEB4B886",
            ],
          },
        },
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::CloudFront::Distribution",
    },
    "gevannicfsampleCloudFrontDistributionOrigin2S3OriginAccessControl13D1FEFE": Object {
      "Properties": Object {
        "OriginAccessControlConfig": Object {
          "Name": "gevannicfsampleCloudfrontgevOrigin2S3OriginAccessControl0B600342",
          "OriginAccessControlOriginType": "s3",
          "SigningBehavior": "always",
          "SigningProtocol": "sigv4",
        },
      },
      "Type": "AWS::CloudFront::OriginAccessControl",
    },
    "gevannicfsampleCloudFrontWebBucket9A2CE5C9": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "webContentBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketAutoDeleteObjectsCustomResource4EDA3CC2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontWebBucketPolicyED75C388",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketPolicyED75C388": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "s3:GetObject",
              "Condition": Object {
                "StringEquals": Object {
                  "AWS:SourceArn": Object {
                    "Fn::Join": Array [
                      "",
                      Array [
                        "arn:",
                        Object {
                          "Ref": "AWS::Partition",
                        },
                        ":cloudfront::",
                        Object {
                          "Ref": "AWS::AccountId",
                        },
                        ":distribution/",
                        Object {
                          "Ref": "gevannicfsampleCloudFrontDistributionAC208915",
                        },
                      ],
                    ],
                  },
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "cloudfront.amazonaws.com",
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                        "Arn",
                      ],
                    },
                    "/*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketAutoDeleteObjectsCustomResource7C6FC775": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "LogDeliveryWrite",
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching 5`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "gevannicfsampleDashboard1A3EDEB7": Object {
      "Properties": Object {
        "DashboardBody": Object {
          "Fn::Join": Array [
            "",
            Array [
              "{\\"widgets\\":[{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":0,\\"properties\\":{\\"markdown\\":\\"# Requests\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":24,\\"x\\":0,\\"y\\":1,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Requests\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":false,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"Requests\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60,\\"stat\\":\\"Sum\\"}]],\\"yAxis\\":{}}},{\\"type\\":\\"text\\",\\"width\\":24,\\"height\\":1,\\"x\\":0,\\"y\\":25,\\"properties\\":{\\"markdown\\":\\"# Errors\\"}},{\\"type\\":\\"metric\\",\\"width\\":6,\\"height\\":24,\\"x\\":0,\\"y\\":26,\\"properties\\":{\\"view\\":\\"timeSeries\\",\\"title\\":\\"CloudFront Error Rates\\",\\"region\\":\\"",
              Object {
                "Ref": "AWS::Region",
              },
              "\\",\\"stacked\\":true,\\"metrics\\":[[\\"AWS/CloudFront\\",\\"5xxErrorRate\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}],[\\"AWS/CloudFront\\",\\"4xxErrorRate\\",\\"DistributionId\\",\\"",
              Object {
                "Fn::ImportValue": "gevanni-cf-sample-Cloudfront:ExportsOutputRefgevannicfsampleCloudFrontDistributionAC20891593D65F5D",
              },
              "\\",\\"Region\\",\\"Global\\",{\\"label\\":\\"\${PROP('MetricName')} /\${PROP('Period')}sec\\",\\"region\\":\\"us-east-1\\",\\"period\\":60}]],\\"yAxis\\":{}}}]}",
            ],
          ],
        },
        "DashboardName": "gevanni-cf-sample-ECSApp",
      },
      "Type": "AWS::CloudWatch::Dashboard",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;

exports[`gevanni-cf-sample Guest Stacks should snapshot matching open search 1`] = `
Object {
  "Outputs": Object {
    "CollectionEndpoint": Object {
      "Export": Object {
        "Name": "gevanni-cf-sample-CollectionEndpoint",
      },
      "Value": Object {
        "Fn::GetAtt": Array [
          "OpenSearchCollection",
          "CollectionEndpoint",
        ],
      },
    },
  },
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "OpenSearchCollection": Object {
      "DependsOn": Array [
        "gevannicfsampleSecurityPolicy",
        "OpenSearchDataAccessPolicy",
        "OpenSearchNetworkPolicy",
      ],
      "Properties": Object {
        "Name": "gevanni",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
        "Type": "SEARCH",
      },
      "Type": "AWS::OpenSearchServerless::Collection",
    },
    "OpenSearchDataAccessPolicy": Object {
      "Properties": Object {
        "Name": "opensearch-data-access-policy",
        "Policy": Object {
          "Fn::Join": Array [
            "",
            Array [
              "[{\\"Rules\\":[{\\"Resource\\":[\\"collection/gevanni\\"],\\"Permission\\":[\\"aoss:CreateCollectionItems\\",\\"aoss:DeleteCollectionItems\\",\\"aoss:UpdateCollectionItems\\",\\"aoss:DescribeCollectionItems\\"],\\"ResourceType\\":\\"collection\\"},{\\"Resource\\":[\\"index/gevanni/*\\"],\\"Permission\\":[\\"aoss:CreateIndex\\",\\"aoss:DeleteIndex\\",\\"aoss:UpdateIndex\\",\\"aoss:DescribeIndex\\",\\"aoss:ReadDocument\\",\\"aoss:WriteDocument\\"],\\"ResourceType\\":\\"index\\"}],\\"Principal\\":[\\"",
              Object {
                "Fn::GetAtt": Array [
                  "OpenSearchRole299A17B1",
                  "Arn",
                ],
              },
              "\\"],\\"Description\\":\\"Full access to data\\"}]",
            ],
          ],
        },
        "Type": "data",
      },
      "Type": "AWS::OpenSearchServerless::AccessPolicy",
    },
    "OpenSearchNetworkPolicy": Object {
      "Properties": Object {
        "Name": "opensearch-network-policy",
        "Policy": "[{\\"Rules\\":[{\\"Resource\\":[\\"collection/gevanni\\"],\\"ResourceType\\":\\"collection\\"}],\\"AllowFromPublic\\":false,\\"SourceVPCEs\\":[\\"vpce-sample\\"]}]",
        "Type": "network",
      },
      "Type": "AWS::OpenSearchServerless::SecurityPolicy",
    },
    "OpenSearchRole299A17B1": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      "arn:",
                      Object {
                        "Ref": "AWS::Partition",
                      },
                      ":iam::accountid-sample:root",
                    ],
                  ],
                },
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "RoleName": "gevanni-cf-sample-RoleForOpenSearchRole",
        "Tags": Array [
          Object {
            "Key": "Environment",
            "Value": "dev",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "gevannicfsampleSecurityPolicy": Object {
      "Properties": Object {
        "Name": "opensearch-collection-policy",
        "Policy": "{\\"Rules\\":[{\\"ResourceType\\":\\"collection\\",\\"Resource\\":[\\"collection/gevanni\\"]}],\\"AWSOwnedKey\\":true}",
        "Type": "encryption",
      },
      "Type": "AWS::OpenSearchServerless::SecurityPolicy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
