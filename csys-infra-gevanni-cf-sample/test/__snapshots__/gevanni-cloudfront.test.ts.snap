// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`gevanni-cf-sample Cloudfront Stacks should snapshot matching 1`] = `
Object {
  "Parameters": Object {
    "BootstrapVersion": Object {
      "Default": "/cdk-bootstrap/hnb659fds/version",
      "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]",
      "Type": "AWS::SSM::Parameter::Value<String>",
    },
  },
  "Resources": Object {
    "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": Object {
      "DependsOn": Array [
        "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
      ],
      "Properties": Object {
        "Code": Object {
          "S3Bucket": Object {
            "Fn::Sub": "cdk-hnb659fds-assets-\${AWS::AccountId}-ap-northeast-1",
          },
          "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip",
        },
        "Description": Object {
          "Fn::Join": Array [
            "",
            Array [
              "Lambda function for auto-deleting objects in ",
              Object {
                "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
              },
              " S3 bucket.",
            ],
          ],
        },
        "Handler": "index.handler",
        "MemorySize": 128,
        "Role": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
            "Arn",
          ],
        },
        "Runtime": "nodejs20.x",
        "Timeout": 900,
      },
      "Type": "AWS::Lambda::Function",
    },
    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": Object {
      "Properties": Object {
        "AssumeRolePolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "sts:AssumeRole",
              "Effect": "Allow",
              "Principal": Object {
                "Service": "lambda.amazonaws.com",
              },
            },
          ],
          "Version": "2012-10-17",
        },
        "ManagedPolicyArns": Array [
          Object {
            "Fn::Sub": "arn:\${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
          },
        ],
      },
      "Type": "AWS::IAM::Role",
    },
    "gevannicfsampleCloudFrontBackendBucket194144B2": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "backendBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketAutoDeleteObjectsCustomResource50AFDC77": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontBackendBucketPolicy7054983D",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontBackendBucketPolicy7054983D": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontBackendBucket194144B2",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontBackendBucket194144B2",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontBackendBucket194144B2",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontClosedBucket522EF4F5": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "closedBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketAutoDeleteObjectsCustomResource866CF88C": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontClosedBucketPolicyF22DC279": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontClosedBucket522EF4F5",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontClosedBucket522EF4F5",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketAutoDeleteObjectsCustomResource77545AD2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontCloudFrontLogBucketPolicy4D1B2B32": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFrontDistributionAC208915": Object {
      "Properties": Object {
        "DistributionConfig": Object {
          "CacheBehaviors": Array [
            Object {
              "CachePolicyId": "658327ea-f89d-4fab-a63d-7e88639e58f6",
              "Compress": true,
              "PathPattern": "/static/*",
              "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "ViewerProtocolPolicy": "redirect-to-https",
            },
          ],
          "CustomErrorResponses": Array [
            Object {
              "ErrorCachingMinTTL": 20,
              "ErrorCode": 403,
              "ResponseCode": 403,
              "ResponsePagePath": "/static/sorry.html",
            },
          ],
          "DefaultCacheBehavior": Object {
            "AllowedMethods": Array [
              "GET",
              "HEAD",
              "OPTIONS",
              "PUT",
              "PATCH",
              "POST",
              "DELETE",
            ],
            "CachePolicyId": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad",
            "Compress": true,
            "OriginRequestPolicyId": "216adef6-5c7f-47e4-b989-5492eafa07d3",
            "TargetOriginId": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
            "ViewerProtocolPolicy": "redirect-to-https",
          },
          "DefaultRootObject": "/",
          "Enabled": true,
          "HttpVersion": "http2",
          "IPV6Enabled": true,
          "Logging": Object {
            "Bucket": Object {
              "Fn::GetAtt": Array [
                "gevannicfsampleCloudFrontCloudFrontLogBucket30EDDD63",
                "RegionalDomainName",
              ],
            },
            "IncludeCookies": true,
            "Prefix": "CloudFrontAccessLogs/",
          },
          "Origins": Array [
            Object {
              "CustomOriginConfig": Object {
                "OriginProtocolPolicy": "http-only",
                "OriginSSLProtocols": Array [
                  "TLSv1.2",
                ],
              },
              "DomainName": "www.example.com",
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin1C6227165",
              "OriginCustomHeaders": Array [
                Object {
                  "HeaderName": "x-pre-shared-key",
                  "HeaderValue": "pre-string-for-preSharedKey",
                },
              ],
            },
            Object {
              "DomainName": Object {
                "Fn::GetAtt": Array [
                  "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                  "RegionalDomainName",
                ],
              },
              "Id": "gevannicfsampleCloudfrontgevannicfsampleCloudFrontDistributionOrigin24FBBA6D6",
              "OriginAccessControlId": Object {
                "Fn::GetAtt": Array [
                  "gevannicfsampleCloudFrontDistributionOrigin2S3OriginAccessControl13D1FEFE",
                  "Id",
                ],
              },
              "S3OriginConfig": Object {
                "OriginAccessIdentity": "",
              },
            },
          ],
        },
      },
      "Type": "AWS::CloudFront::Distribution",
    },
    "gevannicfsampleCloudFrontDistributionOrigin2S3OriginAccessControl13D1FEFE": Object {
      "Properties": Object {
        "OriginAccessControlConfig": Object {
          "Name": "gevannicfsampleCloudfrontgevOrigin2S3OriginAccessControl0B600342",
          "OriginAccessControlOriginType": "s3",
          "SigningBehavior": "always",
          "SigningProtocol": "sigv4",
        },
      },
      "Type": "AWS::CloudFront::OriginAccessControl",
    },
    "gevannicfsampleCloudFrontWebBucket9A2CE5C9": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "Private",
        "BucketEncryption": Object {
          "ServerSideEncryptionConfiguration": Array [
            Object {
              "ServerSideEncryptionByDefault": Object {
                "SSEAlgorithm": "AES256",
              },
            },
          ],
        },
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "NoncurrentVersionExpiration": Object {
                "NoncurrentDays": 14,
              },
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "LoggingConfiguration": Object {
          "DestinationBucketName": Object {
            "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
          },
          "LogFilePrefix": "webContentBucket/",
        },
        "PublicAccessBlockConfiguration": Object {
          "BlockPublicAcls": true,
          "BlockPublicPolicy": true,
          "IgnorePublicAcls": true,
          "RestrictPublicBuckets": true,
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
        "VersioningConfiguration": Object {
          "Status": "Enabled",
        },
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketAutoDeleteObjectsCustomResource4EDA3CC2": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFrontWebBucketPolicyED75C388",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFrontWebBucketPolicyED75C388": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": "s3:GetObject",
              "Condition": Object {
                "StringEquals": Object {
                  "AWS:SourceArn": Object {
                    "Fn::Join": Array [
                      "",
                      Array [
                        "arn:",
                        Object {
                          "Ref": "AWS::Partition",
                        },
                        ":cloudfront::",
                        Object {
                          "Ref": "AWS::AccountId",
                        },
                        ":distribution/",
                        Object {
                          "Ref": "gevannicfsampleCloudFrontDistributionAC208915",
                        },
                      ],
                    ],
                  },
                },
              },
              "Effect": "Allow",
              "Principal": Object {
                "Service": "cloudfront.amazonaws.com",
              },
              "Resource": Object {
                "Fn::Join": Array [
                  "",
                  Array [
                    Object {
                      "Fn::GetAtt": Array [
                        "gevannicfsampleCloudFrontWebBucket9A2CE5C9",
                        "Arn",
                      ],
                    },
                    "/*",
                  ],
                ],
              },
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketAutoDeleteObjectsCustomResource7C6FC775": Object {
      "DeletionPolicy": "Delete",
      "DependsOn": Array [
        "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75",
      ],
      "Properties": Object {
        "BucketName": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "ServiceToken": Object {
          "Fn::GetAtt": Array [
            "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F",
            "Arn",
          ],
        },
      },
      "Type": "Custom::S3AutoDeleteObjects",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748": Object {
      "DeletionPolicy": "Delete",
      "Properties": Object {
        "AccessControl": "LogDeliveryWrite",
        "LifecycleConfiguration": Object {
          "Rules": Array [
            Object {
              "ExpirationInDays": 1825,
              "Status": "Enabled",
            },
            Object {
              "AbortIncompleteMultipartUpload": Object {
                "DaysAfterInitiation": 30,
              },
              "Status": "Enabled",
            },
          ],
        },
        "OwnershipControls": Object {
          "Rules": Array [
            Object {
              "ObjectOwnership": "ObjectWriter",
            },
          ],
        },
        "Tags": Array [
          Object {
            "Key": "aws-cdk:auto-delete-objects",
            "Value": "true",
          },
        ],
      },
      "Type": "AWS::S3::Bucket",
      "UpdateReplacePolicy": "Delete",
    },
    "gevannicfsampleCloudFronts3AccessLogsBucketPolicy9F5B9D75": Object {
      "Properties": Object {
        "Bucket": Object {
          "Ref": "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
        },
        "PolicyDocument": Object {
          "Statement": Array [
            Object {
              "Action": "s3:*",
              "Condition": Object {
                "Bool": Object {
                  "aws:SecureTransport": "false",
                },
              },
              "Effect": "Deny",
              "Principal": Object {
                "AWS": "*",
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
            Object {
              "Action": Array [
                "s3:PutBucketPolicy",
                "s3:GetBucket*",
                "s3:List*",
                "s3:DeleteObject*",
              ],
              "Effect": "Allow",
              "Principal": Object {
                "AWS": Object {
                  "Fn::GetAtt": Array [
                    "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092",
                    "Arn",
                  ],
                },
              },
              "Resource": Array [
                Object {
                  "Fn::GetAtt": Array [
                    "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                    "Arn",
                  ],
                },
                Object {
                  "Fn::Join": Array [
                    "",
                    Array [
                      Object {
                        "Fn::GetAtt": Array [
                          "gevannicfsampleCloudFronts3AccessLogsBucketCC07F748",
                          "Arn",
                        ],
                      },
                      "/*",
                    ],
                  ],
                },
              ],
            },
          ],
          "Version": "2012-10-17",
        },
      },
      "Type": "AWS::S3::BucketPolicy",
    },
  },
  "Rules": Object {
    "CheckBootstrapVersion": Object {
      "Assertions": Array [
        Object {
          "Assert": Object {
            "Fn::Not": Array [
              Object {
                "Fn::Contains": Array [
                  Array [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                  ],
                  Object {
                    "Ref": "BootstrapVersion",
                  },
                ],
              },
            ],
          },
          "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI.",
        },
      ],
    },
  },
}
`;
