import * as cdk from 'aws-cdk-lib';
import { Match, Template } from 'aws-cdk-lib/assertions';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import { IConfig } from '../params/interface';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { RoleForAppTeam } from '../lib/stack/role-for-app-team-construct';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';

// Account and Region on test
//  cdk.process.env.* returns undefined, and cdk.Stack.of(this).* returns ${Token[Region.4]} at test time.
//  In such case, RegionInfo.get(cdk.Stack.of(this).region) returns error and test will fail.
//  So we pass 'ap-northeast-1' as region code.
const procEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION ?? 'ap-northeast-1',
};

const pjPrefix = 'gevanni-cf-sample';
var app: any;


const envKey = 'dev';

const config: IConfig = require('../params/' + envKey);

function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnv;
  }
}

beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';
  cdk.Tags.of(app).add(envTagName, envKey);
})

describe(`${pjPrefix} Cloudfront Stacks`, () => {
  const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
    pjPrefix: pjPrefix,
    webAcl: undefined,
    CertificateIdentifier: config.CertificateIdentifier,
    cloudFrontParam: config.CloudFrontParam,
    appAlbsParam: config.appAlbsParam,
    logRemovalPolicyParam: config.LogRemovalPolicyParam,
    webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
    env: getProcEnv(),
    crossRegionReferences: true,
    accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
    webContentVersionExpiration: config.webContentVersionExpiration,
  });

  const template = Template.fromStack(cloudfront);

  test('should snapshot matching', () => {
    expect(template).toMatchSnapshot();
  });

  test('should define cloudfrontID', () => {
    expect(cloudfront.cfDistributionId).toBeDefined();
  });

  test('should resource created', () => {
    template.resourceCountIs('AWS::S3::Bucket', 5);
  });

  test('should create CloudFront distribution', () => {
    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Enabled: true,
        DefaultCacheBehavior: {
          ViewerProtocolPolicy: 'redirect-to-https',
        },
      },
    });
  });

  test('should create IAM role with correct policies', () => {
    template.hasResourceProperties('AWS::IAM::Role', {
      AssumeRolePolicyDocument: {
        Statement: [
          {
            Effect: 'Allow',
            Principal: {
              Service: 'lambda.amazonaws.com',
            },
            Action: 'sts:AssumeRole',
          },
        ],
      },
    });
  });

  // test cloudfront have create origin with "DomainName": "www.example.com"
  test('should create CloudFront origin with correct domain name', () => {
    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Origins: [
          {
            DomainName: 'www.example.com',
          },
          {
            DomainName: {
              'Fn::GetAtt': [
                Match.stringLikeRegexp('CloudFrontWebBucket'),
                'RegionalDomainName',
              ],
            },
          },
        ],
      },
    });
  });

  // test cloudfront enable logging
  test('should enable CloudFront logging', () => {
    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Logging: {
          // Enabled: true,
          Bucket: {
            'Fn::GetAtt': [
              Match.stringLikeRegexp('CloudFrontLogBucket'),
              'RegionalDomainName',
            ],
          },
          IncludeCookies: true,
          Prefix: "CloudFrontAccessLogs/"
        },
      },
    });
  });
  // test s3 BackendBucket enable auto delete objects
  test('should enable auto delete objects for BackendBucket', () => {
    template.hasResourceProperties('Custom::S3AutoDeleteObjects', {
      BucketName: Match.objectLike({
        Ref: Match.stringLikeRegexp('BackendBucket'),
      }),
    });
  })
  test('should disable auto delete objects for BackendBucket', () => {
    const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-delete-object`, {
      pjPrefix: pjPrefix,
      webAcl: undefined,
      CertificateIdentifier: config.CertificateIdentifier,
      cloudFrontParam: config.CloudFrontParam,
      appAlbsParam: config.appAlbsParam,
      logRemovalPolicyParam: {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: false,
        emptyOnDelete: true,
      },
      webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      env: getProcEnv(),
      crossRegionReferences: true,
      accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      webContentVersionExpiration: config.webContentVersionExpiration,
    });
    const template = Template.fromStack(cloudfront);

    template.hasResourceProperties('Custom::S3AutoDeleteObjects', {
      BucketName: Match.not({
        Ref: Match.stringLikeRegexp('BackendBucket'),
      }),
    });
  })

  // test case createClosedBucket = true
  test('should create closed bucket when createClosedBucket = true', () => {
    const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront-create-closed-bucket`, {
      pjPrefix: pjPrefix,
      webAcl: undefined,
      CertificateIdentifier: config.CertificateIdentifier,
      cloudFrontParam: {
        fqdn: '',
        createClosedBucket: true,
      },
      appAlbsParam: config.appAlbsParam,
      logRemovalPolicyParam: {
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        autoDeleteObjects: false,
        emptyOnDelete: true,
      },
      webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
      env: getProcEnv(),
      crossRegionReferences: true,
      accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      webContentVersionExpiration: config.webContentVersionExpiration,
    });
    const template = Template.fromStack(cloudfront);

    template.hasResourceProperties('AWS::CloudFront::Distribution', {
      DistributionConfig: {
        Origins: [
          {
            DomainName: {
              'Fn::GetAtt': [
                Match.stringLikeRegexp('CloudFrontClosedBucket'),
                'RegionalDomainName',
              ],
            },
          },
        ],
      },
    });
  });


});
