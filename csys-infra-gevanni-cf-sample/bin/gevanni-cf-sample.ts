import * as cdk from 'aws-cdk-lib';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import * as fs from 'fs';
import { IConfig } from '../params/interface';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { RoleForAppTeam } from '../lib/stack/role-for-app-team-construct';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import { TidbBackupStack } from '../lib/stack/tidb-backup-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';

const app = new cdk.App();

// ----------------------- Load context variables ------------------------------
// This context need to be specified in args
const argContext = 'environment';
const envKey = app.node.tryGetContext(argContext);
if (envKey == undefined)
  throw new Error(`Please specify environment with context option. ex) cdk deploy -c ${argContext}=dev`);
//Read Typescript Environment file
const TsEnvPath = './params/' + envKey + '.ts';
if (!fs.existsSync(TsEnvPath)) throw new Error(`Can't find a ts environment file [../params/` + envKey + `.ts]`);

//ESLintではrequireの利用が禁止されているため除外コメントを追加
//https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/issues/29#issuecomment-**********
const config: IConfig = require('../params/' + envKey);

// Add envName to Stack for avoiding duplication of Stack names.
const pjPrefix = config.Env.envName + 'gevanni-cf-sample';

// ----------------------- Environment variables for stack ------------------------------
// Default environment
const procEnvDefault = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

// Define account id and region from context.
// If "env" isn't defined on the environment variable in context, use account and region specified by "--profile".
function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnvDefault;
  }
}

// ----------------------- Share Resources Stacks ------------------------------

const shareResourcesStack = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
  pjPrefix: pjPrefix,
  env: getProcEnv(),
});

// ----------------------- Guest System Stacks ------------------------------

const wafCloudfront = new WafCfStack(app, `${pjPrefix}-WafCloudfront`, {
  pjPrefix: pjPrefix,
  scope: 'CLOUDFRONT',
  env: {
    account: getProcEnv().account,
    region: 'us-east-1',
  },
  crossRegionReferences: true,
  ...config.WafParam,
  wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
  csirtWAFParam: config.CSIRTWAFParamCF,
  wafLogToNewRelicSetting: config.wafLogToNewRelicSetting,
});

new OidcStack(app, `${pjPrefix}-OIDC`, {
  OrganizationName: config.OidcParam.OrganizationName,
  RepositoryNames: config.OidcParam.RepositoryNames,
  env: getProcEnv(),
});

const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
  pjPrefix: pjPrefix,
  webAcl: wafCloudfront.webAcl,
  CertificateIdentifier: config.CertificateIdentifier,
  cloudFrontParam: config.CloudFrontParam,
  appAlbsParam: config.appAlbsParam,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
  env: getProcEnv(),
  crossRegionReferences: true,
  accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
  webContentVersionExpiration: config.webContentVersionExpiration,
  cloudfrontLogToNewRelicSetting: config.cloudfrontLogToNewRelicSetting
});

new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
  pjPrefix: `${pjPrefix}`,
  dashboardName: `${pjPrefix}-ECSApp`,
  cfDistributionId: cloudfront.cfDistributionId,
  env: getProcEnv(),
});

if (config.roleForAppTeamParam) {
  new RoleForAppTeam(app, `${pjPrefix}-SMSStack`, {
    pjPrefix: `${pjPrefix}`,
    allowedRoles: config.roleForAppTeamParam?.allowedRoles,
    env: getProcEnv(),
  });
}

if (config.openSearchParam) {
  const opensearch = new OpenSearchStack(app, `${pjPrefix}-Opensearch`, {
    pjPrefix: `${pjPrefix}`,
    env: getProcEnv(),
    ...config.openSearchParam,
  });
}

if (config.TidbBackupParam.tidbCloudAccountId && config.TidbBackupParam.tidbCloudExternalId) {
  new TidbBackupStack(app, `${pjPrefix}-TidbBackup`, {
    pjPrefix,
    backupBucketRemovalPolicy: config.OtherRemovalPolicyParam,
    ...config.TidbBackupParam,
  });
}

// --------------------------------- Tagging  -------------------------------------

// Tagging "Environment" tag to all resources in this app
const envTagName = 'Environment';
cdk.Tags.of(app).add(envTagName, config.Env.envName);
